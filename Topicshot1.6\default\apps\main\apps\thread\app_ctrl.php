<?php 
# @*************************************************************************@
# @ Software author: <PERSON><PERSON> (<PERSON>ur_TL)                               @
# @ UI/UX Designer & Web developer ;)                                       @
# @                                                                         @
# @*************************************************************************@
# @ Instagram: https://www.instagram.com/mansur_tl                          @
# @ VK: https://vk.com/mansur_tl_uiux                                       @
# @ Envato: http://codecanyon.net/user/mansur_tl                            @
# @ Behance: https://www.behance.net/mansur_tl                              @
# @ Telegram: https://t.me/mansurtl_contact                                 @
# @*************************************************************************@
# @ E-mail: <EMAIL>                                      @
# @ Website: https://www.mansurtl.com                                       @
# @*************************************************************************@
# @ ColibriSM - The Ultimate Social Network PHP Script                      @
# @ Copyright (c)  ColibriSM. All rights reserved                           @
# @*************************************************************************@

function cl_get_thread_data($post_id = false) {
	global $db, $cl;

	if (not_num($post_id)) {
		return false;
	}

	$post           = cl_raw_post_data($post_id);
	$data           = array(
		'post'      => array(),
		'next'      => array(),
		'can_reply' => true,
		'can_see'   => true
	);

	if (cl_queryset($post) && $post["status"] != "orphan") {
		$data['can_reply'] = cl_can_reply($post);
		$data['post']      = cl_post_data($post);
		$data['next']      = cl_get_thread_child_posts($post_id, 30);
	}

	return $data;
}

function cl_get_thread_parent_posts($post_obj = array()) {
    global $db, $cl;

    if (not_empty($post_obj['thread_id'])) {
        $db = $db->where('id', $post_obj['thread_id']);
        $db = $db->where('status', array('active','inactive','deleted'),'IN');
        $qr = $db->getOne(T_PUBS);

        if (cl_queryset($qr)) {
        	$cl['_'][] = cl_post_data($qr);

        	return cl_get_thread_parent_posts($qr);
        }
    }

    else {
    	return ((not_empty($cl['_'])) ? $cl['_'] : false);
    }
}

function cl_get_thread_child_posts($post_id = false, $limit = null, $offset = false, $offset_to = 'lt') {
    global $db, $cl;

    if (not_num($post_id)) {
    	return false;
    }

    $offset_to    = (($offset_to == 'gt') ? '>' : '<');
    $data         = array();
	$db           = $db->where('thread_id', $post_id);
	$db           = $db->where('status', array('active','inactive','deleted'),'IN');
	$db           = ((is_posnum($offset)) ? $db->where('id', $offset, $offset_to) : $db);
	$db           = $db->orderBy('id','DESC');
	$child_posts  = $db->get(T_PUBS, $limit);

	if (cl_queryset($child_posts)) {
		foreach ($child_posts as $row) {
			$row['replys'] = array();
			$db            = $db->where('thread_id', $row['id']);
			$db            = $db->where('status', array('active','inactive','deleted'),'IN');
			$db            = $db->orderBy('id','DESC');
			$replys        = $db->get(T_PUBS, 2);

			if (cl_queryset($replys)) {
				foreach ($replys as $reply) {
					$row['replys'][] = cl_post_data($reply);
				}
			}

			$data[] = cl_post_data($row);
		}
	}

	return $data;
}
