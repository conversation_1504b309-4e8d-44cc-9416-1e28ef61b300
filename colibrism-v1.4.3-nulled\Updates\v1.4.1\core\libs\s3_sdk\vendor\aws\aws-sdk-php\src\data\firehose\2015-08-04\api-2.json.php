<?php
// This file was auto-generated from sdk-root/src/data/firehose/2015-08-04/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-08-04', 'endpointPrefix' => 'firehose', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'Firehose', 'serviceFullName' => 'Amazon Kinesis Firehose', 'serviceId' => 'Firehose', 'signatureVersion' => 'v4', 'targetPrefix' => 'Firehose_20150804', 'uid' => 'firehose-2015-08-04', ], 'operations' => [ 'CreateDeliveryStream' => [ 'name' => 'CreateDeliveryStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDeliveryStreamInput', ], 'output' => [ 'shape' => 'CreateDeliveryStreamOutput', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidKMSResourceException', ], ], ], 'DeleteDeliveryStream' => [ 'name' => 'DeleteDeliveryStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDeliveryStreamInput', ], 'output' => [ 'shape' => 'DeleteDeliveryStreamOutput', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeDeliveryStream' => [ 'name' => 'DescribeDeliveryStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDeliveryStreamInput', ], 'output' => [ 'shape' => 'DescribeDeliveryStreamOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListDeliveryStreams' => [ 'name' => 'ListDeliveryStreams', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDeliveryStreamsInput', ], 'output' => [ 'shape' => 'ListDeliveryStreamsOutput', ], ], 'ListTagsForDeliveryStream' => [ 'name' => 'ListTagsForDeliveryStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForDeliveryStreamInput', ], 'output' => [ 'shape' => 'ListTagsForDeliveryStreamOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'PutRecord' => [ 'name' => 'PutRecord', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRecordInput', ], 'output' => [ 'shape' => 'PutRecordOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InvalidKMSResourceException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'PutRecordBatch' => [ 'name' => 'PutRecordBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRecordBatchInput', ], 'output' => [ 'shape' => 'PutRecordBatchOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InvalidKMSResourceException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'StartDeliveryStreamEncryption' => [ 'name' => 'StartDeliveryStreamEncryption', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDeliveryStreamEncryptionInput', ], 'output' => [ 'shape' => 'StartDeliveryStreamEncryptionOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidKMSResourceException', ], ], ], 'StopDeliveryStreamEncryption' => [ 'name' => 'StopDeliveryStreamEncryption', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopDeliveryStreamEncryptionInput', ], 'output' => [ 'shape' => 'StopDeliveryStreamEncryptionOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'TagDeliveryStream' => [ 'name' => 'TagDeliveryStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagDeliveryStreamInput', ], 'output' => [ 'shape' => 'TagDeliveryStreamOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UntagDeliveryStream' => [ 'name' => 'UntagDeliveryStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagDeliveryStreamInput', ], 'output' => [ 'shape' => 'UntagDeliveryStreamOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UpdateDestination' => [ 'name' => 'UpdateDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDestinationInput', ], 'output' => [ 'shape' => 'UpdateDestinationOutput', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], ], 'shapes' => [ 'AWSKMSKeyARN' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'arn:.*', ], 'AmazonOpenSearchServerlessBufferingHints' => [ 'type' => 'structure', 'members' => [ 'IntervalInSeconds' => [ 'shape' => 'AmazonOpenSearchServerlessBufferingIntervalInSeconds', ], 'SizeInMBs' => [ 'shape' => 'AmazonOpenSearchServerlessBufferingSizeInMBs', ], ], ], 'AmazonOpenSearchServerlessBufferingIntervalInSeconds' => [ 'type' => 'integer', 'max' => 900, 'min' => 60, ], 'AmazonOpenSearchServerlessBufferingSizeInMBs' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'AmazonOpenSearchServerlessCollectionEndpoint' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'https:.*', ], 'AmazonOpenSearchServerlessDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'RoleARN', 'IndexName', 'S3Configuration', ], 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'CollectionEndpoint' => [ 'shape' => 'AmazonOpenSearchServerlessCollectionEndpoint', ], 'IndexName' => [ 'shape' => 'AmazonOpenSearchServerlessIndexName', ], 'BufferingHints' => [ 'shape' => 'AmazonOpenSearchServerlessBufferingHints', ], 'RetryOptions' => [ 'shape' => 'AmazonOpenSearchServerlessRetryOptions', ], 'S3BackupMode' => [ 'shape' => 'AmazonOpenSearchServerlessS3BackupMode', ], 'S3Configuration' => [ 'shape' => 'S3DestinationConfiguration', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'VpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], ], ], 'AmazonOpenSearchServerlessDestinationDescription' => [ 'type' => 'structure', 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'CollectionEndpoint' => [ 'shape' => 'AmazonOpenSearchServerlessCollectionEndpoint', ], 'IndexName' => [ 'shape' => 'AmazonOpenSearchServerlessIndexName', ], 'BufferingHints' => [ 'shape' => 'AmazonOpenSearchServerlessBufferingHints', ], 'RetryOptions' => [ 'shape' => 'AmazonOpenSearchServerlessRetryOptions', ], 'S3BackupMode' => [ 'shape' => 'AmazonOpenSearchServerlessS3BackupMode', ], 'S3DestinationDescription' => [ 'shape' => 'S3DestinationDescription', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'VpcConfigurationDescription' => [ 'shape' => 'VpcConfigurationDescription', ], ], ], 'AmazonOpenSearchServerlessDestinationUpdate' => [ 'type' => 'structure', 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'CollectionEndpoint' => [ 'shape' => 'AmazonOpenSearchServerlessCollectionEndpoint', ], 'IndexName' => [ 'shape' => 'AmazonOpenSearchServerlessIndexName', ], 'BufferingHints' => [ 'shape' => 'AmazonOpenSearchServerlessBufferingHints', ], 'RetryOptions' => [ 'shape' => 'AmazonOpenSearchServerlessRetryOptions', ], 'S3Update' => [ 'shape' => 'S3DestinationUpdate', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], ], ], 'AmazonOpenSearchServerlessIndexName' => [ 'type' => 'string', 'max' => 80, 'min' => 1, 'pattern' => '.*', ], 'AmazonOpenSearchServerlessRetryDurationInSeconds' => [ 'type' => 'integer', 'max' => 7200, 'min' => 0, ], 'AmazonOpenSearchServerlessRetryOptions' => [ 'type' => 'structure', 'members' => [ 'DurationInSeconds' => [ 'shape' => 'AmazonOpenSearchServerlessRetryDurationInSeconds', ], ], ], 'AmazonOpenSearchServerlessS3BackupMode' => [ 'type' => 'string', 'enum' => [ 'FailedDocumentsOnly', 'AllDocuments', ], ], 'AmazonopensearchserviceBufferingHints' => [ 'type' => 'structure', 'members' => [ 'IntervalInSeconds' => [ 'shape' => 'AmazonopensearchserviceBufferingIntervalInSeconds', ], 'SizeInMBs' => [ 'shape' => 'AmazonopensearchserviceBufferingSizeInMBs', ], ], ], 'AmazonopensearchserviceBufferingIntervalInSeconds' => [ 'type' => 'integer', 'max' => 900, 'min' => 60, ], 'AmazonopensearchserviceBufferingSizeInMBs' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'AmazonopensearchserviceClusterEndpoint' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'https:.*', ], 'AmazonopensearchserviceDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'RoleARN', 'IndexName', 'S3Configuration', ], 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'DomainARN' => [ 'shape' => 'AmazonopensearchserviceDomainARN', ], 'ClusterEndpoint' => [ 'shape' => 'AmazonopensearchserviceClusterEndpoint', ], 'IndexName' => [ 'shape' => 'AmazonopensearchserviceIndexName', ], 'TypeName' => [ 'shape' => 'AmazonopensearchserviceTypeName', ], 'IndexRotationPeriod' => [ 'shape' => 'AmazonopensearchserviceIndexRotationPeriod', ], 'BufferingHints' => [ 'shape' => 'AmazonopensearchserviceBufferingHints', ], 'RetryOptions' => [ 'shape' => 'AmazonopensearchserviceRetryOptions', ], 'S3BackupMode' => [ 'shape' => 'AmazonopensearchserviceS3BackupMode', ], 'S3Configuration' => [ 'shape' => 'S3DestinationConfiguration', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'VpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], 'DocumentIdOptions' => [ 'shape' => 'DocumentIdOptions', ], ], ], 'AmazonopensearchserviceDestinationDescription' => [ 'type' => 'structure', 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'DomainARN' => [ 'shape' => 'AmazonopensearchserviceDomainARN', ], 'ClusterEndpoint' => [ 'shape' => 'AmazonopensearchserviceClusterEndpoint', ], 'IndexName' => [ 'shape' => 'AmazonopensearchserviceIndexName', ], 'TypeName' => [ 'shape' => 'AmazonopensearchserviceTypeName', ], 'IndexRotationPeriod' => [ 'shape' => 'AmazonopensearchserviceIndexRotationPeriod', ], 'BufferingHints' => [ 'shape' => 'AmazonopensearchserviceBufferingHints', ], 'RetryOptions' => [ 'shape' => 'AmazonopensearchserviceRetryOptions', ], 'S3BackupMode' => [ 'shape' => 'AmazonopensearchserviceS3BackupMode', ], 'S3DestinationDescription' => [ 'shape' => 'S3DestinationDescription', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'VpcConfigurationDescription' => [ 'shape' => 'VpcConfigurationDescription', ], 'DocumentIdOptions' => [ 'shape' => 'DocumentIdOptions', ], ], ], 'AmazonopensearchserviceDestinationUpdate' => [ 'type' => 'structure', 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'DomainARN' => [ 'shape' => 'AmazonopensearchserviceDomainARN', ], 'ClusterEndpoint' => [ 'shape' => 'AmazonopensearchserviceClusterEndpoint', ], 'IndexName' => [ 'shape' => 'AmazonopensearchserviceIndexName', ], 'TypeName' => [ 'shape' => 'AmazonopensearchserviceTypeName', ], 'IndexRotationPeriod' => [ 'shape' => 'AmazonopensearchserviceIndexRotationPeriod', ], 'BufferingHints' => [ 'shape' => 'AmazonopensearchserviceBufferingHints', ], 'RetryOptions' => [ 'shape' => 'AmazonopensearchserviceRetryOptions', ], 'S3Update' => [ 'shape' => 'S3DestinationUpdate', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'DocumentIdOptions' => [ 'shape' => 'DocumentIdOptions', ], ], ], 'AmazonopensearchserviceDomainARN' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'arn:.*', ], 'AmazonopensearchserviceIndexName' => [ 'type' => 'string', 'max' => 80, 'min' => 1, 'pattern' => '.*', ], 'AmazonopensearchserviceIndexRotationPeriod' => [ 'type' => 'string', 'enum' => [ 'NoRotation', 'OneHour', 'OneDay', 'OneWeek', 'OneMonth', ], ], 'AmazonopensearchserviceRetryDurationInSeconds' => [ 'type' => 'integer', 'max' => 7200, 'min' => 0, ], 'AmazonopensearchserviceRetryOptions' => [ 'type' => 'structure', 'members' => [ 'DurationInSeconds' => [ 'shape' => 'AmazonopensearchserviceRetryDurationInSeconds', ], ], ], 'AmazonopensearchserviceS3BackupMode' => [ 'type' => 'string', 'enum' => [ 'FailedDocumentsOnly', 'AllDocuments', ], ], 'AmazonopensearchserviceTypeName' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => '.*', ], 'AuthenticationConfiguration' => [ 'type' => 'structure', 'required' => [ 'RoleARN', 'Connectivity', ], 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'Connectivity' => [ 'shape' => 'Connectivity', ], ], ], 'BlockSizeBytes' => [ 'type' => 'integer', 'min' => 67108864, ], 'BooleanObject' => [ 'type' => 'boolean', ], 'BucketARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:.*', ], 'BufferingHints' => [ 'type' => 'structure', 'members' => [ 'SizeInMBs' => [ 'shape' => 'SizeInMBs', ], 'IntervalInSeconds' => [ 'shape' => 'IntervalInSeconds', ], ], ], 'CloudWatchLoggingOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'BooleanObject', ], 'LogGroupName' => [ 'shape' => 'LogGroupName', ], 'LogStreamName' => [ 'shape' => 'LogStreamName', ], ], ], 'ClusterJDBCURL' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'jdbc:(redshift|postgresql)://((?!-)[A-Za-z0-9-]{1,63}(?<!-)\\.)+(redshift(-serverless)?)\\.([a-zA-Z0-9\\.]+):\\d{1,5}/[a-zA-Z0-9_$-]+', ], 'ColumnToJsonKeyMappings' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyStringWithoutWhitespace', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], 'CompressionFormat' => [ 'type' => 'string', 'enum' => [ 'UNCOMPRESSED', 'GZIP', 'ZIP', 'Snappy', 'HADOOP_SNAPPY', ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Connectivity' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'PRIVATE', ], ], 'ContentEncoding' => [ 'type' => 'string', 'enum' => [ 'NONE', 'GZIP', ], ], 'CopyCommand' => [ 'type' => 'structure', 'required' => [ 'DataTableName', ], 'members' => [ 'DataTableName' => [ 'shape' => 'DataTableName', ], 'DataTableColumns' => [ 'shape' => 'DataTableColumns', ], 'CopyOptions' => [ 'shape' => 'CopyOptions', ], ], ], 'CopyOptions' => [ 'type' => 'string', 'max' => 204800, 'min' => 0, 'pattern' => '.*', ], 'CreateDeliveryStreamInput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamName', ], 'members' => [ 'DeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'DeliveryStreamType' => [ 'shape' => 'DeliveryStreamType', ], 'KinesisStreamSourceConfiguration' => [ 'shape' => 'KinesisStreamSourceConfiguration', ], 'DeliveryStreamEncryptionConfigurationInput' => [ 'shape' => 'DeliveryStreamEncryptionConfigurationInput', ], 'S3DestinationConfiguration' => [ 'shape' => 'S3DestinationConfiguration', 'deprecated' => true, ], 'ExtendedS3DestinationConfiguration' => [ 'shape' => 'ExtendedS3DestinationConfiguration', ], 'RedshiftDestinationConfiguration' => [ 'shape' => 'RedshiftDestinationConfiguration', ], 'ElasticsearchDestinationConfiguration' => [ 'shape' => 'ElasticsearchDestinationConfiguration', ], 'AmazonopensearchserviceDestinationConfiguration' => [ 'shape' => 'AmazonopensearchserviceDestinationConfiguration', ], 'SplunkDestinationConfiguration' => [ 'shape' => 'SplunkDestinationConfiguration', ], 'HttpEndpointDestinationConfiguration' => [ 'shape' => 'HttpEndpointDestinationConfiguration', ], 'Tags' => [ 'shape' => 'TagDeliveryStreamInputTagList', ], 'AmazonOpenSearchServerlessDestinationConfiguration' => [ 'shape' => 'AmazonOpenSearchServerlessDestinationConfiguration', ], 'MSKSourceConfiguration' => [ 'shape' => 'MSKSourceConfiguration', ], ], ], 'CreateDeliveryStreamOutput' => [ 'type' => 'structure', 'members' => [ 'DeliveryStreamARN' => [ 'shape' => 'DeliveryStreamARN', ], ], ], 'Data' => [ 'type' => 'blob', 'max' => 1024000, 'min' => 0, ], 'DataFormatConversionConfiguration' => [ 'type' => 'structure', 'members' => [ 'SchemaConfiguration' => [ 'shape' => 'SchemaConfiguration', ], 'InputFormatConfiguration' => [ 'shape' => 'InputFormatConfiguration', ], 'OutputFormatConfiguration' => [ 'shape' => 'OutputFormatConfiguration', ], 'Enabled' => [ 'shape' => 'BooleanObject', ], ], ], 'DataTableColumns' => [ 'type' => 'string', 'max' => 204800, 'min' => 0, 'pattern' => '.*', ], 'DataTableName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '.*', ], 'DefaultDocumentIdFormat' => [ 'type' => 'string', 'enum' => [ 'FIREHOSE_DEFAULT', 'NO_DOCUMENT_ID', ], ], 'DeleteDeliveryStreamInput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamName', ], 'members' => [ 'DeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'AllowForceDelete' => [ 'shape' => 'BooleanObject', ], ], ], 'DeleteDeliveryStreamOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeliveryStartTimestamp' => [ 'type' => 'timestamp', ], 'DeliveryStreamARN' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'arn:.*', ], 'DeliveryStreamDescription' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamName', 'DeliveryStreamARN', 'DeliveryStreamStatus', 'DeliveryStreamType', 'VersionId', 'Destinations', 'HasMoreDestinations', ], 'members' => [ 'DeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'DeliveryStreamARN' => [ 'shape' => 'DeliveryStreamARN', ], 'DeliveryStreamStatus' => [ 'shape' => 'DeliveryStreamStatus', ], 'FailureDescription' => [ 'shape' => 'FailureDescription', ], 'DeliveryStreamEncryptionConfiguration' => [ 'shape' => 'DeliveryStreamEncryptionConfiguration', ], 'DeliveryStreamType' => [ 'shape' => 'DeliveryStreamType', ], 'VersionId' => [ 'shape' => 'DeliveryStreamVersionId', ], 'CreateTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdateTimestamp' => [ 'shape' => 'Timestamp', ], 'Source' => [ 'shape' => 'SourceDescription', ], 'Destinations' => [ 'shape' => 'DestinationDescriptionList', ], 'HasMoreDestinations' => [ 'shape' => 'BooleanObject', ], ], ], 'DeliveryStreamEncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'KeyARN' => [ 'shape' => 'AWSKMSKeyARN', ], 'KeyType' => [ 'shape' => 'KeyType', ], 'Status' => [ 'shape' => 'DeliveryStreamEncryptionStatus', ], 'FailureDescription' => [ 'shape' => 'FailureDescription', ], ], ], 'DeliveryStreamEncryptionConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'KeyType', ], 'members' => [ 'KeyARN' => [ 'shape' => 'AWSKMSKeyARN', ], 'KeyType' => [ 'shape' => 'KeyType', ], ], ], 'DeliveryStreamEncryptionStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'ENABLING', 'ENABLING_FAILED', 'DISABLED', 'DISABLING', 'DISABLING_FAILED', ], ], 'DeliveryStreamFailureType' => [ 'type' => 'string', 'enum' => [ 'RETIRE_KMS_GRANT_FAILED', 'CREATE_KMS_GRANT_FAILED', 'KMS_ACCESS_DENIED', 'DISABLED_KMS_KEY', 'INVALID_KMS_KEY', 'KMS_KEY_NOT_FOUND', 'KMS_OPT_IN_REQUIRED', 'CREATE_ENI_FAILED', 'DELETE_ENI_FAILED', 'SUBNET_NOT_FOUND', 'SECURITY_GROUP_NOT_FOUND', 'ENI_ACCESS_DENIED', 'SUBNET_ACCESS_DENIED', 'SECURITY_GROUP_ACCESS_DENIED', 'UNKNOWN_ERROR', ], ], 'DeliveryStreamName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.-]+', ], 'DeliveryStreamNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeliveryStreamName', ], ], 'DeliveryStreamStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATING_FAILED', 'DELETING', 'DELETING_FAILED', 'ACTIVE', ], ], 'DeliveryStreamType' => [ 'type' => 'string', 'enum' => [ 'DirectPut', 'KinesisStreamAsSource', 'MSKAsSource', ], ], 'DeliveryStreamVersionId' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '[0-9]+', ], 'DescribeDeliveryStreamInput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamName', ], 'members' => [ 'DeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'Limit' => [ 'shape' => 'DescribeDeliveryStreamInputLimit', ], 'ExclusiveStartDestinationId' => [ 'shape' => 'DestinationId', ], ], ], 'DescribeDeliveryStreamInputLimit' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'DescribeDeliveryStreamOutput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamDescription', ], 'members' => [ 'DeliveryStreamDescription' => [ 'shape' => 'DeliveryStreamDescription', ], ], ], 'Deserializer' => [ 'type' => 'structure', 'members' => [ 'OpenXJsonSerDe' => [ 'shape' => 'OpenXJsonSerDe', ], 'HiveJsonSerDe' => [ 'shape' => 'HiveJsonSerDe', ], ], ], 'DestinationDescription' => [ 'type' => 'structure', 'required' => [ 'DestinationId', ], 'members' => [ 'DestinationId' => [ 'shape' => 'DestinationId', ], 'S3DestinationDescription' => [ 'shape' => 'S3DestinationDescription', ], 'ExtendedS3DestinationDescription' => [ 'shape' => 'ExtendedS3DestinationDescription', ], 'RedshiftDestinationDescription' => [ 'shape' => 'RedshiftDestinationDescription', ], 'ElasticsearchDestinationDescription' => [ 'shape' => 'ElasticsearchDestinationDescription', ], 'AmazonopensearchserviceDestinationDescription' => [ 'shape' => 'AmazonopensearchserviceDestinationDescription', ], 'SplunkDestinationDescription' => [ 'shape' => 'SplunkDestinationDescription', ], 'HttpEndpointDestinationDescription' => [ 'shape' => 'HttpEndpointDestinationDescription', ], 'AmazonOpenSearchServerlessDestinationDescription' => [ 'shape' => 'AmazonOpenSearchServerlessDestinationDescription', ], ], ], 'DestinationDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DestinationDescription', ], ], 'DestinationId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]+', ], 'DocumentIdOptions' => [ 'type' => 'structure', 'required' => [ 'DefaultDocumentIdFormat', ], 'members' => [ 'DefaultDocumentIdFormat' => [ 'shape' => 'DefaultDocumentIdFormat', ], ], ], 'DynamicPartitioningConfiguration' => [ 'type' => 'structure', 'members' => [ 'RetryOptions' => [ 'shape' => 'RetryOptions', ], 'Enabled' => [ 'shape' => 'BooleanObject', ], ], ], 'ElasticsearchBufferingHints' => [ 'type' => 'structure', 'members' => [ 'IntervalInSeconds' => [ 'shape' => 'ElasticsearchBufferingIntervalInSeconds', ], 'SizeInMBs' => [ 'shape' => 'ElasticsearchBufferingSizeInMBs', ], ], ], 'ElasticsearchBufferingIntervalInSeconds' => [ 'type' => 'integer', 'max' => 900, 'min' => 60, ], 'ElasticsearchBufferingSizeInMBs' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ElasticsearchClusterEndpoint' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'https:.*', ], 'ElasticsearchDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'RoleARN', 'IndexName', 'S3Configuration', ], 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'DomainARN' => [ 'shape' => 'ElasticsearchDomainARN', ], 'ClusterEndpoint' => [ 'shape' => 'ElasticsearchClusterEndpoint', ], 'IndexName' => [ 'shape' => 'ElasticsearchIndexName', ], 'TypeName' => [ 'shape' => 'ElasticsearchTypeName', ], 'IndexRotationPeriod' => [ 'shape' => 'ElasticsearchIndexRotationPeriod', ], 'BufferingHints' => [ 'shape' => 'ElasticsearchBufferingHints', ], 'RetryOptions' => [ 'shape' => 'ElasticsearchRetryOptions', ], 'S3BackupMode' => [ 'shape' => 'ElasticsearchS3BackupMode', ], 'S3Configuration' => [ 'shape' => 'S3DestinationConfiguration', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'VpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], 'DocumentIdOptions' => [ 'shape' => 'DocumentIdOptions', ], ], ], 'ElasticsearchDestinationDescription' => [ 'type' => 'structure', 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'DomainARN' => [ 'shape' => 'ElasticsearchDomainARN', ], 'ClusterEndpoint' => [ 'shape' => 'ElasticsearchClusterEndpoint', ], 'IndexName' => [ 'shape' => 'ElasticsearchIndexName', ], 'TypeName' => [ 'shape' => 'ElasticsearchTypeName', ], 'IndexRotationPeriod' => [ 'shape' => 'ElasticsearchIndexRotationPeriod', ], 'BufferingHints' => [ 'shape' => 'ElasticsearchBufferingHints', ], 'RetryOptions' => [ 'shape' => 'ElasticsearchRetryOptions', ], 'S3BackupMode' => [ 'shape' => 'ElasticsearchS3BackupMode', ], 'S3DestinationDescription' => [ 'shape' => 'S3DestinationDescription', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'VpcConfigurationDescription' => [ 'shape' => 'VpcConfigurationDescription', ], 'DocumentIdOptions' => [ 'shape' => 'DocumentIdOptions', ], ], ], 'ElasticsearchDestinationUpdate' => [ 'type' => 'structure', 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'DomainARN' => [ 'shape' => 'ElasticsearchDomainARN', ], 'ClusterEndpoint' => [ 'shape' => 'ElasticsearchClusterEndpoint', ], 'IndexName' => [ 'shape' => 'ElasticsearchIndexName', ], 'TypeName' => [ 'shape' => 'ElasticsearchTypeName', ], 'IndexRotationPeriod' => [ 'shape' => 'ElasticsearchIndexRotationPeriod', ], 'BufferingHints' => [ 'shape' => 'ElasticsearchBufferingHints', ], 'RetryOptions' => [ 'shape' => 'ElasticsearchRetryOptions', ], 'S3Update' => [ 'shape' => 'S3DestinationUpdate', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'DocumentIdOptions' => [ 'shape' => 'DocumentIdOptions', ], ], ], 'ElasticsearchDomainARN' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'arn:.*', ], 'ElasticsearchIndexName' => [ 'type' => 'string', 'max' => 80, 'min' => 1, 'pattern' => '.*', ], 'ElasticsearchIndexRotationPeriod' => [ 'type' => 'string', 'enum' => [ 'NoRotation', 'OneHour', 'OneDay', 'OneWeek', 'OneMonth', ], ], 'ElasticsearchRetryDurationInSeconds' => [ 'type' => 'integer', 'max' => 7200, 'min' => 0, ], 'ElasticsearchRetryOptions' => [ 'type' => 'structure', 'members' => [ 'DurationInSeconds' => [ 'shape' => 'ElasticsearchRetryDurationInSeconds', ], ], ], 'ElasticsearchS3BackupMode' => [ 'type' => 'string', 'enum' => [ 'FailedDocumentsOnly', 'AllDocuments', ], ], 'ElasticsearchTypeName' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => '.*', ], 'EncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'NoEncryptionConfig' => [ 'shape' => 'NoEncryptionConfig', ], 'KMSEncryptionConfig' => [ 'shape' => 'KMSEncryptionConfig', ], ], ], 'ErrorCode' => [ 'type' => 'string', ], 'ErrorMessage' => [ 'type' => 'string', ], 'ErrorOutputPrefix' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', ], 'ExtendedS3DestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'RoleARN', 'BucketARN', ], 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'BucketARN' => [ 'shape' => 'BucketARN', ], 'Prefix' => [ 'shape' => 'Prefix', ], 'ErrorOutputPrefix' => [ 'shape' => 'ErrorOutputPrefix', ], 'BufferingHints' => [ 'shape' => 'BufferingHints', ], 'CompressionFormat' => [ 'shape' => 'CompressionFormat', ], 'EncryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'S3BackupMode' => [ 'shape' => 'S3BackupMode', ], 'S3BackupConfiguration' => [ 'shape' => 'S3DestinationConfiguration', ], 'DataFormatConversionConfiguration' => [ 'shape' => 'DataFormatConversionConfiguration', ], 'DynamicPartitioningConfiguration' => [ 'shape' => 'DynamicPartitioningConfiguration', ], ], ], 'ExtendedS3DestinationDescription' => [ 'type' => 'structure', 'required' => [ 'RoleARN', 'BucketARN', 'BufferingHints', 'CompressionFormat', 'EncryptionConfiguration', ], 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'BucketARN' => [ 'shape' => 'BucketARN', ], 'Prefix' => [ 'shape' => 'Prefix', ], 'ErrorOutputPrefix' => [ 'shape' => 'ErrorOutputPrefix', ], 'BufferingHints' => [ 'shape' => 'BufferingHints', ], 'CompressionFormat' => [ 'shape' => 'CompressionFormat', ], 'EncryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'S3BackupMode' => [ 'shape' => 'S3BackupMode', ], 'S3BackupDescription' => [ 'shape' => 'S3DestinationDescription', ], 'DataFormatConversionConfiguration' => [ 'shape' => 'DataFormatConversionConfiguration', ], 'DynamicPartitioningConfiguration' => [ 'shape' => 'DynamicPartitioningConfiguration', ], ], ], 'ExtendedS3DestinationUpdate' => [ 'type' => 'structure', 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'BucketARN' => [ 'shape' => 'BucketARN', ], 'Prefix' => [ 'shape' => 'Prefix', ], 'ErrorOutputPrefix' => [ 'shape' => 'ErrorOutputPrefix', ], 'BufferingHints' => [ 'shape' => 'BufferingHints', ], 'CompressionFormat' => [ 'shape' => 'CompressionFormat', ], 'EncryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'S3BackupMode' => [ 'shape' => 'S3BackupMode', ], 'S3BackupUpdate' => [ 'shape' => 'S3DestinationUpdate', ], 'DataFormatConversionConfiguration' => [ 'shape' => 'DataFormatConversionConfiguration', ], 'DynamicPartitioningConfiguration' => [ 'shape' => 'DynamicPartitioningConfiguration', ], ], ], 'FailureDescription' => [ 'type' => 'structure', 'required' => [ 'Type', 'Details', ], 'members' => [ 'Type' => [ 'shape' => 'DeliveryStreamFailureType', ], 'Details' => [ 'shape' => 'NonEmptyString', ], ], ], 'HECAcknowledgmentTimeoutInSeconds' => [ 'type' => 'integer', 'max' => 600, 'min' => 180, ], 'HECEndpoint' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '.*', ], 'HECEndpointType' => [ 'type' => 'string', 'enum' => [ 'Raw', 'Event', ], ], 'HECToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '.*', ], 'HiveJsonSerDe' => [ 'type' => 'structure', 'members' => [ 'TimestampFormats' => [ 'shape' => 'ListOfNonEmptyStrings', ], ], ], 'HttpEndpointAccessKey' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'pattern' => '.*', 'sensitive' => true, ], 'HttpEndpointAttributeName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^(?!\\s*$).+', 'sensitive' => true, ], 'HttpEndpointAttributeValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', 'sensitive' => true, ], 'HttpEndpointBufferingHints' => [ 'type' => 'structure', 'members' => [ 'SizeInMBs' => [ 'shape' => 'HttpEndpointBufferingSizeInMBs', ], 'IntervalInSeconds' => [ 'shape' => 'HttpEndpointBufferingIntervalInSeconds', ], ], ], 'HttpEndpointBufferingIntervalInSeconds' => [ 'type' => 'integer', 'max' => 900, 'min' => 60, ], 'HttpEndpointBufferingSizeInMBs' => [ 'type' => 'integer', 'max' => 64, 'min' => 1, ], 'HttpEndpointCommonAttribute' => [ 'type' => 'structure', 'required' => [ 'AttributeName', 'AttributeValue', ], 'members' => [ 'AttributeName' => [ 'shape' => 'HttpEndpointAttributeName', ], 'AttributeValue' => [ 'shape' => 'HttpEndpointAttributeValue', ], ], ], 'HttpEndpointCommonAttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HttpEndpointCommonAttribute', ], 'max' => 50, 'min' => 0, ], 'HttpEndpointConfiguration' => [ 'type' => 'structure', 'required' => [ 'Url', ], 'members' => [ 'Url' => [ 'shape' => 'HttpEndpointUrl', ], 'Name' => [ 'shape' => 'HttpEndpointName', ], 'AccessKey' => [ 'shape' => 'HttpEndpointAccessKey', ], ], ], 'HttpEndpointDescription' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'HttpEndpointUrl', ], 'Name' => [ 'shape' => 'HttpEndpointName', ], ], ], 'HttpEndpointDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'EndpointConfiguration', 'S3Configuration', ], 'members' => [ 'EndpointConfiguration' => [ 'shape' => 'HttpEndpointConfiguration', ], 'BufferingHints' => [ 'shape' => 'HttpEndpointBufferingHints', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'RequestConfiguration' => [ 'shape' => 'HttpEndpointRequestConfiguration', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'RetryOptions' => [ 'shape' => 'HttpEndpointRetryOptions', ], 'S3BackupMode' => [ 'shape' => 'HttpEndpointS3BackupMode', ], 'S3Configuration' => [ 'shape' => 'S3DestinationConfiguration', ], ], ], 'HttpEndpointDestinationDescription' => [ 'type' => 'structure', 'members' => [ 'EndpointConfiguration' => [ 'shape' => 'HttpEndpointDescription', ], 'BufferingHints' => [ 'shape' => 'HttpEndpointBufferingHints', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'RequestConfiguration' => [ 'shape' => 'HttpEndpointRequestConfiguration', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'RetryOptions' => [ 'shape' => 'HttpEndpointRetryOptions', ], 'S3BackupMode' => [ 'shape' => 'HttpEndpointS3BackupMode', ], 'S3DestinationDescription' => [ 'shape' => 'S3DestinationDescription', ], ], ], 'HttpEndpointDestinationUpdate' => [ 'type' => 'structure', 'members' => [ 'EndpointConfiguration' => [ 'shape' => 'HttpEndpointConfiguration', ], 'BufferingHints' => [ 'shape' => 'HttpEndpointBufferingHints', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], 'RequestConfiguration' => [ 'shape' => 'HttpEndpointRequestConfiguration', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'RetryOptions' => [ 'shape' => 'HttpEndpointRetryOptions', ], 'S3BackupMode' => [ 'shape' => 'HttpEndpointS3BackupMode', ], 'S3Update' => [ 'shape' => 'S3DestinationUpdate', ], ], ], 'HttpEndpointName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'HttpEndpointRequestConfiguration' => [ 'type' => 'structure', 'members' => [ 'ContentEncoding' => [ 'shape' => 'ContentEncoding', ], 'CommonAttributes' => [ 'shape' => 'HttpEndpointCommonAttributesList', ], ], ], 'HttpEndpointRetryDurationInSeconds' => [ 'type' => 'integer', 'max' => 7200, 'min' => 0, ], 'HttpEndpointRetryOptions' => [ 'type' => 'structure', 'members' => [ 'DurationInSeconds' => [ 'shape' => 'HttpEndpointRetryDurationInSeconds', ], ], ], 'HttpEndpointS3BackupMode' => [ 'type' => 'string', 'enum' => [ 'FailedDataOnly', 'AllData', ], ], 'HttpEndpointUrl' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => 'https://.*', 'sensitive' => true, ], 'InputFormatConfiguration' => [ 'type' => 'structure', 'members' => [ 'Deserializer' => [ 'shape' => 'Deserializer', ], ], ], 'IntervalInSeconds' => [ 'type' => 'integer', 'max' => 900, 'min' => 60, ], 'InvalidArgumentException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidKMSResourceException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'KMSEncryptionConfig' => [ 'type' => 'structure', 'required' => [ 'AWSKMSKeyARN', ], 'members' => [ 'AWSKMSKeyARN' => [ 'shape' => 'AWSKMSKeyARN', ], ], ], 'KeyType' => [ 'type' => 'string', 'enum' => [ 'AWS_OWNED_CMK', 'CUSTOMER_MANAGED_CMK', ], ], 'KinesisStreamARN' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'arn:.*', ], 'KinesisStreamSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'KinesisStreamARN', 'RoleARN', ], 'members' => [ 'KinesisStreamARN' => [ 'shape' => 'KinesisStreamARN', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], ], ], 'KinesisStreamSourceDescription' => [ 'type' => 'structure', 'members' => [ 'KinesisStreamARN' => [ 'shape' => 'KinesisStreamARN', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'DeliveryStartTimestamp' => [ 'shape' => 'DeliveryStartTimestamp', ], ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListDeliveryStreamsInput' => [ 'type' => 'structure', 'members' => [ 'Limit' => [ 'shape' => 'ListDeliveryStreamsInputLimit', ], 'DeliveryStreamType' => [ 'shape' => 'DeliveryStreamType', ], 'ExclusiveStartDeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], ], ], 'ListDeliveryStreamsInputLimit' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'ListDeliveryStreamsOutput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamNames', 'HasMoreDeliveryStreams', ], 'members' => [ 'DeliveryStreamNames' => [ 'shape' => 'DeliveryStreamNameList', ], 'HasMoreDeliveryStreams' => [ 'shape' => 'BooleanObject', ], ], ], 'ListOfNonEmptyStrings' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'ListOfNonEmptyStringsWithoutWhitespace' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyStringWithoutWhitespace', ], ], 'ListTagsForDeliveryStreamInput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamName', ], 'members' => [ 'DeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'ExclusiveStartTagKey' => [ 'shape' => 'TagKey', ], 'Limit' => [ 'shape' => 'ListTagsForDeliveryStreamInputLimit', ], ], ], 'ListTagsForDeliveryStreamInputLimit' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'ListTagsForDeliveryStreamOutput' => [ 'type' => 'structure', 'required' => [ 'Tags', 'HasMoreTags', ], 'members' => [ 'Tags' => [ 'shape' => 'ListTagsForDeliveryStreamOutputTagList', ], 'HasMoreTags' => [ 'shape' => 'BooleanObject', ], ], ], 'ListTagsForDeliveryStreamOutputTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 0, ], 'LogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '[\\.\\-_/#A-Za-z0-9]*', ], 'LogStreamName' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '[^:*]*', ], 'MSKClusterARN' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'arn:.*', ], 'MSKSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'MSKClusterARN', 'TopicName', 'AuthenticationConfiguration', ], 'members' => [ 'MSKClusterARN' => [ 'shape' => 'MSKClusterARN', ], 'TopicName' => [ 'shape' => 'TopicName', ], 'AuthenticationConfiguration' => [ 'shape' => 'AuthenticationConfiguration', ], ], ], 'MSKSourceDescription' => [ 'type' => 'structure', 'members' => [ 'MSKClusterARN' => [ 'shape' => 'MSKClusterARN', ], 'TopicName' => [ 'shape' => 'TopicName', ], 'AuthenticationConfiguration' => [ 'shape' => 'AuthenticationConfiguration', ], 'DeliveryStartTimestamp' => [ 'shape' => 'DeliveryStartTimestamp', ], ], ], 'NoEncryptionConfig' => [ 'type' => 'string', 'enum' => [ 'NoEncryption', ], ], 'NonEmptyString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'NonEmptyStringWithoutWhitespace' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^\\S+$', ], 'NonNegativeIntegerObject' => [ 'type' => 'integer', 'min' => 0, ], 'OpenXJsonSerDe' => [ 'type' => 'structure', 'members' => [ 'ConvertDotsInJsonKeysToUnderscores' => [ 'shape' => 'BooleanObject', ], 'CaseInsensitive' => [ 'shape' => 'BooleanObject', ], 'ColumnToJsonKeyMappings' => [ 'shape' => 'ColumnToJsonKeyMappings', ], ], ], 'OrcCompression' => [ 'type' => 'string', 'enum' => [ 'NONE', 'ZLIB', 'SNAPPY', ], ], 'OrcFormatVersion' => [ 'type' => 'string', 'enum' => [ 'V0_11', 'V0_12', ], ], 'OrcRowIndexStride' => [ 'type' => 'integer', 'min' => 1000, ], 'OrcSerDe' => [ 'type' => 'structure', 'members' => [ 'StripeSizeBytes' => [ 'shape' => 'OrcStripeSizeBytes', ], 'BlockSizeBytes' => [ 'shape' => 'BlockSizeBytes', ], 'RowIndexStride' => [ 'shape' => 'OrcRowIndexStride', ], 'EnablePadding' => [ 'shape' => 'BooleanObject', ], 'PaddingTolerance' => [ 'shape' => 'Proportion', ], 'Compression' => [ 'shape' => 'OrcCompression', ], 'BloomFilterColumns' => [ 'shape' => 'ListOfNonEmptyStringsWithoutWhitespace', ], 'BloomFilterFalsePositiveProbability' => [ 'shape' => 'Proportion', ], 'DictionaryKeyThreshold' => [ 'shape' => 'Proportion', ], 'FormatVersion' => [ 'shape' => 'OrcFormatVersion', ], ], ], 'OrcStripeSizeBytes' => [ 'type' => 'integer', 'min' => 8388608, ], 'OutputFormatConfiguration' => [ 'type' => 'structure', 'members' => [ 'Serializer' => [ 'shape' => 'Serializer', ], ], ], 'ParquetCompression' => [ 'type' => 'string', 'enum' => [ 'UNCOMPRESSED', 'GZIP', 'SNAPPY', ], ], 'ParquetPageSizeBytes' => [ 'type' => 'integer', 'min' => 65536, ], 'ParquetSerDe' => [ 'type' => 'structure', 'members' => [ 'BlockSizeBytes' => [ 'shape' => 'BlockSizeBytes', ], 'PageSizeBytes' => [ 'shape' => 'ParquetPageSizeBytes', ], 'Compression' => [ 'shape' => 'ParquetCompression', ], 'EnableDictionaryCompression' => [ 'shape' => 'BooleanObject', ], 'MaxPaddingBytes' => [ 'shape' => 'NonNegativeIntegerObject', ], 'WriterVersion' => [ 'shape' => 'ParquetWriterVersion', ], ], ], 'ParquetWriterVersion' => [ 'type' => 'string', 'enum' => [ 'V1', 'V2', ], ], 'Password' => [ 'type' => 'string', 'max' => 512, 'min' => 6, 'pattern' => '.*', 'sensitive' => true, ], 'Prefix' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', ], 'ProcessingConfiguration' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'BooleanObject', ], 'Processors' => [ 'shape' => 'ProcessorList', ], ], ], 'Processor' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'ProcessorType', ], 'Parameters' => [ 'shape' => 'ProcessorParameterList', ], ], ], 'ProcessorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Processor', ], ], 'ProcessorParameter' => [ 'type' => 'structure', 'required' => [ 'ParameterName', 'ParameterValue', ], 'members' => [ 'ParameterName' => [ 'shape' => 'ProcessorParameterName', ], 'ParameterValue' => [ 'shape' => 'ProcessorParameterValue', ], ], ], 'ProcessorParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProcessorParameter', ], ], 'ProcessorParameterName' => [ 'type' => 'string', 'enum' => [ 'LambdaArn', 'NumberOfRetries', 'MetadataExtractionQuery', 'JsonParsingEngine', 'RoleArn', 'BufferSizeInMBs', 'BufferIntervalInSeconds', 'SubRecordType', 'Delimiter', 'CompressionFormat', ], ], 'ProcessorParameterValue' => [ 'type' => 'string', 'max' => 5120, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'ProcessorType' => [ 'type' => 'string', 'enum' => [ 'RecordDeAggregation', 'Decompression', 'Lambda', 'MetadataExtraction', 'AppendDelimiterToRecord', ], ], 'Proportion' => [ 'type' => 'double', 'max' => 1, 'min' => 0, ], 'PutRecordBatchInput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamName', 'Records', ], 'members' => [ 'DeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'Records' => [ 'shape' => 'PutRecordBatchRequestEntryList', ], ], ], 'PutRecordBatchOutput' => [ 'type' => 'structure', 'required' => [ 'FailedPutCount', 'RequestResponses', ], 'members' => [ 'FailedPutCount' => [ 'shape' => 'NonNegativeIntegerObject', ], 'Encrypted' => [ 'shape' => 'BooleanObject', ], 'RequestResponses' => [ 'shape' => 'PutRecordBatchResponseEntryList', ], ], ], 'PutRecordBatchRequestEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Record', ], 'max' => 500, 'min' => 1, ], 'PutRecordBatchResponseEntry' => [ 'type' => 'structure', 'members' => [ 'RecordId' => [ 'shape' => 'PutResponseRecordId', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'PutRecordBatchResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PutRecordBatchResponseEntry', ], 'max' => 500, 'min' => 1, ], 'PutRecordInput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamName', 'Record', ], 'members' => [ 'DeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'Record' => [ 'shape' => 'Record', ], ], ], 'PutRecordOutput' => [ 'type' => 'structure', 'required' => [ 'RecordId', ], 'members' => [ 'RecordId' => [ 'shape' => 'PutResponseRecordId', ], 'Encrypted' => [ 'shape' => 'BooleanObject', ], ], ], 'PutResponseRecordId' => [ 'type' => 'string', 'min' => 1, ], 'Record' => [ 'type' => 'structure', 'required' => [ 'Data', ], 'members' => [ 'Data' => [ 'shape' => 'Data', ], ], ], 'RedshiftDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'RoleARN', 'ClusterJDBCURL', 'CopyCommand', 'Username', 'Password', 'S3Configuration', ], 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'ClusterJDBCURL' => [ 'shape' => 'ClusterJDBCURL', ], 'CopyCommand' => [ 'shape' => 'CopyCommand', ], 'Username' => [ 'shape' => 'Username', ], 'Password' => [ 'shape' => 'Password', ], 'RetryOptions' => [ 'shape' => 'RedshiftRetryOptions', ], 'S3Configuration' => [ 'shape' => 'S3DestinationConfiguration', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'S3BackupMode' => [ 'shape' => 'RedshiftS3BackupMode', ], 'S3BackupConfiguration' => [ 'shape' => 'S3DestinationConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], ], ], 'RedshiftDestinationDescription' => [ 'type' => 'structure', 'required' => [ 'RoleARN', 'ClusterJDBCURL', 'CopyCommand', 'Username', 'S3DestinationDescription', ], 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'ClusterJDBCURL' => [ 'shape' => 'ClusterJDBCURL', ], 'CopyCommand' => [ 'shape' => 'CopyCommand', ], 'Username' => [ 'shape' => 'Username', ], 'RetryOptions' => [ 'shape' => 'RedshiftRetryOptions', ], 'S3DestinationDescription' => [ 'shape' => 'S3DestinationDescription', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'S3BackupMode' => [ 'shape' => 'RedshiftS3BackupMode', ], 'S3BackupDescription' => [ 'shape' => 'S3DestinationDescription', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], ], ], 'RedshiftDestinationUpdate' => [ 'type' => 'structure', 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'ClusterJDBCURL' => [ 'shape' => 'ClusterJDBCURL', ], 'CopyCommand' => [ 'shape' => 'CopyCommand', ], 'Username' => [ 'shape' => 'Username', ], 'Password' => [ 'shape' => 'Password', ], 'RetryOptions' => [ 'shape' => 'RedshiftRetryOptions', ], 'S3Update' => [ 'shape' => 'S3DestinationUpdate', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'S3BackupMode' => [ 'shape' => 'RedshiftS3BackupMode', ], 'S3BackupUpdate' => [ 'shape' => 'S3DestinationUpdate', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], ], ], 'RedshiftRetryDurationInSeconds' => [ 'type' => 'integer', 'max' => 7200, 'min' => 0, ], 'RedshiftRetryOptions' => [ 'type' => 'structure', 'members' => [ 'DurationInSeconds' => [ 'shape' => 'RedshiftRetryDurationInSeconds', ], ], ], 'RedshiftS3BackupMode' => [ 'type' => 'string', 'enum' => [ 'Disabled', 'Enabled', ], ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'RetryDurationInSeconds' => [ 'type' => 'integer', 'max' => 7200, 'min' => 0, ], 'RetryOptions' => [ 'type' => 'structure', 'members' => [ 'DurationInSeconds' => [ 'shape' => 'RetryDurationInSeconds', ], ], ], 'RoleARN' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'arn:.*', ], 'S3BackupMode' => [ 'type' => 'string', 'enum' => [ 'Disabled', 'Enabled', ], ], 'S3DestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'RoleARN', 'BucketARN', ], 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'BucketARN' => [ 'shape' => 'BucketARN', ], 'Prefix' => [ 'shape' => 'Prefix', ], 'ErrorOutputPrefix' => [ 'shape' => 'ErrorOutputPrefix', ], 'BufferingHints' => [ 'shape' => 'BufferingHints', ], 'CompressionFormat' => [ 'shape' => 'CompressionFormat', ], 'EncryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], ], ], 'S3DestinationDescription' => [ 'type' => 'structure', 'required' => [ 'RoleARN', 'BucketARN', 'BufferingHints', 'CompressionFormat', 'EncryptionConfiguration', ], 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'BucketARN' => [ 'shape' => 'BucketARN', ], 'Prefix' => [ 'shape' => 'Prefix', ], 'ErrorOutputPrefix' => [ 'shape' => 'ErrorOutputPrefix', ], 'BufferingHints' => [ 'shape' => 'BufferingHints', ], 'CompressionFormat' => [ 'shape' => 'CompressionFormat', ], 'EncryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], ], ], 'S3DestinationUpdate' => [ 'type' => 'structure', 'members' => [ 'RoleARN' => [ 'shape' => 'RoleARN', ], 'BucketARN' => [ 'shape' => 'BucketARN', ], 'Prefix' => [ 'shape' => 'Prefix', ], 'ErrorOutputPrefix' => [ 'shape' => 'ErrorOutputPrefix', ], 'BufferingHints' => [ 'shape' => 'BufferingHints', ], 'CompressionFormat' => [ 'shape' => 'CompressionFormat', ], 'EncryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], ], ], 'SchemaConfiguration' => [ 'type' => 'structure', 'members' => [ 'RoleARN' => [ 'shape' => 'NonEmptyStringWithoutWhitespace', ], 'CatalogId' => [ 'shape' => 'NonEmptyStringWithoutWhitespace', ], 'DatabaseName' => [ 'shape' => 'NonEmptyStringWithoutWhitespace', ], 'TableName' => [ 'shape' => 'NonEmptyStringWithoutWhitespace', ], 'Region' => [ 'shape' => 'NonEmptyStringWithoutWhitespace', ], 'VersionId' => [ 'shape' => 'NonEmptyStringWithoutWhitespace', ], ], ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyStringWithoutWhitespace', ], 'max' => 5, 'min' => 1, ], 'Serializer' => [ 'type' => 'structure', 'members' => [ 'ParquetSerDe' => [ 'shape' => 'ParquetSerDe', ], 'OrcSerDe' => [ 'shape' => 'OrcSerDe', ], ], ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'SizeInMBs' => [ 'type' => 'integer', 'max' => 128, 'min' => 1, ], 'SourceDescription' => [ 'type' => 'structure', 'members' => [ 'KinesisStreamSourceDescription' => [ 'shape' => 'KinesisStreamSourceDescription', ], 'MSKSourceDescription' => [ 'shape' => 'MSKSourceDescription', ], ], ], 'SplunkDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'HECEndpoint', 'HECEndpointType', 'HECToken', 'S3Configuration', ], 'members' => [ 'HECEndpoint' => [ 'shape' => 'HECEndpoint', ], 'HECEndpointType' => [ 'shape' => 'HECEndpointType', ], 'HECToken' => [ 'shape' => 'HECToken', ], 'HECAcknowledgmentTimeoutInSeconds' => [ 'shape' => 'HECAcknowledgmentTimeoutInSeconds', ], 'RetryOptions' => [ 'shape' => 'SplunkRetryOptions', ], 'S3BackupMode' => [ 'shape' => 'SplunkS3BackupMode', ], 'S3Configuration' => [ 'shape' => 'S3DestinationConfiguration', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], ], ], 'SplunkDestinationDescription' => [ 'type' => 'structure', 'members' => [ 'HECEndpoint' => [ 'shape' => 'HECEndpoint', ], 'HECEndpointType' => [ 'shape' => 'HECEndpointType', ], 'HECToken' => [ 'shape' => 'HECToken', ], 'HECAcknowledgmentTimeoutInSeconds' => [ 'shape' => 'HECAcknowledgmentTimeoutInSeconds', ], 'RetryOptions' => [ 'shape' => 'SplunkRetryOptions', ], 'S3BackupMode' => [ 'shape' => 'SplunkS3BackupMode', ], 'S3DestinationDescription' => [ 'shape' => 'S3DestinationDescription', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], ], ], 'SplunkDestinationUpdate' => [ 'type' => 'structure', 'members' => [ 'HECEndpoint' => [ 'shape' => 'HECEndpoint', ], 'HECEndpointType' => [ 'shape' => 'HECEndpointType', ], 'HECToken' => [ 'shape' => 'HECToken', ], 'HECAcknowledgmentTimeoutInSeconds' => [ 'shape' => 'HECAcknowledgmentTimeoutInSeconds', ], 'RetryOptions' => [ 'shape' => 'SplunkRetryOptions', ], 'S3BackupMode' => [ 'shape' => 'SplunkS3BackupMode', ], 'S3Update' => [ 'shape' => 'S3DestinationUpdate', ], 'ProcessingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'CloudWatchLoggingOptions' => [ 'shape' => 'CloudWatchLoggingOptions', ], ], ], 'SplunkRetryDurationInSeconds' => [ 'type' => 'integer', 'max' => 7200, 'min' => 0, ], 'SplunkRetryOptions' => [ 'type' => 'structure', 'members' => [ 'DurationInSeconds' => [ 'shape' => 'SplunkRetryDurationInSeconds', ], ], ], 'SplunkS3BackupMode' => [ 'type' => 'string', 'enum' => [ 'FailedEventsOnly', 'AllEvents', ], ], 'StartDeliveryStreamEncryptionInput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamName', ], 'members' => [ 'DeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'DeliveryStreamEncryptionConfigurationInput' => [ 'shape' => 'DeliveryStreamEncryptionConfigurationInput', ], ], ], 'StartDeliveryStreamEncryptionOutput' => [ 'type' => 'structure', 'members' => [], ], 'StopDeliveryStreamEncryptionInput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamName', ], 'members' => [ 'DeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], ], ], 'StopDeliveryStreamEncryptionOutput' => [ 'type' => 'structure', 'members' => [], ], 'SubnetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyStringWithoutWhitespace', ], 'max' => 16, 'min' => 1, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagDeliveryStreamInput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamName', 'Tags', ], 'members' => [ 'DeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'Tags' => [ 'shape' => 'TagDeliveryStreamInputTagList', ], ], ], 'TagDeliveryStreamInputTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 1, ], 'TagDeliveryStreamOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@%]*$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@%]*$', ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TopicName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\\\._\\\\-]+', ], 'UntagDeliveryStreamInput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamName', 'TagKeys', ], 'members' => [ 'DeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagDeliveryStreamOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDestinationInput' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamName', 'CurrentDeliveryStreamVersionId', 'DestinationId', ], 'members' => [ 'DeliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'CurrentDeliveryStreamVersionId' => [ 'shape' => 'DeliveryStreamVersionId', ], 'DestinationId' => [ 'shape' => 'DestinationId', ], 'S3DestinationUpdate' => [ 'shape' => 'S3DestinationUpdate', 'deprecated' => true, ], 'ExtendedS3DestinationUpdate' => [ 'shape' => 'ExtendedS3DestinationUpdate', ], 'RedshiftDestinationUpdate' => [ 'shape' => 'RedshiftDestinationUpdate', ], 'ElasticsearchDestinationUpdate' => [ 'shape' => 'ElasticsearchDestinationUpdate', ], 'AmazonopensearchserviceDestinationUpdate' => [ 'shape' => 'AmazonopensearchserviceDestinationUpdate', ], 'SplunkDestinationUpdate' => [ 'shape' => 'SplunkDestinationUpdate', ], 'HttpEndpointDestinationUpdate' => [ 'shape' => 'HttpEndpointDestinationUpdate', ], 'AmazonOpenSearchServerlessDestinationUpdate' => [ 'shape' => 'AmazonOpenSearchServerlessDestinationUpdate', ], ], ], 'UpdateDestinationOutput' => [ 'type' => 'structure', 'members' => [], ], 'Username' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '.*', 'sensitive' => true, ], 'VpcConfiguration' => [ 'type' => 'structure', 'required' => [ 'SubnetIds', 'RoleARN', 'SecurityGroupIds', ], 'members' => [ 'SubnetIds' => [ 'shape' => 'SubnetIdList', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], ], ], 'VpcConfigurationDescription' => [ 'type' => 'structure', 'required' => [ 'SubnetIds', 'RoleARN', 'SecurityGroupIds', 'VpcId', ], 'members' => [ 'SubnetIds' => [ 'shape' => 'SubnetIdList', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], 'VpcId' => [ 'shape' => 'NonEmptyStringWithoutWhitespace', ], ], ], ],];
