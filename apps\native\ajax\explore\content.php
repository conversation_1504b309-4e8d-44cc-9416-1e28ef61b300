<?php
# @*************************************************************************@
# @ Software author: <PERSON><PERSON> (<PERSON>ur_TL)                               @
# @ UI/UX Designer & Web developer ;)                                       @
# @                                                                         @
# @*************************************************************************@
# @ Instagram: https://www.instagram.com/mansur_tl                          @
# @ VK: https://vk.com/mansur_tl_uiux                                       @
# @ Envato: http://codecanyon.net/user/mansur_tl                            @
# @ Behance: https://www.behance.net/mansur_tl                              @
# @ Telegram: https://t.me/mansurtl_contact                                 @
# @*************************************************************************@
# @ E-mail: <EMAIL>                                      @
# @ Website: https://www.mansurtl.com                                       @
# @*************************************************************************@
# @ ColibriSM - The Ultimate Social Network PHP Script                      @
# @ Copyright (c)  ColibriSM. All rights reserved                           @
# @*************************************************************************@

// Prevent any HTML output before JSON response - CRITICAL FIX
ob_start();

require_once(cl_full_path("core/apps/explore/app_ctrl.php"));

if ($action == 'load_more') {
    if (empty($cl["is_logged"])) {
        $data['status'] = 400;
        $data['error']  = 'Invalid access token';
    }
    else {
        $data['err_code'] = "0";
        $data['status']   = 400;
        $offset           = fetch_or_get($_GET['offset'], 0);
        $type             = fetch_or_get($_GET['type'], null);
        $search_query     = fetch_or_get($_GET['q'], null);
        $query_result     = array();
        $html_arr         = array();

        // Debug information
        $data['debug'] = array(
            'offset' => $offset,
            'type' => $type,
            'search_query' => $search_query,
            'is_logged' => $cl["is_logged"],
            'user_id' => isset($me['id']) ? $me['id'] : 'not_set'
        );

    if (is_posnum($offset)) {
    	if ($type == "htags") {
            if (not_empty($search_query)) {
                $search_query = cl_text_secure($search_query);
                $search_query = cl_croptxt($search_query, 32);
            }

            $query_result = cl_search_hashtags($search_query, $offset, 10);
            
            if (not_empty($query_result)) {
                foreach ($query_result as $cl['li']) {
                    try {
                        // Capture template output using output buffering
                        ob_start();
                        cl_template('explore/includes/li/htag_li');
                        $template_output = ob_get_contents();
                        ob_end_clean();

                        if ($template_output && trim($template_output)) {
                            $html_arr[] = $template_output;
                        }
                    } catch (Exception $e) {
                        // Clean any partial output
                        if (ob_get_level()) {
                            ob_end_clean();
                        }
                        $data['error'] = 'Template error: ' . $e->getMessage();
                        $data['status'] = 500;
                        return;
                    }
                }

                $data['status'] = 200;
                $data['html']   = implode("", $html_arr);
            }
        }

        else if($type == "people") {
            if (not_empty($search_query)) {
                $search_query = cl_text_secure($search_query);
                $search_query = cl_croptxt($search_query, 32);
            }

            $query_result = cl_search_people($search_query, $offset, 10);

            if (not_empty($query_result)) {
                foreach ($query_result as $cl['li']) {
                    try {
                        // Capture template output using output buffering
                        ob_start();
                        cl_template('explore/includes/li/people_li');
                        $template_output = ob_get_contents();
                        ob_end_clean();

                        if ($template_output && trim($template_output)) {
                            $html_arr[] = $template_output;
                        }
                    } catch (Exception $e) {
                        // Clean any partial output
                        if (ob_get_level()) {
                            ob_end_clean();
                        }
                        error_log("Template error for people: " . $e->getMessage());
                        // Continue with other items even if one fails
                    }
                }

                $data['status'] = 200;
                $data['html']   = implode("", $html_arr);
            }
        }

        else if($type == "posts") {
            if (not_empty($search_query)) {
                $search_query = cl_text_secure($search_query);
                $search_query = cl_croptxt($search_query, 32);
            }

            $query_result = cl_search_posts($search_query, $offset, 10);

            if (not_empty($query_result)) {
                foreach ($query_result as $cl['li']) {
                    try {
                        // Capture template output using output buffering
                        ob_start();
                        cl_template('timeline/post');
                        $template_output = ob_get_contents();
                        ob_end_clean();

                        if ($template_output && trim($template_output)) {
                            $html_arr[] = $template_output;
                        }
                    } catch (Exception $e) {
                        // Clean any partial output
                        if (ob_get_level()) {
                            ob_end_clean();
                        }
                        error_log("Template error for post: " . $e->getMessage());
                        // Continue with other posts even if one fails
                    }
                }

                $data['status'] = 200;
                $data['html']   = implode("", $html_arr);
            }
        }
        }
    }
}

else if($action == 'search') {
    if (empty($cl["is_logged"])) {
        $data['status'] = 400;
        $data['error']  = 'Invalid access token';
    }
    else {
        $data['err_code'] = "0";
        $data['status']   = 400;
        $type             = fetch_or_get($_GET['type'], null);
        $search_query     = fetch_or_get($_GET['q'], null);
        $query_result     = array();
        $html_arr         = array();

    if (not_empty($search_query) && len($search_query) >= 2) {
        if ($type == "htags") {
            $search_query = cl_text_secure($search_query);
            $search_query = cl_croptxt($search_query, 32);
            $query_result = cl_search_hashtags($search_query, false, 10);
            
            if (not_empty($query_result)) {
                foreach ($query_result as $cl['li']) {
                    try {
                        // Capture template output using output buffering
                        ob_start();
                        cl_template('explore/includes/li/htag_li');
                        $template_output = ob_get_contents();
                        ob_end_clean();

                        if ($template_output && trim($template_output)) {
                            $html_arr[] = $template_output;
                        }
                    } catch (Exception $e) {
                        // Clean any partial output
                        if (ob_get_level()) {
                            ob_end_clean();
                        }
                        error_log("Template error for hashtag: " . $e->getMessage());
                        // Continue with other items even if one fails
                    }
                }

                $data['status'] = 200;
                $data['html']   = implode("", $html_arr);
            }
        }

        else if($type == "people") {
            $search_query = cl_text_secure($search_query);
            $search_query = cl_croptxt($search_query, 32);
            $query_result = cl_search_people($search_query, false, 10);

            if (not_empty($query_result)) {
                foreach ($query_result as $cl['li']) {
                    try {
                        // Capture template output using output buffering
                        ob_start();
                        cl_template('explore/includes/li/people_li');
                        $template_output = ob_get_contents();
                        ob_end_clean();

                        if ($template_output && trim($template_output)) {
                            $html_arr[] = $template_output;
                        }
                    } catch (Exception $e) {
                        // Clean any partial output
                        if (ob_get_level()) {
                            ob_end_clean();
                        }
                        error_log("Template error for people: " . $e->getMessage());
                        // Continue with other items even if one fails
                    }
                }

                $data['status'] = 200;
                $data['html']   = implode("", $html_arr);
            }
        }

        else if($type == "posts") {
            $search_query = cl_text_secure($search_query);
            $search_query = cl_croptxt($search_query, 32);
            $query_result = cl_search_posts($search_query, false, 10);

            if (not_empty($query_result)) {
                foreach ($query_result as $cl['li']) {
                    try {
                        // Capture template output using output buffering
                        ob_start();
                        cl_template('timeline/post');
                        $template_output = ob_get_contents();
                        ob_end_clean();

                        if ($template_output && trim($template_output)) {
                            $html_arr[] = $template_output;
                        }
                    } catch (Exception $e) {
                        // Clean any partial output
                        if (ob_get_level()) {
                            ob_end_clean();
                        }
                        error_log("Template error for post: " . $e->getMessage());
                        // Continue with other posts even if one fails
                    }
                }

                $data['status'] = 200;
                $data['html']   = implode("", $html_arr);
            }
        }
        }
    }
}

// Clean any remaining output buffer to prevent HTML output before JSON
if (ob_get_level()) {
    ob_end_clean();
}