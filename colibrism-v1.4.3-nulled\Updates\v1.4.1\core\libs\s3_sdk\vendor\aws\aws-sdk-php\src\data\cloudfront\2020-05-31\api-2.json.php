<?php
// This file was auto-generated from sdk-root/src/data/cloudfront/2020-05-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-05-31', 'endpointPrefix' => 'cloudfront', 'globalEndpoint' => 'cloudfront.amazonaws.com', 'protocol' => 'rest-xml', 'serviceAbbreviation' => 'CloudFront', 'serviceFullName' => 'Amazon CloudFront', 'serviceId' => 'CloudFront', 'signatureVersion' => 'v4', 'uid' => 'cloudfront-2020-05-31', ], 'operations' => [ 'AssociateAlias' => [ 'name' => 'AssociateAlias2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/distribution/{TargetDistributionId}/associate-alias', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateAliasRequest', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'TooManyDistributionCNAMEs', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'AccessDenied', ], ], ], 'CopyDistribution' => [ 'name' => 'CopyDistribution2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distribution/{PrimaryDistributionId}/copy', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CopyDistributionRequest', 'locationName' => 'CopyDistributionRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'CopyDistributionResult', ], 'errors' => [ [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'DistributionAlreadyExists', ], [ 'shape' => 'InvalidOrigin', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'InvalidViewerCertificate', ], [ 'shape' => 'InvalidMinimumProtocolVersion', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyDistributionCNAMEs', ], [ 'shape' => 'TooManyDistributions', ], [ 'shape' => 'InvalidDefaultRootObject', ], [ 'shape' => 'InvalidRelativePath', ], [ 'shape' => 'InvalidErrorCode', ], [ 'shape' => 'InvalidResponseCode', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidRequiredProtocol', ], [ 'shape' => 'NoSuchOrigin', ], [ 'shape' => 'TooManyOrigins', ], [ 'shape' => 'TooManyOriginGroupsPerDistribution', ], [ 'shape' => 'TooManyCacheBehaviors', ], [ 'shape' => 'TooManyCookieNamesInWhiteList', ], [ 'shape' => 'InvalidForwardCookies', ], [ 'shape' => 'TooManyHeadersInForwardedValues', ], [ 'shape' => 'InvalidHeadersForS3Origin', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'TooManyCertificates', ], [ 'shape' => 'InvalidLocationCode', ], [ 'shape' => 'InvalidGeoRestrictionParameter', ], [ 'shape' => 'InvalidProtocolSettings', ], [ 'shape' => 'InvalidTTLOrder', ], [ 'shape' => 'InvalidWebACLId', ], [ 'shape' => 'TooManyOriginCustomHeaders', ], [ 'shape' => 'TooManyQueryStringParameters', ], [ 'shape' => 'InvalidQueryStringParameters', ], [ 'shape' => 'TooManyDistributionsWithLambdaAssociations', ], [ 'shape' => 'TooManyDistributionsWithSingleFunctionARN', ], [ 'shape' => 'TooManyLambdaFunctionAssociations', ], [ 'shape' => 'InvalidLambdaFunctionAssociation', ], [ 'shape' => 'TooManyDistributionsWithFunctionAssociations', ], [ 'shape' => 'TooManyFunctionAssociations', ], [ 'shape' => 'InvalidFunctionAssociation', ], [ 'shape' => 'InvalidOriginReadTimeout', ], [ 'shape' => 'InvalidOriginKeepaliveTimeout', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'IllegalFieldLevelEncryptionConfigAssociationWithCacheBehavior', ], [ 'shape' => 'TooManyDistributionsAssociatedToFieldLevelEncryptionConfig', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToCachePolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginAccessControl', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToResponseHeadersPolicy', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToKeyGroup', ], [ 'shape' => 'TooManyKeyGroupsAssociatedToDistribution', ], [ 'shape' => 'TrustedKeyGroupDoesNotExist', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'RealtimeLogConfigOwnerMismatch', ], ], ], 'CreateCachePolicy' => [ 'name' => 'CreateCachePolicy2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/cache-policy', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCachePolicyRequest', ], 'output' => [ 'shape' => 'CreateCachePolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'CachePolicyAlreadyExists', ], [ 'shape' => 'TooManyCachePolicies', ], [ 'shape' => 'TooManyHeadersInCachePolicy', ], [ 'shape' => 'TooManyCookiesInCachePolicy', ], [ 'shape' => 'TooManyQueryStringsInCachePolicy', ], ], ], 'CreateCloudFrontOriginAccessIdentity' => [ 'name' => 'CreateCloudFrontOriginAccessIdentity2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/origin-access-identity/cloudfront', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCloudFrontOriginAccessIdentityRequest', ], 'output' => [ 'shape' => 'CreateCloudFrontOriginAccessIdentityResult', ], 'errors' => [ [ 'shape' => 'CloudFrontOriginAccessIdentityAlreadyExists', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyCloudFrontOriginAccessIdentities', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InconsistentQuantities', ], ], ], 'CreateContinuousDeploymentPolicy' => [ 'name' => 'CreateContinuousDeploymentPolicy2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/continuous-deployment-policy', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateContinuousDeploymentPolicyRequest', ], 'output' => [ 'shape' => 'CreateContinuousDeploymentPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'ContinuousDeploymentPolicyAlreadyExists', ], [ 'shape' => 'TooManyContinuousDeploymentPolicies', ], [ 'shape' => 'StagingDistributionInUse', ], ], ], 'CreateDistribution' => [ 'name' => 'CreateDistribution2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distribution', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDistributionRequest', ], 'output' => [ 'shape' => 'CreateDistributionResult', ], 'errors' => [ [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'DistributionAlreadyExists', ], [ 'shape' => 'InvalidOrigin', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'IllegalOriginAccessConfiguration', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginAccessControl', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'InvalidViewerCertificate', ], [ 'shape' => 'InvalidMinimumProtocolVersion', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyDistributionCNAMEs', ], [ 'shape' => 'TooManyDistributions', ], [ 'shape' => 'InvalidDefaultRootObject', ], [ 'shape' => 'InvalidRelativePath', ], [ 'shape' => 'InvalidErrorCode', ], [ 'shape' => 'InvalidResponseCode', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidRequiredProtocol', ], [ 'shape' => 'NoSuchOrigin', ], [ 'shape' => 'TooManyOrigins', ], [ 'shape' => 'TooManyOriginGroupsPerDistribution', ], [ 'shape' => 'TooManyCacheBehaviors', ], [ 'shape' => 'TooManyCookieNamesInWhiteList', ], [ 'shape' => 'InvalidForwardCookies', ], [ 'shape' => 'TooManyHeadersInForwardedValues', ], [ 'shape' => 'InvalidHeadersForS3Origin', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'TooManyCertificates', ], [ 'shape' => 'InvalidLocationCode', ], [ 'shape' => 'InvalidGeoRestrictionParameter', ], [ 'shape' => 'InvalidProtocolSettings', ], [ 'shape' => 'InvalidTTLOrder', ], [ 'shape' => 'InvalidWebACLId', ], [ 'shape' => 'TooManyOriginCustomHeaders', ], [ 'shape' => 'TooManyQueryStringParameters', ], [ 'shape' => 'InvalidQueryStringParameters', ], [ 'shape' => 'TooManyDistributionsWithLambdaAssociations', ], [ 'shape' => 'TooManyDistributionsWithSingleFunctionARN', ], [ 'shape' => 'TooManyLambdaFunctionAssociations', ], [ 'shape' => 'InvalidLambdaFunctionAssociation', ], [ 'shape' => 'TooManyDistributionsWithFunctionAssociations', ], [ 'shape' => 'TooManyFunctionAssociations', ], [ 'shape' => 'InvalidFunctionAssociation', ], [ 'shape' => 'InvalidOriginReadTimeout', ], [ 'shape' => 'InvalidOriginKeepaliveTimeout', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'IllegalFieldLevelEncryptionConfigAssociationWithCacheBehavior', ], [ 'shape' => 'TooManyDistributionsAssociatedToFieldLevelEncryptionConfig', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToCachePolicy', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToResponseHeadersPolicy', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToKeyGroup', ], [ 'shape' => 'TooManyKeyGroupsAssociatedToDistribution', ], [ 'shape' => 'TrustedKeyGroupDoesNotExist', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'RealtimeLogConfigOwnerMismatch', ], [ 'shape' => 'ContinuousDeploymentPolicyInUse', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], [ 'shape' => 'InvalidDomainNameForOriginAccessControl', ], ], ], 'CreateDistributionWithTags' => [ 'name' => 'CreateDistributionWithTags2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distribution?WithTags', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDistributionWithTagsRequest', ], 'output' => [ 'shape' => 'CreateDistributionWithTagsResult', ], 'errors' => [ [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'DistributionAlreadyExists', ], [ 'shape' => 'InvalidOrigin', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'IllegalOriginAccessConfiguration', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'InvalidViewerCertificate', ], [ 'shape' => 'InvalidMinimumProtocolVersion', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyDistributionCNAMEs', ], [ 'shape' => 'TooManyDistributions', ], [ 'shape' => 'InvalidDefaultRootObject', ], [ 'shape' => 'InvalidRelativePath', ], [ 'shape' => 'InvalidErrorCode', ], [ 'shape' => 'InvalidResponseCode', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidRequiredProtocol', ], [ 'shape' => 'NoSuchOrigin', ], [ 'shape' => 'TooManyOrigins', ], [ 'shape' => 'TooManyOriginGroupsPerDistribution', ], [ 'shape' => 'TooManyCacheBehaviors', ], [ 'shape' => 'TooManyCookieNamesInWhiteList', ], [ 'shape' => 'InvalidForwardCookies', ], [ 'shape' => 'TooManyHeadersInForwardedValues', ], [ 'shape' => 'InvalidHeadersForS3Origin', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'TooManyCertificates', ], [ 'shape' => 'InvalidLocationCode', ], [ 'shape' => 'InvalidGeoRestrictionParameter', ], [ 'shape' => 'InvalidProtocolSettings', ], [ 'shape' => 'InvalidTTLOrder', ], [ 'shape' => 'InvalidWebACLId', ], [ 'shape' => 'TooManyOriginCustomHeaders', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'TooManyQueryStringParameters', ], [ 'shape' => 'InvalidQueryStringParameters', ], [ 'shape' => 'TooManyDistributionsWithLambdaAssociations', ], [ 'shape' => 'TooManyDistributionsWithSingleFunctionARN', ], [ 'shape' => 'TooManyLambdaFunctionAssociations', ], [ 'shape' => 'InvalidLambdaFunctionAssociation', ], [ 'shape' => 'TooManyDistributionsWithFunctionAssociations', ], [ 'shape' => 'TooManyFunctionAssociations', ], [ 'shape' => 'InvalidFunctionAssociation', ], [ 'shape' => 'InvalidOriginReadTimeout', ], [ 'shape' => 'InvalidOriginKeepaliveTimeout', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'IllegalFieldLevelEncryptionConfigAssociationWithCacheBehavior', ], [ 'shape' => 'TooManyDistributionsAssociatedToFieldLevelEncryptionConfig', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToCachePolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginAccessControl', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToResponseHeadersPolicy', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToKeyGroup', ], [ 'shape' => 'TooManyKeyGroupsAssociatedToDistribution', ], [ 'shape' => 'TrustedKeyGroupDoesNotExist', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'RealtimeLogConfigOwnerMismatch', ], [ 'shape' => 'ContinuousDeploymentPolicyInUse', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], [ 'shape' => 'InvalidDomainNameForOriginAccessControl', ], ], ], 'CreateFieldLevelEncryptionConfig' => [ 'name' => 'CreateFieldLevelEncryptionConfig2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/field-level-encryption', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFieldLevelEncryptionConfigRequest', ], 'output' => [ 'shape' => 'CreateFieldLevelEncryptionConfigResult', ], 'errors' => [ [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchFieldLevelEncryptionProfile', ], [ 'shape' => 'FieldLevelEncryptionConfigAlreadyExists', ], [ 'shape' => 'TooManyFieldLevelEncryptionConfigs', ], [ 'shape' => 'TooManyFieldLevelEncryptionQueryArgProfiles', ], [ 'shape' => 'TooManyFieldLevelEncryptionContentTypeProfiles', ], [ 'shape' => 'QueryArgProfileEmpty', ], ], ], 'CreateFieldLevelEncryptionProfile' => [ 'name' => 'CreateFieldLevelEncryptionProfile2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/field-level-encryption-profile', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFieldLevelEncryptionProfileRequest', ], 'output' => [ 'shape' => 'CreateFieldLevelEncryptionProfileResult', ], 'errors' => [ [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchPublicKey', ], [ 'shape' => 'FieldLevelEncryptionProfileAlreadyExists', ], [ 'shape' => 'FieldLevelEncryptionProfileSizeExceeded', ], [ 'shape' => 'TooManyFieldLevelEncryptionProfiles', ], [ 'shape' => 'TooManyFieldLevelEncryptionEncryptionEntities', ], [ 'shape' => 'TooManyFieldLevelEncryptionFieldPatterns', ], ], ], 'CreateFunction' => [ 'name' => 'CreateFunction2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/function', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFunctionRequest', 'locationName' => 'CreateFunctionRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'CreateFunctionResult', ], 'errors' => [ [ 'shape' => 'TooManyFunctions', ], [ 'shape' => 'FunctionAlreadyExists', ], [ 'shape' => 'FunctionSizeLimitExceeded', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'CreateInvalidation' => [ 'name' => 'CreateInvalidation2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distribution/{DistributionId}/invalidation', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateInvalidationRequest', ], 'output' => [ 'shape' => 'CreateInvalidationResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'BatchTooLarge', ], [ 'shape' => 'TooManyInvalidationsInProgress', ], [ 'shape' => 'InconsistentQuantities', ], ], ], 'CreateKeyGroup' => [ 'name' => 'CreateKeyGroup2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/key-group', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateKeyGroupRequest', ], 'output' => [ 'shape' => 'CreateKeyGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'KeyGroupAlreadyExists', ], [ 'shape' => 'TooManyKeyGroups', ], [ 'shape' => 'TooManyPublicKeysInKeyGroup', ], ], ], 'CreateKeyValueStore' => [ 'name' => 'CreateKeyValueStore2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/key-value-store/', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateKeyValueStoreRequest', 'locationName' => 'CreateKeyValueStoreRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'CreateKeyValueStoreResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityLimitExceeded', ], [ 'shape' => 'EntityAlreadyExists', ], [ 'shape' => 'EntitySizeLimitExceeded', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateMonitoringSubscription' => [ 'name' => 'CreateMonitoringSubscription2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distributions/{DistributionId}/monitoring-subscription/', ], 'input' => [ 'shape' => 'CreateMonitoringSubscriptionRequest', ], 'output' => [ 'shape' => 'CreateMonitoringSubscriptionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'MonitoringSubscriptionAlreadyExists', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'CreateOriginAccessControl' => [ 'name' => 'CreateOriginAccessControl2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/origin-access-control', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateOriginAccessControlRequest', ], 'output' => [ 'shape' => 'CreateOriginAccessControlResult', ], 'errors' => [ [ 'shape' => 'OriginAccessControlAlreadyExists', ], [ 'shape' => 'TooManyOriginAccessControls', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateOriginRequestPolicy' => [ 'name' => 'CreateOriginRequestPolicy2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/origin-request-policy', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateOriginRequestPolicyRequest', ], 'output' => [ 'shape' => 'CreateOriginRequestPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'OriginRequestPolicyAlreadyExists', ], [ 'shape' => 'TooManyOriginRequestPolicies', ], [ 'shape' => 'TooManyHeadersInOriginRequestPolicy', ], [ 'shape' => 'TooManyCookiesInOriginRequestPolicy', ], [ 'shape' => 'TooManyQueryStringsInOriginRequestPolicy', ], ], ], 'CreatePublicKey' => [ 'name' => 'CreatePublicKey2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/public-key', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreatePublicKeyRequest', ], 'output' => [ 'shape' => 'CreatePublicKeyResult', ], 'errors' => [ [ 'shape' => 'PublicKeyAlreadyExists', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'TooManyPublicKeys', ], ], ], 'CreateRealtimeLogConfig' => [ 'name' => 'CreateRealtimeLogConfig2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/realtime-log-config', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateRealtimeLogConfigRequest', 'locationName' => 'CreateRealtimeLogConfigRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'CreateRealtimeLogConfigResult', ], 'errors' => [ [ 'shape' => 'RealtimeLogConfigAlreadyExists', ], [ 'shape' => 'TooManyRealtimeLogConfigs', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'AccessDenied', ], ], ], 'CreateResponseHeadersPolicy' => [ 'name' => 'CreateResponseHeadersPolicy2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/response-headers-policy', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateResponseHeadersPolicyRequest', ], 'output' => [ 'shape' => 'CreateResponseHeadersPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'ResponseHeadersPolicyAlreadyExists', ], [ 'shape' => 'TooManyResponseHeadersPolicies', ], [ 'shape' => 'TooManyCustomHeadersInResponseHeadersPolicy', ], [ 'shape' => 'TooLongCSPInResponseHeadersPolicy', ], [ 'shape' => 'TooManyRemoveHeadersInResponseHeadersPolicy', ], ], ], 'CreateStreamingDistribution' => [ 'name' => 'CreateStreamingDistribution2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/streaming-distribution', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateStreamingDistributionRequest', ], 'output' => [ 'shape' => 'CreateStreamingDistributionResult', ], 'errors' => [ [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'StreamingDistributionAlreadyExists', ], [ 'shape' => 'InvalidOrigin', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyStreamingDistributionCNAMEs', ], [ 'shape' => 'TooManyStreamingDistributions', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InconsistentQuantities', ], ], ], 'CreateStreamingDistributionWithTags' => [ 'name' => 'CreateStreamingDistributionWithTags2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/streaming-distribution?WithTags', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateStreamingDistributionWithTagsRequest', ], 'output' => [ 'shape' => 'CreateStreamingDistributionWithTagsResult', ], 'errors' => [ [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'StreamingDistributionAlreadyExists', ], [ 'shape' => 'InvalidOrigin', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyStreamingDistributionCNAMEs', ], [ 'shape' => 'TooManyStreamingDistributions', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidTagging', ], ], ], 'DeleteCachePolicy' => [ 'name' => 'DeleteCachePolicy2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/cache-policy/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteCachePolicyRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'IllegalDelete', ], [ 'shape' => 'CachePolicyInUse', ], ], ], 'DeleteCloudFrontOriginAccessIdentity' => [ 'name' => 'DeleteCloudFrontOriginAccessIdentity2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/origin-access-identity/cloudfront/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteCloudFrontOriginAccessIdentityRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchCloudFrontOriginAccessIdentity', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'CloudFrontOriginAccessIdentityInUse', ], ], ], 'DeleteContinuousDeploymentPolicy' => [ 'name' => 'DeleteContinuousDeploymentPolicy2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/continuous-deployment-policy/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteContinuousDeploymentPolicyRequest', ], 'errors' => [ [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'ContinuousDeploymentPolicyInUse', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], ], ], 'DeleteDistribution' => [ 'name' => 'DeleteDistribution2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/distribution/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteDistributionRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'DistributionNotDisabled', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'PreconditionFailed', ], ], ], 'DeleteFieldLevelEncryptionConfig' => [ 'name' => 'DeleteFieldLevelEncryptionConfig2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/field-level-encryption/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFieldLevelEncryptionConfigRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'FieldLevelEncryptionConfigInUse', ], ], ], 'DeleteFieldLevelEncryptionProfile' => [ 'name' => 'DeleteFieldLevelEncryptionProfile2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/field-level-encryption-profile/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFieldLevelEncryptionProfileRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchFieldLevelEncryptionProfile', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'FieldLevelEncryptionProfileInUse', ], ], ], 'DeleteFunction' => [ 'name' => 'DeleteFunction2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/function/{Name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFunctionRequest', ], 'errors' => [ [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchFunctionExists', ], [ 'shape' => 'FunctionInUse', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'DeleteKeyGroup' => [ 'name' => 'DeleteKeyGroup2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/key-group/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteKeyGroupRequest', ], 'errors' => [ [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchResource', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'ResourceInUse', ], ], ], 'DeleteKeyValueStore' => [ 'name' => 'DeleteKeyValueStore2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/key-value-store/{Name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteKeyValueStoreRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'CannotDeleteEntityWhileInUse', ], [ 'shape' => 'PreconditionFailed', ], ], 'idempotent' => true, ], 'DeleteMonitoringSubscription' => [ 'name' => 'DeleteMonitoringSubscription2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/distributions/{DistributionId}/monitoring-subscription/', ], 'input' => [ 'shape' => 'DeleteMonitoringSubscriptionRequest', ], 'output' => [ 'shape' => 'DeleteMonitoringSubscriptionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'NoSuchMonitoringSubscription', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'DeleteOriginAccessControl' => [ 'name' => 'DeleteOriginAccessControl2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/origin-access-control/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteOriginAccessControlRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchOriginAccessControl', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'OriginAccessControlInUse', ], ], ], 'DeleteOriginRequestPolicy' => [ 'name' => 'DeleteOriginRequestPolicy2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/origin-request-policy/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteOriginRequestPolicyRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'IllegalDelete', ], [ 'shape' => 'OriginRequestPolicyInUse', ], ], ], 'DeletePublicKey' => [ 'name' => 'DeletePublicKey2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/public-key/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeletePublicKeyRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'PublicKeyInUse', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchPublicKey', ], [ 'shape' => 'PreconditionFailed', ], ], ], 'DeleteRealtimeLogConfig' => [ 'name' => 'DeleteRealtimeLogConfig2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/delete-realtime-log-config/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteRealtimeLogConfigRequest', 'locationName' => 'DeleteRealtimeLogConfigRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'errors' => [ [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'RealtimeLogConfigInUse', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'AccessDenied', ], ], ], 'DeleteResponseHeadersPolicy' => [ 'name' => 'DeleteResponseHeadersPolicy2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/response-headers-policy/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteResponseHeadersPolicyRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'IllegalDelete', ], [ 'shape' => 'ResponseHeadersPolicyInUse', ], ], ], 'DeleteStreamingDistribution' => [ 'name' => 'DeleteStreamingDistribution2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/streaming-distribution/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteStreamingDistributionRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'StreamingDistributionNotDisabled', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchStreamingDistribution', ], [ 'shape' => 'PreconditionFailed', ], ], ], 'DescribeFunction' => [ 'name' => 'DescribeFunction2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/function/{Name}/describe', ], 'input' => [ 'shape' => 'DescribeFunctionRequest', ], 'output' => [ 'shape' => 'DescribeFunctionResult', ], 'errors' => [ [ 'shape' => 'NoSuchFunctionExists', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'DescribeKeyValueStore' => [ 'name' => 'DescribeKeyValueStore2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/key-value-store/{Name}', ], 'input' => [ 'shape' => 'DescribeKeyValueStoreRequest', ], 'output' => [ 'shape' => 'DescribeKeyValueStoreResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'EntityNotFound', ], ], ], 'GetCachePolicy' => [ 'name' => 'GetCachePolicy2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/cache-policy/{Id}', ], 'input' => [ 'shape' => 'GetCachePolicyRequest', ], 'output' => [ 'shape' => 'GetCachePolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchCachePolicy', ], ], ], 'GetCachePolicyConfig' => [ 'name' => 'GetCachePolicyConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/cache-policy/{Id}/config', ], 'input' => [ 'shape' => 'GetCachePolicyConfigRequest', ], 'output' => [ 'shape' => 'GetCachePolicyConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchCachePolicy', ], ], ], 'GetCloudFrontOriginAccessIdentity' => [ 'name' => 'GetCloudFrontOriginAccessIdentity2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-access-identity/cloudfront/{Id}', ], 'input' => [ 'shape' => 'GetCloudFrontOriginAccessIdentityRequest', ], 'output' => [ 'shape' => 'GetCloudFrontOriginAccessIdentityResult', ], 'errors' => [ [ 'shape' => 'NoSuchCloudFrontOriginAccessIdentity', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetCloudFrontOriginAccessIdentityConfig' => [ 'name' => 'GetCloudFrontOriginAccessIdentityConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-access-identity/cloudfront/{Id}/config', ], 'input' => [ 'shape' => 'GetCloudFrontOriginAccessIdentityConfigRequest', ], 'output' => [ 'shape' => 'GetCloudFrontOriginAccessIdentityConfigResult', ], 'errors' => [ [ 'shape' => 'NoSuchCloudFrontOriginAccessIdentity', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetContinuousDeploymentPolicy' => [ 'name' => 'GetContinuousDeploymentPolicy2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/continuous-deployment-policy/{Id}', ], 'input' => [ 'shape' => 'GetContinuousDeploymentPolicyRequest', ], 'output' => [ 'shape' => 'GetContinuousDeploymentPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], ], ], 'GetContinuousDeploymentPolicyConfig' => [ 'name' => 'GetContinuousDeploymentPolicyConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/continuous-deployment-policy/{Id}/config', ], 'input' => [ 'shape' => 'GetContinuousDeploymentPolicyConfigRequest', ], 'output' => [ 'shape' => 'GetContinuousDeploymentPolicyConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], ], ], 'GetDistribution' => [ 'name' => 'GetDistribution2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution/{Id}', ], 'input' => [ 'shape' => 'GetDistributionRequest', ], 'output' => [ 'shape' => 'GetDistributionResult', ], 'errors' => [ [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetDistributionConfig' => [ 'name' => 'GetDistributionConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution/{Id}/config', ], 'input' => [ 'shape' => 'GetDistributionConfigRequest', ], 'output' => [ 'shape' => 'GetDistributionConfigResult', ], 'errors' => [ [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetFieldLevelEncryption' => [ 'name' => 'GetFieldLevelEncryption2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/field-level-encryption/{Id}', ], 'input' => [ 'shape' => 'GetFieldLevelEncryptionRequest', ], 'output' => [ 'shape' => 'GetFieldLevelEncryptionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], ], ], 'GetFieldLevelEncryptionConfig' => [ 'name' => 'GetFieldLevelEncryptionConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/field-level-encryption/{Id}/config', ], 'input' => [ 'shape' => 'GetFieldLevelEncryptionConfigRequest', ], 'output' => [ 'shape' => 'GetFieldLevelEncryptionConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], ], ], 'GetFieldLevelEncryptionProfile' => [ 'name' => 'GetFieldLevelEncryptionProfile2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/field-level-encryption-profile/{Id}', ], 'input' => [ 'shape' => 'GetFieldLevelEncryptionProfileRequest', ], 'output' => [ 'shape' => 'GetFieldLevelEncryptionProfileResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchFieldLevelEncryptionProfile', ], ], ], 'GetFieldLevelEncryptionProfileConfig' => [ 'name' => 'GetFieldLevelEncryptionProfileConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/field-level-encryption-profile/{Id}/config', ], 'input' => [ 'shape' => 'GetFieldLevelEncryptionProfileConfigRequest', ], 'output' => [ 'shape' => 'GetFieldLevelEncryptionProfileConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchFieldLevelEncryptionProfile', ], ], ], 'GetFunction' => [ 'name' => 'GetFunction2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/function/{Name}', ], 'input' => [ 'shape' => 'GetFunctionRequest', ], 'output' => [ 'shape' => 'GetFunctionResult', ], 'errors' => [ [ 'shape' => 'NoSuchFunctionExists', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'GetInvalidation' => [ 'name' => 'GetInvalidation2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution/{DistributionId}/invalidation/{Id}', ], 'input' => [ 'shape' => 'GetInvalidationRequest', ], 'output' => [ 'shape' => 'GetInvalidationResult', ], 'errors' => [ [ 'shape' => 'NoSuchInvalidation', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetKeyGroup' => [ 'name' => 'GetKeyGroup2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/key-group/{Id}', ], 'input' => [ 'shape' => 'GetKeyGroupRequest', ], 'output' => [ 'shape' => 'GetKeyGroupResult', ], 'errors' => [ [ 'shape' => 'NoSuchResource', ], ], ], 'GetKeyGroupConfig' => [ 'name' => 'GetKeyGroupConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/key-group/{Id}/config', ], 'input' => [ 'shape' => 'GetKeyGroupConfigRequest', ], 'output' => [ 'shape' => 'GetKeyGroupConfigResult', ], 'errors' => [ [ 'shape' => 'NoSuchResource', ], ], ], 'GetMonitoringSubscription' => [ 'name' => 'GetMonitoringSubscription2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributions/{DistributionId}/monitoring-subscription/', ], 'input' => [ 'shape' => 'GetMonitoringSubscriptionRequest', ], 'output' => [ 'shape' => 'GetMonitoringSubscriptionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'NoSuchMonitoringSubscription', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'GetOriginAccessControl' => [ 'name' => 'GetOriginAccessControl2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-access-control/{Id}', ], 'input' => [ 'shape' => 'GetOriginAccessControlRequest', ], 'output' => [ 'shape' => 'GetOriginAccessControlResult', ], 'errors' => [ [ 'shape' => 'NoSuchOriginAccessControl', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetOriginAccessControlConfig' => [ 'name' => 'GetOriginAccessControlConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-access-control/{Id}/config', ], 'input' => [ 'shape' => 'GetOriginAccessControlConfigRequest', ], 'output' => [ 'shape' => 'GetOriginAccessControlConfigResult', ], 'errors' => [ [ 'shape' => 'NoSuchOriginAccessControl', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetOriginRequestPolicy' => [ 'name' => 'GetOriginRequestPolicy2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-request-policy/{Id}', ], 'input' => [ 'shape' => 'GetOriginRequestPolicyRequest', ], 'output' => [ 'shape' => 'GetOriginRequestPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], ], ], 'GetOriginRequestPolicyConfig' => [ 'name' => 'GetOriginRequestPolicyConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-request-policy/{Id}/config', ], 'input' => [ 'shape' => 'GetOriginRequestPolicyConfigRequest', ], 'output' => [ 'shape' => 'GetOriginRequestPolicyConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], ], ], 'GetPublicKey' => [ 'name' => 'GetPublicKey2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/public-key/{Id}', ], 'input' => [ 'shape' => 'GetPublicKeyRequest', ], 'output' => [ 'shape' => 'GetPublicKeyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchPublicKey', ], ], ], 'GetPublicKeyConfig' => [ 'name' => 'GetPublicKeyConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/public-key/{Id}/config', ], 'input' => [ 'shape' => 'GetPublicKeyConfigRequest', ], 'output' => [ 'shape' => 'GetPublicKeyConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchPublicKey', ], ], ], 'GetRealtimeLogConfig' => [ 'name' => 'GetRealtimeLogConfig2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/get-realtime-log-config/', ], 'input' => [ 'shape' => 'GetRealtimeLogConfigRequest', 'locationName' => 'GetRealtimeLogConfigRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'GetRealtimeLogConfigResult', ], 'errors' => [ [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetResponseHeadersPolicy' => [ 'name' => 'GetResponseHeadersPolicy2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/response-headers-policy/{Id}', ], 'input' => [ 'shape' => 'GetResponseHeadersPolicyRequest', ], 'output' => [ 'shape' => 'GetResponseHeadersPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], ], ], 'GetResponseHeadersPolicyConfig' => [ 'name' => 'GetResponseHeadersPolicyConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/response-headers-policy/{Id}/config', ], 'input' => [ 'shape' => 'GetResponseHeadersPolicyConfigRequest', ], 'output' => [ 'shape' => 'GetResponseHeadersPolicyConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], ], ], 'GetStreamingDistribution' => [ 'name' => 'GetStreamingDistribution2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/streaming-distribution/{Id}', ], 'input' => [ 'shape' => 'GetStreamingDistributionRequest', ], 'output' => [ 'shape' => 'GetStreamingDistributionResult', ], 'errors' => [ [ 'shape' => 'NoSuchStreamingDistribution', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetStreamingDistributionConfig' => [ 'name' => 'GetStreamingDistributionConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/streaming-distribution/{Id}/config', ], 'input' => [ 'shape' => 'GetStreamingDistributionConfigRequest', ], 'output' => [ 'shape' => 'GetStreamingDistributionConfigResult', ], 'errors' => [ [ 'shape' => 'NoSuchStreamingDistribution', ], [ 'shape' => 'AccessDenied', ], ], ], 'ListCachePolicies' => [ 'name' => 'ListCachePolicies2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/cache-policy', ], 'input' => [ 'shape' => 'ListCachePoliciesRequest', ], 'output' => [ 'shape' => 'ListCachePoliciesResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListCloudFrontOriginAccessIdentities' => [ 'name' => 'ListCloudFrontOriginAccessIdentities2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-access-identity/cloudfront', ], 'input' => [ 'shape' => 'ListCloudFrontOriginAccessIdentitiesRequest', ], 'output' => [ 'shape' => 'ListCloudFrontOriginAccessIdentitiesResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListConflictingAliases' => [ 'name' => 'ListConflictingAliases2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/conflicting-alias', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConflictingAliasesRequest', ], 'output' => [ 'shape' => 'ListConflictingAliasesResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchDistribution', ], ], ], 'ListContinuousDeploymentPolicies' => [ 'name' => 'ListContinuousDeploymentPolicies2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/continuous-deployment-policy', ], 'input' => [ 'shape' => 'ListContinuousDeploymentPoliciesRequest', ], 'output' => [ 'shape' => 'ListContinuousDeploymentPoliciesResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], ], ], 'ListDistributions' => [ 'name' => 'ListDistributions2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution', ], 'input' => [ 'shape' => 'ListDistributionsRequest', ], 'output' => [ 'shape' => 'ListDistributionsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributionsByCachePolicyId' => [ 'name' => 'ListDistributionsByCachePolicyId2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByCachePolicyId/{CachePolicyId}', ], 'input' => [ 'shape' => 'ListDistributionsByCachePolicyIdRequest', ], 'output' => [ 'shape' => 'ListDistributionsByCachePolicyIdResult', ], 'errors' => [ [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'AccessDenied', ], ], ], 'ListDistributionsByKeyGroup' => [ 'name' => 'ListDistributionsByKeyGroup2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByKeyGroupId/{KeyGroupId}', ], 'input' => [ 'shape' => 'ListDistributionsByKeyGroupRequest', ], 'output' => [ 'shape' => 'ListDistributionsByKeyGroupResult', ], 'errors' => [ [ 'shape' => 'NoSuchResource', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributionsByOriginRequestPolicyId' => [ 'name' => 'ListDistributionsByOriginRequestPolicyId2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByOriginRequestPolicyId/{OriginRequestPolicyId}', ], 'input' => [ 'shape' => 'ListDistributionsByOriginRequestPolicyIdRequest', ], 'output' => [ 'shape' => 'ListDistributionsByOriginRequestPolicyIdResult', ], 'errors' => [ [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'AccessDenied', ], ], ], 'ListDistributionsByRealtimeLogConfig' => [ 'name' => 'ListDistributionsByRealtimeLogConfig2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distributionsByRealtimeLogConfig/', ], 'input' => [ 'shape' => 'ListDistributionsByRealtimeLogConfigRequest', 'locationName' => 'ListDistributionsByRealtimeLogConfigRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'ListDistributionsByRealtimeLogConfigResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributionsByResponseHeadersPolicyId' => [ 'name' => 'ListDistributionsByResponseHeadersPolicyId2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByResponseHeadersPolicyId/{ResponseHeadersPolicyId}', ], 'input' => [ 'shape' => 'ListDistributionsByResponseHeadersPolicyIdRequest', ], 'output' => [ 'shape' => 'ListDistributionsByResponseHeadersPolicyIdResult', ], 'errors' => [ [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'AccessDenied', ], ], ], 'ListDistributionsByWebACLId' => [ 'name' => 'ListDistributionsByWebACLId2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByWebACLId/{WebACLId}', ], 'input' => [ 'shape' => 'ListDistributionsByWebACLIdRequest', ], 'output' => [ 'shape' => 'ListDistributionsByWebACLIdResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidWebACLId', ], ], ], 'ListFieldLevelEncryptionConfigs' => [ 'name' => 'ListFieldLevelEncryptionConfigs2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/field-level-encryption', ], 'input' => [ 'shape' => 'ListFieldLevelEncryptionConfigsRequest', ], 'output' => [ 'shape' => 'ListFieldLevelEncryptionConfigsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListFieldLevelEncryptionProfiles' => [ 'name' => 'ListFieldLevelEncryptionProfiles2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/field-level-encryption-profile', ], 'input' => [ 'shape' => 'ListFieldLevelEncryptionProfilesRequest', ], 'output' => [ 'shape' => 'ListFieldLevelEncryptionProfilesResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListFunctions' => [ 'name' => 'ListFunctions2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/function', ], 'input' => [ 'shape' => 'ListFunctionsRequest', ], 'output' => [ 'shape' => 'ListFunctionsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'ListInvalidations' => [ 'name' => 'ListInvalidations2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution/{DistributionId}/invalidation', ], 'input' => [ 'shape' => 'ListInvalidationsRequest', ], 'output' => [ 'shape' => 'ListInvalidationsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], ], ], 'ListKeyGroups' => [ 'name' => 'ListKeyGroups2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/key-group', ], 'input' => [ 'shape' => 'ListKeyGroupsRequest', ], 'output' => [ 'shape' => 'ListKeyGroupsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListKeyValueStores' => [ 'name' => 'ListKeyValueStores2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/key-value-store', ], 'input' => [ 'shape' => 'ListKeyValueStoresRequest', ], 'output' => [ 'shape' => 'ListKeyValueStoresResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListOriginAccessControls' => [ 'name' => 'ListOriginAccessControls2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-access-control', ], 'input' => [ 'shape' => 'ListOriginAccessControlsRequest', ], 'output' => [ 'shape' => 'ListOriginAccessControlsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListOriginRequestPolicies' => [ 'name' => 'ListOriginRequestPolicies2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-request-policy', ], 'input' => [ 'shape' => 'ListOriginRequestPoliciesRequest', ], 'output' => [ 'shape' => 'ListOriginRequestPoliciesResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListPublicKeys' => [ 'name' => 'ListPublicKeys2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/public-key', ], 'input' => [ 'shape' => 'ListPublicKeysRequest', ], 'output' => [ 'shape' => 'ListPublicKeysResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListRealtimeLogConfigs' => [ 'name' => 'ListRealtimeLogConfigs2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/realtime-log-config', ], 'input' => [ 'shape' => 'ListRealtimeLogConfigsRequest', ], 'output' => [ 'shape' => 'ListRealtimeLogConfigsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], ], ], 'ListResponseHeadersPolicies' => [ 'name' => 'ListResponseHeadersPolicies2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/response-headers-policy', ], 'input' => [ 'shape' => 'ListResponseHeadersPoliciesRequest', ], 'output' => [ 'shape' => 'ListResponseHeadersPoliciesResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListStreamingDistributions' => [ 'name' => 'ListStreamingDistributions2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/streaming-distribution', ], 'input' => [ 'shape' => 'ListStreamingDistributionsRequest', ], 'output' => [ 'shape' => 'ListStreamingDistributionsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/tagging', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'NoSuchResource', ], ], ], 'PublishFunction' => [ 'name' => 'PublishFunction2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/function/{Name}/publish', ], 'input' => [ 'shape' => 'PublishFunctionRequest', ], 'output' => [ 'shape' => 'PublishFunctionResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchFunctionExists', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'TagResource' => [ 'name' => 'TagResource2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/tagging?Operation=Tag', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'NoSuchResource', ], ], ], 'TestFunction' => [ 'name' => 'TestFunction2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/function/{Name}/test', ], 'input' => [ 'shape' => 'TestFunctionRequest', 'locationName' => 'TestFunctionRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'TestFunctionResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchFunctionExists', ], [ 'shape' => 'TestFunctionFailed', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/tagging?Operation=Untag', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'NoSuchResource', ], ], ], 'UpdateCachePolicy' => [ 'name' => 'UpdateCachePolicy2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/cache-policy/{Id}', ], 'input' => [ 'shape' => 'UpdateCachePolicyRequest', ], 'output' => [ 'shape' => 'UpdateCachePolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'CachePolicyAlreadyExists', ], [ 'shape' => 'TooManyHeadersInCachePolicy', ], [ 'shape' => 'TooManyCookiesInCachePolicy', ], [ 'shape' => 'TooManyQueryStringsInCachePolicy', ], ], ], 'UpdateCloudFrontOriginAccessIdentity' => [ 'name' => 'UpdateCloudFrontOriginAccessIdentity2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/origin-access-identity/cloudfront/{Id}/config', ], 'input' => [ 'shape' => 'UpdateCloudFrontOriginAccessIdentityRequest', ], 'output' => [ 'shape' => 'UpdateCloudFrontOriginAccessIdentityResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'NoSuchCloudFrontOriginAccessIdentity', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InconsistentQuantities', ], ], ], 'UpdateContinuousDeploymentPolicy' => [ 'name' => 'UpdateContinuousDeploymentPolicy2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/continuous-deployment-policy/{Id}', ], 'input' => [ 'shape' => 'UpdateContinuousDeploymentPolicyRequest', ], 'output' => [ 'shape' => 'UpdateContinuousDeploymentPolicyResult', ], 'errors' => [ [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'StagingDistributionInUse', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], ], ], 'UpdateDistribution' => [ 'name' => 'UpdateDistribution2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/distribution/{Id}/config', ], 'input' => [ 'shape' => 'UpdateDistributionRequest', ], 'output' => [ 'shape' => 'UpdateDistributionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'TooManyDistributionCNAMEs', ], [ 'shape' => 'InvalidDefaultRootObject', ], [ 'shape' => 'InvalidRelativePath', ], [ 'shape' => 'InvalidErrorCode', ], [ 'shape' => 'InvalidResponseCode', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'InvalidViewerCertificate', ], [ 'shape' => 'InvalidMinimumProtocolVersion', ], [ 'shape' => 'InvalidRequiredProtocol', ], [ 'shape' => 'NoSuchOrigin', ], [ 'shape' => 'TooManyOrigins', ], [ 'shape' => 'TooManyOriginGroupsPerDistribution', ], [ 'shape' => 'TooManyCacheBehaviors', ], [ 'shape' => 'TooManyCookieNamesInWhiteList', ], [ 'shape' => 'InvalidForwardCookies', ], [ 'shape' => 'TooManyHeadersInForwardedValues', ], [ 'shape' => 'InvalidHeadersForS3Origin', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'TooManyCertificates', ], [ 'shape' => 'InvalidLocationCode', ], [ 'shape' => 'InvalidGeoRestrictionParameter', ], [ 'shape' => 'InvalidTTLOrder', ], [ 'shape' => 'InvalidWebACLId', ], [ 'shape' => 'TooManyOriginCustomHeaders', ], [ 'shape' => 'TooManyQueryStringParameters', ], [ 'shape' => 'InvalidQueryStringParameters', ], [ 'shape' => 'TooManyDistributionsWithLambdaAssociations', ], [ 'shape' => 'TooManyDistributionsWithSingleFunctionARN', ], [ 'shape' => 'TooManyLambdaFunctionAssociations', ], [ 'shape' => 'InvalidLambdaFunctionAssociation', ], [ 'shape' => 'TooManyDistributionsWithFunctionAssociations', ], [ 'shape' => 'TooManyFunctionAssociations', ], [ 'shape' => 'InvalidFunctionAssociation', ], [ 'shape' => 'InvalidOriginReadTimeout', ], [ 'shape' => 'InvalidOriginKeepaliveTimeout', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'IllegalFieldLevelEncryptionConfigAssociationWithCacheBehavior', ], [ 'shape' => 'TooManyDistributionsAssociatedToFieldLevelEncryptionConfig', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToCachePolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginAccessControl', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToResponseHeadersPolicy', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToKeyGroup', ], [ 'shape' => 'TooManyKeyGroupsAssociatedToDistribution', ], [ 'shape' => 'TrustedKeyGroupDoesNotExist', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'RealtimeLogConfigOwnerMismatch', ], [ 'shape' => 'ContinuousDeploymentPolicyInUse', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], [ 'shape' => 'StagingDistributionInUse', ], [ 'shape' => 'IllegalOriginAccessConfiguration', ], [ 'shape' => 'InvalidDomainNameForOriginAccessControl', ], ], ], 'UpdateDistributionWithStagingConfig' => [ 'name' => 'UpdateDistributionWithStagingConfig2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/distribution/{Id}/promote-staging-config', ], 'input' => [ 'shape' => 'UpdateDistributionWithStagingConfigRequest', ], 'output' => [ 'shape' => 'UpdateDistributionWithStagingConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'TooManyDistributionCNAMEs', ], [ 'shape' => 'InvalidDefaultRootObject', ], [ 'shape' => 'InvalidRelativePath', ], [ 'shape' => 'InvalidErrorCode', ], [ 'shape' => 'InvalidResponseCode', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'InvalidViewerCertificate', ], [ 'shape' => 'InvalidMinimumProtocolVersion', ], [ 'shape' => 'InvalidRequiredProtocol', ], [ 'shape' => 'NoSuchOrigin', ], [ 'shape' => 'TooManyOrigins', ], [ 'shape' => 'TooManyOriginGroupsPerDistribution', ], [ 'shape' => 'TooManyCacheBehaviors', ], [ 'shape' => 'TooManyCookieNamesInWhiteList', ], [ 'shape' => 'InvalidForwardCookies', ], [ 'shape' => 'TooManyHeadersInForwardedValues', ], [ 'shape' => 'InvalidHeadersForS3Origin', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'TooManyCertificates', ], [ 'shape' => 'InvalidLocationCode', ], [ 'shape' => 'InvalidGeoRestrictionParameter', ], [ 'shape' => 'InvalidTTLOrder', ], [ 'shape' => 'InvalidWebACLId', ], [ 'shape' => 'TooManyOriginCustomHeaders', ], [ 'shape' => 'TooManyQueryStringParameters', ], [ 'shape' => 'InvalidQueryStringParameters', ], [ 'shape' => 'TooManyDistributionsWithLambdaAssociations', ], [ 'shape' => 'TooManyDistributionsWithSingleFunctionARN', ], [ 'shape' => 'TooManyLambdaFunctionAssociations', ], [ 'shape' => 'InvalidLambdaFunctionAssociation', ], [ 'shape' => 'TooManyDistributionsWithFunctionAssociations', ], [ 'shape' => 'TooManyFunctionAssociations', ], [ 'shape' => 'InvalidFunctionAssociation', ], [ 'shape' => 'InvalidOriginReadTimeout', ], [ 'shape' => 'InvalidOriginKeepaliveTimeout', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'IllegalFieldLevelEncryptionConfigAssociationWithCacheBehavior', ], [ 'shape' => 'TooManyDistributionsAssociatedToFieldLevelEncryptionConfig', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToCachePolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginAccessControl', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToResponseHeadersPolicy', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToKeyGroup', ], [ 'shape' => 'TooManyKeyGroupsAssociatedToDistribution', ], [ 'shape' => 'TrustedKeyGroupDoesNotExist', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'RealtimeLogConfigOwnerMismatch', ], ], ], 'UpdateFieldLevelEncryptionConfig' => [ 'name' => 'UpdateFieldLevelEncryptionConfig2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/field-level-encryption/{Id}/config', ], 'input' => [ 'shape' => 'UpdateFieldLevelEncryptionConfigRequest', ], 'output' => [ 'shape' => 'UpdateFieldLevelEncryptionConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchFieldLevelEncryptionProfile', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'TooManyFieldLevelEncryptionQueryArgProfiles', ], [ 'shape' => 'TooManyFieldLevelEncryptionContentTypeProfiles', ], [ 'shape' => 'QueryArgProfileEmpty', ], ], ], 'UpdateFieldLevelEncryptionProfile' => [ 'name' => 'UpdateFieldLevelEncryptionProfile2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/field-level-encryption-profile/{Id}/config', ], 'input' => [ 'shape' => 'UpdateFieldLevelEncryptionProfileRequest', ], 'output' => [ 'shape' => 'UpdateFieldLevelEncryptionProfileResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'FieldLevelEncryptionProfileAlreadyExists', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchPublicKey', ], [ 'shape' => 'NoSuchFieldLevelEncryptionProfile', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'FieldLevelEncryptionProfileSizeExceeded', ], [ 'shape' => 'TooManyFieldLevelEncryptionEncryptionEntities', ], [ 'shape' => 'TooManyFieldLevelEncryptionFieldPatterns', ], ], ], 'UpdateFunction' => [ 'name' => 'UpdateFunction2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/function/{Name}', ], 'input' => [ 'shape' => 'UpdateFunctionRequest', 'locationName' => 'UpdateFunctionRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'UpdateFunctionResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchFunctionExists', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'FunctionSizeLimitExceeded', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'UpdateKeyGroup' => [ 'name' => 'UpdateKeyGroup2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/key-group/{Id}', ], 'input' => [ 'shape' => 'UpdateKeyGroupRequest', ], 'output' => [ 'shape' => 'UpdateKeyGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchResource', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'KeyGroupAlreadyExists', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'TooManyPublicKeysInKeyGroup', ], ], ], 'UpdateKeyValueStore' => [ 'name' => 'UpdateKeyValueStore2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/key-value-store/{Name}', ], 'input' => [ 'shape' => 'UpdateKeyValueStoreRequest', 'locationName' => 'UpdateKeyValueStoreRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'UpdateKeyValueStoreResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'PreconditionFailed', ], ], 'idempotent' => true, ], 'UpdateOriginAccessControl' => [ 'name' => 'UpdateOriginAccessControl2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/origin-access-control/{Id}/config', ], 'input' => [ 'shape' => 'UpdateOriginAccessControlRequest', ], 'output' => [ 'shape' => 'UpdateOriginAccessControlResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'OriginAccessControlAlreadyExists', ], [ 'shape' => 'NoSuchOriginAccessControl', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'InvalidArgument', ], ], ], 'UpdateOriginRequestPolicy' => [ 'name' => 'UpdateOriginRequestPolicy2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/origin-request-policy/{Id}', ], 'input' => [ 'shape' => 'UpdateOriginRequestPolicyRequest', ], 'output' => [ 'shape' => 'UpdateOriginRequestPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'OriginRequestPolicyAlreadyExists', ], [ 'shape' => 'TooManyHeadersInOriginRequestPolicy', ], [ 'shape' => 'TooManyCookiesInOriginRequestPolicy', ], [ 'shape' => 'TooManyQueryStringsInOriginRequestPolicy', ], ], ], 'UpdatePublicKey' => [ 'name' => 'UpdatePublicKey2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/public-key/{Id}/config', ], 'input' => [ 'shape' => 'UpdatePublicKeyRequest', ], 'output' => [ 'shape' => 'UpdatePublicKeyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'CannotChangeImmutablePublicKeyFields', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'NoSuchPublicKey', ], [ 'shape' => 'PreconditionFailed', ], ], ], 'UpdateRealtimeLogConfig' => [ 'name' => 'UpdateRealtimeLogConfig2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/realtime-log-config/', ], 'input' => [ 'shape' => 'UpdateRealtimeLogConfigRequest', 'locationName' => 'UpdateRealtimeLogConfigRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'UpdateRealtimeLogConfigResult', ], 'errors' => [ [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'AccessDenied', ], ], ], 'UpdateResponseHeadersPolicy' => [ 'name' => 'UpdateResponseHeadersPolicy2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/response-headers-policy/{Id}', ], 'input' => [ 'shape' => 'UpdateResponseHeadersPolicyRequest', ], 'output' => [ 'shape' => 'UpdateResponseHeadersPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'ResponseHeadersPolicyAlreadyExists', ], [ 'shape' => 'TooManyCustomHeadersInResponseHeadersPolicy', ], [ 'shape' => 'TooLongCSPInResponseHeadersPolicy', ], [ 'shape' => 'TooManyRemoveHeadersInResponseHeadersPolicy', ], ], ], 'UpdateStreamingDistribution' => [ 'name' => 'UpdateStreamingDistribution2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/streaming-distribution/{Id}/config', ], 'input' => [ 'shape' => 'UpdateStreamingDistributionRequest', ], 'output' => [ 'shape' => 'UpdateStreamingDistributionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'NoSuchStreamingDistribution', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'TooManyStreamingDistributionCNAMEs', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'InconsistentQuantities', ], ], ], ], 'shapes' => [ 'AccessControlAllowHeadersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Header', ], ], 'AccessControlAllowMethodsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseHeadersPolicyAccessControlAllowMethodsValues', 'locationName' => 'Method', ], ], 'AccessControlAllowOriginsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Origin', ], ], 'AccessControlExposeHeadersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Header', ], ], 'AccessDenied' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'ActiveTrustedKeyGroups' => [ 'type' => 'structure', 'required' => [ 'Enabled', 'Quantity', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'KGKeyPairIdsList', ], ], ], 'ActiveTrustedSigners' => [ 'type' => 'structure', 'required' => [ 'Enabled', 'Quantity', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'SignerList', ], ], ], 'AliasICPRecordal' => [ 'type' => 'structure', 'members' => [ 'CNAME' => [ 'shape' => 'string', ], 'ICPRecordalStatus' => [ 'shape' => 'ICPRecordalStatus', ], ], ], 'AliasICPRecordals' => [ 'type' => 'list', 'member' => [ 'shape' => 'AliasICPRecordal', 'locationName' => 'AliasICPRecordal', ], ], 'AliasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'CNAME', ], ], 'Aliases' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'AliasList', ], ], ], 'AllowedMethods' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'MethodsList', ], 'CachedMethods' => [ 'shape' => 'CachedMethods', ], ], ], 'AssociateAliasRequest' => [ 'type' => 'structure', 'required' => [ 'TargetDistributionId', 'Alias', ], 'members' => [ 'TargetDistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'TargetDistributionId', ], 'Alias' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Alias', ], ], ], 'AwsAccountNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'AwsAccountNumber', ], ], 'BatchTooLarge' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 413, ], 'exception' => true, ], 'CNAMEAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CacheBehavior' => [ 'type' => 'structure', 'required' => [ 'PathPattern', 'TargetOriginId', 'ViewerProtocolPolicy', ], 'members' => [ 'PathPattern' => [ 'shape' => 'string', ], 'TargetOriginId' => [ 'shape' => 'string', ], 'TrustedSigners' => [ 'shape' => 'TrustedSigners', ], 'TrustedKeyGroups' => [ 'shape' => 'TrustedKeyGroups', ], 'ViewerProtocolPolicy' => [ 'shape' => 'ViewerProtocolPolicy', ], 'AllowedMethods' => [ 'shape' => 'AllowedMethods', ], 'SmoothStreaming' => [ 'shape' => 'boolean', ], 'Compress' => [ 'shape' => 'boolean', ], 'LambdaFunctionAssociations' => [ 'shape' => 'LambdaFunctionAssociations', ], 'FunctionAssociations' => [ 'shape' => 'FunctionAssociations', ], 'FieldLevelEncryptionId' => [ 'shape' => 'string', ], 'RealtimeLogConfigArn' => [ 'shape' => 'string', ], 'CachePolicyId' => [ 'shape' => 'string', ], 'OriginRequestPolicyId' => [ 'shape' => 'string', ], 'ResponseHeadersPolicyId' => [ 'shape' => 'string', ], 'ForwardedValues' => [ 'shape' => 'ForwardedValues', 'deprecated' => true, ], 'MinTTL' => [ 'shape' => 'long', 'deprecated' => true, ], 'DefaultTTL' => [ 'shape' => 'long', 'deprecated' => true, ], 'MaxTTL' => [ 'shape' => 'long', 'deprecated' => true, ], ], ], 'CacheBehaviorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheBehavior', 'locationName' => 'CacheBehavior', ], ], 'CacheBehaviors' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'CacheBehaviorList', ], ], ], 'CachePolicy' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'CachePolicyConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'CachePolicyConfig' => [ 'shape' => 'CachePolicyConfig', ], ], ], 'CachePolicyAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CachePolicyConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'MinTTL', ], 'members' => [ 'Comment' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'DefaultTTL' => [ 'shape' => 'long', ], 'MaxTTL' => [ 'shape' => 'long', ], 'MinTTL' => [ 'shape' => 'long', ], 'ParametersInCacheKeyAndForwardedToOrigin' => [ 'shape' => 'ParametersInCacheKeyAndForwardedToOrigin', ], ], ], 'CachePolicyCookieBehavior' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', 'allExcept', 'all', ], ], 'CachePolicyCookiesConfig' => [ 'type' => 'structure', 'required' => [ 'CookieBehavior', ], 'members' => [ 'CookieBehavior' => [ 'shape' => 'CachePolicyCookieBehavior', ], 'Cookies' => [ 'shape' => 'CookieNames', ], ], ], 'CachePolicyHeaderBehavior' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', ], ], 'CachePolicyHeadersConfig' => [ 'type' => 'structure', 'required' => [ 'HeaderBehavior', ], 'members' => [ 'HeaderBehavior' => [ 'shape' => 'CachePolicyHeaderBehavior', ], 'Headers' => [ 'shape' => 'Headers', ], ], ], 'CachePolicyInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CachePolicyList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'CachePolicySummaryList', ], ], ], 'CachePolicyQueryStringBehavior' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', 'allExcept', 'all', ], ], 'CachePolicyQueryStringsConfig' => [ 'type' => 'structure', 'required' => [ 'QueryStringBehavior', ], 'members' => [ 'QueryStringBehavior' => [ 'shape' => 'CachePolicyQueryStringBehavior', ], 'QueryStrings' => [ 'shape' => 'QueryStringNames', ], ], ], 'CachePolicySummary' => [ 'type' => 'structure', 'required' => [ 'Type', 'CachePolicy', ], 'members' => [ 'Type' => [ 'shape' => 'CachePolicyType', ], 'CachePolicy' => [ 'shape' => 'CachePolicy', ], ], ], 'CachePolicySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CachePolicySummary', 'locationName' => 'CachePolicySummary', ], ], 'CachePolicyType' => [ 'type' => 'string', 'enum' => [ 'managed', 'custom', ], ], 'CachedMethods' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'MethodsList', ], ], ], 'CannotChangeImmutablePublicKeyFields' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'CannotDeleteEntityWhileInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CertificateSource' => [ 'type' => 'string', 'enum' => [ 'cloudfront', 'iam', 'acm', ], ], 'CloudFrontOriginAccessIdentity' => [ 'type' => 'structure', 'required' => [ 'Id', 'S3CanonicalUserId', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'S3CanonicalUserId' => [ 'shape' => 'string', ], 'CloudFrontOriginAccessIdentityConfig' => [ 'shape' => 'CloudFrontOriginAccessIdentityConfig', ], ], ], 'CloudFrontOriginAccessIdentityAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CloudFrontOriginAccessIdentityConfig' => [ 'type' => 'structure', 'required' => [ 'CallerReference', 'Comment', ], 'members' => [ 'CallerReference' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], ], ], 'CloudFrontOriginAccessIdentityInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CloudFrontOriginAccessIdentityList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'CloudFrontOriginAccessIdentitySummaryList', ], ], ], 'CloudFrontOriginAccessIdentitySummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'S3CanonicalUserId', 'Comment', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'S3CanonicalUserId' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], ], ], 'CloudFrontOriginAccessIdentitySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CloudFrontOriginAccessIdentitySummary', 'locationName' => 'CloudFrontOriginAccessIdentitySummary', ], ], 'CommentType' => [ 'type' => 'string', 'sensitive' => true, ], 'ConflictingAlias' => [ 'type' => 'structure', 'members' => [ 'Alias' => [ 'shape' => 'string', ], 'DistributionId' => [ 'shape' => 'string', ], 'AccountId' => [ 'shape' => 'string', ], ], ], 'ConflictingAliases' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConflictingAlias', 'locationName' => 'ConflictingAlias', ], ], 'ConflictingAliasesList' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'ConflictingAliases', ], ], ], 'ContentTypeProfile' => [ 'type' => 'structure', 'required' => [ 'Format', 'ContentType', ], 'members' => [ 'Format' => [ 'shape' => 'Format', ], 'ProfileId' => [ 'shape' => 'string', ], 'ContentType' => [ 'shape' => 'string', ], ], ], 'ContentTypeProfileConfig' => [ 'type' => 'structure', 'required' => [ 'ForwardWhenContentTypeIsUnknown', ], 'members' => [ 'ForwardWhenContentTypeIsUnknown' => [ 'shape' => 'boolean', ], 'ContentTypeProfiles' => [ 'shape' => 'ContentTypeProfiles', ], ], ], 'ContentTypeProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentTypeProfile', 'locationName' => 'ContentTypeProfile', ], ], 'ContentTypeProfiles' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'ContentTypeProfileList', ], ], ], 'ContinuousDeploymentPolicy' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'ContinuousDeploymentPolicyConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'ContinuousDeploymentPolicyConfig' => [ 'shape' => 'ContinuousDeploymentPolicyConfig', ], ], ], 'ContinuousDeploymentPolicyAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ContinuousDeploymentPolicyConfig' => [ 'type' => 'structure', 'required' => [ 'StagingDistributionDnsNames', 'Enabled', ], 'members' => [ 'StagingDistributionDnsNames' => [ 'shape' => 'StagingDistributionDnsNames', ], 'Enabled' => [ 'shape' => 'boolean', ], 'TrafficConfig' => [ 'shape' => 'TrafficConfig', ], ], ], 'ContinuousDeploymentPolicyInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ContinuousDeploymentPolicyList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'ContinuousDeploymentPolicySummaryList', ], ], ], 'ContinuousDeploymentPolicySummary' => [ 'type' => 'structure', 'required' => [ 'ContinuousDeploymentPolicy', ], 'members' => [ 'ContinuousDeploymentPolicy' => [ 'shape' => 'ContinuousDeploymentPolicy', ], ], ], 'ContinuousDeploymentPolicySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContinuousDeploymentPolicySummary', 'locationName' => 'ContinuousDeploymentPolicySummary', ], ], 'ContinuousDeploymentPolicyType' => [ 'type' => 'string', 'enum' => [ 'SingleWeight', 'SingleHeader', ], ], 'ContinuousDeploymentSingleHeaderConfig' => [ 'type' => 'structure', 'required' => [ 'Header', 'Value', ], 'members' => [ 'Header' => [ 'shape' => 'string', ], 'Value' => [ 'shape' => 'string', ], ], ], 'ContinuousDeploymentSingleWeightConfig' => [ 'type' => 'structure', 'required' => [ 'Weight', ], 'members' => [ 'Weight' => [ 'shape' => 'float', ], 'SessionStickinessConfig' => [ 'shape' => 'SessionStickinessConfig', ], ], ], 'CookieNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Name', ], ], 'CookieNames' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'CookieNameList', ], ], ], 'CookiePreference' => [ 'type' => 'structure', 'required' => [ 'Forward', ], 'members' => [ 'Forward' => [ 'shape' => 'ItemSelection', ], 'WhitelistedNames' => [ 'shape' => 'CookieNames', ], ], ], 'CopyDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'PrimaryDistributionId', 'CallerReference', ], 'members' => [ 'PrimaryDistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'PrimaryDistributionId', ], 'Staging' => [ 'shape' => 'boolean', 'location' => 'header', 'locationName' => 'Staging', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], 'CallerReference' => [ 'shape' => 'string', ], 'Enabled' => [ 'shape' => 'boolean', ], ], ], 'CopyDistributionResult' => [ 'type' => 'structure', 'members' => [ 'Distribution' => [ 'shape' => 'Distribution', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'Distribution', ], 'CreateCachePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'CachePolicyConfig', ], 'members' => [ 'CachePolicyConfig' => [ 'shape' => 'CachePolicyConfig', 'locationName' => 'CachePolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'CachePolicyConfig', ], 'CreateCachePolicyResult' => [ 'type' => 'structure', 'members' => [ 'CachePolicy' => [ 'shape' => 'CachePolicy', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CachePolicy', ], 'CreateCloudFrontOriginAccessIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'CloudFrontOriginAccessIdentityConfig', ], 'members' => [ 'CloudFrontOriginAccessIdentityConfig' => [ 'shape' => 'CloudFrontOriginAccessIdentityConfig', 'locationName' => 'CloudFrontOriginAccessIdentityConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'CloudFrontOriginAccessIdentityConfig', ], 'CreateCloudFrontOriginAccessIdentityResult' => [ 'type' => 'structure', 'members' => [ 'CloudFrontOriginAccessIdentity' => [ 'shape' => 'CloudFrontOriginAccessIdentity', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CloudFrontOriginAccessIdentity', ], 'CreateContinuousDeploymentPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ContinuousDeploymentPolicyConfig', ], 'members' => [ 'ContinuousDeploymentPolicyConfig' => [ 'shape' => 'ContinuousDeploymentPolicyConfig', 'locationName' => 'ContinuousDeploymentPolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'ContinuousDeploymentPolicyConfig', ], 'CreateContinuousDeploymentPolicyResult' => [ 'type' => 'structure', 'members' => [ 'ContinuousDeploymentPolicy' => [ 'shape' => 'ContinuousDeploymentPolicy', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ContinuousDeploymentPolicy', ], 'CreateDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionConfig', ], 'members' => [ 'DistributionConfig' => [ 'shape' => 'DistributionConfig', 'locationName' => 'DistributionConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'DistributionConfig', ], 'CreateDistributionResult' => [ 'type' => 'structure', 'members' => [ 'Distribution' => [ 'shape' => 'Distribution', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'Distribution', ], 'CreateDistributionWithTagsRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionConfigWithTags', ], 'members' => [ 'DistributionConfigWithTags' => [ 'shape' => 'DistributionConfigWithTags', 'locationName' => 'DistributionConfigWithTags', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'DistributionConfigWithTags', ], 'CreateDistributionWithTagsResult' => [ 'type' => 'structure', 'members' => [ 'Distribution' => [ 'shape' => 'Distribution', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'Distribution', ], 'CreateFieldLevelEncryptionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FieldLevelEncryptionConfig', ], 'members' => [ 'FieldLevelEncryptionConfig' => [ 'shape' => 'FieldLevelEncryptionConfig', 'locationName' => 'FieldLevelEncryptionConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'FieldLevelEncryptionConfig', ], 'CreateFieldLevelEncryptionConfigResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryption' => [ 'shape' => 'FieldLevelEncryption', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryption', ], 'CreateFieldLevelEncryptionProfileRequest' => [ 'type' => 'structure', 'required' => [ 'FieldLevelEncryptionProfileConfig', ], 'members' => [ 'FieldLevelEncryptionProfileConfig' => [ 'shape' => 'FieldLevelEncryptionProfileConfig', 'locationName' => 'FieldLevelEncryptionProfileConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'FieldLevelEncryptionProfileConfig', ], 'CreateFieldLevelEncryptionProfileResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionProfile' => [ 'shape' => 'FieldLevelEncryptionProfile', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryptionProfile', ], 'CreateFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'FunctionConfig', 'FunctionCode', ], 'members' => [ 'Name' => [ 'shape' => 'FunctionName', ], 'FunctionConfig' => [ 'shape' => 'FunctionConfig', ], 'FunctionCode' => [ 'shape' => 'FunctionBlob', ], ], ], 'CreateFunctionResult' => [ 'type' => 'structure', 'members' => [ 'FunctionSummary' => [ 'shape' => 'FunctionSummary', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FunctionSummary', ], 'CreateInvalidationRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', 'InvalidationBatch', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionId', ], 'InvalidationBatch' => [ 'shape' => 'InvalidationBatch', 'locationName' => 'InvalidationBatch', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'InvalidationBatch', ], 'CreateInvalidationResult' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'Invalidation' => [ 'shape' => 'Invalidation', ], ], 'payload' => 'Invalidation', ], 'CreateKeyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'KeyGroupConfig', ], 'members' => [ 'KeyGroupConfig' => [ 'shape' => 'KeyGroupConfig', 'locationName' => 'KeyGroupConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'KeyGroupConfig', ], 'CreateKeyGroupResult' => [ 'type' => 'structure', 'members' => [ 'KeyGroup' => [ 'shape' => 'KeyGroup', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'KeyGroup', ], 'CreateKeyValueStoreRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'KeyValueStoreName', ], 'Comment' => [ 'shape' => 'KeyValueStoreComment', ], 'ImportSource' => [ 'shape' => 'ImportSource', ], ], ], 'CreateKeyValueStoreResult' => [ 'type' => 'structure', 'members' => [ 'KeyValueStore' => [ 'shape' => 'KeyValueStore', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], ], 'payload' => 'KeyValueStore', ], 'CreateMonitoringSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'MonitoringSubscription', 'DistributionId', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionId', ], 'MonitoringSubscription' => [ 'shape' => 'MonitoringSubscription', 'locationName' => 'MonitoringSubscription', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'MonitoringSubscription', ], 'CreateMonitoringSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'MonitoringSubscription' => [ 'shape' => 'MonitoringSubscription', ], ], 'payload' => 'MonitoringSubscription', ], 'CreateOriginAccessControlRequest' => [ 'type' => 'structure', 'required' => [ 'OriginAccessControlConfig', ], 'members' => [ 'OriginAccessControlConfig' => [ 'shape' => 'OriginAccessControlConfig', 'locationName' => 'OriginAccessControlConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'OriginAccessControlConfig', ], 'CreateOriginAccessControlResult' => [ 'type' => 'structure', 'members' => [ 'OriginAccessControl' => [ 'shape' => 'OriginAccessControl', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginAccessControl', ], 'CreateOriginRequestPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'OriginRequestPolicyConfig', ], 'members' => [ 'OriginRequestPolicyConfig' => [ 'shape' => 'OriginRequestPolicyConfig', 'locationName' => 'OriginRequestPolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'OriginRequestPolicyConfig', ], 'CreateOriginRequestPolicyResult' => [ 'type' => 'structure', 'members' => [ 'OriginRequestPolicy' => [ 'shape' => 'OriginRequestPolicy', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginRequestPolicy', ], 'CreatePublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'PublicKeyConfig', ], 'members' => [ 'PublicKeyConfig' => [ 'shape' => 'PublicKeyConfig', 'locationName' => 'PublicKeyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'PublicKeyConfig', ], 'CreatePublicKeyResult' => [ 'type' => 'structure', 'members' => [ 'PublicKey' => [ 'shape' => 'PublicKey', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'PublicKey', ], 'CreateRealtimeLogConfigRequest' => [ 'type' => 'structure', 'required' => [ 'EndPoints', 'Fields', 'Name', 'SamplingRate', ], 'members' => [ 'EndPoints' => [ 'shape' => 'EndPointList', ], 'Fields' => [ 'shape' => 'FieldList', ], 'Name' => [ 'shape' => 'string', ], 'SamplingRate' => [ 'shape' => 'long', ], ], ], 'CreateRealtimeLogConfigResult' => [ 'type' => 'structure', 'members' => [ 'RealtimeLogConfig' => [ 'shape' => 'RealtimeLogConfig', ], ], ], 'CreateResponseHeadersPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResponseHeadersPolicyConfig', ], 'members' => [ 'ResponseHeadersPolicyConfig' => [ 'shape' => 'ResponseHeadersPolicyConfig', 'locationName' => 'ResponseHeadersPolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'ResponseHeadersPolicyConfig', ], 'CreateResponseHeadersPolicyResult' => [ 'type' => 'structure', 'members' => [ 'ResponseHeadersPolicy' => [ 'shape' => 'ResponseHeadersPolicy', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ResponseHeadersPolicy', ], 'CreateStreamingDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'StreamingDistributionConfig', ], 'members' => [ 'StreamingDistributionConfig' => [ 'shape' => 'StreamingDistributionConfig', 'locationName' => 'StreamingDistributionConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'StreamingDistributionConfig', ], 'CreateStreamingDistributionResult' => [ 'type' => 'structure', 'members' => [ 'StreamingDistribution' => [ 'shape' => 'StreamingDistribution', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'StreamingDistribution', ], 'CreateStreamingDistributionWithTagsRequest' => [ 'type' => 'structure', 'required' => [ 'StreamingDistributionConfigWithTags', ], 'members' => [ 'StreamingDistributionConfigWithTags' => [ 'shape' => 'StreamingDistributionConfigWithTags', 'locationName' => 'StreamingDistributionConfigWithTags', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'StreamingDistributionConfigWithTags', ], 'CreateStreamingDistributionWithTagsResult' => [ 'type' => 'structure', 'members' => [ 'StreamingDistribution' => [ 'shape' => 'StreamingDistribution', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'StreamingDistribution', ], 'CustomErrorResponse' => [ 'type' => 'structure', 'required' => [ 'ErrorCode', ], 'members' => [ 'ErrorCode' => [ 'shape' => 'integer', ], 'ResponsePagePath' => [ 'shape' => 'string', ], 'ResponseCode' => [ 'shape' => 'string', ], 'ErrorCachingMinTTL' => [ 'shape' => 'long', ], ], ], 'CustomErrorResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomErrorResponse', 'locationName' => 'CustomErrorResponse', ], ], 'CustomErrorResponses' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'CustomErrorResponseList', ], ], ], 'CustomHeaders' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'OriginCustomHeadersList', ], ], ], 'CustomOriginConfig' => [ 'type' => 'structure', 'required' => [ 'HTTPPort', 'HTTPSPort', 'OriginProtocolPolicy', ], 'members' => [ 'HTTPPort' => [ 'shape' => 'integer', ], 'HTTPSPort' => [ 'shape' => 'integer', ], 'OriginProtocolPolicy' => [ 'shape' => 'OriginProtocolPolicy', ], 'OriginSslProtocols' => [ 'shape' => 'OriginSslProtocols', ], 'OriginReadTimeout' => [ 'shape' => 'integer', ], 'OriginKeepaliveTimeout' => [ 'shape' => 'integer', ], ], ], 'DefaultCacheBehavior' => [ 'type' => 'structure', 'required' => [ 'TargetOriginId', 'ViewerProtocolPolicy', ], 'members' => [ 'TargetOriginId' => [ 'shape' => 'string', ], 'TrustedSigners' => [ 'shape' => 'TrustedSigners', ], 'TrustedKeyGroups' => [ 'shape' => 'TrustedKeyGroups', ], 'ViewerProtocolPolicy' => [ 'shape' => 'ViewerProtocolPolicy', ], 'AllowedMethods' => [ 'shape' => 'AllowedMethods', ], 'SmoothStreaming' => [ 'shape' => 'boolean', ], 'Compress' => [ 'shape' => 'boolean', ], 'LambdaFunctionAssociations' => [ 'shape' => 'LambdaFunctionAssociations', ], 'FunctionAssociations' => [ 'shape' => 'FunctionAssociations', ], 'FieldLevelEncryptionId' => [ 'shape' => 'string', ], 'RealtimeLogConfigArn' => [ 'shape' => 'string', ], 'CachePolicyId' => [ 'shape' => 'string', ], 'OriginRequestPolicyId' => [ 'shape' => 'string', ], 'ResponseHeadersPolicyId' => [ 'shape' => 'string', ], 'ForwardedValues' => [ 'shape' => 'ForwardedValues', 'deprecated' => true, ], 'MinTTL' => [ 'shape' => 'long', 'deprecated' => true, ], 'DefaultTTL' => [ 'shape' => 'long', 'deprecated' => true, ], 'MaxTTL' => [ 'shape' => 'long', 'deprecated' => true, ], ], ], 'DeleteCachePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteCloudFrontOriginAccessIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteContinuousDeploymentPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteFieldLevelEncryptionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteFieldLevelEncryptionProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'IfMatch', 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Name', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteKeyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteKeyValueStoreRequest' => [ 'type' => 'structure', 'required' => [ 'IfMatch', 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'KeyValueStoreName', 'location' => 'uri', 'locationName' => 'Name', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteMonitoringSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionId', ], ], ], 'DeleteMonitoringSubscriptionResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteOriginAccessControlRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteOriginRequestPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeletePublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteRealtimeLogConfigRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], ], ], 'DeleteResponseHeadersPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteStreamingDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DescribeFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Name', ], 'Stage' => [ 'shape' => 'FunctionStage', 'location' => 'querystring', 'locationName' => 'Stage', ], ], ], 'DescribeFunctionResult' => [ 'type' => 'structure', 'members' => [ 'FunctionSummary' => [ 'shape' => 'FunctionSummary', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FunctionSummary', ], 'DescribeKeyValueStoreRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'KeyValueStoreName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DescribeKeyValueStoreResult' => [ 'type' => 'structure', 'members' => [ 'KeyValueStore' => [ 'shape' => 'KeyValueStore', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'KeyValueStore', ], 'Distribution' => [ 'type' => 'structure', 'required' => [ 'Id', 'ARN', 'Status', 'LastModifiedTime', 'InProgressInvalidationBatches', 'DomainName', 'DistributionConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'InProgressInvalidationBatches' => [ 'shape' => 'integer', ], 'DomainName' => [ 'shape' => 'string', ], 'ActiveTrustedSigners' => [ 'shape' => 'ActiveTrustedSigners', ], 'ActiveTrustedKeyGroups' => [ 'shape' => 'ActiveTrustedKeyGroups', ], 'DistributionConfig' => [ 'shape' => 'DistributionConfig', ], 'AliasICPRecordals' => [ 'shape' => 'AliasICPRecordals', ], ], ], 'DistributionAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'DistributionConfig' => [ 'type' => 'structure', 'required' => [ 'CallerReference', 'Origins', 'DefaultCacheBehavior', 'Comment', 'Enabled', ], 'members' => [ 'CallerReference' => [ 'shape' => 'string', ], 'Aliases' => [ 'shape' => 'Aliases', ], 'DefaultRootObject' => [ 'shape' => 'string', ], 'Origins' => [ 'shape' => 'Origins', ], 'OriginGroups' => [ 'shape' => 'OriginGroups', ], 'DefaultCacheBehavior' => [ 'shape' => 'DefaultCacheBehavior', ], 'CacheBehaviors' => [ 'shape' => 'CacheBehaviors', ], 'CustomErrorResponses' => [ 'shape' => 'CustomErrorResponses', ], 'Comment' => [ 'shape' => 'CommentType', ], 'Logging' => [ 'shape' => 'LoggingConfig', ], 'PriceClass' => [ 'shape' => 'PriceClass', ], 'Enabled' => [ 'shape' => 'boolean', ], 'ViewerCertificate' => [ 'shape' => 'ViewerCertificate', ], 'Restrictions' => [ 'shape' => 'Restrictions', ], 'WebACLId' => [ 'shape' => 'string', ], 'HttpVersion' => [ 'shape' => 'HttpVersion', ], 'IsIPV6Enabled' => [ 'shape' => 'boolean', ], 'ContinuousDeploymentPolicyId' => [ 'shape' => 'string', ], 'Staging' => [ 'shape' => 'boolean', ], ], ], 'DistributionConfigWithTags' => [ 'type' => 'structure', 'required' => [ 'DistributionConfig', 'Tags', ], 'members' => [ 'DistributionConfig' => [ 'shape' => 'DistributionConfig', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'DistributionIdList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'DistributionIdListSummary', ], ], ], 'DistributionIdListSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'DistributionId', ], ], 'DistributionList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'DistributionSummaryList', ], ], ], 'DistributionNotDisabled' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'DistributionSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'ARN', 'Status', 'LastModifiedTime', 'DomainName', 'Aliases', 'Origins', 'DefaultCacheBehavior', 'CacheBehaviors', 'CustomErrorResponses', 'Comment', 'PriceClass', 'Enabled', 'ViewerCertificate', 'Restrictions', 'WebACLId', 'HttpVersion', 'IsIPV6Enabled', 'Staging', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'DomainName' => [ 'shape' => 'string', ], 'Aliases' => [ 'shape' => 'Aliases', ], 'Origins' => [ 'shape' => 'Origins', ], 'OriginGroups' => [ 'shape' => 'OriginGroups', ], 'DefaultCacheBehavior' => [ 'shape' => 'DefaultCacheBehavior', ], 'CacheBehaviors' => [ 'shape' => 'CacheBehaviors', ], 'CustomErrorResponses' => [ 'shape' => 'CustomErrorResponses', ], 'Comment' => [ 'shape' => 'string', ], 'PriceClass' => [ 'shape' => 'PriceClass', ], 'Enabled' => [ 'shape' => 'boolean', ], 'ViewerCertificate' => [ 'shape' => 'ViewerCertificate', ], 'Restrictions' => [ 'shape' => 'Restrictions', ], 'WebACLId' => [ 'shape' => 'string', ], 'HttpVersion' => [ 'shape' => 'HttpVersion', ], 'IsIPV6Enabled' => [ 'shape' => 'boolean', ], 'AliasICPRecordals' => [ 'shape' => 'AliasICPRecordals', ], 'Staging' => [ 'shape' => 'boolean', ], ], ], 'DistributionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DistributionSummary', 'locationName' => 'DistributionSummary', ], ], 'EncryptionEntities' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'EncryptionEntityList', ], ], ], 'EncryptionEntity' => [ 'type' => 'structure', 'required' => [ 'PublicKeyId', 'ProviderId', 'FieldPatterns', ], 'members' => [ 'PublicKeyId' => [ 'shape' => 'string', ], 'ProviderId' => [ 'shape' => 'string', ], 'FieldPatterns' => [ 'shape' => 'FieldPatterns', ], ], ], 'EncryptionEntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EncryptionEntity', 'locationName' => 'EncryptionEntity', ], ], 'EndPoint' => [ 'type' => 'structure', 'required' => [ 'StreamType', ], 'members' => [ 'StreamType' => [ 'shape' => 'string', ], 'KinesisStreamConfig' => [ 'shape' => 'KinesisStreamConfig', ], ], ], 'EndPointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndPoint', ], ], 'EntityAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'EntityLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'EntityNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'EntitySizeLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 413, ], 'exception' => true, ], 'EventType' => [ 'type' => 'string', 'enum' => [ 'viewer-request', 'viewer-response', 'origin-request', 'origin-response', ], ], 'FieldLevelEncryption' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'FieldLevelEncryptionConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'FieldLevelEncryptionConfig' => [ 'shape' => 'FieldLevelEncryptionConfig', ], ], ], 'FieldLevelEncryptionConfig' => [ 'type' => 'structure', 'required' => [ 'CallerReference', ], 'members' => [ 'CallerReference' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], 'QueryArgProfileConfig' => [ 'shape' => 'QueryArgProfileConfig', ], 'ContentTypeProfileConfig' => [ 'shape' => 'ContentTypeProfileConfig', ], ], ], 'FieldLevelEncryptionConfigAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'FieldLevelEncryptionConfigInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'FieldLevelEncryptionList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'FieldLevelEncryptionSummaryList', ], ], ], 'FieldLevelEncryptionProfile' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'FieldLevelEncryptionProfileConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'FieldLevelEncryptionProfileConfig' => [ 'shape' => 'FieldLevelEncryptionProfileConfig', ], ], ], 'FieldLevelEncryptionProfileAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'FieldLevelEncryptionProfileConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'CallerReference', 'EncryptionEntities', ], 'members' => [ 'Name' => [ 'shape' => 'string', ], 'CallerReference' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], 'EncryptionEntities' => [ 'shape' => 'EncryptionEntities', ], ], ], 'FieldLevelEncryptionProfileInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'FieldLevelEncryptionProfileList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'FieldLevelEncryptionProfileSummaryList', ], ], ], 'FieldLevelEncryptionProfileSizeExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'FieldLevelEncryptionProfileSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'Name', 'EncryptionEntities', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'Name' => [ 'shape' => 'string', ], 'EncryptionEntities' => [ 'shape' => 'EncryptionEntities', ], 'Comment' => [ 'shape' => 'string', ], ], ], 'FieldLevelEncryptionProfileSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldLevelEncryptionProfileSummary', 'locationName' => 'FieldLevelEncryptionProfileSummary', ], ], 'FieldLevelEncryptionSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'Comment' => [ 'shape' => 'string', ], 'QueryArgProfileConfig' => [ 'shape' => 'QueryArgProfileConfig', ], 'ContentTypeProfileConfig' => [ 'shape' => 'ContentTypeProfileConfig', ], ], ], 'FieldLevelEncryptionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldLevelEncryptionSummary', 'locationName' => 'FieldLevelEncryptionSummary', ], ], 'FieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Field', ], ], 'FieldPatternList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'FieldPattern', ], ], 'FieldPatterns' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'FieldPatternList', ], ], ], 'Format' => [ 'type' => 'string', 'enum' => [ 'URLEncoded', ], ], 'ForwardedValues' => [ 'type' => 'structure', 'required' => [ 'QueryString', 'Cookies', ], 'members' => [ 'QueryString' => [ 'shape' => 'boolean', ], 'Cookies' => [ 'shape' => 'CookiePreference', ], 'Headers' => [ 'shape' => 'Headers', ], 'QueryStringCacheKeys' => [ 'shape' => 'QueryStringCacheKeys', ], ], ], 'FrameOptionsList' => [ 'type' => 'string', 'enum' => [ 'DENY', 'SAMEORIGIN', ], ], 'FunctionARN' => [ 'type' => 'string', 'max' => 108, 'pattern' => 'arn:aws:cloudfront::[0-9]{12}:function\\/[a-zA-Z0-9-_]{1,64}$', ], 'FunctionAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'FunctionAssociation' => [ 'type' => 'structure', 'required' => [ 'FunctionARN', 'EventType', ], 'members' => [ 'FunctionARN' => [ 'shape' => 'FunctionARN', ], 'EventType' => [ 'shape' => 'EventType', ], ], ], 'FunctionAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionAssociation', 'locationName' => 'FunctionAssociation', ], ], 'FunctionAssociations' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'FunctionAssociationList', ], ], ], 'FunctionBlob' => [ 'type' => 'blob', 'max' => 40960, 'min' => 1, 'sensitive' => true, ], 'FunctionConfig' => [ 'type' => 'structure', 'required' => [ 'Comment', 'Runtime', ], 'members' => [ 'Comment' => [ 'shape' => 'string', ], 'Runtime' => [ 'shape' => 'FunctionRuntime', ], 'KeyValueStoreAssociations' => [ 'shape' => 'KeyValueStoreAssociations', ], ], ], 'FunctionEventObject' => [ 'type' => 'blob', 'max' => 40960, 'sensitive' => true, ], 'FunctionExecutionLogList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], 'sensitive' => true, ], 'FunctionInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'FunctionList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'FunctionSummaryList', ], ], ], 'FunctionMetadata' => [ 'type' => 'structure', 'required' => [ 'FunctionARN', 'LastModifiedTime', ], 'members' => [ 'FunctionARN' => [ 'shape' => 'string', ], 'Stage' => [ 'shape' => 'FunctionStage', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], ], ], 'FunctionName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_]{1,64}$', ], 'FunctionRuntime' => [ 'type' => 'string', 'enum' => [ 'cloudfront-js-1.0', 'cloudfront-js-2.0', ], ], 'FunctionSizeLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 413, ], 'exception' => true, ], 'FunctionStage' => [ 'type' => 'string', 'enum' => [ 'DEVELOPMENT', 'LIVE', ], ], 'FunctionSummary' => [ 'type' => 'structure', 'required' => [ 'Name', 'FunctionConfig', 'FunctionMetadata', ], 'members' => [ 'Name' => [ 'shape' => 'FunctionName', ], 'Status' => [ 'shape' => 'string', ], 'FunctionConfig' => [ 'shape' => 'FunctionConfig', ], 'FunctionMetadata' => [ 'shape' => 'FunctionMetadata', ], ], ], 'FunctionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionSummary', 'locationName' => 'FunctionSummary', ], ], 'GeoRestriction' => [ 'type' => 'structure', 'required' => [ 'RestrictionType', 'Quantity', ], 'members' => [ 'RestrictionType' => [ 'shape' => 'GeoRestrictionType', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'LocationList', ], ], ], 'GeoRestrictionType' => [ 'type' => 'string', 'enum' => [ 'blacklist', 'whitelist', 'none', ], ], 'GetCachePolicyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetCachePolicyConfigResult' => [ 'type' => 'structure', 'members' => [ 'CachePolicyConfig' => [ 'shape' => 'CachePolicyConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CachePolicyConfig', ], 'GetCachePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetCachePolicyResult' => [ 'type' => 'structure', 'members' => [ 'CachePolicy' => [ 'shape' => 'CachePolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CachePolicy', ], 'GetCloudFrontOriginAccessIdentityConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetCloudFrontOriginAccessIdentityConfigResult' => [ 'type' => 'structure', 'members' => [ 'CloudFrontOriginAccessIdentityConfig' => [ 'shape' => 'CloudFrontOriginAccessIdentityConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CloudFrontOriginAccessIdentityConfig', ], 'GetCloudFrontOriginAccessIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetCloudFrontOriginAccessIdentityResult' => [ 'type' => 'structure', 'members' => [ 'CloudFrontOriginAccessIdentity' => [ 'shape' => 'CloudFrontOriginAccessIdentity', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CloudFrontOriginAccessIdentity', ], 'GetContinuousDeploymentPolicyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetContinuousDeploymentPolicyConfigResult' => [ 'type' => 'structure', 'members' => [ 'ContinuousDeploymentPolicyConfig' => [ 'shape' => 'ContinuousDeploymentPolicyConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ContinuousDeploymentPolicyConfig', ], 'GetContinuousDeploymentPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetContinuousDeploymentPolicyResult' => [ 'type' => 'structure', 'members' => [ 'ContinuousDeploymentPolicy' => [ 'shape' => 'ContinuousDeploymentPolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ContinuousDeploymentPolicy', ], 'GetDistributionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetDistributionConfigResult' => [ 'type' => 'structure', 'members' => [ 'DistributionConfig' => [ 'shape' => 'DistributionConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'DistributionConfig', ], 'GetDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetDistributionResult' => [ 'type' => 'structure', 'members' => [ 'Distribution' => [ 'shape' => 'Distribution', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'Distribution', ], 'GetFieldLevelEncryptionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetFieldLevelEncryptionConfigResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionConfig' => [ 'shape' => 'FieldLevelEncryptionConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryptionConfig', ], 'GetFieldLevelEncryptionProfileConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetFieldLevelEncryptionProfileConfigResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionProfileConfig' => [ 'shape' => 'FieldLevelEncryptionProfileConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryptionProfileConfig', ], 'GetFieldLevelEncryptionProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetFieldLevelEncryptionProfileResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionProfile' => [ 'shape' => 'FieldLevelEncryptionProfile', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryptionProfile', ], 'GetFieldLevelEncryptionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetFieldLevelEncryptionResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryption' => [ 'shape' => 'FieldLevelEncryption', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryption', ], 'GetFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Name', ], 'Stage' => [ 'shape' => 'FunctionStage', 'location' => 'querystring', 'locationName' => 'Stage', ], ], ], 'GetFunctionResult' => [ 'type' => 'structure', 'members' => [ 'FunctionCode' => [ 'shape' => 'FunctionBlob', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], 'ContentType' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Content-Type', ], ], 'payload' => 'FunctionCode', ], 'GetInvalidationRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', 'Id', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionId', ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetInvalidationResult' => [ 'type' => 'structure', 'members' => [ 'Invalidation' => [ 'shape' => 'Invalidation', ], ], 'payload' => 'Invalidation', ], 'GetKeyGroupConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetKeyGroupConfigResult' => [ 'type' => 'structure', 'members' => [ 'KeyGroupConfig' => [ 'shape' => 'KeyGroupConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'KeyGroupConfig', ], 'GetKeyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetKeyGroupResult' => [ 'type' => 'structure', 'members' => [ 'KeyGroup' => [ 'shape' => 'KeyGroup', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'KeyGroup', ], 'GetMonitoringSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionId', ], ], ], 'GetMonitoringSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'MonitoringSubscription' => [ 'shape' => 'MonitoringSubscription', ], ], 'payload' => 'MonitoringSubscription', ], 'GetOriginAccessControlConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetOriginAccessControlConfigResult' => [ 'type' => 'structure', 'members' => [ 'OriginAccessControlConfig' => [ 'shape' => 'OriginAccessControlConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginAccessControlConfig', ], 'GetOriginAccessControlRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetOriginAccessControlResult' => [ 'type' => 'structure', 'members' => [ 'OriginAccessControl' => [ 'shape' => 'OriginAccessControl', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginAccessControl', ], 'GetOriginRequestPolicyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetOriginRequestPolicyConfigResult' => [ 'type' => 'structure', 'members' => [ 'OriginRequestPolicyConfig' => [ 'shape' => 'OriginRequestPolicyConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginRequestPolicyConfig', ], 'GetOriginRequestPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetOriginRequestPolicyResult' => [ 'type' => 'structure', 'members' => [ 'OriginRequestPolicy' => [ 'shape' => 'OriginRequestPolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginRequestPolicy', ], 'GetPublicKeyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetPublicKeyConfigResult' => [ 'type' => 'structure', 'members' => [ 'PublicKeyConfig' => [ 'shape' => 'PublicKeyConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'PublicKeyConfig', ], 'GetPublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetPublicKeyResult' => [ 'type' => 'structure', 'members' => [ 'PublicKey' => [ 'shape' => 'PublicKey', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'PublicKey', ], 'GetRealtimeLogConfigRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], ], ], 'GetRealtimeLogConfigResult' => [ 'type' => 'structure', 'members' => [ 'RealtimeLogConfig' => [ 'shape' => 'RealtimeLogConfig', ], ], ], 'GetResponseHeadersPolicyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetResponseHeadersPolicyConfigResult' => [ 'type' => 'structure', 'members' => [ 'ResponseHeadersPolicyConfig' => [ 'shape' => 'ResponseHeadersPolicyConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ResponseHeadersPolicyConfig', ], 'GetResponseHeadersPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetResponseHeadersPolicyResult' => [ 'type' => 'structure', 'members' => [ 'ResponseHeadersPolicy' => [ 'shape' => 'ResponseHeadersPolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ResponseHeadersPolicy', ], 'GetStreamingDistributionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetStreamingDistributionConfigResult' => [ 'type' => 'structure', 'members' => [ 'StreamingDistributionConfig' => [ 'shape' => 'StreamingDistributionConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'StreamingDistributionConfig', ], 'GetStreamingDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetStreamingDistributionResult' => [ 'type' => 'structure', 'members' => [ 'StreamingDistribution' => [ 'shape' => 'StreamingDistribution', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'StreamingDistribution', ], 'HeaderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Name', ], ], 'Headers' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'HeaderList', ], ], ], 'HttpVersion' => [ 'type' => 'string', 'enum' => [ 'http1.1', 'http2', 'http3', 'http2and3', ], ], 'ICPRecordalStatus' => [ 'type' => 'string', 'enum' => [ 'APPROVED', 'SUSPENDED', 'PENDING', ], ], 'IllegalDelete' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'IllegalFieldLevelEncryptionConfigAssociationWithCacheBehavior' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'IllegalOriginAccessConfiguration' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'IllegalUpdate' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ImportSource' => [ 'type' => 'structure', 'required' => [ 'SourceType', 'SourceARN', ], 'members' => [ 'SourceType' => [ 'shape' => 'ImportSourceType', ], 'SourceARN' => [ 'shape' => 'string', ], ], ], 'ImportSourceType' => [ 'type' => 'string', 'enum' => [ 'S3', ], ], 'InconsistentQuantities' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidArgument' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidDefaultRootObject' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidDomainNameForOriginAccessControl' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidErrorCode' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidForwardCookies' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidFunctionAssociation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidGeoRestrictionParameter' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidHeadersForS3Origin' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidIfMatchVersion' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidLambdaFunctionAssociation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidLocationCode' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidMinimumProtocolVersion' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidOrigin' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidOriginAccessControl' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidOriginAccessIdentity' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidOriginKeepaliveTimeout' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidOriginReadTimeout' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidProtocolSettings' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidQueryStringParameters' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRelativePath' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRequiredProtocol' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidResponseCode' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidTTLOrder' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidTagging' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidViewerCertificate' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidWebACLId' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Invalidation' => [ 'type' => 'structure', 'required' => [ 'Id', 'Status', 'CreateTime', 'InvalidationBatch', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'CreateTime' => [ 'shape' => 'timestamp', ], 'InvalidationBatch' => [ 'shape' => 'InvalidationBatch', ], ], ], 'InvalidationBatch' => [ 'type' => 'structure', 'required' => [ 'Paths', 'CallerReference', ], 'members' => [ 'Paths' => [ 'shape' => 'Paths', ], 'CallerReference' => [ 'shape' => 'string', ], ], ], 'InvalidationList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'InvalidationSummaryList', ], ], ], 'InvalidationSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'CreateTime', 'Status', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'CreateTime' => [ 'shape' => 'timestamp', ], 'Status' => [ 'shape' => 'string', ], ], ], 'InvalidationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvalidationSummary', 'locationName' => 'InvalidationSummary', ], ], 'ItemSelection' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', 'all', ], ], 'KGKeyPairIds' => [ 'type' => 'structure', 'members' => [ 'KeyGroupId' => [ 'shape' => 'string', ], 'KeyPairIds' => [ 'shape' => 'KeyPairIds', ], ], ], 'KGKeyPairIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KGKeyPairIds', 'locationName' => 'KeyGroup', ], ], 'KeyGroup' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'KeyGroupConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'KeyGroupConfig' => [ 'shape' => 'KeyGroupConfig', ], ], ], 'KeyGroupAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'KeyGroupConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'Items', ], 'members' => [ 'Name' => [ 'shape' => 'string', ], 'Items' => [ 'shape' => 'PublicKeyIdList', ], 'Comment' => [ 'shape' => 'string', ], ], ], 'KeyGroupList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'KeyGroupSummaryList', ], ], ], 'KeyGroupSummary' => [ 'type' => 'structure', 'required' => [ 'KeyGroup', ], 'members' => [ 'KeyGroup' => [ 'shape' => 'KeyGroup', ], ], ], 'KeyGroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyGroupSummary', 'locationName' => 'KeyGroupSummary', ], ], 'KeyPairIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'KeyPairId', ], ], 'KeyPairIds' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'KeyPairIdList', ], ], ], 'KeyValueStore' => [ 'type' => 'structure', 'required' => [ 'Name', 'Id', 'Comment', 'ARN', 'LastModifiedTime', ], 'members' => [ 'Name' => [ 'shape' => 'string', ], 'Id' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], ], ], 'KeyValueStoreARN' => [ 'type' => 'string', 'max' => 85, 'pattern' => 'arn:aws:cloudfront::[0-9]{12}:key-value-store\\/[0-9a-fA-F-]{36}$', ], 'KeyValueStoreAssociation' => [ 'type' => 'structure', 'required' => [ 'KeyValueStoreARN', ], 'members' => [ 'KeyValueStoreARN' => [ 'shape' => 'KeyValueStoreARN', ], ], ], 'KeyValueStoreAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyValueStoreAssociation', 'locationName' => 'KeyValueStoreAssociation', ], ], 'KeyValueStoreAssociations' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'KeyValueStoreAssociationList', ], ], ], 'KeyValueStoreComment' => [ 'type' => 'string', 'max' => 128, ], 'KeyValueStoreList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'KeyValueStoreSummaryList', ], ], ], 'KeyValueStoreName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_]{1,64}$', ], 'KeyValueStoreSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyValueStore', 'locationName' => 'KeyValueStore', ], ], 'KinesisStreamConfig' => [ 'type' => 'structure', 'required' => [ 'RoleARN', 'StreamARN', ], 'members' => [ 'RoleARN' => [ 'shape' => 'string', ], 'StreamARN' => [ 'shape' => 'string', ], ], ], 'LambdaFunctionARN' => [ 'type' => 'string', ], 'LambdaFunctionAssociation' => [ 'type' => 'structure', 'required' => [ 'LambdaFunctionARN', 'EventType', ], 'members' => [ 'LambdaFunctionARN' => [ 'shape' => 'LambdaFunctionARN', ], 'EventType' => [ 'shape' => 'EventType', ], 'IncludeBody' => [ 'shape' => 'boolean', ], ], ], 'LambdaFunctionAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaFunctionAssociation', 'locationName' => 'LambdaFunctionAssociation', ], ], 'LambdaFunctionAssociations' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'LambdaFunctionAssociationList', ], ], ], 'ListCachePoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'CachePolicyType', 'location' => 'querystring', 'locationName' => 'Type', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListCachePoliciesResult' => [ 'type' => 'structure', 'members' => [ 'CachePolicyList' => [ 'shape' => 'CachePolicyList', ], ], 'payload' => 'CachePolicyList', ], 'ListCloudFrontOriginAccessIdentitiesRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListCloudFrontOriginAccessIdentitiesResult' => [ 'type' => 'structure', 'members' => [ 'CloudFrontOriginAccessIdentityList' => [ 'shape' => 'CloudFrontOriginAccessIdentityList', ], ], 'payload' => 'CloudFrontOriginAccessIdentityList', ], 'ListConflictingAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', 'Alias', ], 'members' => [ 'DistributionId' => [ 'shape' => 'distributionIdString', 'location' => 'querystring', 'locationName' => 'DistributionId', ], 'Alias' => [ 'shape' => 'aliasString', 'location' => 'querystring', 'locationName' => 'Alias', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'listConflictingAliasesMaxItemsInteger', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListConflictingAliasesResult' => [ 'type' => 'structure', 'members' => [ 'ConflictingAliasesList' => [ 'shape' => 'ConflictingAliasesList', ], ], 'payload' => 'ConflictingAliasesList', ], 'ListContinuousDeploymentPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListContinuousDeploymentPoliciesResult' => [ 'type' => 'structure', 'members' => [ 'ContinuousDeploymentPolicyList' => [ 'shape' => 'ContinuousDeploymentPolicyList', ], ], 'payload' => 'ContinuousDeploymentPolicyList', ], 'ListDistributionsByCachePolicyIdRequest' => [ 'type' => 'structure', 'required' => [ 'CachePolicyId', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'CachePolicyId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'CachePolicyId', ], ], ], 'ListDistributionsByCachePolicyIdResult' => [ 'type' => 'structure', 'members' => [ 'DistributionIdList' => [ 'shape' => 'DistributionIdList', ], ], 'payload' => 'DistributionIdList', ], 'ListDistributionsByKeyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'KeyGroupId', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'KeyGroupId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'KeyGroupId', ], ], ], 'ListDistributionsByKeyGroupResult' => [ 'type' => 'structure', 'members' => [ 'DistributionIdList' => [ 'shape' => 'DistributionIdList', ], ], 'payload' => 'DistributionIdList', ], 'ListDistributionsByOriginRequestPolicyIdRequest' => [ 'type' => 'structure', 'required' => [ 'OriginRequestPolicyId', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'OriginRequestPolicyId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'OriginRequestPolicyId', ], ], ], 'ListDistributionsByOriginRequestPolicyIdResult' => [ 'type' => 'structure', 'members' => [ 'DistributionIdList' => [ 'shape' => 'DistributionIdList', ], ], 'payload' => 'DistributionIdList', ], 'ListDistributionsByRealtimeLogConfigRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'string', ], 'RealtimeLogConfigName' => [ 'shape' => 'string', ], 'RealtimeLogConfigArn' => [ 'shape' => 'string', ], ], ], 'ListDistributionsByRealtimeLogConfigResult' => [ 'type' => 'structure', 'members' => [ 'DistributionList' => [ 'shape' => 'DistributionList', ], ], 'payload' => 'DistributionList', ], 'ListDistributionsByResponseHeadersPolicyIdRequest' => [ 'type' => 'structure', 'required' => [ 'ResponseHeadersPolicyId', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'ResponseHeadersPolicyId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'ResponseHeadersPolicyId', ], ], ], 'ListDistributionsByResponseHeadersPolicyIdResult' => [ 'type' => 'structure', 'members' => [ 'DistributionIdList' => [ 'shape' => 'DistributionIdList', ], ], 'payload' => 'DistributionIdList', ], 'ListDistributionsByWebACLIdRequest' => [ 'type' => 'structure', 'required' => [ 'WebACLId', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'WebACLId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'WebACLId', ], ], ], 'ListDistributionsByWebACLIdResult' => [ 'type' => 'structure', 'members' => [ 'DistributionList' => [ 'shape' => 'DistributionList', ], ], 'payload' => 'DistributionList', ], 'ListDistributionsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListDistributionsResult' => [ 'type' => 'structure', 'members' => [ 'DistributionList' => [ 'shape' => 'DistributionList', ], ], 'payload' => 'DistributionList', ], 'ListFieldLevelEncryptionConfigsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListFieldLevelEncryptionConfigsResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionList' => [ 'shape' => 'FieldLevelEncryptionList', ], ], 'payload' => 'FieldLevelEncryptionList', ], 'ListFieldLevelEncryptionProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListFieldLevelEncryptionProfilesResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionProfileList' => [ 'shape' => 'FieldLevelEncryptionProfileList', ], ], 'payload' => 'FieldLevelEncryptionProfileList', ], 'ListFunctionsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'Stage' => [ 'shape' => 'FunctionStage', 'location' => 'querystring', 'locationName' => 'Stage', ], ], ], 'ListFunctionsResult' => [ 'type' => 'structure', 'members' => [ 'FunctionList' => [ 'shape' => 'FunctionList', ], ], 'payload' => 'FunctionList', ], 'ListInvalidationsRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionId', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListInvalidationsResult' => [ 'type' => 'structure', 'members' => [ 'InvalidationList' => [ 'shape' => 'InvalidationList', ], ], 'payload' => 'InvalidationList', ], 'ListKeyGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListKeyGroupsResult' => [ 'type' => 'structure', 'members' => [ 'KeyGroupList' => [ 'shape' => 'KeyGroupList', ], ], 'payload' => 'KeyGroupList', ], 'ListKeyValueStoresRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'Status' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Status', ], ], ], 'ListKeyValueStoresResult' => [ 'type' => 'structure', 'members' => [ 'KeyValueStoreList' => [ 'shape' => 'KeyValueStoreList', ], ], 'payload' => 'KeyValueStoreList', ], 'ListOriginAccessControlsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListOriginAccessControlsResult' => [ 'type' => 'structure', 'members' => [ 'OriginAccessControlList' => [ 'shape' => 'OriginAccessControlList', ], ], 'payload' => 'OriginAccessControlList', ], 'ListOriginRequestPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'OriginRequestPolicyType', 'location' => 'querystring', 'locationName' => 'Type', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListOriginRequestPoliciesResult' => [ 'type' => 'structure', 'members' => [ 'OriginRequestPolicyList' => [ 'shape' => 'OriginRequestPolicyList', ], ], 'payload' => 'OriginRequestPolicyList', ], 'ListPublicKeysRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListPublicKeysResult' => [ 'type' => 'structure', 'members' => [ 'PublicKeyList' => [ 'shape' => 'PublicKeyList', ], ], 'payload' => 'PublicKeyList', ], 'ListRealtimeLogConfigsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], ], ], 'ListRealtimeLogConfigsResult' => [ 'type' => 'structure', 'members' => [ 'RealtimeLogConfigs' => [ 'shape' => 'RealtimeLogConfigs', ], ], 'payload' => 'RealtimeLogConfigs', ], 'ListResponseHeadersPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ResponseHeadersPolicyType', 'location' => 'querystring', 'locationName' => 'Type', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListResponseHeadersPoliciesResult' => [ 'type' => 'structure', 'members' => [ 'ResponseHeadersPolicyList' => [ 'shape' => 'ResponseHeadersPolicyList', ], ], 'payload' => 'ResponseHeadersPolicyList', ], 'ListStreamingDistributionsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListStreamingDistributionsResult' => [ 'type' => 'structure', 'members' => [ 'StreamingDistributionList' => [ 'shape' => 'StreamingDistributionList', ], ], 'payload' => 'StreamingDistributionList', ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', ], 'members' => [ 'Resource' => [ 'shape' => 'ResourceARN', 'location' => 'querystring', 'locationName' => 'Resource', ], ], ], 'ListTagsForResourceResult' => [ 'type' => 'structure', 'required' => [ 'Tags', ], 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], 'payload' => 'Tags', ], 'LocationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Location', ], ], 'LoggingConfig' => [ 'type' => 'structure', 'required' => [ 'Enabled', 'IncludeCookies', 'Bucket', 'Prefix', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'IncludeCookies' => [ 'shape' => 'boolean', ], 'Bucket' => [ 'shape' => 'string', ], 'Prefix' => [ 'shape' => 'string', ], ], ], 'Method' => [ 'type' => 'string', 'enum' => [ 'GET', 'HEAD', 'POST', 'PUT', 'PATCH', 'OPTIONS', 'DELETE', ], ], 'MethodsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Method', 'locationName' => 'Method', ], ], 'MinimumProtocolVersion' => [ 'type' => 'string', 'enum' => [ 'SSLv3', 'TLSv1', 'TLSv1_2016', 'TLSv1.1_2016', 'TLSv1.2_2018', 'TLSv1.2_2019', 'TLSv1.2_2021', ], ], 'MissingBody' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'MonitoringSubscription' => [ 'type' => 'structure', 'members' => [ 'RealtimeMetricsSubscriptionConfig' => [ 'shape' => 'RealtimeMetricsSubscriptionConfig', ], ], ], 'MonitoringSubscriptionAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'NoSuchCachePolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchCloudFrontOriginAccessIdentity' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchContinuousDeploymentPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchDistribution' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchFieldLevelEncryptionConfig' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchFieldLevelEncryptionProfile' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchFunctionExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchInvalidation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchMonitoringSubscription' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchOrigin' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchOriginAccessControl' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchOriginRequestPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchPublicKey' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchRealtimeLogConfig' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchResource' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchResponseHeadersPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchStreamingDistribution' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'Origin' => [ 'type' => 'structure', 'required' => [ 'Id', 'DomainName', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'DomainName' => [ 'shape' => 'string', ], 'OriginPath' => [ 'shape' => 'string', ], 'CustomHeaders' => [ 'shape' => 'CustomHeaders', ], 'S3OriginConfig' => [ 'shape' => 'S3OriginConfig', ], 'CustomOriginConfig' => [ 'shape' => 'CustomOriginConfig', ], 'ConnectionAttempts' => [ 'shape' => 'integer', ], 'ConnectionTimeout' => [ 'shape' => 'integer', ], 'OriginShield' => [ 'shape' => 'OriginShield', ], 'OriginAccessControlId' => [ 'shape' => 'string', ], ], ], 'OriginAccessControl' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'OriginAccessControlConfig' => [ 'shape' => 'OriginAccessControlConfig', ], ], ], 'OriginAccessControlAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'OriginAccessControlConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'SigningProtocol', 'SigningBehavior', 'OriginAccessControlOriginType', ], 'members' => [ 'Name' => [ 'shape' => 'string', ], 'Description' => [ 'shape' => 'string', ], 'SigningProtocol' => [ 'shape' => 'OriginAccessControlSigningProtocols', ], 'SigningBehavior' => [ 'shape' => 'OriginAccessControlSigningBehaviors', ], 'OriginAccessControlOriginType' => [ 'shape' => 'OriginAccessControlOriginTypes', ], ], ], 'OriginAccessControlInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'OriginAccessControlList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'OriginAccessControlSummaryList', ], ], ], 'OriginAccessControlOriginTypes' => [ 'type' => 'string', 'enum' => [ 's3', 'mediastore', ], ], 'OriginAccessControlSigningBehaviors' => [ 'type' => 'string', 'enum' => [ 'never', 'always', 'no-override', ], ], 'OriginAccessControlSigningProtocols' => [ 'type' => 'string', 'enum' => [ 'sigv4', ], ], 'OriginAccessControlSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'Description', 'Name', 'SigningProtocol', 'SigningBehavior', 'OriginAccessControlOriginType', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'Description' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'SigningProtocol' => [ 'shape' => 'OriginAccessControlSigningProtocols', ], 'SigningBehavior' => [ 'shape' => 'OriginAccessControlSigningBehaviors', ], 'OriginAccessControlOriginType' => [ 'shape' => 'OriginAccessControlOriginTypes', ], ], ], 'OriginAccessControlSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginAccessControlSummary', 'locationName' => 'OriginAccessControlSummary', ], ], 'OriginCustomHeader' => [ 'type' => 'structure', 'required' => [ 'HeaderName', 'HeaderValue', ], 'members' => [ 'HeaderName' => [ 'shape' => 'string', ], 'HeaderValue' => [ 'shape' => 'sensitiveStringType', ], ], ], 'OriginCustomHeadersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginCustomHeader', 'locationName' => 'OriginCustomHeader', ], ], 'OriginGroup' => [ 'type' => 'structure', 'required' => [ 'Id', 'FailoverCriteria', 'Members', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'FailoverCriteria' => [ 'shape' => 'OriginGroupFailoverCriteria', ], 'Members' => [ 'shape' => 'OriginGroupMembers', ], ], ], 'OriginGroupFailoverCriteria' => [ 'type' => 'structure', 'required' => [ 'StatusCodes', ], 'members' => [ 'StatusCodes' => [ 'shape' => 'StatusCodes', ], ], ], 'OriginGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginGroup', 'locationName' => 'OriginGroup', ], ], 'OriginGroupMember' => [ 'type' => 'structure', 'required' => [ 'OriginId', ], 'members' => [ 'OriginId' => [ 'shape' => 'string', ], ], ], 'OriginGroupMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginGroupMember', 'locationName' => 'OriginGroupMember', ], 'max' => 2, 'min' => 2, ], 'OriginGroupMembers' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'OriginGroupMemberList', ], ], ], 'OriginGroups' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'OriginGroupList', ], ], ], 'OriginList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Origin', 'locationName' => 'Origin', ], 'min' => 1, ], 'OriginProtocolPolicy' => [ 'type' => 'string', 'enum' => [ 'http-only', 'match-viewer', 'https-only', ], ], 'OriginRequestPolicy' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'OriginRequestPolicyConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'OriginRequestPolicyConfig' => [ 'shape' => 'OriginRequestPolicyConfig', ], ], ], 'OriginRequestPolicyAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'OriginRequestPolicyConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'HeadersConfig', 'CookiesConfig', 'QueryStringsConfig', ], 'members' => [ 'Comment' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'HeadersConfig' => [ 'shape' => 'OriginRequestPolicyHeadersConfig', ], 'CookiesConfig' => [ 'shape' => 'OriginRequestPolicyCookiesConfig', ], 'QueryStringsConfig' => [ 'shape' => 'OriginRequestPolicyQueryStringsConfig', ], ], ], 'OriginRequestPolicyCookieBehavior' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', 'all', 'allExcept', ], ], 'OriginRequestPolicyCookiesConfig' => [ 'type' => 'structure', 'required' => [ 'CookieBehavior', ], 'members' => [ 'CookieBehavior' => [ 'shape' => 'OriginRequestPolicyCookieBehavior', ], 'Cookies' => [ 'shape' => 'CookieNames', ], ], ], 'OriginRequestPolicyHeaderBehavior' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', 'allViewer', 'allViewerAndWhitelistCloudFront', 'allExcept', ], ], 'OriginRequestPolicyHeadersConfig' => [ 'type' => 'structure', 'required' => [ 'HeaderBehavior', ], 'members' => [ 'HeaderBehavior' => [ 'shape' => 'OriginRequestPolicyHeaderBehavior', ], 'Headers' => [ 'shape' => 'Headers', ], ], ], 'OriginRequestPolicyInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'OriginRequestPolicyList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'OriginRequestPolicySummaryList', ], ], ], 'OriginRequestPolicyQueryStringBehavior' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', 'all', 'allExcept', ], ], 'OriginRequestPolicyQueryStringsConfig' => [ 'type' => 'structure', 'required' => [ 'QueryStringBehavior', ], 'members' => [ 'QueryStringBehavior' => [ 'shape' => 'OriginRequestPolicyQueryStringBehavior', ], 'QueryStrings' => [ 'shape' => 'QueryStringNames', ], ], ], 'OriginRequestPolicySummary' => [ 'type' => 'structure', 'required' => [ 'Type', 'OriginRequestPolicy', ], 'members' => [ 'Type' => [ 'shape' => 'OriginRequestPolicyType', ], 'OriginRequestPolicy' => [ 'shape' => 'OriginRequestPolicy', ], ], ], 'OriginRequestPolicySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginRequestPolicySummary', 'locationName' => 'OriginRequestPolicySummary', ], ], 'OriginRequestPolicyType' => [ 'type' => 'string', 'enum' => [ 'managed', 'custom', ], ], 'OriginShield' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'OriginShieldRegion' => [ 'shape' => 'OriginShieldRegion', ], ], ], 'OriginShieldRegion' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[a-z]{2}-[a-z]+-\\d', ], 'OriginSslProtocols' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'SslProtocolsList', ], ], ], 'Origins' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'OriginList', ], ], ], 'ParametersInCacheKeyAndForwardedToOrigin' => [ 'type' => 'structure', 'required' => [ 'EnableAcceptEncodingGzip', 'HeadersConfig', 'CookiesConfig', 'QueryStringsConfig', ], 'members' => [ 'EnableAcceptEncodingGzip' => [ 'shape' => 'boolean', ], 'EnableAcceptEncodingBrotli' => [ 'shape' => 'boolean', ], 'HeadersConfig' => [ 'shape' => 'CachePolicyHeadersConfig', ], 'CookiesConfig' => [ 'shape' => 'CachePolicyCookiesConfig', ], 'QueryStringsConfig' => [ 'shape' => 'CachePolicyQueryStringsConfig', ], ], ], 'PathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Path', ], ], 'Paths' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'PathList', ], ], ], 'PreconditionFailed' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 412, ], 'exception' => true, ], 'PriceClass' => [ 'type' => 'string', 'enum' => [ 'PriceClass_100', 'PriceClass_200', 'PriceClass_All', ], ], 'PublicKey' => [ 'type' => 'structure', 'required' => [ 'Id', 'CreatedTime', 'PublicKeyConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'PublicKeyConfig' => [ 'shape' => 'PublicKeyConfig', ], ], ], 'PublicKeyAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'PublicKeyConfig' => [ 'type' => 'structure', 'required' => [ 'CallerReference', 'Name', 'EncodedKey', ], 'members' => [ 'CallerReference' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'EncodedKey' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], ], ], 'PublicKeyIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'PublicKey', ], ], 'PublicKeyInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'PublicKeyList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'PublicKeySummaryList', ], ], ], 'PublicKeySummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'Name', 'CreatedTime', 'EncodedKey', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'EncodedKey' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], ], ], 'PublicKeySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PublicKeySummary', 'locationName' => 'PublicKeySummary', ], ], 'PublishFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IfMatch', ], 'members' => [ 'Name' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Name', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'PublishFunctionResult' => [ 'type' => 'structure', 'members' => [ 'FunctionSummary' => [ 'shape' => 'FunctionSummary', ], ], 'payload' => 'FunctionSummary', ], 'QueryArgProfile' => [ 'type' => 'structure', 'required' => [ 'QueryArg', 'ProfileId', ], 'members' => [ 'QueryArg' => [ 'shape' => 'string', ], 'ProfileId' => [ 'shape' => 'string', ], ], ], 'QueryArgProfileConfig' => [ 'type' => 'structure', 'required' => [ 'ForwardWhenQueryArgProfileIsUnknown', ], 'members' => [ 'ForwardWhenQueryArgProfileIsUnknown' => [ 'shape' => 'boolean', ], 'QueryArgProfiles' => [ 'shape' => 'QueryArgProfiles', ], ], ], 'QueryArgProfileEmpty' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'QueryArgProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryArgProfile', 'locationName' => 'QueryArgProfile', ], ], 'QueryArgProfiles' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'QueryArgProfileList', ], ], ], 'QueryStringCacheKeys' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'QueryStringCacheKeysList', ], ], ], 'QueryStringCacheKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Name', ], ], 'QueryStringNames' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'QueryStringNamesList', ], ], ], 'QueryStringNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Name', ], ], 'RealtimeLogConfig' => [ 'type' => 'structure', 'required' => [ 'ARN', 'Name', 'SamplingRate', 'EndPoints', 'Fields', ], 'members' => [ 'ARN' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'SamplingRate' => [ 'shape' => 'long', ], 'EndPoints' => [ 'shape' => 'EndPointList', ], 'Fields' => [ 'shape' => 'FieldList', ], ], ], 'RealtimeLogConfigAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'RealtimeLogConfigInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'RealtimeLogConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RealtimeLogConfig', ], ], 'RealtimeLogConfigOwnerMismatch' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'RealtimeLogConfigs' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'IsTruncated', 'Marker', ], 'members' => [ 'MaxItems' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'RealtimeLogConfigList', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], ], ], 'RealtimeMetricsSubscriptionConfig' => [ 'type' => 'structure', 'required' => [ 'RealtimeMetricsSubscriptionStatus', ], 'members' => [ 'RealtimeMetricsSubscriptionStatus' => [ 'shape' => 'RealtimeMetricsSubscriptionStatus', ], ], ], 'RealtimeMetricsSubscriptionStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'ReferrerPolicyList' => [ 'type' => 'string', 'enum' => [ 'no-referrer', 'no-referrer-when-downgrade', 'origin', 'origin-when-cross-origin', 'same-origin', 'strict-origin', 'strict-origin-when-cross-origin', 'unsafe-url', ], ], 'ResourceARN' => [ 'type' => 'string', 'pattern' => 'arn:aws(-cn)?:cloudfront::[0-9]+:.*', ], 'ResourceInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResponseHeadersPolicy' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'ResponseHeadersPolicyConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'ResponseHeadersPolicyConfig' => [ 'shape' => 'ResponseHeadersPolicyConfig', ], ], ], 'ResponseHeadersPolicyAccessControlAllowHeaders' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'AccessControlAllowHeadersList', ], ], ], 'ResponseHeadersPolicyAccessControlAllowMethods' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'AccessControlAllowMethodsList', ], ], ], 'ResponseHeadersPolicyAccessControlAllowMethodsValues' => [ 'type' => 'string', 'enum' => [ 'GET', 'POST', 'OPTIONS', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'ALL', ], ], 'ResponseHeadersPolicyAccessControlAllowOrigins' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'AccessControlAllowOriginsList', ], ], ], 'ResponseHeadersPolicyAccessControlExposeHeaders' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'AccessControlExposeHeadersList', ], ], ], 'ResponseHeadersPolicyAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResponseHeadersPolicyConfig' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Comment' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'CorsConfig' => [ 'shape' => 'ResponseHeadersPolicyCorsConfig', ], 'SecurityHeadersConfig' => [ 'shape' => 'ResponseHeadersPolicySecurityHeadersConfig', ], 'ServerTimingHeadersConfig' => [ 'shape' => 'ResponseHeadersPolicyServerTimingHeadersConfig', ], 'CustomHeadersConfig' => [ 'shape' => 'ResponseHeadersPolicyCustomHeadersConfig', ], 'RemoveHeadersConfig' => [ 'shape' => 'ResponseHeadersPolicyRemoveHeadersConfig', ], ], ], 'ResponseHeadersPolicyContentSecurityPolicy' => [ 'type' => 'structure', 'required' => [ 'Override', 'ContentSecurityPolicy', ], 'members' => [ 'Override' => [ 'shape' => 'boolean', ], 'ContentSecurityPolicy' => [ 'shape' => 'string', ], ], ], 'ResponseHeadersPolicyContentTypeOptions' => [ 'type' => 'structure', 'required' => [ 'Override', ], 'members' => [ 'Override' => [ 'shape' => 'boolean', ], ], ], 'ResponseHeadersPolicyCorsConfig' => [ 'type' => 'structure', 'required' => [ 'AccessControlAllowOrigins', 'AccessControlAllowHeaders', 'AccessControlAllowMethods', 'AccessControlAllowCredentials', 'OriginOverride', ], 'members' => [ 'AccessControlAllowOrigins' => [ 'shape' => 'ResponseHeadersPolicyAccessControlAllowOrigins', ], 'AccessControlAllowHeaders' => [ 'shape' => 'ResponseHeadersPolicyAccessControlAllowHeaders', ], 'AccessControlAllowMethods' => [ 'shape' => 'ResponseHeadersPolicyAccessControlAllowMethods', ], 'AccessControlAllowCredentials' => [ 'shape' => 'boolean', ], 'AccessControlExposeHeaders' => [ 'shape' => 'ResponseHeadersPolicyAccessControlExposeHeaders', ], 'AccessControlMaxAgeSec' => [ 'shape' => 'integer', ], 'OriginOverride' => [ 'shape' => 'boolean', ], ], ], 'ResponseHeadersPolicyCustomHeader' => [ 'type' => 'structure', 'required' => [ 'Header', 'Value', 'Override', ], 'members' => [ 'Header' => [ 'shape' => 'string', ], 'Value' => [ 'shape' => 'string', ], 'Override' => [ 'shape' => 'boolean', ], ], ], 'ResponseHeadersPolicyCustomHeaderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseHeadersPolicyCustomHeader', 'locationName' => 'ResponseHeadersPolicyCustomHeader', ], ], 'ResponseHeadersPolicyCustomHeadersConfig' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'ResponseHeadersPolicyCustomHeaderList', ], ], ], 'ResponseHeadersPolicyFrameOptions' => [ 'type' => 'structure', 'required' => [ 'Override', 'FrameOption', ], 'members' => [ 'Override' => [ 'shape' => 'boolean', ], 'FrameOption' => [ 'shape' => 'FrameOptionsList', ], ], ], 'ResponseHeadersPolicyInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResponseHeadersPolicyList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'ResponseHeadersPolicySummaryList', ], ], ], 'ResponseHeadersPolicyReferrerPolicy' => [ 'type' => 'structure', 'required' => [ 'Override', 'ReferrerPolicy', ], 'members' => [ 'Override' => [ 'shape' => 'boolean', ], 'ReferrerPolicy' => [ 'shape' => 'ReferrerPolicyList', ], ], ], 'ResponseHeadersPolicyRemoveHeader' => [ 'type' => 'structure', 'required' => [ 'Header', ], 'members' => [ 'Header' => [ 'shape' => 'string', ], ], ], 'ResponseHeadersPolicyRemoveHeaderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseHeadersPolicyRemoveHeader', 'locationName' => 'ResponseHeadersPolicyRemoveHeader', ], ], 'ResponseHeadersPolicyRemoveHeadersConfig' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'ResponseHeadersPolicyRemoveHeaderList', ], ], ], 'ResponseHeadersPolicySecurityHeadersConfig' => [ 'type' => 'structure', 'members' => [ 'XSSProtection' => [ 'shape' => 'ResponseHeadersPolicyXSSProtection', ], 'FrameOptions' => [ 'shape' => 'ResponseHeadersPolicyFrameOptions', ], 'ReferrerPolicy' => [ 'shape' => 'ResponseHeadersPolicyReferrerPolicy', ], 'ContentSecurityPolicy' => [ 'shape' => 'ResponseHeadersPolicyContentSecurityPolicy', ], 'ContentTypeOptions' => [ 'shape' => 'ResponseHeadersPolicyContentTypeOptions', ], 'StrictTransportSecurity' => [ 'shape' => 'ResponseHeadersPolicyStrictTransportSecurity', ], ], ], 'ResponseHeadersPolicyServerTimingHeadersConfig' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'SamplingRate' => [ 'shape' => 'SamplingRate', ], ], ], 'ResponseHeadersPolicyStrictTransportSecurity' => [ 'type' => 'structure', 'required' => [ 'Override', 'AccessControlMaxAgeSec', ], 'members' => [ 'Override' => [ 'shape' => 'boolean', ], 'IncludeSubdomains' => [ 'shape' => 'boolean', ], 'Preload' => [ 'shape' => 'boolean', ], 'AccessControlMaxAgeSec' => [ 'shape' => 'integer', ], ], ], 'ResponseHeadersPolicySummary' => [ 'type' => 'structure', 'required' => [ 'Type', 'ResponseHeadersPolicy', ], 'members' => [ 'Type' => [ 'shape' => 'ResponseHeadersPolicyType', ], 'ResponseHeadersPolicy' => [ 'shape' => 'ResponseHeadersPolicy', ], ], ], 'ResponseHeadersPolicySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseHeadersPolicySummary', 'locationName' => 'ResponseHeadersPolicySummary', ], ], 'ResponseHeadersPolicyType' => [ 'type' => 'string', 'enum' => [ 'managed', 'custom', ], ], 'ResponseHeadersPolicyXSSProtection' => [ 'type' => 'structure', 'required' => [ 'Override', 'Protection', ], 'members' => [ 'Override' => [ 'shape' => 'boolean', ], 'Protection' => [ 'shape' => 'boolean', ], 'ModeBlock' => [ 'shape' => 'boolean', ], 'ReportUri' => [ 'shape' => 'string', ], ], ], 'Restrictions' => [ 'type' => 'structure', 'required' => [ 'GeoRestriction', ], 'members' => [ 'GeoRestriction' => [ 'shape' => 'GeoRestriction', ], ], ], 'S3Origin' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'OriginAccessIdentity', ], 'members' => [ 'DomainName' => [ 'shape' => 'string', ], 'OriginAccessIdentity' => [ 'shape' => 'string', ], ], ], 'S3OriginConfig' => [ 'type' => 'structure', 'required' => [ 'OriginAccessIdentity', ], 'members' => [ 'OriginAccessIdentity' => [ 'shape' => 'string', ], ], ], 'SSLSupportMethod' => [ 'type' => 'string', 'enum' => [ 'sni-only', 'vip', 'static-ip', ], ], 'SamplingRate' => [ 'type' => 'double', 'max' => 100.0, 'min' => 0.0, ], 'SessionStickinessConfig' => [ 'type' => 'structure', 'required' => [ 'IdleTTL', 'MaximumTTL', ], 'members' => [ 'IdleTTL' => [ 'shape' => 'integer', ], 'MaximumTTL' => [ 'shape' => 'integer', ], ], ], 'Signer' => [ 'type' => 'structure', 'members' => [ 'AwsAccountNumber' => [ 'shape' => 'string', ], 'KeyPairIds' => [ 'shape' => 'KeyPairIds', ], ], ], 'SignerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Signer', 'locationName' => 'Signer', ], ], 'SslProtocol' => [ 'type' => 'string', 'enum' => [ 'SSLv3', 'TLSv1', 'TLSv1.1', 'TLSv1.2', ], ], 'SslProtocolsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SslProtocol', 'locationName' => 'SslProtocol', ], ], 'StagingDistributionDnsNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'DnsName', ], ], 'StagingDistributionDnsNames' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'StagingDistributionDnsNameList', ], ], ], 'StagingDistributionInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'StatusCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'integer', 'locationName' => 'StatusCode', ], 'min' => 1, ], 'StatusCodes' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'StatusCodeList', ], ], ], 'StreamingDistribution' => [ 'type' => 'structure', 'required' => [ 'Id', 'ARN', 'Status', 'DomainName', 'ActiveTrustedSigners', 'StreamingDistributionConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'DomainName' => [ 'shape' => 'string', ], 'ActiveTrustedSigners' => [ 'shape' => 'ActiveTrustedSigners', ], 'StreamingDistributionConfig' => [ 'shape' => 'StreamingDistributionConfig', ], ], ], 'StreamingDistributionAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'StreamingDistributionConfig' => [ 'type' => 'structure', 'required' => [ 'CallerReference', 'S3Origin', 'Comment', 'TrustedSigners', 'Enabled', ], 'members' => [ 'CallerReference' => [ 'shape' => 'string', ], 'S3Origin' => [ 'shape' => 'S3Origin', ], 'Aliases' => [ 'shape' => 'Aliases', ], 'Comment' => [ 'shape' => 'string', ], 'Logging' => [ 'shape' => 'StreamingLoggingConfig', ], 'TrustedSigners' => [ 'shape' => 'TrustedSigners', ], 'PriceClass' => [ 'shape' => 'PriceClass', ], 'Enabled' => [ 'shape' => 'boolean', ], ], ], 'StreamingDistributionConfigWithTags' => [ 'type' => 'structure', 'required' => [ 'StreamingDistributionConfig', 'Tags', ], 'members' => [ 'StreamingDistributionConfig' => [ 'shape' => 'StreamingDistributionConfig', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'StreamingDistributionList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'StreamingDistributionSummaryList', ], ], ], 'StreamingDistributionNotDisabled' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'StreamingDistributionSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'ARN', 'Status', 'LastModifiedTime', 'DomainName', 'S3Origin', 'Aliases', 'TrustedSigners', 'Comment', 'PriceClass', 'Enabled', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'DomainName' => [ 'shape' => 'string', ], 'S3Origin' => [ 'shape' => 'S3Origin', ], 'Aliases' => [ 'shape' => 'Aliases', ], 'TrustedSigners' => [ 'shape' => 'TrustedSigners', ], 'Comment' => [ 'shape' => 'string', ], 'PriceClass' => [ 'shape' => 'PriceClass', ], 'Enabled' => [ 'shape' => 'boolean', ], ], ], 'StreamingDistributionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamingDistributionSummary', 'locationName' => 'StreamingDistributionSummary', ], ], 'StreamingLoggingConfig' => [ 'type' => 'structure', 'required' => [ 'Enabled', 'Bucket', 'Prefix', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'Bucket' => [ 'shape' => 'string', ], 'Prefix' => [ 'shape' => 'string', ], ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', 'locationName' => 'Key', ], ], 'TagKeys' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'TagKeyList', ], ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', 'locationName' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', 'Tags', ], 'members' => [ 'Resource' => [ 'shape' => 'ResourceARN', 'location' => 'querystring', 'locationName' => 'Resource', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'Tags', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'Tags', ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'Tags' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'TagList', ], ], ], 'TestFunctionFailed' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'TestFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IfMatch', 'EventObject', ], 'members' => [ 'Name' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Name', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], 'Stage' => [ 'shape' => 'FunctionStage', ], 'EventObject' => [ 'shape' => 'FunctionEventObject', ], ], ], 'TestFunctionResult' => [ 'type' => 'structure', 'members' => [ 'TestResult' => [ 'shape' => 'TestResult', ], ], 'payload' => 'TestResult', ], 'TestResult' => [ 'type' => 'structure', 'members' => [ 'FunctionSummary' => [ 'shape' => 'FunctionSummary', ], 'ComputeUtilization' => [ 'shape' => 'string', ], 'FunctionExecutionLogs' => [ 'shape' => 'FunctionExecutionLogList', ], 'FunctionErrorMessage' => [ 'shape' => 'sensitiveStringType', ], 'FunctionOutput' => [ 'shape' => 'sensitiveStringType', ], ], ], 'TooLongCSPInResponseHeadersPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyCacheBehaviors' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyCachePolicies' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyCertificates' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyCloudFrontOriginAccessIdentities' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyContinuousDeploymentPolicies' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyCookieNamesInWhiteList' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyCookiesInCachePolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyCookiesInOriginRequestPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyCustomHeadersInResponseHeadersPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyDistributionCNAMEs' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyDistributions' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyDistributionsAssociatedToCachePolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyDistributionsAssociatedToFieldLevelEncryptionConfig' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyDistributionsAssociatedToKeyGroup' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyDistributionsAssociatedToOriginAccessControl' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyDistributionsAssociatedToOriginRequestPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyDistributionsAssociatedToResponseHeadersPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyDistributionsWithFunctionAssociations' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyDistributionsWithLambdaAssociations' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyDistributionsWithSingleFunctionARN' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyFieldLevelEncryptionConfigs' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyFieldLevelEncryptionContentTypeProfiles' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyFieldLevelEncryptionEncryptionEntities' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyFieldLevelEncryptionFieldPatterns' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyFieldLevelEncryptionProfiles' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyFieldLevelEncryptionQueryArgProfiles' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyFunctionAssociations' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyFunctions' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyHeadersInCachePolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyHeadersInForwardedValues' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyHeadersInOriginRequestPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyInvalidationsInProgress' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyKeyGroups' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyKeyGroupsAssociatedToDistribution' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyLambdaFunctionAssociations' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyOriginAccessControls' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyOriginCustomHeaders' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyOriginGroupsPerDistribution' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyOriginRequestPolicies' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyOrigins' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyPublicKeys' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyPublicKeysInKeyGroup' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyQueryStringParameters' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyQueryStringsInCachePolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyQueryStringsInOriginRequestPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyRealtimeLogConfigs' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyRemoveHeadersInResponseHeadersPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyResponseHeadersPolicies' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyStreamingDistributionCNAMEs' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyStreamingDistributions' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TooManyTrustedSigners' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TrafficConfig' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'SingleWeightConfig' => [ 'shape' => 'ContinuousDeploymentSingleWeightConfig', ], 'SingleHeaderConfig' => [ 'shape' => 'ContinuousDeploymentSingleHeaderConfig', ], 'Type' => [ 'shape' => 'ContinuousDeploymentPolicyType', ], ], ], 'TrustedKeyGroupDoesNotExist' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TrustedKeyGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'KeyGroup', ], ], 'TrustedKeyGroups' => [ 'type' => 'structure', 'required' => [ 'Enabled', 'Quantity', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'TrustedKeyGroupIdList', ], ], ], 'TrustedSignerDoesNotExist' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TrustedSigners' => [ 'type' => 'structure', 'required' => [ 'Enabled', 'Quantity', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'AwsAccountNumberList', ], ], ], 'UnsupportedOperation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', 'TagKeys', ], 'members' => [ 'Resource' => [ 'shape' => 'ResourceARN', 'location' => 'querystring', 'locationName' => 'Resource', ], 'TagKeys' => [ 'shape' => 'TagKeys', 'locationName' => 'TagKeys', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'TagKeys', ], 'UpdateCachePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'CachePolicyConfig', 'Id', ], 'members' => [ 'CachePolicyConfig' => [ 'shape' => 'CachePolicyConfig', 'locationName' => 'CachePolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'CachePolicyConfig', ], 'UpdateCachePolicyResult' => [ 'type' => 'structure', 'members' => [ 'CachePolicy' => [ 'shape' => 'CachePolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CachePolicy', ], 'UpdateCloudFrontOriginAccessIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'CloudFrontOriginAccessIdentityConfig', 'Id', ], 'members' => [ 'CloudFrontOriginAccessIdentityConfig' => [ 'shape' => 'CloudFrontOriginAccessIdentityConfig', 'locationName' => 'CloudFrontOriginAccessIdentityConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'CloudFrontOriginAccessIdentityConfig', ], 'UpdateCloudFrontOriginAccessIdentityResult' => [ 'type' => 'structure', 'members' => [ 'CloudFrontOriginAccessIdentity' => [ 'shape' => 'CloudFrontOriginAccessIdentity', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CloudFrontOriginAccessIdentity', ], 'UpdateContinuousDeploymentPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ContinuousDeploymentPolicyConfig', 'Id', ], 'members' => [ 'ContinuousDeploymentPolicyConfig' => [ 'shape' => 'ContinuousDeploymentPolicyConfig', 'locationName' => 'ContinuousDeploymentPolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'ContinuousDeploymentPolicyConfig', ], 'UpdateContinuousDeploymentPolicyResult' => [ 'type' => 'structure', 'members' => [ 'ContinuousDeploymentPolicy' => [ 'shape' => 'ContinuousDeploymentPolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ContinuousDeploymentPolicy', ], 'UpdateDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionConfig', 'Id', ], 'members' => [ 'DistributionConfig' => [ 'shape' => 'DistributionConfig', 'locationName' => 'DistributionConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'DistributionConfig', ], 'UpdateDistributionResult' => [ 'type' => 'structure', 'members' => [ 'Distribution' => [ 'shape' => 'Distribution', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'Distribution', ], 'UpdateDistributionWithStagingConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'StagingDistributionId' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'StagingDistributionId', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'UpdateDistributionWithStagingConfigResult' => [ 'type' => 'structure', 'members' => [ 'Distribution' => [ 'shape' => 'Distribution', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'Distribution', ], 'UpdateFieldLevelEncryptionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FieldLevelEncryptionConfig', 'Id', ], 'members' => [ 'FieldLevelEncryptionConfig' => [ 'shape' => 'FieldLevelEncryptionConfig', 'locationName' => 'FieldLevelEncryptionConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'FieldLevelEncryptionConfig', ], 'UpdateFieldLevelEncryptionConfigResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryption' => [ 'shape' => 'FieldLevelEncryption', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryption', ], 'UpdateFieldLevelEncryptionProfileRequest' => [ 'type' => 'structure', 'required' => [ 'FieldLevelEncryptionProfileConfig', 'Id', ], 'members' => [ 'FieldLevelEncryptionProfileConfig' => [ 'shape' => 'FieldLevelEncryptionProfileConfig', 'locationName' => 'FieldLevelEncryptionProfileConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'FieldLevelEncryptionProfileConfig', ], 'UpdateFieldLevelEncryptionProfileResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionProfile' => [ 'shape' => 'FieldLevelEncryptionProfile', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryptionProfile', ], 'UpdateFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'IfMatch', 'FunctionConfig', 'FunctionCode', 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Name', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], 'FunctionConfig' => [ 'shape' => 'FunctionConfig', ], 'FunctionCode' => [ 'shape' => 'FunctionBlob', ], ], ], 'UpdateFunctionResult' => [ 'type' => 'structure', 'members' => [ 'FunctionSummary' => [ 'shape' => 'FunctionSummary', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETtag', ], ], 'payload' => 'FunctionSummary', ], 'UpdateKeyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'KeyGroupConfig', 'Id', ], 'members' => [ 'KeyGroupConfig' => [ 'shape' => 'KeyGroupConfig', 'locationName' => 'KeyGroupConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'KeyGroupConfig', ], 'UpdateKeyGroupResult' => [ 'type' => 'structure', 'members' => [ 'KeyGroup' => [ 'shape' => 'KeyGroup', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'KeyGroup', ], 'UpdateKeyValueStoreRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Comment', 'IfMatch', ], 'members' => [ 'Name' => [ 'shape' => 'KeyValueStoreName', 'location' => 'uri', 'locationName' => 'Name', ], 'Comment' => [ 'shape' => 'KeyValueStoreComment', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'UpdateKeyValueStoreResult' => [ 'type' => 'structure', 'members' => [ 'KeyValueStore' => [ 'shape' => 'KeyValueStore', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'KeyValueStore', ], 'UpdateOriginAccessControlRequest' => [ 'type' => 'structure', 'required' => [ 'OriginAccessControlConfig', 'Id', ], 'members' => [ 'OriginAccessControlConfig' => [ 'shape' => 'OriginAccessControlConfig', 'locationName' => 'OriginAccessControlConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'OriginAccessControlConfig', ], 'UpdateOriginAccessControlResult' => [ 'type' => 'structure', 'members' => [ 'OriginAccessControl' => [ 'shape' => 'OriginAccessControl', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginAccessControl', ], 'UpdateOriginRequestPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'OriginRequestPolicyConfig', 'Id', ], 'members' => [ 'OriginRequestPolicyConfig' => [ 'shape' => 'OriginRequestPolicyConfig', 'locationName' => 'OriginRequestPolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'OriginRequestPolicyConfig', ], 'UpdateOriginRequestPolicyResult' => [ 'type' => 'structure', 'members' => [ 'OriginRequestPolicy' => [ 'shape' => 'OriginRequestPolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginRequestPolicy', ], 'UpdatePublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'PublicKeyConfig', 'Id', ], 'members' => [ 'PublicKeyConfig' => [ 'shape' => 'PublicKeyConfig', 'locationName' => 'PublicKeyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'PublicKeyConfig', ], 'UpdatePublicKeyResult' => [ 'type' => 'structure', 'members' => [ 'PublicKey' => [ 'shape' => 'PublicKey', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'PublicKey', ], 'UpdateRealtimeLogConfigRequest' => [ 'type' => 'structure', 'members' => [ 'EndPoints' => [ 'shape' => 'EndPointList', ], 'Fields' => [ 'shape' => 'FieldList', ], 'Name' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], 'SamplingRate' => [ 'shape' => 'long', ], ], ], 'UpdateRealtimeLogConfigResult' => [ 'type' => 'structure', 'members' => [ 'RealtimeLogConfig' => [ 'shape' => 'RealtimeLogConfig', ], ], ], 'UpdateResponseHeadersPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResponseHeadersPolicyConfig', 'Id', ], 'members' => [ 'ResponseHeadersPolicyConfig' => [ 'shape' => 'ResponseHeadersPolicyConfig', 'locationName' => 'ResponseHeadersPolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'ResponseHeadersPolicyConfig', ], 'UpdateResponseHeadersPolicyResult' => [ 'type' => 'structure', 'members' => [ 'ResponseHeadersPolicy' => [ 'shape' => 'ResponseHeadersPolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ResponseHeadersPolicy', ], 'UpdateStreamingDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'StreamingDistributionConfig', 'Id', ], 'members' => [ 'StreamingDistributionConfig' => [ 'shape' => 'StreamingDistributionConfig', 'locationName' => 'StreamingDistributionConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'StreamingDistributionConfig', ], 'UpdateStreamingDistributionResult' => [ 'type' => 'structure', 'members' => [ 'StreamingDistribution' => [ 'shape' => 'StreamingDistribution', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'StreamingDistribution', ], 'ViewerCertificate' => [ 'type' => 'structure', 'members' => [ 'CloudFrontDefaultCertificate' => [ 'shape' => 'boolean', ], 'IAMCertificateId' => [ 'shape' => 'string', ], 'ACMCertificateArn' => [ 'shape' => 'string', ], 'SSLSupportMethod' => [ 'shape' => 'SSLSupportMethod', ], 'MinimumProtocolVersion' => [ 'shape' => 'MinimumProtocolVersion', ], 'Certificate' => [ 'shape' => 'string', 'deprecated' => true, ], 'CertificateSource' => [ 'shape' => 'CertificateSource', 'deprecated' => true, ], ], ], 'ViewerProtocolPolicy' => [ 'type' => 'string', 'enum' => [ 'allow-all', 'https-only', 'redirect-to-https', ], ], 'aliasString' => [ 'type' => 'string', 'max' => 253, ], 'boolean' => [ 'type' => 'boolean', ], 'distributionIdString' => [ 'type' => 'string', 'max' => 25, ], 'float' => [ 'type' => 'float', ], 'integer' => [ 'type' => 'integer', ], 'listConflictingAliasesMaxItemsInteger' => [ 'type' => 'integer', 'max' => 100, ], 'long' => [ 'type' => 'long', ], 'sensitiveStringType' => [ 'type' => 'string', 'sensitive' => true, ], 'string' => [ 'type' => 'string', ], 'timestamp' => [ 'type' => 'timestamp', ], ],];
