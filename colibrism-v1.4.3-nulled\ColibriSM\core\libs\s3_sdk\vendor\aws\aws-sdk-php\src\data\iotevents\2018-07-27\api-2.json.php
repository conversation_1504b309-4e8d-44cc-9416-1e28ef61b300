<?php
// This file was auto-generated from sdk-root/src/data/iotevents/2018-07-27/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-07-27', 'endpointPrefix' => 'iotevents', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS IoT Events', 'serviceId' => 'IoT Events', 'signatureVersion' => 'v4', 'signingName' => 'iotevents', 'uid' => 'iotevents-2018-07-27', ], 'operations' => [ 'CreateAlarmModel' => [ 'name' => 'CreateAlarmModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/alarm-models', ], 'input' => [ 'shape' => 'CreateAlarmModelRequest', ], 'output' => [ 'shape' => 'CreateAlarmModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateDetectorModel' => [ 'name' => 'CreateDetectorModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector-models', ], 'input' => [ 'shape' => 'CreateDetectorModelRequest', ], 'output' => [ 'shape' => 'CreateDetectorModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateInput' => [ 'name' => 'CreateInput', 'http' => [ 'method' => 'POST', 'requestUri' => '/inputs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateInputRequest', ], 'output' => [ 'shape' => 'CreateInputResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'DeleteAlarmModel' => [ 'name' => 'DeleteAlarmModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/alarm-models/{alarmModelName}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAlarmModelRequest', ], 'output' => [ 'shape' => 'DeleteAlarmModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteDetectorModel' => [ 'name' => 'DeleteDetectorModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/detector-models/{detectorModelName}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteDetectorModelRequest', ], 'output' => [ 'shape' => 'DeleteDetectorModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteInput' => [ 'name' => 'DeleteInput', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/inputs/{inputName}', ], 'input' => [ 'shape' => 'DeleteInputRequest', ], 'output' => [ 'shape' => 'DeleteInputResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DescribeAlarmModel' => [ 'name' => 'DescribeAlarmModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/alarm-models/{alarmModelName}', ], 'input' => [ 'shape' => 'DescribeAlarmModelRequest', ], 'output' => [ 'shape' => 'DescribeAlarmModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeDetectorModel' => [ 'name' => 'DescribeDetectorModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector-models/{detectorModelName}', ], 'input' => [ 'shape' => 'DescribeDetectorModelRequest', ], 'output' => [ 'shape' => 'DescribeDetectorModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeDetectorModelAnalysis' => [ 'name' => 'DescribeDetectorModelAnalysis', 'http' => [ 'method' => 'GET', 'requestUri' => '/analysis/detector-models/{analysisId}', ], 'input' => [ 'shape' => 'DescribeDetectorModelAnalysisRequest', ], 'output' => [ 'shape' => 'DescribeDetectorModelAnalysisResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeInput' => [ 'name' => 'DescribeInput', 'http' => [ 'method' => 'GET', 'requestUri' => '/inputs/{inputName}', ], 'input' => [ 'shape' => 'DescribeInputRequest', ], 'output' => [ 'shape' => 'DescribeInputResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeLoggingOptions' => [ 'name' => 'DescribeLoggingOptions', 'http' => [ 'method' => 'GET', 'requestUri' => '/logging', ], 'input' => [ 'shape' => 'DescribeLoggingOptionsRequest', ], 'output' => [ 'shape' => 'DescribeLoggingOptionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'GetDetectorModelAnalysisResults' => [ 'name' => 'GetDetectorModelAnalysisResults', 'http' => [ 'method' => 'GET', 'requestUri' => '/analysis/detector-models/{analysisId}/results', ], 'input' => [ 'shape' => 'GetDetectorModelAnalysisResultsRequest', ], 'output' => [ 'shape' => 'GetDetectorModelAnalysisResultsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListAlarmModelVersions' => [ 'name' => 'ListAlarmModelVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/alarm-models/{alarmModelName}/versions', ], 'input' => [ 'shape' => 'ListAlarmModelVersionsRequest', ], 'output' => [ 'shape' => 'ListAlarmModelVersionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListAlarmModels' => [ 'name' => 'ListAlarmModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/alarm-models', ], 'input' => [ 'shape' => 'ListAlarmModelsRequest', ], 'output' => [ 'shape' => 'ListAlarmModelsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListDetectorModelVersions' => [ 'name' => 'ListDetectorModelVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector-models/{detectorModelName}/versions', ], 'input' => [ 'shape' => 'ListDetectorModelVersionsRequest', ], 'output' => [ 'shape' => 'ListDetectorModelVersionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListDetectorModels' => [ 'name' => 'ListDetectorModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector-models', ], 'input' => [ 'shape' => 'ListDetectorModelsRequest', ], 'output' => [ 'shape' => 'ListDetectorModelsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListInputRoutings' => [ 'name' => 'ListInputRoutings', 'http' => [ 'method' => 'POST', 'requestUri' => '/input-routings', ], 'input' => [ 'shape' => 'ListInputRoutingsRequest', ], 'output' => [ 'shape' => 'ListInputRoutingsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListInputs' => [ 'name' => 'ListInputs', 'http' => [ 'method' => 'GET', 'requestUri' => '/inputs', ], 'input' => [ 'shape' => 'ListInputsRequest', ], 'output' => [ 'shape' => 'ListInputsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'PutLoggingOptions' => [ 'name' => 'PutLoggingOptions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/logging', ], 'input' => [ 'shape' => 'PutLoggingOptionsRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'StartDetectorModelAnalysis' => [ 'name' => 'StartDetectorModelAnalysis', 'http' => [ 'method' => 'POST', 'requestUri' => '/analysis/detector-models/', ], 'input' => [ 'shape' => 'StartDetectorModelAnalysisRequest', ], 'output' => [ 'shape' => 'StartDetectorModelAnalysisResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateAlarmModel' => [ 'name' => 'UpdateAlarmModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/alarm-models/{alarmModelName}', ], 'input' => [ 'shape' => 'UpdateAlarmModelRequest', ], 'output' => [ 'shape' => 'UpdateAlarmModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateDetectorModel' => [ 'name' => 'UpdateDetectorModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector-models/{detectorModelName}', ], 'input' => [ 'shape' => 'UpdateDetectorModelRequest', ], 'output' => [ 'shape' => 'UpdateDetectorModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateInput' => [ 'name' => 'UpdateInput', 'http' => [ 'method' => 'PUT', 'requestUri' => '/inputs/{inputName}', ], 'input' => [ 'shape' => 'UpdateInputRequest', ], 'output' => [ 'shape' => 'UpdateInputResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceInUseException', ], ], ], ], 'shapes' => [ 'AcknowledgeFlow' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'AcknowledgeFlowEnabled', ], ], ], 'AcknowledgeFlowEnabled' => [ 'type' => 'boolean', 'box' => true, ], 'Action' => [ 'type' => 'structure', 'members' => [ 'setVariable' => [ 'shape' => 'SetVariableAction', ], 'sns' => [ 'shape' => 'SNSTopicPublishAction', ], 'iotTopicPublish' => [ 'shape' => 'IotTopicPublishAction', ], 'setTimer' => [ 'shape' => 'SetTimerAction', ], 'clearTimer' => [ 'shape' => 'ClearTimerAction', ], 'resetTimer' => [ 'shape' => 'ResetTimerAction', ], 'lambda' => [ 'shape' => 'LambdaAction', ], 'iotEvents' => [ 'shape' => 'IotEventsAction', ], 'sqs' => [ 'shape' => 'SqsAction', ], 'firehose' => [ 'shape' => 'FirehoseAction', ], 'dynamoDB' => [ 'shape' => 'DynamoDBAction', ], 'dynamoDBv2' => [ 'shape' => 'DynamoDBv2Action', ], 'iotSiteWise' => [ 'shape' => 'IotSiteWiseAction', ], ], ], 'Actions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Action', ], ], 'AlarmAction' => [ 'type' => 'structure', 'members' => [ 'sns' => [ 'shape' => 'SNSTopicPublishAction', ], 'iotTopicPublish' => [ 'shape' => 'IotTopicPublishAction', ], 'lambda' => [ 'shape' => 'LambdaAction', ], 'iotEvents' => [ 'shape' => 'IotEventsAction', ], 'sqs' => [ 'shape' => 'SqsAction', ], 'firehose' => [ 'shape' => 'FirehoseAction', ], 'dynamoDB' => [ 'shape' => 'DynamoDBAction', ], 'dynamoDBv2' => [ 'shape' => 'DynamoDBv2Action', ], 'iotSiteWise' => [ 'shape' => 'IotSiteWiseAction', ], ], ], 'AlarmActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlarmAction', ], ], 'AlarmCapabilities' => [ 'type' => 'structure', 'members' => [ 'initializationConfiguration' => [ 'shape' => 'InitializationConfiguration', ], 'acknowledgeFlow' => [ 'shape' => 'AcknowledgeFlow', ], ], ], 'AlarmEventActions' => [ 'type' => 'structure', 'members' => [ 'alarmActions' => [ 'shape' => 'AlarmActions', ], ], ], 'AlarmModelArn' => [ 'type' => 'string', ], 'AlarmModelDescription' => [ 'type' => 'string', 'max' => 128, ], 'AlarmModelName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_-]+$', ], 'AlarmModelSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlarmModelSummary', ], ], 'AlarmModelSummary' => [ 'type' => 'structure', 'members' => [ 'creationTime' => [ 'shape' => 'Timestamp', ], 'alarmModelDescription' => [ 'shape' => 'AlarmModelDescription', ], 'alarmModelName' => [ 'shape' => 'AlarmModelName', ], ], ], 'AlarmModelVersion' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'AlarmModelVersionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ACTIVATING', 'INACTIVE', 'FAILED', ], ], 'AlarmModelVersionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlarmModelVersionSummary', ], ], 'AlarmModelVersionSummary' => [ 'type' => 'structure', 'members' => [ 'alarmModelName' => [ 'shape' => 'AlarmModelName', ], 'alarmModelArn' => [ 'shape' => 'AlarmModelArn', ], 'alarmModelVersion' => [ 'shape' => 'AlarmModelVersion', ], 'roleArn' => [ 'shape' => 'AmazonResourceName', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AlarmModelVersionStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], ], ], 'AlarmNotification' => [ 'type' => 'structure', 'members' => [ 'notificationActions' => [ 'shape' => 'NotificationActions', ], ], ], 'AlarmRule' => [ 'type' => 'structure', 'members' => [ 'simpleRule' => [ 'shape' => 'SimpleRule', ], ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'AnalysisId' => [ 'type' => 'string', ], 'AnalysisMessage' => [ 'type' => 'string', ], 'AnalysisResult' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'AnalysisType', ], 'level' => [ 'shape' => 'AnalysisResultLevel', ], 'message' => [ 'shape' => 'AnalysisMessage', ], 'locations' => [ 'shape' => 'AnalysisResultLocations', ], ], ], 'AnalysisResultLevel' => [ 'type' => 'string', 'enum' => [ 'INFO', 'WARNING', 'ERROR', ], ], 'AnalysisResultLocation' => [ 'type' => 'structure', 'members' => [ 'path' => [ 'shape' => 'AnalysisResultLocationPath', ], ], ], 'AnalysisResultLocationPath' => [ 'type' => 'string', ], 'AnalysisResultLocations' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisResultLocation', ], ], 'AnalysisResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisResult', ], ], 'AnalysisStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'COMPLETE', 'FAILED', ], ], 'AnalysisType' => [ 'type' => 'string', ], 'AssetId' => [ 'type' => 'string', ], 'AssetModelId' => [ 'type' => 'string', ], 'AssetPropertyAlias' => [ 'type' => 'string', ], 'AssetPropertyBooleanValue' => [ 'type' => 'string', ], 'AssetPropertyDoubleValue' => [ 'type' => 'string', ], 'AssetPropertyEntryId' => [ 'type' => 'string', ], 'AssetPropertyId' => [ 'type' => 'string', ], 'AssetPropertyIntegerValue' => [ 'type' => 'string', ], 'AssetPropertyOffsetInNanos' => [ 'type' => 'string', ], 'AssetPropertyQuality' => [ 'type' => 'string', ], 'AssetPropertyStringValue' => [ 'type' => 'string', ], 'AssetPropertyTimeInSeconds' => [ 'type' => 'string', ], 'AssetPropertyTimestamp' => [ 'type' => 'structure', 'required' => [ 'timeInSeconds', ], 'members' => [ 'timeInSeconds' => [ 'shape' => 'AssetPropertyTimeInSeconds', ], 'offsetInNanos' => [ 'shape' => 'AssetPropertyOffsetInNanos', ], ], ], 'AssetPropertyValue' => [ 'type' => 'structure', 'members' => [ 'value' => [ 'shape' => 'AssetPropertyVariant', ], 'timestamp' => [ 'shape' => 'AssetPropertyTimestamp', ], 'quality' => [ 'shape' => 'AssetPropertyQuality', ], ], ], 'AssetPropertyVariant' => [ 'type' => 'structure', 'members' => [ 'stringValue' => [ 'shape' => 'AssetPropertyStringValue', ], 'integerValue' => [ 'shape' => 'AssetPropertyIntegerValue', ], 'doubleValue' => [ 'shape' => 'AssetPropertyDoubleValue', ], 'booleanValue' => [ 'shape' => 'AssetPropertyBooleanValue', ], ], ], 'Attribute' => [ 'type' => 'structure', 'required' => [ 'jsonPath', ], 'members' => [ 'jsonPath' => [ 'shape' => 'AttributeJsonPath', ], ], ], 'AttributeJsonPath' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^((`[\\w\\- ]+`)|([\\w\\-]+))(\\.((`[\\w- ]+`)|([\\w\\-]+)))*$', ], 'Attributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attribute', ], 'max' => 200, 'min' => 1, ], 'ClearTimerAction' => [ 'type' => 'structure', 'required' => [ 'timerName', ], 'members' => [ 'timerName' => [ 'shape' => 'TimerName', ], ], ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'GREATER', 'GREATER_OR_EQUAL', 'LESS', 'LESS_OR_EQUAL', 'EQUAL', 'NOT_EQUAL', ], ], 'Condition' => [ 'type' => 'string', 'max' => 512, ], 'ContentExpression' => [ 'type' => 'string', 'min' => 1, ], 'CreateAlarmModelRequest' => [ 'type' => 'structure', 'required' => [ 'alarmModelName', 'roleArn', 'alarmRule', ], 'members' => [ 'alarmModelName' => [ 'shape' => 'AlarmModelName', ], 'alarmModelDescription' => [ 'shape' => 'AlarmModelDescription', ], 'roleArn' => [ 'shape' => 'AmazonResourceName', ], 'tags' => [ 'shape' => 'Tags', ], 'key' => [ 'shape' => 'AttributeJsonPath', ], 'severity' => [ 'shape' => 'Severity', ], 'alarmRule' => [ 'shape' => 'AlarmRule', ], 'alarmNotification' => [ 'shape' => 'AlarmNotification', ], 'alarmEventActions' => [ 'shape' => 'AlarmEventActions', ], 'alarmCapabilities' => [ 'shape' => 'AlarmCapabilities', ], ], ], 'CreateAlarmModelResponse' => [ 'type' => 'structure', 'members' => [ 'creationTime' => [ 'shape' => 'Timestamp', ], 'alarmModelArn' => [ 'shape' => 'AlarmModelArn', ], 'alarmModelVersion' => [ 'shape' => 'AlarmModelVersion', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AlarmModelVersionStatus', ], ], ], 'CreateDetectorModelRequest' => [ 'type' => 'structure', 'required' => [ 'detectorModelName', 'detectorModelDefinition', 'roleArn', ], 'members' => [ 'detectorModelName' => [ 'shape' => 'DetectorModelName', ], 'detectorModelDefinition' => [ 'shape' => 'DetectorModelDefinition', ], 'detectorModelDescription' => [ 'shape' => 'DetectorModelDescription', ], 'key' => [ 'shape' => 'AttributeJsonPath', ], 'roleArn' => [ 'shape' => 'AmazonResourceName', ], 'tags' => [ 'shape' => 'Tags', ], 'evaluationMethod' => [ 'shape' => 'EvaluationMethod', ], ], ], 'CreateDetectorModelResponse' => [ 'type' => 'structure', 'members' => [ 'detectorModelConfiguration' => [ 'shape' => 'DetectorModelConfiguration', ], ], ], 'CreateInputRequest' => [ 'type' => 'structure', 'required' => [ 'inputName', 'inputDefinition', ], 'members' => [ 'inputName' => [ 'shape' => 'InputName', ], 'inputDescription' => [ 'shape' => 'InputDescription', ], 'inputDefinition' => [ 'shape' => 'InputDefinition', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateInputResponse' => [ 'type' => 'structure', 'members' => [ 'inputConfiguration' => [ 'shape' => 'InputConfiguration', ], ], ], 'DeleteAlarmModelRequest' => [ 'type' => 'structure', 'required' => [ 'alarmModelName', ], 'members' => [ 'alarmModelName' => [ 'shape' => 'AlarmModelName', 'location' => 'uri', 'locationName' => 'alarmModelName', ], ], ], 'DeleteAlarmModelResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDetectorModelRequest' => [ 'type' => 'structure', 'required' => [ 'detectorModelName', ], 'members' => [ 'detectorModelName' => [ 'shape' => 'DetectorModelName', 'location' => 'uri', 'locationName' => 'detectorModelName', ], ], ], 'DeleteDetectorModelResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInputRequest' => [ 'type' => 'structure', 'required' => [ 'inputName', ], 'members' => [ 'inputName' => [ 'shape' => 'InputName', 'location' => 'uri', 'locationName' => 'inputName', ], ], ], 'DeleteInputResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeliveryStreamName' => [ 'type' => 'string', ], 'DescribeAlarmModelRequest' => [ 'type' => 'structure', 'required' => [ 'alarmModelName', ], 'members' => [ 'alarmModelName' => [ 'shape' => 'AlarmModelName', 'location' => 'uri', 'locationName' => 'alarmModelName', ], 'alarmModelVersion' => [ 'shape' => 'AlarmModelVersion', 'location' => 'querystring', 'locationName' => 'version', ], ], ], 'DescribeAlarmModelResponse' => [ 'type' => 'structure', 'members' => [ 'creationTime' => [ 'shape' => 'Timestamp', ], 'alarmModelArn' => [ 'shape' => 'AlarmModelArn', ], 'alarmModelVersion' => [ 'shape' => 'AlarmModelVersion', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AlarmModelVersionStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'alarmModelName' => [ 'shape' => 'AlarmModelName', ], 'alarmModelDescription' => [ 'shape' => 'AlarmModelDescription', ], 'roleArn' => [ 'shape' => 'AmazonResourceName', ], 'key' => [ 'shape' => 'AttributeJsonPath', ], 'severity' => [ 'shape' => 'Severity', ], 'alarmRule' => [ 'shape' => 'AlarmRule', ], 'alarmNotification' => [ 'shape' => 'AlarmNotification', ], 'alarmEventActions' => [ 'shape' => 'AlarmEventActions', ], 'alarmCapabilities' => [ 'shape' => 'AlarmCapabilities', ], ], ], 'DescribeDetectorModelAnalysisRequest' => [ 'type' => 'structure', 'required' => [ 'analysisId', ], 'members' => [ 'analysisId' => [ 'shape' => 'AnalysisId', 'location' => 'uri', 'locationName' => 'analysisId', ], ], ], 'DescribeDetectorModelAnalysisResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'AnalysisStatus', ], ], ], 'DescribeDetectorModelRequest' => [ 'type' => 'structure', 'required' => [ 'detectorModelName', ], 'members' => [ 'detectorModelName' => [ 'shape' => 'DetectorModelName', 'location' => 'uri', 'locationName' => 'detectorModelName', ], 'detectorModelVersion' => [ 'shape' => 'DetectorModelVersion', 'location' => 'querystring', 'locationName' => 'version', ], ], ], 'DescribeDetectorModelResponse' => [ 'type' => 'structure', 'members' => [ 'detectorModel' => [ 'shape' => 'DetectorModel', ], ], ], 'DescribeInputRequest' => [ 'type' => 'structure', 'required' => [ 'inputName', ], 'members' => [ 'inputName' => [ 'shape' => 'InputName', 'location' => 'uri', 'locationName' => 'inputName', ], ], ], 'DescribeInputResponse' => [ 'type' => 'structure', 'members' => [ 'input' => [ 'shape' => 'Input', ], ], ], 'DescribeLoggingOptionsRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeLoggingOptionsResponse' => [ 'type' => 'structure', 'members' => [ 'loggingOptions' => [ 'shape' => 'LoggingOptions', ], ], ], 'DetectorDebugOption' => [ 'type' => 'structure', 'required' => [ 'detectorModelName', ], 'members' => [ 'detectorModelName' => [ 'shape' => 'DetectorModelName', ], 'keyValue' => [ 'shape' => 'KeyValue', ], ], ], 'DetectorDebugOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectorDebugOption', ], 'min' => 1, ], 'DetectorModel' => [ 'type' => 'structure', 'members' => [ 'detectorModelDefinition' => [ 'shape' => 'DetectorModelDefinition', ], 'detectorModelConfiguration' => [ 'shape' => 'DetectorModelConfiguration', ], ], ], 'DetectorModelArn' => [ 'type' => 'string', ], 'DetectorModelConfiguration' => [ 'type' => 'structure', 'members' => [ 'detectorModelName' => [ 'shape' => 'DetectorModelName', ], 'detectorModelVersion' => [ 'shape' => 'DetectorModelVersion', ], 'detectorModelDescription' => [ 'shape' => 'DetectorModelDescription', ], 'detectorModelArn' => [ 'shape' => 'DetectorModelArn', ], 'roleArn' => [ 'shape' => 'AmazonResourceName', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'DetectorModelVersionStatus', ], 'key' => [ 'shape' => 'AttributeJsonPath', ], 'evaluationMethod' => [ 'shape' => 'EvaluationMethod', ], ], ], 'DetectorModelDefinition' => [ 'type' => 'structure', 'required' => [ 'states', 'initialStateName', ], 'members' => [ 'states' => [ 'shape' => 'States', ], 'initialStateName' => [ 'shape' => 'StateName', ], ], ], 'DetectorModelDescription' => [ 'type' => 'string', 'max' => 128, ], 'DetectorModelName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_-]+$', ], 'DetectorModelSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectorModelSummary', ], ], 'DetectorModelSummary' => [ 'type' => 'structure', 'members' => [ 'detectorModelName' => [ 'shape' => 'DetectorModelName', ], 'detectorModelDescription' => [ 'shape' => 'DetectorModelDescription', ], 'creationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DetectorModelVersion' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'DetectorModelVersionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ACTIVATING', 'INACTIVE', 'DEPRECATED', 'DRAFT', 'PAUSED', 'FAILED', ], ], 'DetectorModelVersionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectorModelVersionSummary', ], ], 'DetectorModelVersionSummary' => [ 'type' => 'structure', 'members' => [ 'detectorModelName' => [ 'shape' => 'DetectorModelName', ], 'detectorModelVersion' => [ 'shape' => 'DetectorModelVersion', ], 'detectorModelArn' => [ 'shape' => 'DetectorModelArn', ], 'roleArn' => [ 'shape' => 'AmazonResourceName', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'DetectorModelVersionStatus', ], 'evaluationMethod' => [ 'shape' => 'EvaluationMethod', ], ], ], 'DisabledOnInitialization' => [ 'type' => 'boolean', 'box' => true, ], 'DynamoDBAction' => [ 'type' => 'structure', 'required' => [ 'hashKeyField', 'hashKeyValue', 'tableName', ], 'members' => [ 'hashKeyType' => [ 'shape' => 'DynamoKeyType', ], 'hashKeyField' => [ 'shape' => 'DynamoKeyField', ], 'hashKeyValue' => [ 'shape' => 'DynamoKeyValue', ], 'rangeKeyType' => [ 'shape' => 'DynamoKeyType', ], 'rangeKeyField' => [ 'shape' => 'DynamoKeyField', ], 'rangeKeyValue' => [ 'shape' => 'DynamoKeyValue', ], 'operation' => [ 'shape' => 'DynamoOperation', ], 'payloadField' => [ 'shape' => 'DynamoKeyField', ], 'tableName' => [ 'shape' => 'DynamoTableName', ], 'payload' => [ 'shape' => 'Payload', ], ], ], 'DynamoDBv2Action' => [ 'type' => 'structure', 'required' => [ 'tableName', ], 'members' => [ 'tableName' => [ 'shape' => 'DynamoTableName', ], 'payload' => [ 'shape' => 'Payload', ], ], ], 'DynamoKeyField' => [ 'type' => 'string', ], 'DynamoKeyType' => [ 'type' => 'string', ], 'DynamoKeyValue' => [ 'type' => 'string', ], 'DynamoOperation' => [ 'type' => 'string', ], 'DynamoTableName' => [ 'type' => 'string', ], 'EmailConfiguration' => [ 'type' => 'structure', 'required' => [ 'from', 'recipients', ], 'members' => [ 'from' => [ 'shape' => 'FromEmail', ], 'content' => [ 'shape' => 'EmailContent', ], 'recipients' => [ 'shape' => 'EmailRecipients', ], ], ], 'EmailConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailConfiguration', ], 'min' => 1, ], 'EmailContent' => [ 'type' => 'structure', 'members' => [ 'subject' => [ 'shape' => 'EmailSubject', ], 'additionalMessage' => [ 'shape' => 'NotificationAdditionalMessage', ], ], ], 'EmailRecipients' => [ 'type' => 'structure', 'members' => [ 'to' => [ 'shape' => 'RecipientDetails', ], ], ], 'EmailSubject' => [ 'type' => 'string', ], 'EvaluationMethod' => [ 'type' => 'string', 'enum' => [ 'BATCH', 'SERIAL', ], ], 'Event' => [ 'type' => 'structure', 'required' => [ 'eventName', ], 'members' => [ 'eventName' => [ 'shape' => 'EventName', ], 'condition' => [ 'shape' => 'Condition', ], 'actions' => [ 'shape' => 'Actions', ], ], ], 'EventName' => [ 'type' => 'string', 'max' => 128, ], 'Events' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', ], ], 'FirehoseAction' => [ 'type' => 'structure', 'required' => [ 'deliveryStreamName', ], 'members' => [ 'deliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'separator' => [ 'shape' => 'FirehoseSeparator', ], 'payload' => [ 'shape' => 'Payload', ], ], ], 'FirehoseSeparator' => [ 'type' => 'string', 'pattern' => '([\\n\\t])|(\\r\\n)|(,)', ], 'FromEmail' => [ 'type' => 'string', ], 'GetDetectorModelAnalysisResultsRequest' => [ 'type' => 'structure', 'required' => [ 'analysisId', ], 'members' => [ 'analysisId' => [ 'shape' => 'AnalysisId', 'location' => 'uri', 'locationName' => 'analysisId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxAnalysisResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetDetectorModelAnalysisResultsResponse' => [ 'type' => 'structure', 'members' => [ 'analysisResults' => [ 'shape' => 'AnalysisResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'IdentityStoreId' => [ 'type' => 'string', ], 'InitializationConfiguration' => [ 'type' => 'structure', 'required' => [ 'disabledOnInitialization', ], 'members' => [ 'disabledOnInitialization' => [ 'shape' => 'DisabledOnInitialization', ], ], ], 'Input' => [ 'type' => 'structure', 'members' => [ 'inputConfiguration' => [ 'shape' => 'InputConfiguration', ], 'inputDefinition' => [ 'shape' => 'InputDefinition', ], ], ], 'InputArn' => [ 'type' => 'string', ], 'InputConfiguration' => [ 'type' => 'structure', 'required' => [ 'inputName', 'inputArn', 'creationTime', 'lastUpdateTime', 'status', ], 'members' => [ 'inputName' => [ 'shape' => 'InputName', ], 'inputDescription' => [ 'shape' => 'InputDescription', ], 'inputArn' => [ 'shape' => 'InputArn', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'InputStatus', ], ], ], 'InputDefinition' => [ 'type' => 'structure', 'required' => [ 'attributes', ], 'members' => [ 'attributes' => [ 'shape' => 'Attributes', ], ], ], 'InputDescription' => [ 'type' => 'string', 'max' => 128, ], 'InputIdentifier' => [ 'type' => 'structure', 'members' => [ 'iotEventsInputIdentifier' => [ 'shape' => 'IotEventsInputIdentifier', ], 'iotSiteWiseInputIdentifier' => [ 'shape' => 'IotSiteWiseInputIdentifier', ], ], ], 'InputName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z][a-zA-Z0-9_]*$', ], 'InputProperty' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'InputStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'ACTIVE', 'DELETING', ], ], 'InputSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputSummary', ], ], 'InputSummary' => [ 'type' => 'structure', 'members' => [ 'inputName' => [ 'shape' => 'InputName', ], 'inputDescription' => [ 'shape' => 'InputDescription', ], 'inputArn' => [ 'shape' => 'InputArn', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'InputStatus', ], ], ], 'InternalFailureException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'IotEventsAction' => [ 'type' => 'structure', 'required' => [ 'inputName', ], 'members' => [ 'inputName' => [ 'shape' => 'InputName', ], 'payload' => [ 'shape' => 'Payload', ], ], ], 'IotEventsInputIdentifier' => [ 'type' => 'structure', 'required' => [ 'inputName', ], 'members' => [ 'inputName' => [ 'shape' => 'InputName', ], ], ], 'IotSiteWiseAction' => [ 'type' => 'structure', 'members' => [ 'entryId' => [ 'shape' => 'AssetPropertyEntryId', ], 'assetId' => [ 'shape' => 'AssetId', ], 'propertyId' => [ 'shape' => 'AssetPropertyId', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', ], 'propertyValue' => [ 'shape' => 'AssetPropertyValue', ], ], ], 'IotSiteWiseAssetModelPropertyIdentifier' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'propertyId', ], 'members' => [ 'assetModelId' => [ 'shape' => 'AssetModelId', ], 'propertyId' => [ 'shape' => 'AssetPropertyId', ], ], ], 'IotSiteWiseInputIdentifier' => [ 'type' => 'structure', 'members' => [ 'iotSiteWiseAssetModelPropertyIdentifier' => [ 'shape' => 'IotSiteWiseAssetModelPropertyIdentifier', ], ], ], 'IotTopicPublishAction' => [ 'type' => 'structure', 'required' => [ 'mqttTopic', ], 'members' => [ 'mqttTopic' => [ 'shape' => 'MQTTTopic', ], 'payload' => [ 'shape' => 'Payload', ], ], ], 'KeyValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-_:]+$', ], 'LambdaAction' => [ 'type' => 'structure', 'required' => [ 'functionArn', ], 'members' => [ 'functionArn' => [ 'shape' => 'AmazonResourceName', ], 'payload' => [ 'shape' => 'Payload', ], ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], 'ListAlarmModelVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'alarmModelName', ], 'members' => [ 'alarmModelName' => [ 'shape' => 'AlarmModelName', 'location' => 'uri', 'locationName' => 'alarmModelName', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAlarmModelVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'alarmModelVersionSummaries' => [ 'shape' => 'AlarmModelVersionSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAlarmModelsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAlarmModelsResponse' => [ 'type' => 'structure', 'members' => [ 'alarmModelSummaries' => [ 'shape' => 'AlarmModelSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDetectorModelVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'detectorModelName', ], 'members' => [ 'detectorModelName' => [ 'shape' => 'DetectorModelName', 'location' => 'uri', 'locationName' => 'detectorModelName', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDetectorModelVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'detectorModelVersionSummaries' => [ 'shape' => 'DetectorModelVersionSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDetectorModelsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDetectorModelsResponse' => [ 'type' => 'structure', 'members' => [ 'detectorModelSummaries' => [ 'shape' => 'DetectorModelSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInputRoutingsRequest' => [ 'type' => 'structure', 'required' => [ 'inputIdentifier', ], 'members' => [ 'inputIdentifier' => [ 'shape' => 'InputIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInputRoutingsResponse' => [ 'type' => 'structure', 'members' => [ 'routedResources' => [ 'shape' => 'RoutedResources', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInputsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListInputsResponse' => [ 'type' => 'structure', 'members' => [ 'inputSummaries' => [ 'shape' => 'InputSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'LoggingEnabled' => [ 'type' => 'boolean', ], 'LoggingLevel' => [ 'type' => 'string', 'enum' => [ 'ERROR', 'INFO', 'DEBUG', ], ], 'LoggingOptions' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'level', 'enabled', ], 'members' => [ 'roleArn' => [ 'shape' => 'AmazonResourceName', ], 'level' => [ 'shape' => 'LoggingLevel', ], 'enabled' => [ 'shape' => 'LoggingEnabled', ], 'detectorDebugOptions' => [ 'shape' => 'DetectorDebugOptions', ], ], ], 'MQTTTopic' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'MaxAnalysisResults' => [ 'type' => 'integer', ], 'MaxResults' => [ 'type' => 'integer', 'max' => 250, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', ], 'NotificationAction' => [ 'type' => 'structure', 'required' => [ 'action', ], 'members' => [ 'action' => [ 'shape' => 'NotificationTargetActions', ], 'smsConfigurations' => [ 'shape' => 'SMSConfigurations', ], 'emailConfigurations' => [ 'shape' => 'EmailConfigurations', ], ], ], 'NotificationActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationAction', ], 'min' => 1, ], 'NotificationAdditionalMessage' => [ 'type' => 'string', ], 'NotificationTargetActions' => [ 'type' => 'structure', 'members' => [ 'lambdaAction' => [ 'shape' => 'LambdaAction', ], ], ], 'OnEnterLifecycle' => [ 'type' => 'structure', 'members' => [ 'events' => [ 'shape' => 'Events', ], ], ], 'OnExitLifecycle' => [ 'type' => 'structure', 'members' => [ 'events' => [ 'shape' => 'Events', ], ], ], 'OnInputLifecycle' => [ 'type' => 'structure', 'members' => [ 'events' => [ 'shape' => 'Events', ], 'transitionEvents' => [ 'shape' => 'TransitionEvents', ], ], ], 'Payload' => [ 'type' => 'structure', 'required' => [ 'contentExpression', 'type', ], 'members' => [ 'contentExpression' => [ 'shape' => 'ContentExpression', ], 'type' => [ 'shape' => 'PayloadType', ], ], ], 'PayloadType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'JSON', ], ], 'PutLoggingOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'loggingOptions', ], 'members' => [ 'loggingOptions' => [ 'shape' => 'LoggingOptions', ], ], ], 'QueueUrl' => [ 'type' => 'string', ], 'RecipientDetail' => [ 'type' => 'structure', 'members' => [ 'ssoIdentity' => [ 'shape' => 'SSOIdentity', ], ], ], 'RecipientDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecipientDetail', ], 'min' => 1, ], 'ResetTimerAction' => [ 'type' => 'structure', 'required' => [ 'timerName', ], 'members' => [ 'timerName' => [ 'shape' => 'TimerName', ], ], ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], 'resourceId' => [ 'shape' => 'resourceId', ], 'resourceArn' => [ 'shape' => 'resourceArn', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceName' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'RoutedResource' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'RoutedResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutedResource', ], ], 'SMSConfiguration' => [ 'type' => 'structure', 'required' => [ 'recipients', ], 'members' => [ 'senderId' => [ 'shape' => 'SMSSenderId', ], 'additionalMessage' => [ 'shape' => 'NotificationAdditionalMessage', ], 'recipients' => [ 'shape' => 'RecipientDetails', ], ], ], 'SMSConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'SMSConfiguration', ], 'min' => 1, ], 'SMSSenderId' => [ 'type' => 'string', ], 'SNSTopicPublishAction' => [ 'type' => 'structure', 'required' => [ 'targetArn', ], 'members' => [ 'targetArn' => [ 'shape' => 'AmazonResourceName', ], 'payload' => [ 'shape' => 'Payload', ], ], ], 'SSOIdentity' => [ 'type' => 'structure', 'required' => [ 'identityStoreId', ], 'members' => [ 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'userId' => [ 'shape' => 'SSOReferenceId', ], ], ], 'SSOReferenceId' => [ 'type' => 'string', ], 'Seconds' => [ 'type' => 'integer', 'max' => 31622400, 'min' => 1, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'SetTimerAction' => [ 'type' => 'structure', 'required' => [ 'timerName', ], 'members' => [ 'timerName' => [ 'shape' => 'TimerName', ], 'seconds' => [ 'shape' => 'Seconds', 'deprecated' => true, 'deprecatedMessage' => 'seconds is deprecated. You can use durationExpression for SetTimerAction. The value of seconds can be used as a string expression for durationExpression.', ], 'durationExpression' => [ 'shape' => 'VariableValue', ], ], ], 'SetVariableAction' => [ 'type' => 'structure', 'required' => [ 'variableName', 'value', ], 'members' => [ 'variableName' => [ 'shape' => 'VariableName', ], 'value' => [ 'shape' => 'VariableValue', ], ], ], 'Severity' => [ 'type' => 'integer', 'box' => true, 'max' => 2147483647, 'min' => 0, ], 'SimpleRule' => [ 'type' => 'structure', 'required' => [ 'inputProperty', 'comparisonOperator', 'threshold', ], 'members' => [ 'inputProperty' => [ 'shape' => 'InputProperty', ], 'comparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'threshold' => [ 'shape' => 'Threshold', ], ], ], 'SqsAction' => [ 'type' => 'structure', 'required' => [ 'queueUrl', ], 'members' => [ 'queueUrl' => [ 'shape' => 'QueueUrl', ], 'useBase64' => [ 'shape' => 'UseBase64', ], 'payload' => [ 'shape' => 'Payload', ], ], ], 'StartDetectorModelAnalysisRequest' => [ 'type' => 'structure', 'required' => [ 'detectorModelDefinition', ], 'members' => [ 'detectorModelDefinition' => [ 'shape' => 'DetectorModelDefinition', ], ], ], 'StartDetectorModelAnalysisResponse' => [ 'type' => 'structure', 'members' => [ 'analysisId' => [ 'shape' => 'AnalysisId', ], ], ], 'State' => [ 'type' => 'structure', 'required' => [ 'stateName', ], 'members' => [ 'stateName' => [ 'shape' => 'StateName', ], 'onInput' => [ 'shape' => 'OnInputLifecycle', ], 'onEnter' => [ 'shape' => 'OnEnterLifecycle', ], 'onExit' => [ 'shape' => 'OnExitLifecycle', ], ], ], 'StateName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'States' => [ 'type' => 'list', 'member' => [ 'shape' => 'State', ], 'min' => 1, ], 'StatusMessage' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'Threshold' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TimerName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TransitionEvent' => [ 'type' => 'structure', 'required' => [ 'eventName', 'condition', 'nextState', ], 'members' => [ 'eventName' => [ 'shape' => 'EventName', ], 'condition' => [ 'shape' => 'Condition', ], 'actions' => [ 'shape' => 'Actions', ], 'nextState' => [ 'shape' => 'StateName', ], ], ], 'TransitionEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransitionEvent', ], ], 'UnsupportedOperationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 501, ], 'exception' => true, 'fault' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAlarmModelRequest' => [ 'type' => 'structure', 'required' => [ 'alarmModelName', 'roleArn', 'alarmRule', ], 'members' => [ 'alarmModelName' => [ 'shape' => 'AlarmModelName', 'location' => 'uri', 'locationName' => 'alarmModelName', ], 'alarmModelDescription' => [ 'shape' => 'AlarmModelDescription', ], 'roleArn' => [ 'shape' => 'AmazonResourceName', ], 'severity' => [ 'shape' => 'Severity', ], 'alarmRule' => [ 'shape' => 'AlarmRule', ], 'alarmNotification' => [ 'shape' => 'AlarmNotification', ], 'alarmEventActions' => [ 'shape' => 'AlarmEventActions', ], 'alarmCapabilities' => [ 'shape' => 'AlarmCapabilities', ], ], ], 'UpdateAlarmModelResponse' => [ 'type' => 'structure', 'members' => [ 'creationTime' => [ 'shape' => 'Timestamp', ], 'alarmModelArn' => [ 'shape' => 'AlarmModelArn', ], 'alarmModelVersion' => [ 'shape' => 'AlarmModelVersion', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AlarmModelVersionStatus', ], ], ], 'UpdateDetectorModelRequest' => [ 'type' => 'structure', 'required' => [ 'detectorModelName', 'detectorModelDefinition', 'roleArn', ], 'members' => [ 'detectorModelName' => [ 'shape' => 'DetectorModelName', 'location' => 'uri', 'locationName' => 'detectorModelName', ], 'detectorModelDefinition' => [ 'shape' => 'DetectorModelDefinition', ], 'detectorModelDescription' => [ 'shape' => 'DetectorModelDescription', ], 'roleArn' => [ 'shape' => 'AmazonResourceName', ], 'evaluationMethod' => [ 'shape' => 'EvaluationMethod', ], ], ], 'UpdateDetectorModelResponse' => [ 'type' => 'structure', 'members' => [ 'detectorModelConfiguration' => [ 'shape' => 'DetectorModelConfiguration', ], ], ], 'UpdateInputRequest' => [ 'type' => 'structure', 'required' => [ 'inputName', 'inputDefinition', ], 'members' => [ 'inputName' => [ 'shape' => 'InputName', 'location' => 'uri', 'locationName' => 'inputName', ], 'inputDescription' => [ 'shape' => 'InputDescription', ], 'inputDefinition' => [ 'shape' => 'InputDefinition', ], ], ], 'UpdateInputResponse' => [ 'type' => 'structure', 'members' => [ 'inputConfiguration' => [ 'shape' => 'InputConfiguration', ], ], ], 'UseBase64' => [ 'type' => 'boolean', ], 'VariableName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z][a-zA-Z0-9_]*$', ], 'VariableValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'errorMessage' => [ 'type' => 'string', ], 'resourceArn' => [ 'type' => 'string', ], 'resourceId' => [ 'type' => 'string', ], ],];
