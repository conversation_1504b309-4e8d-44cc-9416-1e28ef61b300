<?php
// This file was auto-generated from sdk-root/src/data/cloudfront/2017-03-25/paginators-1.json
return [ 'pagination' => [ 'ListCloudFrontOriginAccessIdentities' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxItems', 'more_results' => 'CloudFrontOriginAccessIdentityList.IsTruncated', 'output_token' => 'CloudFrontOriginAccessIdentityList.NextMarker', 'result_key' => 'CloudFrontOriginAccessIdentityList.Items', ], 'ListDistributions' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxItems', 'more_results' => 'DistributionList.IsTruncated', 'output_token' => 'DistributionList.NextMarker', 'result_key' => 'DistributionList.Items', ], 'ListInvalidations' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxItems', 'more_results' => 'InvalidationList.IsTruncated', 'output_token' => 'InvalidationList.NextMarker', 'result_key' => 'InvalidationList.Items', ], 'ListStreamingDistributions' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxItems', 'more_results' => 'StreamingDistributionList.IsTruncated', 'output_token' => 'StreamingDistributionList.NextMarker', 'result_key' => 'StreamingDistributionList.Items', ], ],];
