<style>
/* FORCE ICON COLORS - Light mode */
body[data-bg=default] button[data-navitem="notifications"] svg,
body[data-bg=default] button[data-navitem="notifications"] svg path,
body[data-bg=default] .go-options .go-options__icon svg,
body[data-bg=default] .go-options .go-options__icon svg path {
    fill: #000 !important;
    stroke: #000 !important;
    color: #000 !important;
    opacity: 1 !important;
}

/* FORCE ICON COLORS - Dark mode */
body[data-bg=dark] button[data-navitem="notifications"] svg,
body[data-bg=dark] button[data-navitem="notifications"] svg path,
body[data-bg=dark] .go-options .go-options__icon svg,
body[data-bg=dark] .go-options .go-options__icon svg path {
    fill: #fff !important;
    stroke: #fff !important;
    color: #fff !important;
    opacity: 1 !important;
}

/* Homepage logo styling */
.timeline-header .lp .nav-link-holder img {
    max-height: 30px;
    width: auto;
    object-fit: contain;
}

/* Homepage header layout adjustments */
[data-app="homepage"] .timeline-header__botline {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 10px 20px;
    position: relative;
}

[data-app="homepage"] .timeline-header__botline .lp {
    flex: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    overflow: visible !important;
    margin-right: 20px;
    max-width: none !important;
    width: auto !important;
}

[data-app="homepage"] .timeline-header__botline .lp .nav-link-holder {
    overflow: visible;
    white-space: nowrap;
    width: auto;
}

[data-app="homepage"] .timeline-header__botline .lp .nav-link-holder img {
    max-height: 27px !important;
    width: auto !important;
    max-width: 185px !important;
    object-fit: contain;
    display: block;
}

[data-app="homepage"] .timeline-header__botline .cp {
    display: none;
}

[data-app="homepage"] .timeline-header__botline .rp {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    margin-left: auto;
}
</style>

<div class="timeline-container" data-app="homepage">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("/"); ?>" data-spa="true">
						<img src="<?php echo cl_link('themes/default/statics/img/logo_home.png'); ?>?v=<?php echo time(); ?>" alt="Home Logo" style="height: 30px;">
					</a>
				</div>
			</div>
			<div class="cp">
				<!-- Center logo removed for homepage -->
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<button data-navitem="notifications" class="navbar-ctrl" data-anchor="<?php echo cl_link("notifications"); ?>" style="background: none; border: none; padding: 8px; margin-right: 10px;">
						<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.3 3C4.43161 3 2 6.58613 2 9.76C2 11.4329 2.65578 12.9708 3.5565 14.297C4.45816 15.6246 5.64283 16.7964 6.80023 17.7598C7.96152 18.7265 9.12341 19.5064 10.0048 20.045C10.4462 20.3147 10.8216 20.5266 11.0968 20.6733C11.2337 20.7463 11.3506 20.8057 11.4414 20.8491C11.4855 20.8702 11.5324 20.8918 11.5766 20.9101C11.5975 20.9188 11.6302 20.932 11.6683 20.9447C11.6869 20.951 11.7186 20.9611 11.7578 20.9707C11.7828 20.9768 11.8779 21 12 21C12.1221 21 12.2165 20.977 12.2415 20.9709C12.2807 20.9613 12.3131 20.951 12.3317 20.9447C12.3698 20.932 12.4025 20.9188 12.4234 20.9101C12.4676 20.8918 12.5145 20.8702 12.5586 20.8491C12.6494 20.8057 12.7663 20.7463 12.9032 20.6733C13.1784 20.5266 13.5538 20.3147 13.9952 20.045C14.8766 19.5064 16.0385 18.7265 17.1998 17.7598C18.3572 16.7964 19.5418 15.6246 20.4435 14.297C21.3442 12.9708 22 11.4329 22 9.76C22 6.58613 19.5684 3 15.7 3C14.0692 3 12.8525 3.61246 12 4.31531C11.1475 3.61246 9.93082 3 8.3 3ZM4 9.76C4 7.37386 5.82839 5 8.3 5C9.74402 5 10.679 5.70949 11.2428 6.36314C11.4327 6.58338 11.7092 6.71 12 6.71C12.2908 6.71 12.5673 6.58338 12.7572 6.36314C13.321 5.70949 14.256 5 15.7 5C18.1716 5 20 7.37387 20 9.76C20 10.9022 19.5508 12.0517 18.789 13.1733C18.0282 14.2936 16.9928 15.3299 15.9202 16.2227C14.8515 17.1123 13.7734 17.8367 12.9523 18.3384C12.5637 18.5759 12.2365 18.7611 12 18.8883C11.7635 18.7611 11.4363 18.5759 11.0477 18.3384C10.2266 17.8367 9.14847 17.1123 8.07975 16.2227C7.00716 15.3299 5.97183 14.2936 5.21099 13.1733C4.44922 12.0517 4 10.9022 4 9.76Z"></path></svg>
						<span class="info-indicators" data-an="new-notifs"><?php echo fetch_or_get($me['new_notifs'], ''); ?></span>
					</button>
					<button class="go-options dropleft">
						<div class="dropdown-toggle icon go-options__icon" data-toggle="dropdown">
							<?php echo cl_ficon('more_horiz'); ?>
						</div>
						<div class="dropdown-menu dropdown-icons">
							<a class="dropdown-item" href="<?php echo cl_link("feed_preferences"); ?>" data-spa="true">
								<span class="flex-item dropdown-item-icon">
									<?php echo cl_ficon('news'); ?>
								</span>
								<span class="flex-item">
									<?php echo cl_translate("Feed preferences"); ?>
								</span>
								<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
									<?php echo cl_ficon('open'); ?>
								</span>
							</a>
						</div>
					</button>
				</div>
			</div>
		</div>
	</div>
	<div class="homepage">


		<?php if ($cl["config"]["swift_system_status"] == "on"): ?>
			<div class="homepage__swifts">
				<div class="swifts-slider">
					<div class="swiper" id="homepage-swifts-slider">
						<div class="swiper-wrapper">
							<div class="swiper-slide">
								<div class="swift-item" onclick="SMColibri.PS.new_swift.add_swift();">
									<div class="swift-item__body create-swift">
										<div class="swift-item__avatar">
											<img src="<?php echo($me['avatar']); ?>" alt="Avatar">
										</div>
										<span class="add-ikon">
											<?php echo cl_ficon("add"); ?>
										</span>
									</div>
									<div class="swift-item__footer">
										<?php echo cl_translate("Create new swift"); ?>
									</div>
								</div>
							</div>

							<?php if (not_empty($cl["tl_swifts"])): ?>
								<?php foreach ($cl["tl_swifts"] as $swift_id => $swift_data): ?>
									<div class="swiper-slide">
										<div data-an="swift-item-<?php echo($swift_data['id']); ?>" class="swift-item <?php if(not_empty($swift_data['has_unseen'])) {echo('active');} ?>" onclick="SMColibri.PS.play_swift.show('<?php echo($swift_id); ?>');">
											<div class="swift-item__body">
												<div class="swift-item__avatar">
													<img src="<?php echo($swift_data['avatar']); ?>" alt="Avatar">
												</div>
											</div>
											<div class="swift-item__footer">
												<?php if (not_empty($swift_data["is_user"])): ?>
													<?php echo cl_translate("Your swifts"); ?>
												<?php else: ?>
													<?php echo($swift_data['name']); ?>
												<?php endif; ?>
											</div>
										</div>
									</div>
								<?php endforeach; ?>
							<?php endif; ?>
						</div>
					</div>
					<div class="swifts-slider__footer">
						<div class="swiper-scrollbar" id="homepage-swifts-scrollbar"></div>
					</div>
				</div>
			</div>
		<?php endif; ?>
	</div>
	<div class="timeline-posts-container">
		<?php if (not_empty($cl["tl_feed"])): ?>
			<?php if (not_empty($cl["admin_pinned_post"])): ?>
				<div class="timeline-posts-ls">
					<?php
						$cl['li'] = $cl["admin_pinned_post"];

						echo cl_template('timeline/post');
					?>
				</div>
			<?php endif; ?>
			<div class="timeline-posts-container">
				<?php $count = 0; ?>
				<?php if (not_empty($cl["tl_feed"])): ?>
						<div class="timeline-posts-ls" data-an="entry-list">
							<?php foreach ($cl["tl_feed"] as $cl["li"]): ?>
									<?php $count++; ?>
									<?php echo cl_template('timeline/post'); ?>

									<?php if ($count == 3): ?>
										<!-- Vlix Inline Video Carousel -->
										<div class="vlix-inline-carousel" style="margin: 20px 0; padding: 0;">
											<div class="vlix-carousel-container" style="display: flex; overflow-x: auto; gap: 12px; padding: 0 20px; scroll-behavior: smooth; -webkit-overflow-scrolling: touch;">
												<div class="vlix-loading-placeholder" style="display: flex; align-items: center; justify-content: center; width: 100%; min-height: 200px; color: #666;">
													<div style="text-align: center;">
														<div style="width: 24px; height: 24px; border: 2px solid #ddd; border-top: 2px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 10px;"></div>
														<p style="margin: 0; font-size: 14px;">Loading Vlix videos...</p>
													</div>
												</div>
											</div>
										</div>

										<style>
										/* Vlix Inline Carousel Styles */
										.vlix-inline-carousel {
											background: transparent;
											border-radius: 0;
											margin: 20px 0;
											padding: 0;
										}

										.vlix-carousel-container {
											display: flex;
											overflow-x: auto;
											gap: 12px;
											padding: 0 20px;
											scroll-behavior: smooth;
											-webkit-overflow-scrolling: touch;
											scrollbar-width: none;
											-ms-overflow-style: none;
										}

										.vlix-carousel-container::-webkit-scrollbar {
											display: none;
										}

										.vlix-video-slide {
											flex: 0 0 auto;
											width: 200px; /* Much bigger like Instagram */
											height: 360px; /* Much taller like Instagram stories */
											border-radius: 0; /* Square edges */
											overflow: hidden;
											position: relative;
											cursor: pointer;
											background: #000;
											transition: transform 0.2s ease;
										}

										.vlix-video-slide:hover {
											transform: translateY(-2px);
										}

										.vlix-video-slide video {
											width: 100%;
											height: 100%;
											object-fit: cover;
										}

										.vlix-video-overlay {
											position: absolute;
											bottom: 0;
											left: 0;
											right: 0;
											background: linear-gradient(transparent, rgba(0,0,0,0.7));
											padding: 8px;
											color: white;
											font-size: 12px;
										}

										.vlix-video-user {
											display: flex;
											align-items: center;
											gap: 4px;
											margin-bottom: 4px;
										}

										.vlix-video-avatar {
											width: 16px;
											height: 16px;
											border-radius: 50%;
											object-fit: cover;
										}

										.vlix-video-username {
											font-weight: 500;
											font-size: 11px;
											white-space: nowrap;
											overflow: hidden;
											text-overflow: ellipsis;
											max-width: 80px;
										}

										.vlix-play-icon {
											position: absolute;
											top: 50%;
											left: 50%;
											transform: translate(-50%, -50%);
											width: 32px;
											height: 32px;
											background: rgba(255,255,255,0.9);
											border-radius: 50%;
											display: flex;
											align-items: center;
											justify-content: center;
											opacity: 0.8;
											transition: opacity 0.2s ease;
										}

										.vlix-video-slide:hover .vlix-play-icon {
											opacity: 1;
										}

										.vlix-play-icon svg {
											width: 14px;
											height: 14px;
											fill: #000;
											margin-left: 2px;
										}

										@keyframes spin {
											0% { transform: rotate(0deg); }
											100% { transform: rotate(360deg); }
										}

										/* Dark mode support - keeping transparent */
										body[data-bg="dark"] .vlix-inline-carousel {
											background: transparent;
										}

										/* Mobile responsive */
										@media (max-width: 768px) {
											.vlix-carousel-container {
												padding: 0 15px 15px;
												gap: 10px;
											}

											.vlix-video-slide {
												width: 160px; /* Much bigger on mobile */
												height: 280px; /* Much taller on mobile */
											}
										}
										</style>

										<script>
										// Load Vlix videos for inline carousel
										(function() {
											console.log('🎬 Loading Vlix inline carousel...');

											const carousel = document.querySelector('.vlix-carousel-container');
											const placeholder = document.querySelector('.vlix-loading-placeholder');

											if (!carousel) {
												console.error('Vlix carousel container not found');
												return;
											}



											function createVideoSlides(videos) {
												// Remove loading placeholder
												if (placeholder) {
													placeholder.remove();
												}

												// Create video slides
												videos.slice(0, 5).forEach(function(video, index) {
													const slide = document.createElement('div');
													slide.className = 'vlix-video-slide';
													slide.onclick = function() {
														console.log('🎬 Vlix video clicked:', video.post_id);

														// Enable sound globally
														if (window.toggleGlobalMute) {
															window.toggleGlobalMute(false);
														}

														// Find the original video element and trigger its click
														const originalVideo = document.getElementById(video.video_id);
														if (originalVideo) {
															console.log('🎬 Found original video element:', originalVideo);

															// Try multiple ways to open the video
															if (window.openOverlay) {
																console.log('🎬 Using openOverlay function');
																window.openOverlay(originalVideo);
															} else if (originalVideo.click) {
																console.log('🎬 Clicking original video element');
																originalVideo.click();
															} else {
																console.error('🎬 No way to open video found');
															}
														} else {
															console.error('🎬 Original video element not found:', video.video_id);

															// Fallback: try to open shorts viewer directly
															if (window.location.pathname !== '/vlix') {
																console.log('🎬 Fallback: navigating to /vlix');
																window.location.href = '/vlix';
															}
														}
													};

													slide.innerHTML =
														'<video muted playsinline preload="metadata" poster="' + (video.poster || '') + '">' +
															'<source src="' + video.video_url + '" type="video/mp4">' +
														'</video>' +
														'<div class="vlix-play-icon">' +
															'<svg viewBox="0 0 24 24">' +
																'<path d="M8 5v14l11-7z"/>' +
															'</svg>' +
														'</div>' +
														'<div class="vlix-video-overlay">' +
															'<div class="vlix-video-user">' +
																'<img class="vlix-video-avatar" src="' + video.user_avatar + '" alt="' + video.user_name + '" onerror="this.style.display=\'none\'">' +
																'<span class="vlix-video-username">' + video.user_name + '</span>' +
															'</div>' +
														'</div>';

													carousel.appendChild(slide);
												});

												console.log('🎬 Vlix carousel loaded with', videos.length, 'videos');
											}

											// Extract videos directly from DOM instead of API
											function extractVideosFromDOM() {
												const videos = [];

												// Find all video elements in the timeline
												const timelineVideos = document.querySelectorAll('video[id^="video-player-"]');
												console.log('🎬 Found', timelineVideos.length, 'timeline videos in DOM');

												timelineVideos.forEach(function(videoElement, index) {
													if (index >= 5) return; // Only take first 5

													const videoId = videoElement.id;
													const postId = videoId.replace('video-player-', '');

													// Get video source - try multiple methods
													let videoSrc = '';
													const sourceElement = videoElement.querySelector('source');
													if (sourceElement && sourceElement.src) {
														videoSrc = sourceElement.src;
													} else if (videoElement.src) {
														videoSrc = videoElement.src;
													} else if (videoElement.getAttribute('data-src')) {
														videoSrc = videoElement.getAttribute('data-src');
													}

													const poster = videoElement.poster || '';

													console.log('🎬 Video extraction for', videoId, ':', {
														src: videoSrc,
														poster: poster,
														hasSource: !!sourceElement,
														elementSrc: videoElement.src,
														dataSrc: videoElement.getAttribute('data-src')
													});

													// Try to find user info from the post container
													const postContainer = videoElement.closest('[data-post-id], .timeline-post, .post-container, .post-wrapper');
													let userName = 'User ' + postId;
													let userAvatar = 'themes/default/statics/img/avatar.png';

													if (postContainer) {
														// Look for user name in various places
														const nameElement = postContainer.querySelector('.user-name, .post-author, .username, [data-username], .profile-name');
														if (nameElement) {
															userName = nameElement.textContent.trim() || nameElement.getAttribute('data-username') || userName;
														}

														// Look for user avatar
														const avatarElement = postContainer.querySelector('.user-avatar img, .avatar img, .profile-pic img, .profile-avatar img');
														if (avatarElement) {
															userAvatar = avatarElement.src || userAvatar;
														}
													}

													const videoData = {
														post_id: postId,
														video_id: videoId,
														video_url: videoSrc,
														poster: poster,
														user_name: userName,
														user_avatar: userAvatar
													};

													videos.push(videoData);
													console.log('🎬 Extracted video:', videoData);
												});

												return videos;
											}

											// Wait for timeline to load, then extract videos from DOM
											setTimeout(function() {
												const videos = extractVideosFromDOM();
												if (videos.length > 0) {
													createVideoSlides(videos);
												} else {
													console.log('🎬 No videos found in DOM');
													if (placeholder) {
														placeholder.innerHTML = '<p style="margin: 0; color: #666; font-size: 14px;">No videos found</p>';
													}
												}
											}, 2000); // Wait 2 seconds for timeline to fully load
										})();
										</script>
									<?php endif; ?>

									<?php
									// Show follow suggestions at strategic intervals with different user sets
									if ($count == 7) {
										// First appearance - Set 1 (after 7 posts)
										$cl["follow_suggestion"] = isset($cl["follow_suggestion_sets"][1]) ? $cl["follow_suggestion_sets"][1] : array();
										$cl["follow_appearance"] = 1;
										$cl["follow_title"] = "Who to follow";
										echo cl_template('home/follow');
									} elseif ($count == 14) {
										// Second appearance - Set 2 (after 14 posts)
										$cl["follow_suggestion"] = isset($cl["follow_suggestion_sets"][2]) ? $cl["follow_suggestion_sets"][2] : array();
										$cl["follow_appearance"] = 2;
										$cl["follow_title"] = "Discover more people";
										echo cl_template('home/follow');
									} elseif ($count == 21) {
										// Third appearance - Set 3 (after 21 posts)
										$cl["follow_suggestion"] = isset($cl["follow_suggestion_sets"][3]) ? $cl["follow_suggestion_sets"][3] : array();
										$cl["follow_appearance"] = 3;
										$cl["follow_title"] = "Connect with others";
										echo cl_template('home/follow');
									}
									?>
							<?php endforeach; ?>
						</div>



						<?php endif; ?>
						</div>
		<?php else: ?>
			<?php echo cl_template('home/includes/no_posts'); ?>
		<?php endif; ?>
	</div>

	<?php if ($cl["config"]["swift_system_status"] == "on"): ?>
		<?php echo cl_template("home/modals/swift_lightbox"); ?>
		<?php echo cl_template("home/modals/swift_pubbox"); ?>
	<?php endif; ?>

	<?php echo cl_template("main/includes/inline_statics/app_statics"); ?>
	<?php echo cl_template("home/scripts/app_master_script"); ?>
</div>
<!-- MDB -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.2.0/mdb.min.css" rel="stylesheet" />
<!-- MDB -->

<script>
	// Initialise Carousel
	const cardSlider = new Carousel(document.querySelector("#cardSlider"), {
		Dots: false,
	})
	const cardSlider2 = new Carousel(document.querySelector("#cardSlider2"), {
		Dots: false,
	})
</script>
<script>
	"use strict";

	var offset = 10;

	jQuery(document).ready(function ($) {
		var _app = $('div[data-app="homepage"]');
		var feed_ls = _app.find('[data-an="entry-list"]');

		_app.find('button[data-an="load-more"]').on('click', function (event) {
			event.preventDefault();

			var _self = $(this);


			$.ajax({
				url: "<?php echo cl_link("native_api/home/<USER>"); ?>",
				type: 'GET',
				dataType: 'json',
				data: {
					offset: offset,
				},
				beforeSend: function () {
					_self.attr('disabled', 'true').text("<?php echo cl_translate("Please wait"); ?>");
				}
			}).done(function (data) {
				if (data.status == 200) {
					feed_ls.append(data.html);

					_self.removeAttr('disabled').text("<?php echo cl_translate("Show more"); ?>");

					offset += 10;
				}

				else {
					_self.text("<?php echo cl_translate("That is all for now!"); ?>");
				}
			});
		});
	});
</script>