<?php
// This file was auto-generated from sdk-root/src/data/location/2020-11-19/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-11-19', 'endpointPrefix' => 'geo', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Location Service', 'serviceId' => 'Location', 'signatureVersion' => 'v4', 'signingName' => 'geo', 'uid' => 'location-2020-11-19', ], 'operations' => [ 'AssociateTrackerConsumer' => [ 'name' => 'AssociateTrackerConsumer', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/consumers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateTrackerConsumerRequest', ], 'output' => [ 'shape' => 'AssociateTrackerConsumerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], ], 'BatchDeleteDevicePositionHistory' => [ 'name' => 'BatchDeleteDevicePositionHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/delete-positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteDevicePositionHistoryRequest', ], 'output' => [ 'shape' => 'BatchDeleteDevicePositionHistoryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'BatchDeleteGeofence' => [ 'name' => 'BatchDeleteGeofence', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/delete-geofences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteGeofenceRequest', ], 'output' => [ 'shape' => 'BatchDeleteGeofenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'BatchEvaluateGeofences' => [ 'name' => 'BatchEvaluateGeofences', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchEvaluateGeofencesRequest', ], 'output' => [ 'shape' => 'BatchEvaluateGeofencesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'BatchGetDevicePosition' => [ 'name' => 'BatchGetDevicePosition', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/get-positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetDevicePositionRequest', ], 'output' => [ 'shape' => 'BatchGetDevicePositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'BatchPutGeofence' => [ 'name' => 'BatchPutGeofence', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/put-geofences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchPutGeofenceRequest', ], 'output' => [ 'shape' => 'BatchPutGeofenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'BatchUpdateDevicePosition' => [ 'name' => 'BatchUpdateDevicePosition', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdateDevicePositionRequest', ], 'output' => [ 'shape' => 'BatchUpdateDevicePositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'CalculateRoute' => [ 'name' => 'CalculateRoute', 'http' => [ 'method' => 'POST', 'requestUri' => '/routes/v0/calculators/{CalculatorName}/calculate/route', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CalculateRouteRequest', ], 'output' => [ 'shape' => 'CalculateRouteResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'routes.', ], ], 'CalculateRouteMatrix' => [ 'name' => 'CalculateRouteMatrix', 'http' => [ 'method' => 'POST', 'requestUri' => '/routes/v0/calculators/{CalculatorName}/calculate/route-matrix', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CalculateRouteMatrixRequest', ], 'output' => [ 'shape' => 'CalculateRouteMatrixResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'routes.', ], ], 'CreateGeofenceCollection' => [ 'name' => 'CreateGeofenceCollection', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateGeofenceCollectionRequest', ], 'output' => [ 'shape' => 'CreateGeofenceCollectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.geofencing.', ], 'idempotent' => true, ], 'CreateKey' => [ 'name' => 'CreateKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/metadata/v0/keys', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateKeyRequest', ], 'output' => [ 'shape' => 'CreateKeyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], 'idempotent' => true, ], 'CreateMap' => [ 'name' => 'CreateMap', 'http' => [ 'method' => 'POST', 'requestUri' => '/maps/v0/maps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMapRequest', ], 'output' => [ 'shape' => 'CreateMapResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.maps.', ], 'idempotent' => true, ], 'CreatePlaceIndex' => [ 'name' => 'CreatePlaceIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/indexes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePlaceIndexRequest', ], 'output' => [ 'shape' => 'CreatePlaceIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.places.', ], 'idempotent' => true, ], 'CreateRouteCalculator' => [ 'name' => 'CreateRouteCalculator', 'http' => [ 'method' => 'POST', 'requestUri' => '/routes/v0/calculators', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateRouteCalculatorRequest', ], 'output' => [ 'shape' => 'CreateRouteCalculatorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.routes.', ], 'idempotent' => true, ], 'CreateTracker' => [ 'name' => 'CreateTracker', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTrackerRequest', ], 'output' => [ 'shape' => 'CreateTrackerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], 'idempotent' => true, ], 'DeleteGeofenceCollection' => [ 'name' => 'DeleteGeofenceCollection', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/geofencing/v0/collections/{CollectionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteGeofenceCollectionRequest', ], 'output' => [ 'shape' => 'DeleteGeofenceCollectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.geofencing.', ], 'idempotent' => true, ], 'DeleteKey' => [ 'name' => 'DeleteKey', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/metadata/v0/keys/{KeyName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteKeyRequest', ], 'output' => [ 'shape' => 'DeleteKeyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], 'idempotent' => true, ], 'DeleteMap' => [ 'name' => 'DeleteMap', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/maps/v0/maps/{MapName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMapRequest', ], 'output' => [ 'shape' => 'DeleteMapResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.maps.', ], 'idempotent' => true, ], 'DeletePlaceIndex' => [ 'name' => 'DeletePlaceIndex', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/places/v0/indexes/{IndexName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePlaceIndexRequest', ], 'output' => [ 'shape' => 'DeletePlaceIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.places.', ], 'idempotent' => true, ], 'DeleteRouteCalculator' => [ 'name' => 'DeleteRouteCalculator', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/routes/v0/calculators/{CalculatorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteRouteCalculatorRequest', ], 'output' => [ 'shape' => 'DeleteRouteCalculatorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.routes.', ], 'idempotent' => true, ], 'DeleteTracker' => [ 'name' => 'DeleteTracker', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tracking/v0/trackers/{TrackerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTrackerRequest', ], 'output' => [ 'shape' => 'DeleteTrackerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], 'idempotent' => true, ], 'DescribeGeofenceCollection' => [ 'name' => 'DescribeGeofenceCollection', 'http' => [ 'method' => 'GET', 'requestUri' => '/geofencing/v0/collections/{CollectionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeGeofenceCollectionRequest', ], 'output' => [ 'shape' => 'DescribeGeofenceCollectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.geofencing.', ], ], 'DescribeKey' => [ 'name' => 'DescribeKey', 'http' => [ 'method' => 'GET', 'requestUri' => '/metadata/v0/keys/{KeyName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeKeyRequest', ], 'output' => [ 'shape' => 'DescribeKeyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], ], 'DescribeMap' => [ 'name' => 'DescribeMap', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeMapRequest', ], 'output' => [ 'shape' => 'DescribeMapResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.maps.', ], ], 'DescribePlaceIndex' => [ 'name' => 'DescribePlaceIndex', 'http' => [ 'method' => 'GET', 'requestUri' => '/places/v0/indexes/{IndexName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribePlaceIndexRequest', ], 'output' => [ 'shape' => 'DescribePlaceIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.places.', ], ], 'DescribeRouteCalculator' => [ 'name' => 'DescribeRouteCalculator', 'http' => [ 'method' => 'GET', 'requestUri' => '/routes/v0/calculators/{CalculatorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeRouteCalculatorRequest', ], 'output' => [ 'shape' => 'DescribeRouteCalculatorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.routes.', ], ], 'DescribeTracker' => [ 'name' => 'DescribeTracker', 'http' => [ 'method' => 'GET', 'requestUri' => '/tracking/v0/trackers/{TrackerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeTrackerRequest', ], 'output' => [ 'shape' => 'DescribeTrackerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], ], 'DisassociateTrackerConsumer' => [ 'name' => 'DisassociateTrackerConsumer', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/consumers/{ConsumerArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateTrackerConsumerRequest', ], 'output' => [ 'shape' => 'DisassociateTrackerConsumerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], ], 'GetDevicePosition' => [ 'name' => 'GetDevicePosition', 'http' => [ 'method' => 'GET', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/devices/{DeviceId}/positions/latest', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDevicePositionRequest', ], 'output' => [ 'shape' => 'GetDevicePositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'GetDevicePositionHistory' => [ 'name' => 'GetDevicePositionHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/devices/{DeviceId}/list-positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDevicePositionHistoryRequest', ], 'output' => [ 'shape' => 'GetDevicePositionHistoryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'GetGeofence' => [ 'name' => 'GetGeofence', 'http' => [ 'method' => 'GET', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/geofences/{GeofenceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGeofenceRequest', ], 'output' => [ 'shape' => 'GetGeofenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'GetMapGlyphs' => [ 'name' => 'GetMapGlyphs', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}/glyphs/{FontStack}/{FontUnicodeRange}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMapGlyphsRequest', ], 'output' => [ 'shape' => 'GetMapGlyphsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'GetMapSprites' => [ 'name' => 'GetMapSprites', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}/sprites/{FileName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMapSpritesRequest', ], 'output' => [ 'shape' => 'GetMapSpritesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'GetMapStyleDescriptor' => [ 'name' => 'GetMapStyleDescriptor', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}/style-descriptor', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMapStyleDescriptorRequest', ], 'output' => [ 'shape' => 'GetMapStyleDescriptorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'GetMapTile' => [ 'name' => 'GetMapTile', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}/tiles/{Z}/{X}/{Y}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMapTileRequest', ], 'output' => [ 'shape' => 'GetMapTileResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'GetPlace' => [ 'name' => 'GetPlace', 'http' => [ 'method' => 'GET', 'requestUri' => '/places/v0/indexes/{IndexName}/places/{PlaceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPlaceRequest', ], 'output' => [ 'shape' => 'GetPlaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'ListDevicePositions' => [ 'name' => 'ListDevicePositions', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/list-positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDevicePositionsRequest', ], 'output' => [ 'shape' => 'ListDevicePositionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'ListGeofenceCollections' => [ 'name' => 'ListGeofenceCollections', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/list-collections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGeofenceCollectionsRequest', ], 'output' => [ 'shape' => 'ListGeofenceCollectionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.geofencing.', ], ], 'ListGeofences' => [ 'name' => 'ListGeofences', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/list-geofences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGeofencesRequest', ], 'output' => [ 'shape' => 'ListGeofencesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'ListKeys' => [ 'name' => 'ListKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/metadata/v0/list-keys', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListKeysRequest', ], 'output' => [ 'shape' => 'ListKeysResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], ], 'ListMaps' => [ 'name' => 'ListMaps', 'http' => [ 'method' => 'POST', 'requestUri' => '/maps/v0/list-maps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMapsRequest', ], 'output' => [ 'shape' => 'ListMapsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.maps.', ], ], 'ListPlaceIndexes' => [ 'name' => 'ListPlaceIndexes', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/list-indexes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPlaceIndexesRequest', ], 'output' => [ 'shape' => 'ListPlaceIndexesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.places.', ], ], 'ListRouteCalculators' => [ 'name' => 'ListRouteCalculators', 'http' => [ 'method' => 'POST', 'requestUri' => '/routes/v0/list-calculators', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRouteCalculatorsRequest', ], 'output' => [ 'shape' => 'ListRouteCalculatorsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.routes.', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], ], 'ListTrackerConsumers' => [ 'name' => 'ListTrackerConsumers', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/list-consumers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrackerConsumersRequest', ], 'output' => [ 'shape' => 'ListTrackerConsumersResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], ], 'ListTrackers' => [ 'name' => 'ListTrackers', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/list-trackers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrackersRequest', ], 'output' => [ 'shape' => 'ListTrackersResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], ], 'PutGeofence' => [ 'name' => 'PutGeofence', 'http' => [ 'method' => 'PUT', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/geofences/{GeofenceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutGeofenceRequest', ], 'output' => [ 'shape' => 'PutGeofenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'SearchPlaceIndexForPosition' => [ 'name' => 'SearchPlaceIndexForPosition', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/indexes/{IndexName}/search/position', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchPlaceIndexForPositionRequest', ], 'output' => [ 'shape' => 'SearchPlaceIndexForPositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'SearchPlaceIndexForSuggestions' => [ 'name' => 'SearchPlaceIndexForSuggestions', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/indexes/{IndexName}/search/suggestions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchPlaceIndexForSuggestionsRequest', ], 'output' => [ 'shape' => 'SearchPlaceIndexForSuggestionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'SearchPlaceIndexForText' => [ 'name' => 'SearchPlaceIndexForText', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/indexes/{IndexName}/search/text', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchPlaceIndexForTextRequest', ], 'output' => [ 'shape' => 'SearchPlaceIndexForTextResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], 'idempotent' => true, ], 'UpdateGeofenceCollection' => [ 'name' => 'UpdateGeofenceCollection', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/geofencing/v0/collections/{CollectionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateGeofenceCollectionRequest', ], 'output' => [ 'shape' => 'UpdateGeofenceCollectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.geofencing.', ], 'idempotent' => true, ], 'UpdateKey' => [ 'name' => 'UpdateKey', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/metadata/v0/keys/{KeyName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateKeyRequest', ], 'output' => [ 'shape' => 'UpdateKeyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], 'idempotent' => true, ], 'UpdateMap' => [ 'name' => 'UpdateMap', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/maps/v0/maps/{MapName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMapRequest', ], 'output' => [ 'shape' => 'UpdateMapResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.maps.', ], 'idempotent' => true, ], 'UpdatePlaceIndex' => [ 'name' => 'UpdatePlaceIndex', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/places/v0/indexes/{IndexName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePlaceIndexRequest', ], 'output' => [ 'shape' => 'UpdatePlaceIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.places.', ], 'idempotent' => true, ], 'UpdateRouteCalculator' => [ 'name' => 'UpdateRouteCalculator', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/routes/v0/calculators/{CalculatorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRouteCalculatorRequest', ], 'output' => [ 'shape' => 'UpdateRouteCalculatorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.routes.', ], 'idempotent' => true, ], 'UpdateTracker' => [ 'name' => 'UpdateTracker', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/tracking/v0/trackers/{TrackerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTrackerRequest', ], 'output' => [ 'shape' => 'UpdateTrackerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ApiKey' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'sensitive' => true, ], 'ApiKeyAction' => [ 'type' => 'string', 'max' => 200, 'min' => 5, 'pattern' => '^geo:\\w*\\*?$', ], 'ApiKeyFilter' => [ 'type' => 'structure', 'members' => [ 'KeyStatus' => [ 'shape' => 'Status', ], ], ], 'ApiKeyRestrictions' => [ 'type' => 'structure', 'required' => [ 'AllowActions', 'AllowResources', ], 'members' => [ 'AllowActions' => [ 'shape' => 'ApiKeyRestrictionsAllowActionsList', ], 'AllowReferers' => [ 'shape' => 'ApiKeyRestrictionsAllowReferersList', ], 'AllowResources' => [ 'shape' => 'ApiKeyRestrictionsAllowResourcesList', ], ], ], 'ApiKeyRestrictionsAllowActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApiKeyAction', ], 'max' => 7, 'min' => 1, ], 'ApiKeyRestrictionsAllowReferersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RefererPattern', ], 'max' => 5, 'min' => 1, ], 'ApiKeyRestrictionsAllowResourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GeoArn', ], 'max' => 5, 'min' => 1, ], 'Arn' => [ 'type' => 'string', 'max' => 1600, 'min' => 0, 'pattern' => '^arn(:[a-z0-9]+([.-][a-z0-9]+)*){2}(:([a-z0-9]+([.-][a-z0-9]+)*)?){2}:([^/].*)?$', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'AssociateTrackerConsumerRequest' => [ 'type' => 'structure', 'required' => [ 'ConsumerArn', 'TrackerName', ], 'members' => [ 'ConsumerArn' => [ 'shape' => 'Arn', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'AssociateTrackerConsumerResponse' => [ 'type' => 'structure', 'members' => [], ], 'BatchDeleteDevicePositionHistoryError' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Error', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'Error' => [ 'shape' => 'BatchItemError', ], ], ], 'BatchDeleteDevicePositionHistoryErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteDevicePositionHistoryError', ], ], 'BatchDeleteDevicePositionHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceIds', 'TrackerName', ], 'members' => [ 'DeviceIds' => [ 'shape' => 'BatchDeleteDevicePositionHistoryRequestDeviceIdsList', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'BatchDeleteDevicePositionHistoryRequestDeviceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Id', ], 'max' => 100, 'min' => 1, ], 'BatchDeleteDevicePositionHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchDeleteDevicePositionHistoryErrorList', ], ], ], 'BatchDeleteGeofenceError' => [ 'type' => 'structure', 'required' => [ 'Error', 'GeofenceId', ], 'members' => [ 'Error' => [ 'shape' => 'BatchItemError', ], 'GeofenceId' => [ 'shape' => 'Id', ], ], ], 'BatchDeleteGeofenceErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteGeofenceError', ], ], 'BatchDeleteGeofenceRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'GeofenceIds', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'GeofenceIds' => [ 'shape' => 'BatchDeleteGeofenceRequestGeofenceIdsList', ], ], ], 'BatchDeleteGeofenceRequestGeofenceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Id', ], 'max' => 10, 'min' => 1, ], 'BatchDeleteGeofenceResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchDeleteGeofenceErrorList', ], ], ], 'BatchEvaluateGeofencesError' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Error', 'SampleTime', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'Error' => [ 'shape' => 'BatchItemError', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], ], ], 'BatchEvaluateGeofencesErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchEvaluateGeofencesError', ], ], 'BatchEvaluateGeofencesRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'DevicePositionUpdates', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'DevicePositionUpdates' => [ 'shape' => 'BatchEvaluateGeofencesRequestDevicePositionUpdatesList', ], ], ], 'BatchEvaluateGeofencesRequestDevicePositionUpdatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevicePositionUpdate', ], 'max' => 10, 'min' => 1, ], 'BatchEvaluateGeofencesResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchEvaluateGeofencesErrorList', ], ], ], 'BatchGetDevicePositionError' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Error', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'Error' => [ 'shape' => 'BatchItemError', ], ], ], 'BatchGetDevicePositionErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetDevicePositionError', ], ], 'BatchGetDevicePositionRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceIds', 'TrackerName', ], 'members' => [ 'DeviceIds' => [ 'shape' => 'BatchGetDevicePositionRequestDeviceIdsList', ], 'TrackerName' => [ 'shape' => 'BatchGetDevicePositionRequestTrackerNameString', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'BatchGetDevicePositionRequestDeviceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Id', ], 'max' => 10, 'min' => 1, ], 'BatchGetDevicePositionRequestTrackerNameString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '^[-._\\w]+$', ], 'BatchGetDevicePositionResponse' => [ 'type' => 'structure', 'required' => [ 'DevicePositions', 'Errors', ], 'members' => [ 'DevicePositions' => [ 'shape' => 'DevicePositionList', ], 'Errors' => [ 'shape' => 'BatchGetDevicePositionErrorList', ], ], ], 'BatchItemError' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'BatchItemErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'BatchItemErrorCode' => [ 'type' => 'string', 'enum' => [ 'AccessDeniedError', 'ConflictError', 'InternalServerError', 'ResourceNotFoundError', 'ThrottlingError', 'ValidationError', ], ], 'BatchPutGeofenceError' => [ 'type' => 'structure', 'required' => [ 'Error', 'GeofenceId', ], 'members' => [ 'Error' => [ 'shape' => 'BatchItemError', ], 'GeofenceId' => [ 'shape' => 'Id', ], ], ], 'BatchPutGeofenceErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutGeofenceError', ], ], 'BatchPutGeofenceRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'Entries', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'Entries' => [ 'shape' => 'BatchPutGeofenceRequestEntriesList', ], ], ], 'BatchPutGeofenceRequestEntriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutGeofenceRequestEntry', ], 'max' => 10, 'min' => 1, ], 'BatchPutGeofenceRequestEntry' => [ 'type' => 'structure', 'required' => [ 'GeofenceId', 'Geometry', ], 'members' => [ 'GeofenceId' => [ 'shape' => 'Id', ], 'GeofenceProperties' => [ 'shape' => 'PropertyMap', ], 'Geometry' => [ 'shape' => 'GeofenceGeometry', ], ], ], 'BatchPutGeofenceResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', 'Successes', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchPutGeofenceErrorList', ], 'Successes' => [ 'shape' => 'BatchPutGeofenceSuccessList', ], ], ], 'BatchPutGeofenceSuccess' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'GeofenceId', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'GeofenceId' => [ 'shape' => 'Id', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BatchPutGeofenceSuccessList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutGeofenceSuccess', ], ], 'BatchUpdateDevicePositionError' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Error', 'SampleTime', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'Error' => [ 'shape' => 'BatchItemError', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], ], ], 'BatchUpdateDevicePositionErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateDevicePositionError', ], ], 'BatchUpdateDevicePositionRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'Updates', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'Updates' => [ 'shape' => 'BatchUpdateDevicePositionRequestUpdatesList', ], ], ], 'BatchUpdateDevicePositionRequestUpdatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevicePositionUpdate', ], 'max' => 10, 'min' => 1, ], 'BatchUpdateDevicePositionResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchUpdateDevicePositionErrorList', ], ], ], 'Blob' => [ 'type' => 'blob', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoundingBox' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 4, 'min' => 4, 'sensitive' => true, ], 'CalculateRouteCarModeOptions' => [ 'type' => 'structure', 'members' => [ 'AvoidFerries' => [ 'shape' => 'Boolean', ], 'AvoidTolls' => [ 'shape' => 'Boolean', ], ], ], 'CalculateRouteMatrixRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'DeparturePositions', 'DestinationPositions', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], 'CarModeOptions' => [ 'shape' => 'CalculateRouteCarModeOptions', ], 'DepartNow' => [ 'shape' => 'Boolean', ], 'DeparturePositions' => [ 'shape' => 'CalculateRouteMatrixRequestDeparturePositionsList', ], 'DepartureTime' => [ 'shape' => 'Timestamp', ], 'DestinationPositions' => [ 'shape' => 'CalculateRouteMatrixRequestDestinationPositionsList', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'TravelMode' => [ 'shape' => 'TravelMode', ], 'TruckModeOptions' => [ 'shape' => 'CalculateRouteTruckModeOptions', ], ], ], 'CalculateRouteMatrixRequestDeparturePositionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'max' => 350, 'min' => 1, ], 'CalculateRouteMatrixRequestDestinationPositionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'max' => 350, 'min' => 1, ], 'CalculateRouteMatrixResponse' => [ 'type' => 'structure', 'required' => [ 'RouteMatrix', 'Summary', ], 'members' => [ 'RouteMatrix' => [ 'shape' => 'RouteMatrix', ], 'SnappedDeparturePositions' => [ 'shape' => 'CalculateRouteMatrixResponseSnappedDeparturePositionsList', ], 'SnappedDestinationPositions' => [ 'shape' => 'CalculateRouteMatrixResponseSnappedDestinationPositionsList', ], 'Summary' => [ 'shape' => 'CalculateRouteMatrixSummary', ], ], ], 'CalculateRouteMatrixResponseSnappedDeparturePositionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'max' => 350, 'min' => 1, ], 'CalculateRouteMatrixResponseSnappedDestinationPositionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'max' => 350, 'min' => 1, ], 'CalculateRouteMatrixSummary' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'DistanceUnit', 'ErrorCount', 'RouteCount', ], 'members' => [ 'DataSource' => [ 'shape' => 'String', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'ErrorCount' => [ 'shape' => 'CalculateRouteMatrixSummaryErrorCountInteger', ], 'RouteCount' => [ 'shape' => 'CalculateRouteMatrixSummaryRouteCountInteger', ], ], ], 'CalculateRouteMatrixSummaryErrorCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 160000, 'min' => 1, ], 'CalculateRouteMatrixSummaryRouteCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 160000, 'min' => 1, ], 'CalculateRouteRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'DeparturePosition', 'DestinationPosition', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], 'CarModeOptions' => [ 'shape' => 'CalculateRouteCarModeOptions', ], 'DepartNow' => [ 'shape' => 'Boolean', ], 'DeparturePosition' => [ 'shape' => 'Position', ], 'DepartureTime' => [ 'shape' => 'Timestamp', ], 'DestinationPosition' => [ 'shape' => 'Position', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'IncludeLegGeometry' => [ 'shape' => 'Boolean', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'TravelMode' => [ 'shape' => 'TravelMode', ], 'TruckModeOptions' => [ 'shape' => 'CalculateRouteTruckModeOptions', ], 'WaypointPositions' => [ 'shape' => 'CalculateRouteRequestWaypointPositionsList', ], ], ], 'CalculateRouteRequestWaypointPositionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'max' => 23, 'min' => 0, ], 'CalculateRouteResponse' => [ 'type' => 'structure', 'required' => [ 'Legs', 'Summary', ], 'members' => [ 'Legs' => [ 'shape' => 'LegList', ], 'Summary' => [ 'shape' => 'CalculateRouteSummary', ], ], ], 'CalculateRouteSummary' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'Distance', 'DistanceUnit', 'DurationSeconds', 'RouteBBox', ], 'members' => [ 'DataSource' => [ 'shape' => 'String', ], 'Distance' => [ 'shape' => 'CalculateRouteSummaryDistanceDouble', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'DurationSeconds' => [ 'shape' => 'CalculateRouteSummaryDurationSecondsDouble', ], 'RouteBBox' => [ 'shape' => 'BoundingBox', ], ], ], 'CalculateRouteSummaryDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'CalculateRouteSummaryDurationSecondsDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'CalculateRouteTruckModeOptions' => [ 'type' => 'structure', 'members' => [ 'AvoidFerries' => [ 'shape' => 'Boolean', ], 'AvoidTolls' => [ 'shape' => 'Boolean', ], 'Dimensions' => [ 'shape' => 'TruckDimensions', ], 'Weight' => [ 'shape' => 'TruckWeight', ], ], ], 'Circle' => [ 'type' => 'structure', 'required' => [ 'Center', 'Radius', ], 'members' => [ 'Center' => [ 'shape' => 'Position', ], 'Radius' => [ 'shape' => 'Double', ], ], 'sensitive' => true, ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CountryCode' => [ 'type' => 'string', 'pattern' => '^[A-Z]{3}$', ], 'CountryCode3' => [ 'type' => 'string', 'max' => 3, 'min' => 3, 'pattern' => '^[A-Z]{3}$', ], 'CountryCode3OrEmpty' => [ 'type' => 'string', 'max' => 3, 'min' => 0, 'pattern' => '^[A-Z]{3}$|^$', ], 'CountryCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CountryCode', ], 'max' => 100, 'min' => 1, ], 'CreateGeofenceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. No longer allowed.', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateGeofenceCollectionResponse' => [ 'type' => 'structure', 'required' => [ 'CollectionArn', 'CollectionName', 'CreateTime', ], 'members' => [ 'CollectionArn' => [ 'shape' => 'Arn', ], 'CollectionName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateKeyRequest' => [ 'type' => 'structure', 'required' => [ 'KeyName', 'Restrictions', ], 'members' => [ 'Description' => [ 'shape' => 'ResourceDescription', ], 'ExpireTime' => [ 'shape' => 'Timestamp', ], 'KeyName' => [ 'shape' => 'ResourceName', ], 'NoExpiry' => [ 'shape' => 'Boolean', ], 'Restrictions' => [ 'shape' => 'ApiKeyRestrictions', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateKeyResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'Key', 'KeyArn', 'KeyName', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'Key' => [ 'shape' => 'ApiKey', ], 'KeyArn' => [ 'shape' => 'Arn', ], 'KeyName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateMapRequest' => [ 'type' => 'structure', 'required' => [ 'Configuration', 'MapName', ], 'members' => [ 'Configuration' => [ 'shape' => 'MapConfiguration', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'MapName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateMapResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'MapArn', 'MapName', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'MapArn' => [ 'shape' => 'GeoArn', ], 'MapName' => [ 'shape' => 'ResourceName', ], ], ], 'CreatePlaceIndexRequest' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'IndexName', ], 'members' => [ 'DataSource' => [ 'shape' => 'String', ], 'DataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'IndexName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreatePlaceIndexResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'IndexArn', 'IndexName', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'IndexArn' => [ 'shape' => 'GeoArn', ], 'IndexName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateRouteCalculatorRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'DataSource', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'DataSource' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateRouteCalculatorResponse' => [ 'type' => 'structure', 'required' => [ 'CalculatorArn', 'CalculatorName', 'CreateTime', ], 'members' => [ 'CalculatorArn' => [ 'shape' => 'GeoArn', ], 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'Description' => [ 'shape' => 'ResourceDescription', ], 'EventBridgeEnabled' => [ 'shape' => 'Boolean', ], 'KmsKeyEnableGeospatialQueries' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'PositionFiltering' => [ 'shape' => 'PositionFiltering', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. No longer allowed.', ], 'Tags' => [ 'shape' => 'TagMap', ], 'TrackerName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateTrackerResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'TrackerArn', 'TrackerName', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'TrackerArn' => [ 'shape' => 'Arn', ], 'TrackerName' => [ 'shape' => 'ResourceName', ], ], ], 'DataSourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'IntendedUse' => [ 'shape' => 'IntendedUse', ], ], ], 'DeleteGeofenceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], ], ], 'DeleteGeofenceCollectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteKeyRequest' => [ 'type' => 'structure', 'required' => [ 'KeyName', ], 'members' => [ 'KeyName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'KeyName', ], ], ], 'DeleteKeyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMapRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], ], ], 'DeleteMapResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePlaceIndexRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], ], ], 'DeletePlaceIndexResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRouteCalculatorRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], ], ], 'DeleteRouteCalculatorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'DeleteTrackerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeGeofenceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], ], ], 'DescribeGeofenceCollectionResponse' => [ 'type' => 'structure', 'required' => [ 'CollectionArn', 'CollectionName', 'CreateTime', 'Description', 'UpdateTime', ], 'members' => [ 'CollectionArn' => [ 'shape' => 'Arn', ], 'CollectionName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'GeofenceCount' => [ 'shape' => 'DescribeGeofenceCollectionResponseGeofenceCountInteger', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Unused.', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeGeofenceCollectionResponseGeofenceCountInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'DescribeKeyRequest' => [ 'type' => 'structure', 'required' => [ 'KeyName', ], 'members' => [ 'KeyName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'KeyName', ], ], ], 'DescribeKeyResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'ExpireTime', 'Key', 'KeyArn', 'KeyName', 'Restrictions', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'ExpireTime' => [ 'shape' => 'Timestamp', ], 'Key' => [ 'shape' => 'ApiKey', ], 'KeyArn' => [ 'shape' => 'Arn', ], 'KeyName' => [ 'shape' => 'ResourceName', ], 'Restrictions' => [ 'shape' => 'ApiKeyRestrictions', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeMapRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], ], ], 'DescribeMapResponse' => [ 'type' => 'structure', 'required' => [ 'Configuration', 'CreateTime', 'DataSource', 'Description', 'MapArn', 'MapName', 'UpdateTime', ], 'members' => [ 'Configuration' => [ 'shape' => 'MapConfiguration', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'MapArn' => [ 'shape' => 'GeoArn', ], 'MapName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribePlaceIndexRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], ], ], 'DescribePlaceIndexResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'DataSource', 'DataSourceConfiguration', 'Description', 'IndexArn', 'IndexName', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'DataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'IndexArn' => [ 'shape' => 'GeoArn', ], 'IndexName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeRouteCalculatorRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], ], ], 'DescribeRouteCalculatorResponse' => [ 'type' => 'structure', 'required' => [ 'CalculatorArn', 'CalculatorName', 'CreateTime', 'DataSource', 'Description', 'UpdateTime', ], 'members' => [ 'CalculatorArn' => [ 'shape' => 'GeoArn', ], 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'DescribeTrackerResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'Description', 'TrackerArn', 'TrackerName', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'EventBridgeEnabled' => [ 'shape' => 'Boolean', ], 'KmsKeyEnableGeospatialQueries' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'PositionFiltering' => [ 'shape' => 'PositionFiltering', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Unused.', ], 'Tags' => [ 'shape' => 'TagMap', ], 'TrackerArn' => [ 'shape' => 'Arn', ], 'TrackerName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DevicePosition' => [ 'type' => 'structure', 'required' => [ 'Position', 'ReceivedTime', 'SampleTime', ], 'members' => [ 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'DeviceId' => [ 'shape' => 'Id', ], 'Position' => [ 'shape' => 'Position', ], 'PositionProperties' => [ 'shape' => 'PropertyMap', ], 'ReceivedTime' => [ 'shape' => 'Timestamp', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], ], ], 'DevicePositionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevicePosition', ], ], 'DevicePositionUpdate' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Position', 'SampleTime', ], 'members' => [ 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'DeviceId' => [ 'shape' => 'Id', ], 'Position' => [ 'shape' => 'Position', ], 'PositionProperties' => [ 'shape' => 'PropertyMap', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], ], ], 'DimensionUnit' => [ 'type' => 'string', 'enum' => [ 'Meters', 'Feet', ], ], 'DisassociateTrackerConsumerRequest' => [ 'type' => 'structure', 'required' => [ 'ConsumerArn', 'TrackerName', ], 'members' => [ 'ConsumerArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ConsumerArn', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'DisassociateTrackerConsumerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DistanceUnit' => [ 'type' => 'string', 'enum' => [ 'Kilometers', 'Miles', ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'FilterPlaceCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlaceCategory', ], 'max' => 5, 'min' => 1, ], 'GeoArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 0, 'pattern' => '^arn(:[a-z0-9]+([.-][a-z0-9]+)*):geo(:([a-z0-9]+([.-][a-z0-9]+)*))(:[0-9]+):((\\*)|([-a-z]+[/][*-._\\w]+))$', ], 'GeofenceGeometry' => [ 'type' => 'structure', 'members' => [ 'Circle' => [ 'shape' => 'Circle', ], 'Polygon' => [ 'shape' => 'LinearRings', ], ], ], 'GetDevicePositionHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'TrackerName', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'DeviceId', ], 'EndTimeExclusive' => [ 'shape' => 'Timestamp', ], 'MaxResults' => [ 'shape' => 'GetDevicePositionHistoryRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], 'StartTimeInclusive' => [ 'shape' => 'Timestamp', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'GetDevicePositionHistoryRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'GetDevicePositionHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'DevicePositions', ], 'members' => [ 'DevicePositions' => [ 'shape' => 'DevicePositionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetDevicePositionRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'TrackerName', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'DeviceId', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'GetDevicePositionResponse' => [ 'type' => 'structure', 'required' => [ 'Position', 'ReceivedTime', 'SampleTime', ], 'members' => [ 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'DeviceId' => [ 'shape' => 'Id', ], 'Position' => [ 'shape' => 'Position', ], 'PositionProperties' => [ 'shape' => 'PropertyMap', ], 'ReceivedTime' => [ 'shape' => 'Timestamp', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetGeofenceRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'GeofenceId', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'GeofenceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'GeofenceId', ], ], ], 'GetGeofenceResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'GeofenceId', 'Geometry', 'Status', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'GeofenceId' => [ 'shape' => 'Id', ], 'GeofenceProperties' => [ 'shape' => 'PropertyMap', ], 'Geometry' => [ 'shape' => 'GeofenceGeometry', ], 'Status' => [ 'shape' => 'String', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetMapGlyphsRequest' => [ 'type' => 'structure', 'required' => [ 'FontStack', 'FontUnicodeRange', 'MapName', ], 'members' => [ 'FontStack' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'FontStack', ], 'FontUnicodeRange' => [ 'shape' => 'GetMapGlyphsRequestFontUnicodeRangeString', 'location' => 'uri', 'locationName' => 'FontUnicodeRange', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], ], ], 'GetMapGlyphsRequestFontUnicodeRangeString' => [ 'type' => 'string', 'pattern' => '^[0-9]+-[0-9]+\\.pbf$', ], 'GetMapGlyphsResponse' => [ 'type' => 'structure', 'members' => [ 'Blob' => [ 'shape' => 'Blob', ], 'CacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'ContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], ], 'payload' => 'Blob', ], 'GetMapSpritesRequest' => [ 'type' => 'structure', 'required' => [ 'FileName', 'MapName', ], 'members' => [ 'FileName' => [ 'shape' => 'GetMapSpritesRequestFileNameString', 'location' => 'uri', 'locationName' => 'FileName', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], ], ], 'GetMapSpritesRequestFileNameString' => [ 'type' => 'string', 'pattern' => '^sprites(@2x)?\\.(png|json)$', ], 'GetMapSpritesResponse' => [ 'type' => 'structure', 'members' => [ 'Blob' => [ 'shape' => 'Blob', ], 'CacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'ContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], ], 'payload' => 'Blob', ], 'GetMapStyleDescriptorRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', ], 'members' => [ 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], ], ], 'GetMapStyleDescriptorResponse' => [ 'type' => 'structure', 'members' => [ 'Blob' => [ 'shape' => 'Blob', ], 'CacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'ContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], ], 'payload' => 'Blob', ], 'GetMapTileRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', 'X', 'Y', 'Z', ], 'members' => [ 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], 'X' => [ 'shape' => 'GetMapTileRequestXString', 'location' => 'uri', 'locationName' => 'X', ], 'Y' => [ 'shape' => 'GetMapTileRequestYString', 'location' => 'uri', 'locationName' => 'Y', ], 'Z' => [ 'shape' => 'GetMapTileRequestZString', 'location' => 'uri', 'locationName' => 'Z', ], ], ], 'GetMapTileRequestXString' => [ 'type' => 'string', 'pattern' => '\\d+', ], 'GetMapTileRequestYString' => [ 'type' => 'string', 'pattern' => '\\d+', ], 'GetMapTileRequestZString' => [ 'type' => 'string', 'pattern' => '\\d+', ], 'GetMapTileResponse' => [ 'type' => 'structure', 'members' => [ 'Blob' => [ 'shape' => 'Blob', ], 'CacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'ContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], ], 'payload' => 'Blob', ], 'GetPlaceRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'PlaceId', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'Language' => [ 'shape' => 'LanguageTag', 'location' => 'querystring', 'locationName' => 'language', ], 'PlaceId' => [ 'shape' => 'PlaceId', 'location' => 'uri', 'locationName' => 'PlaceId', ], ], ], 'GetPlaceResponse' => [ 'type' => 'structure', 'required' => [ 'Place', ], 'members' => [ 'Place' => [ 'shape' => 'Place', ], ], ], 'Id' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[-._\\p{L}\\p{N}]+$', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'IntendedUse' => [ 'type' => 'string', 'enum' => [ 'SingleUse', 'Storage', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'LanguageTag' => [ 'type' => 'string', 'max' => 35, 'min' => 2, ], 'Leg' => [ 'type' => 'structure', 'required' => [ 'Distance', 'DurationSeconds', 'EndPosition', 'StartPosition', 'Steps', ], 'members' => [ 'Distance' => [ 'shape' => 'LegDistanceDouble', ], 'DurationSeconds' => [ 'shape' => 'LegDurationSecondsDouble', ], 'EndPosition' => [ 'shape' => 'Position', ], 'Geometry' => [ 'shape' => 'LegGeometry', ], 'StartPosition' => [ 'shape' => 'Position', ], 'Steps' => [ 'shape' => 'StepList', ], ], ], 'LegDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'LegDurationSecondsDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'LegGeometry' => [ 'type' => 'structure', 'members' => [ 'LineString' => [ 'shape' => 'LineString', ], ], ], 'LegList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Leg', ], ], 'LineString' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'min' => 2, ], 'LinearRing' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'min' => 4, ], 'LinearRings' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinearRing', ], 'min' => 1, ], 'ListDevicePositionsRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'FilterGeometry' => [ 'shape' => 'TrackingFilterGeometry', ], 'MaxResults' => [ 'shape' => 'ListDevicePositionsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'ListDevicePositionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListDevicePositionsResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListDevicePositionsResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListDevicePositionsResponseEntry' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Position', 'SampleTime', ], 'members' => [ 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'DeviceId' => [ 'shape' => 'Id', ], 'Position' => [ 'shape' => 'Position', ], 'PositionProperties' => [ 'shape' => 'PropertyMap', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListDevicePositionsResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListDevicePositionsResponseEntry', ], ], 'ListGeofenceCollectionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListGeofenceCollectionsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListGeofenceCollectionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListGeofenceCollectionsResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListGeofenceCollectionsResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListGeofenceCollectionsResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'CreateTime', 'Description', 'UpdateTime', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Unused.', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListGeofenceCollectionsResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListGeofenceCollectionsResponseEntry', ], ], 'ListGeofenceResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'GeofenceId', 'Geometry', 'Status', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'GeofenceId' => [ 'shape' => 'Id', ], 'GeofenceProperties' => [ 'shape' => 'PropertyMap', ], 'Geometry' => [ 'shape' => 'GeofenceGeometry', ], 'Status' => [ 'shape' => 'String', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListGeofenceResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListGeofenceResponseEntry', ], ], 'ListGeofencesRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'MaxResults' => [ 'shape' => 'ListGeofencesRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListGeofencesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListGeofencesResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListGeofenceResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListKeysRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'ApiKeyFilter', ], 'MaxResults' => [ 'shape' => 'ListKeysRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListKeysRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListKeysResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListKeysResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListKeysResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'ExpireTime', 'KeyName', 'Restrictions', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'ExpireTime' => [ 'shape' => 'Timestamp', ], 'KeyName' => [ 'shape' => 'ResourceName', ], 'Restrictions' => [ 'shape' => 'ApiKeyRestrictions', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListKeysResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListKeysResponseEntry', ], ], 'ListMapsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListMapsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListMapsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListMapsResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListMapsResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListMapsResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'DataSource', 'Description', 'MapName', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'MapName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListMapsResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListMapsResponseEntry', ], ], 'ListPlaceIndexesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListPlaceIndexesRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPlaceIndexesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListPlaceIndexesResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListPlaceIndexesResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPlaceIndexesResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'DataSource', 'Description', 'IndexName', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'IndexName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListPlaceIndexesResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListPlaceIndexesResponseEntry', ], ], 'ListRouteCalculatorsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListRouteCalculatorsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListRouteCalculatorsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListRouteCalculatorsResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListRouteCalculatorsResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListRouteCalculatorsResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'CreateTime', 'DataSource', 'Description', 'UpdateTime', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListRouteCalculatorsResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListRouteCalculatorsResponseEntry', ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTrackerConsumersRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'MaxResults' => [ 'shape' => 'ListTrackerConsumersRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'ListTrackerConsumersRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListTrackerConsumersResponse' => [ 'type' => 'structure', 'required' => [ 'ConsumerArns', ], 'members' => [ 'ConsumerArns' => [ 'shape' => 'ArnList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTrackersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListTrackersRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTrackersRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListTrackersResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListTrackersResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTrackersResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'Description', 'TrackerName', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Unused.', ], 'TrackerName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListTrackersResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListTrackersResponseEntry', ], ], 'MapConfiguration' => [ 'type' => 'structure', 'required' => [ 'Style', ], 'members' => [ 'PoliticalView' => [ 'shape' => 'CountryCode3', ], 'Style' => [ 'shape' => 'MapStyle', ], ], ], 'MapConfigurationUpdate' => [ 'type' => 'structure', 'members' => [ 'PoliticalView' => [ 'shape' => 'CountryCode3OrEmpty', ], ], ], 'MapStyle' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[-._\\w]+$', ], 'Place' => [ 'type' => 'structure', 'required' => [ 'Geometry', ], 'members' => [ 'AddressNumber' => [ 'shape' => 'String', ], 'Categories' => [ 'shape' => 'PlaceCategoryList', ], 'Country' => [ 'shape' => 'String', ], 'Geometry' => [ 'shape' => 'PlaceGeometry', ], 'Interpolated' => [ 'shape' => 'Boolean', ], 'Label' => [ 'shape' => 'String', ], 'Municipality' => [ 'shape' => 'String', ], 'Neighborhood' => [ 'shape' => 'String', ], 'PostalCode' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'Street' => [ 'shape' => 'String', ], 'SubRegion' => [ 'shape' => 'String', ], 'SupplementalCategories' => [ 'shape' => 'PlaceSupplementalCategoryList', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'UnitNumber' => [ 'shape' => 'String', ], 'UnitType' => [ 'shape' => 'String', ], ], ], 'PlaceCategory' => [ 'type' => 'string', 'max' => 35, 'min' => 0, ], 'PlaceCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlaceCategory', ], 'max' => 10, 'min' => 1, ], 'PlaceGeometry' => [ 'type' => 'structure', 'members' => [ 'Point' => [ 'shape' => 'Position', ], ], ], 'PlaceId' => [ 'type' => 'string', ], 'PlaceIndexSearchResultLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'PlaceSupplementalCategory' => [ 'type' => 'string', 'max' => 35, 'min' => 0, ], 'PlaceSupplementalCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlaceSupplementalCategory', ], 'max' => 10, 'min' => 1, ], 'Position' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 2, 'min' => 2, 'sensitive' => true, ], 'PositionFiltering' => [ 'type' => 'string', 'enum' => [ 'TimeBased', 'DistanceBased', 'AccuracyBased', ], ], 'PositionalAccuracy' => [ 'type' => 'structure', 'required' => [ 'Horizontal', ], 'members' => [ 'Horizontal' => [ 'shape' => 'PositionalAccuracyHorizontalDouble', ], ], ], 'PositionalAccuracyHorizontalDouble' => [ 'type' => 'double', 'box' => true, 'max' => 10000, 'min' => 0, ], 'PricingPlan' => [ 'type' => 'string', 'enum' => [ 'RequestBasedUsage', 'MobileAssetTracking', 'MobileAssetManagement', ], ], 'PropertyMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PropertyMapKeyString', ], 'value' => [ 'shape' => 'PropertyMapValueString', ], 'max' => 3, 'min' => 0, 'sensitive' => true, ], 'PropertyMapKeyString' => [ 'type' => 'string', 'max' => 20, 'min' => 1, ], 'PropertyMapValueString' => [ 'type' => 'string', 'max' => 40, 'min' => 1, ], 'PutGeofenceRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'GeofenceId', 'Geometry', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'GeofenceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'GeofenceId', ], 'GeofenceProperties' => [ 'shape' => 'PropertyMap', ], 'Geometry' => [ 'shape' => 'GeofenceGeometry', ], ], ], 'PutGeofenceResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'GeofenceId', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'GeofenceId' => [ 'shape' => 'Id', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'RefererPattern' => [ 'type' => 'string', 'max' => 253, 'min' => 0, 'pattern' => '^([$\\-._+!*\\x{60}(),;/?:@=&\\w]|%([0-9a-fA-F?]{2}|[0-9a-fA-F?]?[*]))+$', ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'ResourceName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[-._\\w]+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RouteMatrix' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteMatrixRow', ], ], 'RouteMatrixEntry' => [ 'type' => 'structure', 'members' => [ 'Distance' => [ 'shape' => 'RouteMatrixEntryDistanceDouble', ], 'DurationSeconds' => [ 'shape' => 'RouteMatrixEntryDurationSecondsDouble', ], 'Error' => [ 'shape' => 'RouteMatrixEntryError', ], ], ], 'RouteMatrixEntryDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'RouteMatrixEntryDurationSecondsDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'RouteMatrixEntryError' => [ 'type' => 'structure', 'required' => [ 'Code', ], 'members' => [ 'Code' => [ 'shape' => 'RouteMatrixErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'RouteMatrixErrorCode' => [ 'type' => 'string', 'enum' => [ 'RouteNotFound', 'RouteTooLong', 'PositionsNotFound', 'DestinationPositionNotFound', 'DeparturePositionNotFound', 'OtherValidationError', ], ], 'RouteMatrixRow' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteMatrixEntry', ], ], 'SearchForPositionResult' => [ 'type' => 'structure', 'required' => [ 'Distance', 'Place', ], 'members' => [ 'Distance' => [ 'shape' => 'SearchForPositionResultDistanceDouble', ], 'Place' => [ 'shape' => 'Place', ], 'PlaceId' => [ 'shape' => 'PlaceId', ], ], ], 'SearchForPositionResultDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'SearchForPositionResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchForPositionResult', ], ], 'SearchForSuggestionsResult' => [ 'type' => 'structure', 'required' => [ 'Text', ], 'members' => [ 'Categories' => [ 'shape' => 'PlaceCategoryList', ], 'PlaceId' => [ 'shape' => 'PlaceId', ], 'SupplementalCategories' => [ 'shape' => 'PlaceSupplementalCategoryList', ], 'Text' => [ 'shape' => 'String', ], ], ], 'SearchForSuggestionsResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchForSuggestionsResult', ], ], 'SearchForTextResult' => [ 'type' => 'structure', 'required' => [ 'Place', ], 'members' => [ 'Distance' => [ 'shape' => 'SearchForTextResultDistanceDouble', ], 'Place' => [ 'shape' => 'Place', ], 'PlaceId' => [ 'shape' => 'PlaceId', ], 'Relevance' => [ 'shape' => 'SearchForTextResultRelevanceDouble', ], ], ], 'SearchForTextResultDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'SearchForTextResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchForTextResult', ], ], 'SearchForTextResultRelevanceDouble' => [ 'type' => 'double', 'box' => true, 'max' => 1, 'min' => 0, ], 'SearchPlaceIndexForPositionRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'Position', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'MaxResults' => [ 'shape' => 'PlaceIndexSearchResultLimit', ], 'Position' => [ 'shape' => 'Position', ], ], ], 'SearchPlaceIndexForPositionResponse' => [ 'type' => 'structure', 'required' => [ 'Results', 'Summary', ], 'members' => [ 'Results' => [ 'shape' => 'SearchForPositionResultList', ], 'Summary' => [ 'shape' => 'SearchPlaceIndexForPositionSummary', ], ], ], 'SearchPlaceIndexForPositionSummary' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'Position', ], 'members' => [ 'DataSource' => [ 'shape' => 'String', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'MaxResults' => [ 'shape' => 'PlaceIndexSearchResultLimit', ], 'Position' => [ 'shape' => 'Position', ], ], ], 'SearchPlaceIndexForSuggestionsRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'Text', ], 'members' => [ 'BiasPosition' => [ 'shape' => 'Position', ], 'FilterBBox' => [ 'shape' => 'BoundingBox', ], 'FilterCategories' => [ 'shape' => 'FilterPlaceCategoryList', ], 'FilterCountries' => [ 'shape' => 'CountryCodeList', ], 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'MaxResults' => [ 'shape' => 'SearchPlaceIndexForSuggestionsRequestMaxResultsInteger', ], 'Text' => [ 'shape' => 'SearchPlaceIndexForSuggestionsRequestTextString', ], ], ], 'SearchPlaceIndexForSuggestionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 15, 'min' => 1, ], 'SearchPlaceIndexForSuggestionsRequestTextString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'SearchPlaceIndexForSuggestionsResponse' => [ 'type' => 'structure', 'required' => [ 'Results', 'Summary', ], 'members' => [ 'Results' => [ 'shape' => 'SearchForSuggestionsResultList', ], 'Summary' => [ 'shape' => 'SearchPlaceIndexForSuggestionsSummary', ], ], ], 'SearchPlaceIndexForSuggestionsSummary' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'Text', ], 'members' => [ 'BiasPosition' => [ 'shape' => 'Position', ], 'DataSource' => [ 'shape' => 'String', ], 'FilterBBox' => [ 'shape' => 'BoundingBox', ], 'FilterCategories' => [ 'shape' => 'FilterPlaceCategoryList', ], 'FilterCountries' => [ 'shape' => 'CountryCodeList', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'MaxResults' => [ 'shape' => 'Integer', ], 'Text' => [ 'shape' => 'SensitiveString', ], ], ], 'SearchPlaceIndexForTextRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'Text', ], 'members' => [ 'BiasPosition' => [ 'shape' => 'Position', ], 'FilterBBox' => [ 'shape' => 'BoundingBox', ], 'FilterCategories' => [ 'shape' => 'FilterPlaceCategoryList', ], 'FilterCountries' => [ 'shape' => 'CountryCodeList', ], 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'MaxResults' => [ 'shape' => 'PlaceIndexSearchResultLimit', ], 'Text' => [ 'shape' => 'SearchPlaceIndexForTextRequestTextString', ], ], ], 'SearchPlaceIndexForTextRequestTextString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'SearchPlaceIndexForTextResponse' => [ 'type' => 'structure', 'required' => [ 'Results', 'Summary', ], 'members' => [ 'Results' => [ 'shape' => 'SearchForTextResultList', ], 'Summary' => [ 'shape' => 'SearchPlaceIndexForTextSummary', ], ], ], 'SearchPlaceIndexForTextSummary' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'Text', ], 'members' => [ 'BiasPosition' => [ 'shape' => 'Position', ], 'DataSource' => [ 'shape' => 'String', ], 'FilterBBox' => [ 'shape' => 'BoundingBox', ], 'FilterCategories' => [ 'shape' => 'FilterPlaceCategoryList', ], 'FilterCountries' => [ 'shape' => 'CountryCodeList', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'MaxResults' => [ 'shape' => 'PlaceIndexSearchResultLimit', ], 'ResultBBox' => [ 'shape' => 'BoundingBox', ], 'Text' => [ 'shape' => 'SensitiveString', ], ], ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'Status' => [ 'type' => 'string', 'enum' => [ 'Active', 'Expired', ], ], 'Step' => [ 'type' => 'structure', 'required' => [ 'Distance', 'DurationSeconds', 'EndPosition', 'StartPosition', ], 'members' => [ 'Distance' => [ 'shape' => 'StepDistanceDouble', ], 'DurationSeconds' => [ 'shape' => 'StepDurationSecondsDouble', ], 'EndPosition' => [ 'shape' => 'Position', ], 'GeometryOffset' => [ 'shape' => 'StepGeometryOffsetInteger', ], 'StartPosition' => [ 'shape' => 'Position', ], ], ], 'StepDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'StepDurationSecondsDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'StepGeometryOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'StepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Step', ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z+-=._:/]+$', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[A-Za-z0-9 _=@:.+-/]*$', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'TimeZone' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Offset' => [ 'shape' => 'Integer', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Token' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'TrackingFilterGeometry' => [ 'type' => 'structure', 'members' => [ 'Polygon' => [ 'shape' => 'LinearRings', ], ], ], 'TravelMode' => [ 'type' => 'string', 'enum' => [ 'Car', 'Truck', 'Walking', 'Bicycle', 'Motorcycle', ], ], 'TruckDimensions' => [ 'type' => 'structure', 'members' => [ 'Height' => [ 'shape' => 'TruckDimensionsHeightDouble', ], 'Length' => [ 'shape' => 'TruckDimensionsLengthDouble', ], 'Unit' => [ 'shape' => 'DimensionUnit', ], 'Width' => [ 'shape' => 'TruckDimensionsWidthDouble', ], ], ], 'TruckDimensionsHeightDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'TruckDimensionsLengthDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'TruckDimensionsWidthDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'TruckWeight' => [ 'type' => 'structure', 'members' => [ 'Total' => [ 'shape' => 'TruckWeightTotalDouble', ], 'Unit' => [ 'shape' => 'VehicleWeightUnit', ], ], ], 'TruckWeightTotalDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGeofenceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. No longer allowed.', ], ], ], 'UpdateGeofenceCollectionResponse' => [ 'type' => 'structure', 'required' => [ 'CollectionArn', 'CollectionName', 'UpdateTime', ], 'members' => [ 'CollectionArn' => [ 'shape' => 'Arn', ], 'CollectionName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateKeyRequest' => [ 'type' => 'structure', 'required' => [ 'KeyName', ], 'members' => [ 'Description' => [ 'shape' => 'ResourceDescription', ], 'ExpireTime' => [ 'shape' => 'Timestamp', ], 'ForceUpdate' => [ 'shape' => 'Boolean', ], 'KeyName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'KeyName', ], 'NoExpiry' => [ 'shape' => 'Boolean', ], 'Restrictions' => [ 'shape' => 'ApiKeyRestrictions', ], ], ], 'UpdateKeyResponse' => [ 'type' => 'structure', 'required' => [ 'KeyArn', 'KeyName', 'UpdateTime', ], 'members' => [ 'KeyArn' => [ 'shape' => 'Arn', ], 'KeyName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateMapRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', ], 'members' => [ 'ConfigurationUpdate' => [ 'shape' => 'MapConfigurationUpdate', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', ], ], ], 'UpdateMapResponse' => [ 'type' => 'structure', 'required' => [ 'MapArn', 'MapName', 'UpdateTime', ], 'members' => [ 'MapArn' => [ 'shape' => 'GeoArn', ], 'MapName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdatePlaceIndexRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'DataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', ], ], ], 'UpdatePlaceIndexResponse' => [ 'type' => 'structure', 'required' => [ 'IndexArn', 'IndexName', 'UpdateTime', ], 'members' => [ 'IndexArn' => [ 'shape' => 'GeoArn', ], 'IndexName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateRouteCalculatorRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', ], ], ], 'UpdateRouteCalculatorResponse' => [ 'type' => 'structure', 'required' => [ 'CalculatorArn', 'CalculatorName', 'UpdateTime', ], 'members' => [ 'CalculatorArn' => [ 'shape' => 'GeoArn', ], 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'Description' => [ 'shape' => 'ResourceDescription', ], 'EventBridgeEnabled' => [ 'shape' => 'Boolean', ], 'KmsKeyEnableGeospatialQueries' => [ 'shape' => 'Boolean', ], 'PositionFiltering' => [ 'shape' => 'PositionFiltering', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. No longer allowed.', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'UpdateTrackerResponse' => [ 'type' => 'structure', 'required' => [ 'TrackerArn', 'TrackerName', 'UpdateTime', ], 'members' => [ 'TrackerArn' => [ 'shape' => 'Arn', ], 'TrackerName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'FieldList', 'Message', 'Reason', ], 'members' => [ 'FieldList' => [ 'shape' => 'ValidationExceptionFieldList', 'locationName' => 'fieldList', ], 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', 'locationName' => 'reason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Message', 'Name', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UnknownOperation', 'Missing', 'CannotParse', 'FieldValidationFailed', 'Other', ], ], 'VehicleWeightUnit' => [ 'type' => 'string', 'enum' => [ 'Kilograms', 'Pounds', ], ], ],];
