<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Preview\Wireless;

use Twilio\Options;
use Twilio\Values;

abstract class SimOptions
{

    /**
     * @param string $status 
     * @param string $iccid 
     * @param string $ratePlan 
     * @param string $eId 
     * @param string $simRegistrationCode 
     * @return ReadSimOptions Options builder
     */
    public static function read(
        
        string $status = Values::NONE,
        string $iccid = Values::NONE,
        string $ratePlan = Values::NONE,
        string $eId = Values::NONE,
        string $simRegistrationCode = Values::NONE

    ): ReadSimOptions
    {
        return new ReadSimOptions(
            $status,
            $iccid,
            $ratePlan,
            $eId,
            $simRegistrationCode
        );
    }

    /**
     * @param string $uniqueName 
     * @param string $callbackMethod 
     * @param string $callbackUrl 
     * @param string $friendlyName 
     * @param string $ratePlan 
     * @param string $status 
     * @param string $commandsCallbackMethod 
     * @param string $commandsCallbackUrl 
     * @param string $smsFallbackMethod 
     * @param string $smsFallbackUrl 
     * @param string $smsMethod 
     * @param string $smsUrl 
     * @param string $voiceFallbackMethod 
     * @param string $voiceFallbackUrl 
     * @param string $voiceMethod 
     * @param string $voiceUrl 
     * @return UpdateSimOptions Options builder
     */
    public static function update(
        
        string $uniqueName = Values::NONE,
        string $callbackMethod = Values::NONE,
        string $callbackUrl = Values::NONE,
        string $friendlyName = Values::NONE,
        string $ratePlan = Values::NONE,
        string $status = Values::NONE,
        string $commandsCallbackMethod = Values::NONE,
        string $commandsCallbackUrl = Values::NONE,
        string $smsFallbackMethod = Values::NONE,
        string $smsFallbackUrl = Values::NONE,
        string $smsMethod = Values::NONE,
        string $smsUrl = Values::NONE,
        string $voiceFallbackMethod = Values::NONE,
        string $voiceFallbackUrl = Values::NONE,
        string $voiceMethod = Values::NONE,
        string $voiceUrl = Values::NONE

    ): UpdateSimOptions
    {
        return new UpdateSimOptions(
            $uniqueName,
            $callbackMethod,
            $callbackUrl,
            $friendlyName,
            $ratePlan,
            $status,
            $commandsCallbackMethod,
            $commandsCallbackUrl,
            $smsFallbackMethod,
            $smsFallbackUrl,
            $smsMethod,
            $smsUrl,
            $voiceFallbackMethod,
            $voiceFallbackUrl,
            $voiceMethod,
            $voiceUrl
        );
    }

}


class ReadSimOptions extends Options
    {
    /**
     * @param string $status 
     * @param string $iccid 
     * @param string $ratePlan 
     * @param string $eId 
     * @param string $simRegistrationCode 
     */
    public function __construct(
        
        string $status = Values::NONE,
        string $iccid = Values::NONE,
        string $ratePlan = Values::NONE,
        string $eId = Values::NONE,
        string $simRegistrationCode = Values::NONE

    ) {
        $this->options['status'] = $status;
        $this->options['iccid'] = $iccid;
        $this->options['ratePlan'] = $ratePlan;
        $this->options['eId'] = $eId;
        $this->options['simRegistrationCode'] = $simRegistrationCode;
    }

    /**
     * 
     *
     * @param string $status 
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * 
     *
     * @param string $iccid 
     * @return $this Fluent Builder
     */
    public function setIccid(string $iccid): self
    {
        $this->options['iccid'] = $iccid;
        return $this;
    }

    /**
     * 
     *
     * @param string $ratePlan 
     * @return $this Fluent Builder
     */
    public function setRatePlan(string $ratePlan): self
    {
        $this->options['ratePlan'] = $ratePlan;
        return $this;
    }

    /**
     * 
     *
     * @param string $eId 
     * @return $this Fluent Builder
     */
    public function setEId(string $eId): self
    {
        $this->options['eId'] = $eId;
        return $this;
    }

    /**
     * 
     *
     * @param string $simRegistrationCode 
     * @return $this Fluent Builder
     */
    public function setSimRegistrationCode(string $simRegistrationCode): self
    {
        $this->options['simRegistrationCode'] = $simRegistrationCode;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Wireless.ReadSimOptions ' . $options . ']';
    }
}

class UpdateSimOptions extends Options
    {
    /**
     * @param string $uniqueName 
     * @param string $callbackMethod 
     * @param string $callbackUrl 
     * @param string $friendlyName 
     * @param string $ratePlan 
     * @param string $status 
     * @param string $commandsCallbackMethod 
     * @param string $commandsCallbackUrl 
     * @param string $smsFallbackMethod 
     * @param string $smsFallbackUrl 
     * @param string $smsMethod 
     * @param string $smsUrl 
     * @param string $voiceFallbackMethod 
     * @param string $voiceFallbackUrl 
     * @param string $voiceMethod 
     * @param string $voiceUrl 
     */
    public function __construct(
        
        string $uniqueName = Values::NONE,
        string $callbackMethod = Values::NONE,
        string $callbackUrl = Values::NONE,
        string $friendlyName = Values::NONE,
        string $ratePlan = Values::NONE,
        string $status = Values::NONE,
        string $commandsCallbackMethod = Values::NONE,
        string $commandsCallbackUrl = Values::NONE,
        string $smsFallbackMethod = Values::NONE,
        string $smsFallbackUrl = Values::NONE,
        string $smsMethod = Values::NONE,
        string $smsUrl = Values::NONE,
        string $voiceFallbackMethod = Values::NONE,
        string $voiceFallbackUrl = Values::NONE,
        string $voiceMethod = Values::NONE,
        string $voiceUrl = Values::NONE

    ) {
        $this->options['uniqueName'] = $uniqueName;
        $this->options['callbackMethod'] = $callbackMethod;
        $this->options['callbackUrl'] = $callbackUrl;
        $this->options['friendlyName'] = $friendlyName;
        $this->options['ratePlan'] = $ratePlan;
        $this->options['status'] = $status;
        $this->options['commandsCallbackMethod'] = $commandsCallbackMethod;
        $this->options['commandsCallbackUrl'] = $commandsCallbackUrl;
        $this->options['smsFallbackMethod'] = $smsFallbackMethod;
        $this->options['smsFallbackUrl'] = $smsFallbackUrl;
        $this->options['smsMethod'] = $smsMethod;
        $this->options['smsUrl'] = $smsUrl;
        $this->options['voiceFallbackMethod'] = $voiceFallbackMethod;
        $this->options['voiceFallbackUrl'] = $voiceFallbackUrl;
        $this->options['voiceMethod'] = $voiceMethod;
        $this->options['voiceUrl'] = $voiceUrl;
    }

    /**
     * 
     *
     * @param string $uniqueName 
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * 
     *
     * @param string $callbackMethod 
     * @return $this Fluent Builder
     */
    public function setCallbackMethod(string $callbackMethod): self
    {
        $this->options['callbackMethod'] = $callbackMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $callbackUrl 
     * @return $this Fluent Builder
     */
    public function setCallbackUrl(string $callbackUrl): self
    {
        $this->options['callbackUrl'] = $callbackUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $friendlyName 
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * 
     *
     * @param string $ratePlan 
     * @return $this Fluent Builder
     */
    public function setRatePlan(string $ratePlan): self
    {
        $this->options['ratePlan'] = $ratePlan;
        return $this;
    }

    /**
     * 
     *
     * @param string $status 
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * 
     *
     * @param string $commandsCallbackMethod 
     * @return $this Fluent Builder
     */
    public function setCommandsCallbackMethod(string $commandsCallbackMethod): self
    {
        $this->options['commandsCallbackMethod'] = $commandsCallbackMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $commandsCallbackUrl 
     * @return $this Fluent Builder
     */
    public function setCommandsCallbackUrl(string $commandsCallbackUrl): self
    {
        $this->options['commandsCallbackUrl'] = $commandsCallbackUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $smsFallbackMethod 
     * @return $this Fluent Builder
     */
    public function setSmsFallbackMethod(string $smsFallbackMethod): self
    {
        $this->options['smsFallbackMethod'] = $smsFallbackMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $smsFallbackUrl 
     * @return $this Fluent Builder
     */
    public function setSmsFallbackUrl(string $smsFallbackUrl): self
    {
        $this->options['smsFallbackUrl'] = $smsFallbackUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $smsMethod 
     * @return $this Fluent Builder
     */
    public function setSmsMethod(string $smsMethod): self
    {
        $this->options['smsMethod'] = $smsMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $smsUrl 
     * @return $this Fluent Builder
     */
    public function setSmsUrl(string $smsUrl): self
    {
        $this->options['smsUrl'] = $smsUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $voiceFallbackMethod 
     * @return $this Fluent Builder
     */
    public function setVoiceFallbackMethod(string $voiceFallbackMethod): self
    {
        $this->options['voiceFallbackMethod'] = $voiceFallbackMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $voiceFallbackUrl 
     * @return $this Fluent Builder
     */
    public function setVoiceFallbackUrl(string $voiceFallbackUrl): self
    {
        $this->options['voiceFallbackUrl'] = $voiceFallbackUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $voiceMethod 
     * @return $this Fluent Builder
     */
    public function setVoiceMethod(string $voiceMethod): self
    {
        $this->options['voiceMethod'] = $voiceMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $voiceUrl 
     * @return $this Fluent Builder
     */
    public function setVoiceUrl(string $voiceUrl): self
    {
        $this->options['voiceUrl'] = $voiceUrl;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Wireless.UpdateSimOptions ' . $options . ']';
    }
}

