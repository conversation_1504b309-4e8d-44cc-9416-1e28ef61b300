<div class="cp-app-container" data-app="ui-languages">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                UI Languages
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Manage UI Language settings
                    </h2>
                </div>
                <div class="body">
                    <table class="table">
                        <thead>
                            <th>
                                Lang name
                            </th>
                            <th>
                                Usage
                            </th>
                            <th>
                                Status
                            </th>
                            <th>
                                Default
                            </th>
                            <th>
                                Direction
                            </th>
                            <th>
                                Action
                            </th>
                        </thead>
                        <tbody>
                            <?php foreach ($cl["ui_langs"] as $lang): ?>
                                <tr>
                                    <td>
                                        <b>
                                            <?php echo($lang["name"]); ?>
                                        </b>
                                    </td>
                                    <td>
                                        <span class="badge bg-blue">
                                            <b><?php echo($lang["usage"]); ?> users</b>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($lang["slug"] != $cl["config"]["language"]): ?>
                                            <div class="select-box">
                                                <select onchange="SMC_CPanel.PS.toggle_status(this, <?php echo($lang["id"]); ?>);">
                                                    <option value="active" <?php if($lang["status"] == '1'){echo('selected');}?>>Enabled</option>
                                                    <option value="inactive" <?php if($lang["status"] == '0'){echo('selected');}?>>Disabled</option>
                                                </select>
                                            </div>
                                        <?php else: ?>
                                            <div class="select-box">
                                                <select disabled>
                                                    <option value="active">Enabled</option>
                                                    <option value="inactive">Disabled</option>
                                                </select>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($lang["slug"] == $cl["config"]["language"]): ?>
                                            <span class="icon">
                                                <?php echo cl_ficon("checkmark_circle"); ?>
                                            </span>
                                        <?php else: ?>
                                            <?php if ($lang["status"] == '1'): ?>
                                                <span class="icon pointer" onclick="SMC_CPanel.PS.set_default(<?php echo($lang["id"]); ?>);">
                                                    <?php echo cl_ficon("circle"); ?>
                                                </span>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($lang["is_rtl"] == "Y"): ?>
                                            <span class="badge bg-blue">
                                                <b>RTL</b>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-grey">
                                                <b>LTR</b>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($lang["is_native"] == "1"): ?>
                                            <span class="icon not-allowed" onclick="SMC_CPanel.PS.delete_lang_restriction();">
                                                <?php echo cl_ficon("delete"); ?>
                                            </span>
                                        <?php else: ?>
                                            <?php if ($lang["slug"] == $cl["config"]["language"]): ?>
                                                <span class="icon not-allowed">
                                                    <?php echo cl_ficon("delete"); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="icon pointer" onclick="SMC_CPanel.PS.delete_lang(<?php echo($lang["id"]); ?>);">
                                                    <?php echo cl_ficon("delete"); ?>
                                                </span>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Add new UI language
                    </h2>
                </div>
                    <div class="body">
                        <div class="inline-alertbox-wrapper">
                            <div class="inline-alertbox info">
                                <div class="icon">
                                    <?php echo cl_ficon("info"); ?>
                                </div>
                                <div class="alert-message">
                                    <p>
                                        Please note that after adding a new language, you will need to translate it from English into the one you added. <br>

                                        Files with language texts are located in <b><code>core/langs</code></b>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <form class="form" data-an="form">
                            <div class="form-group">
                                <label>
                                    Select UI language
                                </label>
                                <div class="form-line form-select">
                                    <select name="language" data-size="5" class="form-control">
                                        <?php foreach ($cl['language_codes'] as $lang_name => $lang_data): ?>
                                            <option value="<?php echo($lang_name); ?>">
                                                <?php echo $lang_data["name"]; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>
                                    Status
                                </label>
                                <div class="form-line form-select">
                                    <select name="status" class="form-control">
                                        <option value="active">
                                            Enabled
                                        </option>
                                        <option value="inactive">
                                            Disabled
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>
                                    Direction <b class="col-red">(Important)</b>
                                </label>
                                <div class="form-line form-select">
                                    <select name="direction" class="form-control">
                                        <option value="ltr">
                                            Left to right
                                        </option>
                                        <option value="rtl">
                                            Right to left
                                        </option>
                                    </select>
                                </div>
                                <small class="info-feedback">
                                    Please define the direction <b>(LTR or RTL?)</b> of the language before adding it. Otherwise, you cannot change this value after adding
                                </small>
                            </div>

                            <div class="form-group no-mb">
                                <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                    Add language
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/languages/scripts/app_master_script'); ?>