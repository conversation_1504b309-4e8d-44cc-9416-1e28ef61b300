<?php
// This file was auto-generated from sdk-root/src/data/datazone/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'endpointPrefix' => 'datazone', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon DataZone', 'serviceId' => 'DataZone', 'signatureVersion' => 'v4', 'signingName' => 'datazone', 'uid' => 'datazone-2018-05-10', ], 'operations' => [ 'AcceptPredictions' => [ 'name' => 'AcceptPredictions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/domains/{domainIdentifier}/assets/{identifier}/accept-predictions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AcceptPredictionsInput', ], 'output' => [ 'shape' => 'AcceptPredictionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'AcceptSubscriptionRequest' => [ 'name' => 'AcceptSubscriptionRequest', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/domains/{domainIdentifier}/subscription-requests/{identifier}/accept', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AcceptSubscriptionRequestInput', ], 'output' => [ 'shape' => 'AcceptSubscriptionRequestOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'CancelSubscription' => [ 'name' => 'CancelSubscription', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/domains/{domainIdentifier}/subscriptions/{identifier}/cancel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelSubscriptionInput', ], 'output' => [ 'shape' => 'CancelSubscriptionOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'CreateAsset' => [ 'name' => 'CreateAsset', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/assets', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAssetInput', ], 'output' => [ 'shape' => 'CreateAssetOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'CreateAssetRevision' => [ 'name' => 'CreateAssetRevision', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/assets/{identifier}/revisions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAssetRevisionInput', ], 'output' => [ 'shape' => 'CreateAssetRevisionOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'CreateAssetType' => [ 'name' => 'CreateAssetType', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/asset-types', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAssetTypeInput', ], 'output' => [ 'shape' => 'CreateAssetTypeOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'CreateDataSource' => [ 'name' => 'CreateDataSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/data-sources', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDataSourceInput', ], 'output' => [ 'shape' => 'CreateDataSourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'CreateDomain' => [ 'name' => 'CreateDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDomainInput', ], 'output' => [ 'shape' => 'CreateDomainOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'CreateEnvironment' => [ 'name' => 'CreateEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/environments', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateEnvironmentInput', ], 'output' => [ 'shape' => 'CreateEnvironmentOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'CreateEnvironmentProfile' => [ 'name' => 'CreateEnvironmentProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/environment-profiles', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateEnvironmentProfileInput', ], 'output' => [ 'shape' => 'CreateEnvironmentProfileOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'CreateFormType' => [ 'name' => 'CreateFormType', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/form-types', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFormTypeInput', ], 'output' => [ 'shape' => 'CreateFormTypeOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'CreateGlossary' => [ 'name' => 'CreateGlossary', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/glossaries', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateGlossaryInput', ], 'output' => [ 'shape' => 'CreateGlossaryOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'CreateGlossaryTerm' => [ 'name' => 'CreateGlossaryTerm', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/glossary-terms', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateGlossaryTermInput', ], 'output' => [ 'shape' => 'CreateGlossaryTermOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'CreateGroupProfile' => [ 'name' => 'CreateGroupProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/group-profiles', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateGroupProfileInput', ], 'output' => [ 'shape' => 'CreateGroupProfileOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'CreateListingChangeSet' => [ 'name' => 'CreateListingChangeSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/listings/change-set', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateListingChangeSetInput', ], 'output' => [ 'shape' => 'CreateListingChangeSetOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'CreateProject' => [ 'name' => 'CreateProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/projects', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateProjectInput', ], 'output' => [ 'shape' => 'CreateProjectOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'CreateProjectMembership' => [ 'name' => 'CreateProjectMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/projects/{projectIdentifier}/createMembership', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateProjectMembershipInput', ], 'output' => [ 'shape' => 'CreateProjectMembershipOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'CreateSubscriptionGrant' => [ 'name' => 'CreateSubscriptionGrant', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/subscription-grants', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSubscriptionGrantInput', ], 'output' => [ 'shape' => 'CreateSubscriptionGrantOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'CreateSubscriptionRequest' => [ 'name' => 'CreateSubscriptionRequest', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/subscription-requests', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSubscriptionRequestInput', ], 'output' => [ 'shape' => 'CreateSubscriptionRequestOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'CreateSubscriptionTarget' => [ 'name' => 'CreateSubscriptionTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSubscriptionTargetInput', ], 'output' => [ 'shape' => 'CreateSubscriptionTargetOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'CreateUserProfile' => [ 'name' => 'CreateUserProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/user-profiles', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateUserProfileInput', ], 'output' => [ 'shape' => 'CreateUserProfileOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteAsset' => [ 'name' => 'DeleteAsset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/assets/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAssetInput', ], 'output' => [ 'shape' => 'DeleteAssetOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteAssetType' => [ 'name' => 'DeleteAssetType', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/asset-types/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAssetTypeInput', ], 'output' => [ 'shape' => 'DeleteAssetTypeOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'DeleteDataSource' => [ 'name' => 'DeleteDataSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/data-sources/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDataSourceInput', ], 'output' => [ 'shape' => 'DeleteDataSourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteDomain' => [ 'name' => 'DeleteDomain', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{identifier}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteDomainInput', ], 'output' => [ 'shape' => 'DeleteDomainOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteEnvironment' => [ 'name' => 'DeleteEnvironment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/environments/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteEnvironmentInput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteEnvironmentBlueprintConfiguration' => [ 'name' => 'DeleteEnvironmentBlueprintConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/environment-blueprint-configurations/{environmentBlueprintIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteEnvironmentBlueprintConfigurationInput', ], 'output' => [ 'shape' => 'DeleteEnvironmentBlueprintConfigurationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteEnvironmentProfile' => [ 'name' => 'DeleteEnvironmentProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/environment-profiles/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteEnvironmentProfileInput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteFormType' => [ 'name' => 'DeleteFormType', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/form-types/{formTypeIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFormTypeInput', ], 'output' => [ 'shape' => 'DeleteFormTypeOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'DeleteGlossary' => [ 'name' => 'DeleteGlossary', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/glossaries/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteGlossaryInput', ], 'output' => [ 'shape' => 'DeleteGlossaryOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteGlossaryTerm' => [ 'name' => 'DeleteGlossaryTerm', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/glossary-terms/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteGlossaryTermInput', ], 'output' => [ 'shape' => 'DeleteGlossaryTermOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteListing' => [ 'name' => 'DeleteListing', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/listings/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteListingInput', ], 'output' => [ 'shape' => 'DeleteListingOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteProject' => [ 'name' => 'DeleteProject', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/projects/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteProjectInput', ], 'output' => [ 'shape' => 'DeleteProjectOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteProjectMembership' => [ 'name' => 'DeleteProjectMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/projects/{projectIdentifier}/deleteMembership', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteProjectMembershipInput', ], 'output' => [ 'shape' => 'DeleteProjectMembershipOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteSubscriptionGrant' => [ 'name' => 'DeleteSubscriptionGrant', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/subscription-grants/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSubscriptionGrantInput', ], 'output' => [ 'shape' => 'DeleteSubscriptionGrantOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'DeleteSubscriptionRequest' => [ 'name' => 'DeleteSubscriptionRequest', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/subscription-requests/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSubscriptionRequestInput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'DeleteSubscriptionTarget' => [ 'name' => 'DeleteSubscriptionTarget', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSubscriptionTargetInput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetAsset' => [ 'name' => 'GetAsset', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/assets/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAssetInput', ], 'output' => [ 'shape' => 'GetAssetOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetAssetType' => [ 'name' => 'GetAssetType', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/asset-types/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAssetTypeInput', ], 'output' => [ 'shape' => 'GetAssetTypeOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetDataSource' => [ 'name' => 'GetDataSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/data-sources/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataSourceInput', ], 'output' => [ 'shape' => 'GetDataSourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetDataSourceRun' => [ 'name' => 'GetDataSourceRun', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/data-source-runs/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataSourceRunInput', ], 'output' => [ 'shape' => 'GetDataSourceRunOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetDomain' => [ 'name' => 'GetDomain', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDomainInput', ], 'output' => [ 'shape' => 'GetDomainOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetEnvironment' => [ 'name' => 'GetEnvironment', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/environments/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEnvironmentInput', ], 'output' => [ 'shape' => 'GetEnvironmentOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetEnvironmentBlueprint' => [ 'name' => 'GetEnvironmentBlueprint', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/environment-blueprints/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEnvironmentBlueprintInput', ], 'output' => [ 'shape' => 'GetEnvironmentBlueprintOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetEnvironmentBlueprintConfiguration' => [ 'name' => 'GetEnvironmentBlueprintConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/environment-blueprint-configurations/{environmentBlueprintIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEnvironmentBlueprintConfigurationInput', ], 'output' => [ 'shape' => 'GetEnvironmentBlueprintConfigurationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetEnvironmentProfile' => [ 'name' => 'GetEnvironmentProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/environment-profiles/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEnvironmentProfileInput', ], 'output' => [ 'shape' => 'GetEnvironmentProfileOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetFormType' => [ 'name' => 'GetFormType', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/form-types/{formTypeIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFormTypeInput', ], 'output' => [ 'shape' => 'GetFormTypeOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetGlossary' => [ 'name' => 'GetGlossary', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/glossaries/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGlossaryInput', ], 'output' => [ 'shape' => 'GetGlossaryOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetGlossaryTerm' => [ 'name' => 'GetGlossaryTerm', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/glossary-terms/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGlossaryTermInput', ], 'output' => [ 'shape' => 'GetGlossaryTermOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetGroupProfile' => [ 'name' => 'GetGroupProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/group-profiles/{groupIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGroupProfileInput', ], 'output' => [ 'shape' => 'GetGroupProfileOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetIamPortalLoginUrl' => [ 'name' => 'GetIamPortalLoginUrl', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/get-portal-login-url', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIamPortalLoginUrlInput', ], 'output' => [ 'shape' => 'GetIamPortalLoginUrlOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetListing' => [ 'name' => 'GetListing', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/listings/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetListingInput', ], 'output' => [ 'shape' => 'GetListingOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetProject' => [ 'name' => 'GetProject', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/projects/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProjectInput', ], 'output' => [ 'shape' => 'GetProjectOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetSubscription' => [ 'name' => 'GetSubscription', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/subscriptions/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSubscriptionInput', ], 'output' => [ 'shape' => 'GetSubscriptionOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetSubscriptionGrant' => [ 'name' => 'GetSubscriptionGrant', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/subscription-grants/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSubscriptionGrantInput', ], 'output' => [ 'shape' => 'GetSubscriptionGrantOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetSubscriptionRequestDetails' => [ 'name' => 'GetSubscriptionRequestDetails', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/subscription-requests/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSubscriptionRequestDetailsInput', ], 'output' => [ 'shape' => 'GetSubscriptionRequestDetailsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetSubscriptionTarget' => [ 'name' => 'GetSubscriptionTarget', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSubscriptionTargetInput', ], 'output' => [ 'shape' => 'GetSubscriptionTargetOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetUserProfile' => [ 'name' => 'GetUserProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/user-profiles/{userIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUserProfileInput', ], 'output' => [ 'shape' => 'GetUserProfileOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListAssetRevisions' => [ 'name' => 'ListAssetRevisions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/assets/{identifier}/revisions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAssetRevisionsInput', ], 'output' => [ 'shape' => 'ListAssetRevisionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListDataSourceRunActivities' => [ 'name' => 'ListDataSourceRunActivities', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/data-source-runs/{identifier}/activities', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSourceRunActivitiesInput', ], 'output' => [ 'shape' => 'ListDataSourceRunActivitiesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListDataSourceRuns' => [ 'name' => 'ListDataSourceRuns', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/data-sources/{dataSourceIdentifier}/runs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSourceRunsInput', ], 'output' => [ 'shape' => 'ListDataSourceRunsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListDataSources' => [ 'name' => 'ListDataSources', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/data-sources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSourcesInput', ], 'output' => [ 'shape' => 'ListDataSourcesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListDomains' => [ 'name' => 'ListDomains', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDomainsInput', ], 'output' => [ 'shape' => 'ListDomainsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListEnvironmentBlueprintConfigurations' => [ 'name' => 'ListEnvironmentBlueprintConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/environment-blueprint-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEnvironmentBlueprintConfigurationsInput', ], 'output' => [ 'shape' => 'ListEnvironmentBlueprintConfigurationsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListEnvironmentBlueprints' => [ 'name' => 'ListEnvironmentBlueprints', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/environment-blueprints', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEnvironmentBlueprintsInput', ], 'output' => [ 'shape' => 'ListEnvironmentBlueprintsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListEnvironmentProfiles' => [ 'name' => 'ListEnvironmentProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/environment-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEnvironmentProfilesInput', ], 'output' => [ 'shape' => 'ListEnvironmentProfilesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListEnvironments' => [ 'name' => 'ListEnvironments', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/environments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEnvironmentsInput', ], 'output' => [ 'shape' => 'ListEnvironmentsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListNotifications' => [ 'name' => 'ListNotifications', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/notifications', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNotificationsInput', ], 'output' => [ 'shape' => 'ListNotificationsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListProjectMemberships' => [ 'name' => 'ListProjectMemberships', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/projects/{projectIdentifier}/memberships', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProjectMembershipsInput', ], 'output' => [ 'shape' => 'ListProjectMembershipsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListProjects' => [ 'name' => 'ListProjects', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/projects', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProjectsInput', ], 'output' => [ 'shape' => 'ListProjectsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListSubscriptionGrants' => [ 'name' => 'ListSubscriptionGrants', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/subscription-grants', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSubscriptionGrantsInput', ], 'output' => [ 'shape' => 'ListSubscriptionGrantsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListSubscriptionRequests' => [ 'name' => 'ListSubscriptionRequests', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/subscription-requests', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSubscriptionRequestsInput', ], 'output' => [ 'shape' => 'ListSubscriptionRequestsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListSubscriptionTargets' => [ 'name' => 'ListSubscriptionTargets', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSubscriptionTargetsInput', ], 'output' => [ 'shape' => 'ListSubscriptionTargetsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListSubscriptions' => [ 'name' => 'ListSubscriptions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/domains/{domainIdentifier}/subscriptions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSubscriptionsInput', ], 'output' => [ 'shape' => 'ListSubscriptionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'PutEnvironmentBlueprintConfiguration' => [ 'name' => 'PutEnvironmentBlueprintConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/domains/{domainIdentifier}/environment-blueprint-configurations/{environmentBlueprintIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutEnvironmentBlueprintConfigurationInput', ], 'output' => [ 'shape' => 'PutEnvironmentBlueprintConfigurationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'RejectPredictions' => [ 'name' => 'RejectPredictions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/domains/{domainIdentifier}/assets/{identifier}/reject-predictions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RejectPredictionsInput', ], 'output' => [ 'shape' => 'RejectPredictionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'RejectSubscriptionRequest' => [ 'name' => 'RejectSubscriptionRequest', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/domains/{domainIdentifier}/subscription-requests/{identifier}/reject', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RejectSubscriptionRequestInput', ], 'output' => [ 'shape' => 'RejectSubscriptionRequestOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'RevokeSubscription' => [ 'name' => 'RevokeSubscription', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/domains/{domainIdentifier}/subscriptions/{identifier}/revoke', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RevokeSubscriptionInput', ], 'output' => [ 'shape' => 'RevokeSubscriptionOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'Search' => [ 'name' => 'Search', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchInput', ], 'output' => [ 'shape' => 'SearchOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'SearchGroupProfiles' => [ 'name' => 'SearchGroupProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/search-group-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchGroupProfilesInput', ], 'output' => [ 'shape' => 'SearchGroupProfilesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'SearchListings' => [ 'name' => 'SearchListings', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/listings/search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchListingsInput', ], 'output' => [ 'shape' => 'SearchListingsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'SearchTypes' => [ 'name' => 'SearchTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/types-search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchTypesInput', ], 'output' => [ 'shape' => 'SearchTypesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'SearchUserProfiles' => [ 'name' => 'SearchUserProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/search-user-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchUserProfilesInput', ], 'output' => [ 'shape' => 'SearchUserProfilesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'StartDataSourceRun' => [ 'name' => 'StartDataSourceRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/domains/{domainIdentifier}/data-sources/{dataSourceIdentifier}/runs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartDataSourceRunInput', ], 'output' => [ 'shape' => 'StartDataSourceRunOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'UpdateDataSource' => [ 'name' => 'UpdateDataSource', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v2/domains/{domainIdentifier}/data-sources/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDataSourceInput', ], 'output' => [ 'shape' => 'UpdateDataSourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'UpdateDomain' => [ 'name' => 'UpdateDomain', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/domains/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDomainInput', ], 'output' => [ 'shape' => 'UpdateDomainOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'UpdateEnvironment' => [ 'name' => 'UpdateEnvironment', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v2/domains/{domainIdentifier}/environments/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEnvironmentInput', ], 'output' => [ 'shape' => 'UpdateEnvironmentOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'UpdateEnvironmentProfile' => [ 'name' => 'UpdateEnvironmentProfile', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v2/domains/{domainIdentifier}/environment-profiles/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEnvironmentProfileInput', ], 'output' => [ 'shape' => 'UpdateEnvironmentProfileOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'UpdateGlossary' => [ 'name' => 'UpdateGlossary', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v2/domains/{domainIdentifier}/glossaries/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateGlossaryInput', ], 'output' => [ 'shape' => 'UpdateGlossaryOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'UpdateGlossaryTerm' => [ 'name' => 'UpdateGlossaryTerm', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v2/domains/{domainIdentifier}/glossary-terms/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateGlossaryTermInput', ], 'output' => [ 'shape' => 'UpdateGlossaryTermOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'UpdateGroupProfile' => [ 'name' => 'UpdateGroupProfile', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/domains/{domainIdentifier}/group-profiles/{groupIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateGroupProfileInput', ], 'output' => [ 'shape' => 'UpdateGroupProfileOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'UpdateProject' => [ 'name' => 'UpdateProject', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v2/domains/{domainIdentifier}/projects/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateProjectInput', ], 'output' => [ 'shape' => 'UpdateProjectOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'UpdateSubscriptionGrantStatus' => [ 'name' => 'UpdateSubscriptionGrantStatus', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v2/domains/{domainIdentifier}/subscription-grants/{identifier}/status/{assetIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSubscriptionGrantStatusInput', ], 'output' => [ 'shape' => 'UpdateSubscriptionGrantStatusOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'UpdateSubscriptionRequest' => [ 'name' => 'UpdateSubscriptionRequest', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v2/domains/{domainIdentifier}/subscription-requests/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSubscriptionRequestInput', ], 'output' => [ 'shape' => 'UpdateSubscriptionRequestOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'UpdateSubscriptionTarget' => [ 'name' => 'UpdateSubscriptionTarget', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSubscriptionTargetInput', ], 'output' => [ 'shape' => 'UpdateSubscriptionTargetOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'UpdateUserProfile' => [ 'name' => 'UpdateUserProfile', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/domains/{domainIdentifier}/user-profiles/{userIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateUserProfileInput', ], 'output' => [ 'shape' => 'UpdateUserProfileOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UnauthorizedException', ], ], ], ], 'shapes' => [ 'AcceptChoice' => [ 'type' => 'structure', 'members' => [ 'predictionChoice' => [ 'shape' => 'Integer', ], 'predictionTarget' => [ 'shape' => 'String', ], ], ], 'AcceptChoices' => [ 'type' => 'list', 'member' => [ 'shape' => 'AcceptChoice', ], ], 'AcceptPredictionsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'acceptChoices' => [ 'shape' => 'AcceptChoices', ], 'acceptRule' => [ 'shape' => 'AcceptRule', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'AssetIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], 'revision' => [ 'shape' => 'Revision', 'location' => 'querystring', 'locationName' => 'revision', ], ], ], 'AcceptPredictionsOutput' => [ 'type' => 'structure', 'required' => [ 'assetId', 'domainId', 'revision', ], 'members' => [ 'assetId' => [ 'shape' => 'AssetId', ], 'domainId' => [ 'shape' => 'DomainId', ], 'revision' => [ 'shape' => 'Revision', ], ], ], 'AcceptRule' => [ 'type' => 'structure', 'members' => [ 'rule' => [ 'shape' => 'AcceptRuleBehavior', ], 'threshold' => [ 'shape' => 'Float', ], ], ], 'AcceptRuleBehavior' => [ 'type' => 'string', 'enum' => [ 'ALL', 'NONE', ], ], 'AcceptSubscriptionRequestInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'decisionComment' => [ 'shape' => 'DecisionComment', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionRequestId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'AcceptSubscriptionRequestOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'id', 'requestReason', 'status', 'subscribedListings', 'subscribedPrincipals', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'decisionComment' => [ 'shape' => 'DecisionComment', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'SubscriptionRequestId', ], 'requestReason' => [ 'shape' => 'RequestReason', ], 'reviewerId' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'SubscriptionRequestStatus', ], 'subscribedListings' => [ 'shape' => 'AcceptSubscriptionRequestOutputSubscribedListingsList', ], 'subscribedPrincipals' => [ 'shape' => 'AcceptSubscriptionRequestOutputSubscribedPrincipalsList', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'AcceptSubscriptionRequestOutputSubscribedListingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedListing', ], 'max' => 1, 'min' => 1, ], 'AcceptSubscriptionRequestOutputSubscribedPrincipalsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedPrincipal', ], 'max' => 1, 'min' => 1, ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActionLink' => [ 'type' => 'string', 'sensitive' => true, ], 'ApplicableAssetTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'TypeName', ], ], 'AssetId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'AssetIdentifier' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'AssetItem' => [ 'type' => 'structure', 'required' => [ 'domainId', 'identifier', 'name', 'owningProjectId', 'typeIdentifier', 'typeRevision', ], 'members' => [ 'additionalAttributes' => [ 'shape' => 'AssetItemAdditionalAttributes', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'externalIdentifier' => [ 'shape' => 'ExternalIdentifier', ], 'firstRevisionCreatedAt' => [ 'shape' => 'CreatedAt', ], 'firstRevisionCreatedBy' => [ 'shape' => 'CreatedBy', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'identifier' => [ 'shape' => 'AssetIdentifier', ], 'name' => [ 'shape' => 'AssetName', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'typeIdentifier' => [ 'shape' => 'AssetTypeIdentifier', ], 'typeRevision' => [ 'shape' => 'Revision', ], ], ], 'AssetItemAdditionalAttributes' => [ 'type' => 'structure', 'members' => [ 'formsOutput' => [ 'shape' => 'FormOutputList', ], 'readOnlyFormsOutput' => [ 'shape' => 'FormOutputList', ], ], ], 'AssetListing' => [ 'type' => 'structure', 'members' => [ 'assetId' => [ 'shape' => 'AssetId', ], 'assetRevision' => [ 'shape' => 'Revision', ], 'assetType' => [ 'shape' => 'TypeName', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'forms' => [ 'shape' => 'Forms', ], 'glossaryTerms' => [ 'shape' => 'DetailedGlossaryTerms', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], ], ], 'AssetListingDetails' => [ 'type' => 'structure', 'required' => [ 'listingId', 'listingStatus', ], 'members' => [ 'listingId' => [ 'shape' => 'ListingId', ], 'listingStatus' => [ 'shape' => 'ListingStatus', ], ], ], 'AssetListingItem' => [ 'type' => 'structure', 'members' => [ 'additionalAttributes' => [ 'shape' => 'AssetListingItemAdditionalAttributes', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'description' => [ 'shape' => 'Description', ], 'entityId' => [ 'shape' => 'AssetId', ], 'entityRevision' => [ 'shape' => 'Revision', ], 'entityType' => [ 'shape' => 'TypeName', ], 'glossaryTerms' => [ 'shape' => 'DetailedGlossaryTerms', ], 'listingCreatedBy' => [ 'shape' => 'CreatedBy', ], 'listingId' => [ 'shape' => 'ListingId', ], 'listingRevision' => [ 'shape' => 'Revision', ], 'listingUpdatedBy' => [ 'shape' => 'UpdatedBy', ], 'name' => [ 'shape' => 'AssetName', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], ], ], 'AssetListingItemAdditionalAttributes' => [ 'type' => 'structure', 'members' => [ 'forms' => [ 'shape' => 'Forms', ], ], ], 'AssetName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'sensitive' => true, ], 'AssetRevision' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'AssetId', ], 'revision' => [ 'shape' => 'Revision', ], ], ], 'AssetRevisions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetRevision', ], ], 'AssetTargetNameMap' => [ 'type' => 'structure', 'required' => [ 'assetId', 'targetName', ], 'members' => [ 'assetId' => [ 'shape' => 'AssetId', ], 'targetName' => [ 'shape' => 'String', ], ], ], 'AssetTargetNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetTargetNameMap', ], ], 'AssetTypeIdentifier' => [ 'type' => 'string', 'max' => 385, 'min' => 1, 'pattern' => '^(?!\\.)[\\w\\.]*\\w$', ], 'AssetTypeItem' => [ 'type' => 'structure', 'required' => [ 'domainId', 'formsOutput', 'name', 'owningProjectId', 'revision', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'formsOutput' => [ 'shape' => 'FormsOutputMap', ], 'name' => [ 'shape' => 'TypeName', ], 'originDomainId' => [ 'shape' => 'DomainId', ], 'originProjectId' => [ 'shape' => 'ProjectId', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'revision' => [ 'shape' => 'Revision', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'Attribute' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'AuthType' => [ 'type' => 'string', 'enum' => [ 'IAM_IDC', 'DISABLED', ], ], 'AuthorizedPrincipalIdentifier' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9:/_-]*$', ], 'AuthorizedPrincipalIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthorizedPrincipalIdentifier', ], 'max' => 10, 'min' => 1, ], 'AwsAccountId' => [ 'type' => 'string', 'pattern' => '^\\d{12}$', ], 'AwsRegion' => [ 'type' => 'string', 'pattern' => '^[a-z]{2}-[a-z]{4,10}-\\d$', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BusinessNameGenerationConfiguration' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], ], ], 'CancelSubscriptionInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'CancelSubscriptionOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'id', 'status', 'subscribedListing', 'subscribedPrincipal', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'SubscriptionId', ], 'retainPermissions' => [ 'shape' => 'Boolean', ], 'status' => [ 'shape' => 'SubscriptionStatus', ], 'subscribedListing' => [ 'shape' => 'SubscribedListing', ], 'subscribedPrincipal' => [ 'shape' => 'SubscribedPrincipal', ], 'subscriptionRequestId' => [ 'shape' => 'SubscriptionRequestId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'ChangeAction' => [ 'type' => 'string', 'enum' => [ 'PUBLISH', 'UNPUBLISH', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[\\x21-\\x7E]+$', ], 'CloudFormationProperties' => [ 'type' => 'structure', 'required' => [ 'templateUrl', ], 'members' => [ 'templateUrl' => [ 'shape' => 'String', ], ], ], 'ConfigurableActionParameter' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'ConfigurableActionParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurableActionParameter', ], ], 'ConfigurableActionTypeAuthorization' => [ 'type' => 'string', 'enum' => [ 'IAM', 'HTTPS', ], ], 'ConfigurableEnvironmentAction' => [ 'type' => 'structure', 'required' => [ 'parameters', 'type', ], 'members' => [ 'auth' => [ 'shape' => 'ConfigurableActionTypeAuthorization', ], 'parameters' => [ 'shape' => 'ConfigurableActionParameterList', ], 'type' => [ 'shape' => 'String', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateAssetInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'name', 'owningProjectIdentifier', 'typeIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'externalIdentifier' => [ 'shape' => 'ExternalIdentifier', ], 'formsInput' => [ 'shape' => 'FormInputList', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'name' => [ 'shape' => 'AssetName', ], 'owningProjectIdentifier' => [ 'shape' => 'ProjectId', ], 'predictionConfiguration' => [ 'shape' => 'PredictionConfiguration', ], 'typeIdentifier' => [ 'shape' => 'AssetTypeIdentifier', ], 'typeRevision' => [ 'shape' => 'Revision', ], ], ], 'CreateAssetOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'formsOutput', 'id', 'name', 'owningProjectId', 'revision', 'typeIdentifier', 'typeRevision', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'externalIdentifier' => [ 'shape' => 'ExternalIdentifier', ], 'firstRevisionCreatedAt' => [ 'shape' => 'CreatedAt', ], 'firstRevisionCreatedBy' => [ 'shape' => 'CreatedBy', ], 'formsOutput' => [ 'shape' => 'FormOutputList', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'id' => [ 'shape' => 'AssetId', ], 'listing' => [ 'shape' => 'AssetListingDetails', ], 'name' => [ 'shape' => 'AssetName', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'predictionConfiguration' => [ 'shape' => 'PredictionConfiguration', ], 'readOnlyFormsOutput' => [ 'shape' => 'FormOutputList', ], 'revision' => [ 'shape' => 'Revision', ], 'typeIdentifier' => [ 'shape' => 'AssetTypeIdentifier', ], 'typeRevision' => [ 'shape' => 'Revision', ], ], ], 'CreateAssetRevisionInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'formsInput' => [ 'shape' => 'FormInputList', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'identifier' => [ 'shape' => 'AssetIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], 'name' => [ 'shape' => 'AssetName', ], 'predictionConfiguration' => [ 'shape' => 'PredictionConfiguration', ], 'typeRevision' => [ 'shape' => 'Revision', ], ], ], 'CreateAssetRevisionOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'formsOutput', 'id', 'name', 'owningProjectId', 'revision', 'typeIdentifier', 'typeRevision', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'externalIdentifier' => [ 'shape' => 'ExternalIdentifier', ], 'firstRevisionCreatedAt' => [ 'shape' => 'CreatedAt', ], 'firstRevisionCreatedBy' => [ 'shape' => 'CreatedBy', ], 'formsOutput' => [ 'shape' => 'FormOutputList', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'id' => [ 'shape' => 'AssetId', ], 'listing' => [ 'shape' => 'AssetListingDetails', ], 'name' => [ 'shape' => 'AssetName', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'predictionConfiguration' => [ 'shape' => 'PredictionConfiguration', ], 'readOnlyFormsOutput' => [ 'shape' => 'FormOutputList', ], 'revision' => [ 'shape' => 'Revision', ], 'typeIdentifier' => [ 'shape' => 'AssetTypeIdentifier', ], 'typeRevision' => [ 'shape' => 'Revision', ], ], ], 'CreateAssetTypeInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'formsInput', 'name', 'owningProjectIdentifier', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'formsInput' => [ 'shape' => 'FormsInputMap', ], 'name' => [ 'shape' => 'TypeName', ], 'owningProjectIdentifier' => [ 'shape' => 'ProjectId', ], ], ], 'CreateAssetTypeOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'formsOutput', 'name', 'revision', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'formsOutput' => [ 'shape' => 'FormsOutputMap', ], 'name' => [ 'shape' => 'TypeName', ], 'originDomainId' => [ 'shape' => 'DomainId', ], 'originProjectId' => [ 'shape' => 'ProjectId', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'revision' => [ 'shape' => 'Revision', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'CreateDataSourceInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'environmentIdentifier', 'name', 'projectIdentifier', 'type', ], 'members' => [ 'assetFormsInput' => [ 'shape' => 'FormInputList', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'configuration' => [ 'shape' => 'DataSourceConfigurationInput', ], 'description' => [ 'shape' => 'Description', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'enableSetting' => [ 'shape' => 'EnableSetting', ], 'environmentIdentifier' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'Name', ], 'projectIdentifier' => [ 'shape' => 'String', ], 'publishOnImport' => [ 'shape' => 'Boolean', ], 'recommendation' => [ 'shape' => 'RecommendationConfiguration', ], 'schedule' => [ 'shape' => 'ScheduleConfiguration', ], 'type' => [ 'shape' => 'DataSourceType', ], ], ], 'CreateDataSourceOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'environmentId', 'id', 'name', 'projectId', ], 'members' => [ 'assetFormsOutput' => [ 'shape' => 'FormOutputList', ], 'configuration' => [ 'shape' => 'DataSourceConfigurationOutput', ], 'createdAt' => [ 'shape' => 'DateTime', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'enableSetting' => [ 'shape' => 'EnableSetting', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'errorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'id' => [ 'shape' => 'DataSourceId', ], 'lastRunAt' => [ 'shape' => 'DateTime', ], 'lastRunErrorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'lastRunStatus' => [ 'shape' => 'DataSourceRunStatus', ], 'name' => [ 'shape' => 'Name', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'publishOnImport' => [ 'shape' => 'Boolean', ], 'recommendation' => [ 'shape' => 'RecommendationConfiguration', ], 'schedule' => [ 'shape' => 'ScheduleConfiguration', ], 'status' => [ 'shape' => 'DataSourceStatus', ], 'type' => [ 'shape' => 'DataSourceType', ], 'updatedAt' => [ 'shape' => 'DateTime', ], ], ], 'CreateDomainInput' => [ 'type' => 'structure', 'required' => [ 'domainExecutionRole', 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'String', ], 'domainExecutionRole' => [ 'shape' => 'RoleArn', ], 'kmsKeyIdentifier' => [ 'shape' => 'KmsKeyArn', ], 'name' => [ 'shape' => 'String', ], 'singleSignOn' => [ 'shape' => 'SingleSignOn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateDomainOutput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'arn' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'domainExecutionRole' => [ 'shape' => 'RoleArn', ], 'id' => [ 'shape' => 'DomainId', ], 'kmsKeyIdentifier' => [ 'shape' => 'KmsKeyArn', ], 'name' => [ 'shape' => 'String', ], 'portalUrl' => [ 'shape' => 'String', ], 'singleSignOn' => [ 'shape' => 'SingleSignOn', ], 'status' => [ 'shape' => 'DomainStatus', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateEnvironmentInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'environmentProfileIdentifier', 'name', 'projectIdentifier', ], 'members' => [ 'description' => [ 'shape' => 'String', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentProfileIdentifier' => [ 'shape' => 'EnvironmentProfileId', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'name' => [ 'shape' => 'String', ], 'projectIdentifier' => [ 'shape' => 'ProjectId', ], 'userParameters' => [ 'shape' => 'EnvironmentParametersList', ], ], ], 'CreateEnvironmentOutput' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'domainId', 'environmentProfileId', 'name', 'projectId', 'provider', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AwsAccountId', ], 'awsAccountRegion' => [ 'shape' => 'AwsRegion', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'String', ], 'deploymentProperties' => [ 'shape' => 'DeploymentProperties', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'environmentActions' => [ 'shape' => 'EnvironmentActionList', ], 'environmentBlueprintId' => [ 'shape' => 'EnvironmentBlueprintId', ], 'environmentProfileId' => [ 'shape' => 'EnvironmentProfileId', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'id' => [ 'shape' => 'EnvironmentId', ], 'lastDeployment' => [ 'shape' => 'Deployment', ], 'name' => [ 'shape' => 'EnvironmentName', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'provider' => [ 'shape' => 'String', ], 'provisionedResources' => [ 'shape' => 'ResourceList', ], 'provisioningProperties' => [ 'shape' => 'ProvisioningProperties', ], 'status' => [ 'shape' => 'EnvironmentStatus', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'userParameters' => [ 'shape' => 'CustomParameterList', ], ], ], 'CreateEnvironmentProfileInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'environmentBlueprintIdentifier', 'name', 'projectIdentifier', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AwsAccountId', ], 'awsAccountRegion' => [ 'shape' => 'AwsRegion', ], 'description' => [ 'shape' => 'Description', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentBlueprintIdentifier' => [ 'shape' => 'EnvironmentBlueprintId', ], 'name' => [ 'shape' => 'EnvironmentProfileName', ], 'projectIdentifier' => [ 'shape' => 'ProjectId', ], 'userParameters' => [ 'shape' => 'EnvironmentParametersList', ], ], ], 'CreateEnvironmentProfileOutput' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'domainId', 'environmentBlueprintId', 'id', 'name', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AwsAccountId', ], 'awsAccountRegion' => [ 'shape' => 'AwsRegion', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'environmentBlueprintId' => [ 'shape' => 'EnvironmentBlueprintId', ], 'id' => [ 'shape' => 'EnvironmentProfileId', ], 'name' => [ 'shape' => 'EnvironmentProfileName', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'userParameters' => [ 'shape' => 'CustomParameterList', ], ], ], 'CreateFormTypeInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'model', 'name', 'owningProjectIdentifier', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'model' => [ 'shape' => 'Model', ], 'name' => [ 'shape' => 'FormTypeName', ], 'owningProjectIdentifier' => [ 'shape' => 'ProjectId', ], 'status' => [ 'shape' => 'FormTypeStatus', ], ], ], 'CreateFormTypeOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'name', 'revision', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'name' => [ 'shape' => 'FormTypeName', ], 'originDomainId' => [ 'shape' => 'DomainId', ], 'originProjectId' => [ 'shape' => 'ProjectId', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'revision' => [ 'shape' => 'Revision', ], ], ], 'CreateGlossaryInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'name', 'owningProjectIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'GlossaryDescription', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'name' => [ 'shape' => 'GlossaryName', ], 'owningProjectIdentifier' => [ 'shape' => 'ProjectId', ], 'status' => [ 'shape' => 'GlossaryStatus', ], ], ], 'CreateGlossaryOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'id', 'name', 'owningProjectId', ], 'members' => [ 'description' => [ 'shape' => 'GlossaryDescription', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'GlossaryId', ], 'name' => [ 'shape' => 'GlossaryName', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'status' => [ 'shape' => 'GlossaryStatus', ], ], ], 'CreateGlossaryTermInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'glossaryIdentifier', 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'glossaryIdentifier' => [ 'shape' => 'GlossaryTermId', ], 'longDescription' => [ 'shape' => 'LongDescription', ], 'name' => [ 'shape' => 'GlossaryTermName', ], 'shortDescription' => [ 'shape' => 'ShortDescription', ], 'status' => [ 'shape' => 'GlossaryTermStatus', ], 'termRelations' => [ 'shape' => 'TermRelations', ], ], ], 'CreateGlossaryTermOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'glossaryId', 'id', 'name', 'status', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', ], 'glossaryId' => [ 'shape' => 'GlossaryId', ], 'id' => [ 'shape' => 'GlossaryTermId', ], 'longDescription' => [ 'shape' => 'LongDescription', ], 'name' => [ 'shape' => 'GlossaryTermName', ], 'shortDescription' => [ 'shape' => 'ShortDescription', ], 'status' => [ 'shape' => 'GlossaryTermStatus', ], 'termRelations' => [ 'shape' => 'TermRelations', ], ], ], 'CreateGroupProfileInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'groupIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'groupIdentifier' => [ 'shape' => 'GroupIdentifier', ], ], ], 'CreateGroupProfileOutput' => [ 'type' => 'structure', 'members' => [ 'domainId' => [ 'shape' => 'DomainId', ], 'groupName' => [ 'shape' => 'GroupProfileName', ], 'id' => [ 'shape' => 'GroupProfileId', ], 'status' => [ 'shape' => 'GroupProfileStatus', ], ], ], 'CreateListingChangeSetInput' => [ 'type' => 'structure', 'required' => [ 'action', 'domainIdentifier', 'entityIdentifier', 'entityType', ], 'members' => [ 'action' => [ 'shape' => 'ChangeAction', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'entityIdentifier' => [ 'shape' => 'EntityId', ], 'entityRevision' => [ 'shape' => 'Revision', ], 'entityType' => [ 'shape' => 'EntityType', ], ], ], 'CreateListingChangeSetOutput' => [ 'type' => 'structure', 'required' => [ 'listingId', 'listingRevision', 'status', ], 'members' => [ 'listingId' => [ 'shape' => 'ListingId', ], 'listingRevision' => [ 'shape' => 'Revision', ], 'status' => [ 'shape' => 'ListingStatus', ], ], ], 'CreateProjectInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'name', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'name' => [ 'shape' => 'ProjectName', ], ], ], 'CreateProjectMembershipInput' => [ 'type' => 'structure', 'required' => [ 'designation', 'domainIdentifier', 'member', 'projectIdentifier', ], 'members' => [ 'designation' => [ 'shape' => 'UserDesignation', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'member' => [ 'shape' => 'Member', ], 'projectIdentifier' => [ 'shape' => 'ProjectId', 'location' => 'uri', 'locationName' => 'projectIdentifier', ], ], ], 'CreateProjectMembershipOutput' => [ 'type' => 'structure', 'members' => [], ], 'CreateProjectOutput' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'domainId', 'id', 'name', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'id' => [ 'shape' => 'ProjectId', ], 'lastUpdatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'name' => [ 'shape' => 'ProjectName', ], ], ], 'CreateSubscriptionGrantInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'environmentIdentifier', 'grantedEntity', 'subscriptionTargetIdentifier', ], 'members' => [ 'assetTargetNames' => [ 'shape' => 'AssetTargetNames', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentIdentifier' => [ 'shape' => 'EnvironmentId', ], 'grantedEntity' => [ 'shape' => 'GrantedEntityInput', ], 'subscriptionTargetIdentifier' => [ 'shape' => 'SubscriptionTargetId', ], ], ], 'CreateSubscriptionGrantOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'grantedEntity', 'id', 'status', 'subscriptionTargetId', 'updatedAt', ], 'members' => [ 'assets' => [ 'shape' => 'SubscribedAssets', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'grantedEntity' => [ 'shape' => 'GrantedEntity', ], 'id' => [ 'shape' => 'SubscriptionGrantId', ], 'status' => [ 'shape' => 'SubscriptionGrantOverallStatus', ], 'subscriptionId' => [ 'shape' => 'SubscriptionId', ], 'subscriptionTargetId' => [ 'shape' => 'SubscriptionTargetId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'CreateSubscriptionRequestInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'requestReason', 'subscribedListings', 'subscribedPrincipals', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'requestReason' => [ 'shape' => 'RequestReason', ], 'subscribedListings' => [ 'shape' => 'SubscribedListingInputs', ], 'subscribedPrincipals' => [ 'shape' => 'SubscribedPrincipalInputs', ], ], ], 'CreateSubscriptionRequestOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'id', 'requestReason', 'status', 'subscribedListings', 'subscribedPrincipals', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'decisionComment' => [ 'shape' => 'DecisionComment', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'SubscriptionRequestId', ], 'requestReason' => [ 'shape' => 'RequestReason', ], 'reviewerId' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'SubscriptionRequestStatus', ], 'subscribedListings' => [ 'shape' => 'CreateSubscriptionRequestOutputSubscribedListingsList', ], 'subscribedPrincipals' => [ 'shape' => 'CreateSubscriptionRequestOutputSubscribedPrincipalsList', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'CreateSubscriptionRequestOutputSubscribedListingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedListing', ], 'max' => 1, 'min' => 1, ], 'CreateSubscriptionRequestOutputSubscribedPrincipalsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedPrincipal', ], 'max' => 1, 'min' => 1, ], 'CreateSubscriptionTargetInput' => [ 'type' => 'structure', 'required' => [ 'applicableAssetTypes', 'authorizedPrincipals', 'domainIdentifier', 'environmentIdentifier', 'manageAccessRole', 'name', 'subscriptionTargetConfig', 'type', ], 'members' => [ 'applicableAssetTypes' => [ 'shape' => 'ApplicableAssetTypes', ], 'authorizedPrincipals' => [ 'shape' => 'AuthorizedPrincipalIdentifiers', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentIdentifier', ], 'manageAccessRole' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'SubscriptionTargetName', ], 'provider' => [ 'shape' => 'String', ], 'subscriptionTargetConfig' => [ 'shape' => 'SubscriptionTargetForms', ], 'type' => [ 'shape' => 'String', ], ], ], 'CreateSubscriptionTargetOutput' => [ 'type' => 'structure', 'required' => [ 'applicableAssetTypes', 'authorizedPrincipals', 'createdAt', 'createdBy', 'domainId', 'environmentId', 'id', 'manageAccessRole', 'name', 'projectId', 'provider', 'subscriptionTargetConfig', 'type', ], 'members' => [ 'applicableAssetTypes' => [ 'shape' => 'ApplicableAssetTypes', ], 'authorizedPrincipals' => [ 'shape' => 'AuthorizedPrincipalIdentifiers', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'id' => [ 'shape' => 'SubscriptionTargetId', ], 'manageAccessRole' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'SubscriptionTargetName', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'provider' => [ 'shape' => 'String', ], 'subscriptionTargetConfig' => [ 'shape' => 'SubscriptionTargetForms', ], 'type' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'CreateUserProfileInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'userIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'userIdentifier' => [ 'shape' => 'UserIdentifier', ], 'userType' => [ 'shape' => 'UserType', ], ], ], 'CreateUserProfileOutput' => [ 'type' => 'structure', 'members' => [ 'details' => [ 'shape' => 'UserProfileDetails', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'UserProfileId', ], 'status' => [ 'shape' => 'UserProfileStatus', ], 'type' => [ 'shape' => 'UserProfileType', ], ], ], 'CreatedAt' => [ 'type' => 'timestamp', ], 'CreatedBy' => [ 'type' => 'string', ], 'CronString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => 'cron\\((\\b[0-5]?[0-9]\\b) (\\b2[0-3]\\b|\\b[0-1]?[0-9]\\b) (.*){1,5} (.*){1,5} (.*){1,5} (.*){1,5}\\)', ], 'CustomParameter' => [ 'type' => 'structure', 'required' => [ 'fieldType', 'keyName', ], 'members' => [ 'defaultValue' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'fieldType' => [ 'shape' => 'String', ], 'isEditable' => [ 'shape' => 'Boolean', ], 'isOptional' => [ 'shape' => 'Boolean', ], 'keyName' => [ 'shape' => 'CustomParameterKeyNameString', ], ], ], 'CustomParameterKeyNameString' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z_][a-zA-Z0-9_]*$', ], 'CustomParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomParameter', ], ], 'DataAssetActivityStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'PUBLISHING_FAILED', 'SUCCEEDED_CREATED', 'SUCCEEDED_UPDATED', 'SKIPPED_ALREADY_IMPORTED', 'SKIPPED_ARCHIVED', 'SKIPPED_NO_ACCESS', 'UNCHANGED', ], ], 'DataProductDescription' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'sensitive' => true, ], 'DataProductId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'DataProductItem' => [ 'type' => 'structure', 'members' => [ 'domainId' => [ 'shape' => 'DomainId', ], 'itemId' => [ 'shape' => 'DataProductId', ], ], ], 'DataProductItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataProductItem', ], 'max' => 100, 'min' => 0, ], 'DataProductName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'DataProductSummary' => [ 'type' => 'structure', 'required' => [ 'domainId', 'id', 'name', 'owningProjectId', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'dataProductItems' => [ 'shape' => 'DataProductItems', ], 'description' => [ 'shape' => 'DataProductDescription', ], 'domainId' => [ 'shape' => 'DomainId', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'id' => [ 'shape' => 'DataProductId', ], 'name' => [ 'shape' => 'DataProductName', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'DataSourceConfigurationInput' => [ 'type' => 'structure', 'members' => [ 'glueRunConfiguration' => [ 'shape' => 'GlueRunConfigurationInput', ], 'redshiftRunConfiguration' => [ 'shape' => 'RedshiftRunConfigurationInput', ], ], 'union' => true, ], 'DataSourceConfigurationOutput' => [ 'type' => 'structure', 'members' => [ 'glueRunConfiguration' => [ 'shape' => 'GlueRunConfigurationOutput', ], 'redshiftRunConfiguration' => [ 'shape' => 'RedshiftRunConfigurationOutput', ], ], 'union' => true, ], 'DataSourceErrorMessage' => [ 'type' => 'structure', 'required' => [ 'errorType', ], 'members' => [ 'errorDetail' => [ 'shape' => 'String', ], 'errorType' => [ 'shape' => 'DataSourceErrorType', ], ], ], 'DataSourceErrorType' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED_EXCEPTION', 'CONFLICT_EXCEPTION', 'INTERNAL_SERVER_EXCEPTION', 'RESOURCE_NOT_FOUND_EXCEPTION', 'SERVICE_QUOTA_EXCEEDED_EXCEPTION', 'THROTTLING_EXCEPTION', 'VALIDATION_EXCEPTION', ], ], 'DataSourceId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'DataSourceRunActivities' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceRunActivity', ], ], 'DataSourceRunActivity' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'dataAssetStatus', 'dataSourceRunId', 'database', 'projectId', 'technicalName', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTime', ], 'dataAssetId' => [ 'shape' => 'String', ], 'dataAssetStatus' => [ 'shape' => 'DataAssetActivityStatus', ], 'dataSourceRunId' => [ 'shape' => 'DataSourceRunId', ], 'database' => [ 'shape' => 'Name', ], 'errorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'technicalDescription' => [ 'shape' => 'Description', ], 'technicalName' => [ 'shape' => 'Name', ], 'updatedAt' => [ 'shape' => 'DateTime', ], ], ], 'DataSourceRunId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'DataSourceRunStatus' => [ 'type' => 'string', 'enum' => [ 'REQUESTED', 'RUNNING', 'FAILED', 'PARTIALLY_SUCCEEDED', 'SUCCESS', ], ], 'DataSourceRunSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceRunSummary', ], ], 'DataSourceRunSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'dataSourceId', 'id', 'projectId', 'status', 'type', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTime', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'errorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'id' => [ 'shape' => 'DataSourceRunId', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'runStatisticsForAssets' => [ 'shape' => 'RunStatisticsForAssets', ], 'startedAt' => [ 'shape' => 'DateTime', ], 'status' => [ 'shape' => 'DataSourceRunStatus', ], 'stoppedAt' => [ 'shape' => 'DateTime', ], 'type' => [ 'shape' => 'DataSourceRunType', ], 'updatedAt' => [ 'shape' => 'DateTime', ], ], ], 'DataSourceRunType' => [ 'type' => 'string', 'enum' => [ 'PRIORITIZED', 'SCHEDULED', ], ], 'DataSourceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'FAILED_CREATION', 'READY', 'UPDATING', 'FAILED_UPDATE', 'RUNNING', 'DELETING', 'FAILED_DELETION', ], ], 'DataSourceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceSummary', ], ], 'DataSourceSummary' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'domainId', 'environmentId', 'name', 'status', 'type', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTime', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'domainId' => [ 'shape' => 'DomainId', ], 'enableSetting' => [ 'shape' => 'EnableSetting', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'lastRunAssetCount' => [ 'shape' => 'Integer', ], 'lastRunAt' => [ 'shape' => 'DateTime', ], 'lastRunErrorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'lastRunStatus' => [ 'shape' => 'DataSourceRunStatus', ], 'name' => [ 'shape' => 'Name', ], 'schedule' => [ 'shape' => 'ScheduleConfiguration', ], 'status' => [ 'shape' => 'DataSourceStatus', ], 'type' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'DateTime', ], ], ], 'DataSourceType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'DateTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DecisionComment' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'sensitive' => true, ], 'DeleteAssetInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'AssetIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteAssetOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAssetTypeInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'AssetTypeIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteAssetTypeOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDataSourceInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteDataSourceOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'environmentId', 'id', 'name', 'projectId', ], 'members' => [ 'assetFormsOutput' => [ 'shape' => 'FormOutputList', ], 'configuration' => [ 'shape' => 'DataSourceConfigurationOutput', ], 'createdAt' => [ 'shape' => 'DateTime', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'enableSetting' => [ 'shape' => 'EnableSetting', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'errorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'id' => [ 'shape' => 'DataSourceId', ], 'lastRunAt' => [ 'shape' => 'DateTime', ], 'lastRunErrorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'lastRunStatus' => [ 'shape' => 'DataSourceRunStatus', ], 'name' => [ 'shape' => 'Name', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'publishOnImport' => [ 'shape' => 'Boolean', ], 'schedule' => [ 'shape' => 'ScheduleConfiguration', ], 'status' => [ 'shape' => 'DataSourceStatus', ], 'type' => [ 'shape' => 'DataSourceType', ], 'updatedAt' => [ 'shape' => 'DateTime', ], ], ], 'DeleteDomainInput' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'identifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteDomainOutput' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'DomainStatus', ], ], ], 'DeleteEnvironmentBlueprintConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'environmentBlueprintIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentBlueprintIdentifier' => [ 'shape' => 'EnvironmentBlueprintId', 'location' => 'uri', 'locationName' => 'environmentBlueprintIdentifier', ], ], ], 'DeleteEnvironmentBlueprintConfigurationOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEnvironmentInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteEnvironmentProfileInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'EnvironmentProfileId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteFormTypeInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'formTypeIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'formTypeIdentifier' => [ 'shape' => 'FormTypeIdentifier', 'location' => 'uri', 'locationName' => 'formTypeIdentifier', ], ], ], 'DeleteFormTypeOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteGlossaryInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'GlossaryId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteGlossaryOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteGlossaryTermInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'GlossaryTermId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteGlossaryTermOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteListingInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'ListingId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteListingOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProjectInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'ProjectId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteProjectMembershipInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'member', 'projectIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'member' => [ 'shape' => 'Member', ], 'projectIdentifier' => [ 'shape' => 'ProjectId', 'location' => 'uri', 'locationName' => 'projectIdentifier', ], ], ], 'DeleteProjectMembershipOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProjectOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSubscriptionGrantInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionGrantId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteSubscriptionGrantOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'grantedEntity', 'id', 'status', 'subscriptionTargetId', 'updatedAt', ], 'members' => [ 'assets' => [ 'shape' => 'SubscribedAssets', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'grantedEntity' => [ 'shape' => 'GrantedEntity', ], 'id' => [ 'shape' => 'SubscriptionGrantId', ], 'status' => [ 'shape' => 'SubscriptionGrantOverallStatus', ], 'subscriptionId' => [ 'shape' => 'SubscriptionId', ], 'subscriptionTargetId' => [ 'shape' => 'SubscriptionTargetId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'DeleteSubscriptionRequestInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionRequestId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteSubscriptionTargetInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'environmentIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionTargetId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'Deployment' => [ 'type' => 'structure', 'members' => [ 'deploymentId' => [ 'shape' => 'String', ], 'deploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'deploymentType' => [ 'shape' => 'DeploymentType', ], 'failureReason' => [ 'shape' => 'EnvironmentError', ], 'isDeploymentComplete' => [ 'shape' => 'Boolean', ], 'messages' => [ 'shape' => 'DeploymentMessagesList', ], ], ], 'DeploymentMessage' => [ 'type' => 'string', ], 'DeploymentMessagesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentMessage', ], ], 'DeploymentProperties' => [ 'type' => 'structure', 'members' => [ 'endTimeoutMinutes' => [ 'shape' => 'DeploymentPropertiesEndTimeoutMinutesInteger', ], 'startTimeoutMinutes' => [ 'shape' => 'DeploymentPropertiesStartTimeoutMinutesInteger', ], ], ], 'DeploymentPropertiesEndTimeoutMinutesInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 225, 'min' => 1, ], 'DeploymentPropertiesStartTimeoutMinutesInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 225, 'min' => 1, ], 'DeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCESSFUL', 'FAILED', 'PENDING_DEPLOYMENT', ], ], 'DeploymentType' => [ 'type' => 'string', 'enum' => [ 'CREATE', 'UPDATE', 'DELETE', ], ], 'Description' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'sensitive' => true, ], 'DetailedGlossaryTerm' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'GlossaryTermName', ], 'shortDescription' => [ 'shape' => 'ShortDescription', ], ], ], 'DetailedGlossaryTerms' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetailedGlossaryTerm', ], ], 'DomainDescription' => [ 'type' => 'string', 'sensitive' => true, ], 'DomainId' => [ 'type' => 'string', 'pattern' => '^dzd[-_][a-zA-Z0-9_-]{1,36}$', ], 'DomainName' => [ 'type' => 'string', 'sensitive' => true, ], 'DomainStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'AVAILABLE', 'CREATION_FAILED', 'DELETING', 'DELETED', 'DELETION_FAILED', ], ], 'DomainSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainSummary', ], ], 'DomainSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'id', 'managedAccountId', 'name', 'status', ], 'members' => [ 'arn' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'description' => [ 'shape' => 'DomainDescription', ], 'id' => [ 'shape' => 'DomainId', ], 'lastUpdatedAt' => [ 'shape' => 'UpdatedAt', ], 'managedAccountId' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'DomainName', ], 'portalUrl' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'DomainStatus', ], ], ], 'EnableSetting' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'EnabledRegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegionName', ], 'min' => 0, ], 'EntityId' => [ 'type' => 'string', ], 'EntityType' => [ 'type' => 'string', 'enum' => [ 'ASSET', ], ], 'EnvironmentActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurableEnvironmentAction', ], ], 'EnvironmentBlueprintConfigurationItem' => [ 'type' => 'structure', 'required' => [ 'domainId', 'environmentBlueprintId', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'domainId' => [ 'shape' => 'DomainId', ], 'enabledRegions' => [ 'shape' => 'EnabledRegionList', ], 'environmentBlueprintId' => [ 'shape' => 'EnvironmentBlueprintId', ], 'manageAccessRoleArn' => [ 'shape' => 'RoleArn', ], 'provisioningRoleArn' => [ 'shape' => 'RoleArn', ], 'regionalParameters' => [ 'shape' => 'RegionalParameterMap', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'EnvironmentBlueprintConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentBlueprintConfigurationItem', ], ], 'EnvironmentBlueprintId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'EnvironmentBlueprintName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[\\w -]+$', ], 'EnvironmentBlueprintSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentBlueprintSummary', ], ], 'EnvironmentBlueprintSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'provider', 'provisioningProperties', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'description' => [ 'shape' => 'Description', ], 'id' => [ 'shape' => 'EnvironmentBlueprintId', ], 'name' => [ 'shape' => 'EnvironmentBlueprintName', ], 'provider' => [ 'shape' => 'String', ], 'provisioningProperties' => [ 'shape' => 'ProvisioningProperties', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'EnvironmentError' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'code' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'EnvironmentId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'EnvironmentName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[\\w -]+$', 'sensitive' => true, ], 'EnvironmentParameter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'EnvironmentParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentParameter', ], ], 'EnvironmentProfileId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'EnvironmentProfileName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[\\w -]+$', 'sensitive' => true, ], 'EnvironmentProfileSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentProfileSummary', ], ], 'EnvironmentProfileSummary' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'domainId', 'environmentBlueprintId', 'id', 'name', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AwsAccountId', ], 'awsAccountRegion' => [ 'shape' => 'AwsRegion', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'environmentBlueprintId' => [ 'shape' => 'EnvironmentBlueprintId', ], 'id' => [ 'shape' => 'EnvironmentProfileId', ], 'name' => [ 'shape' => 'EnvironmentProfileName', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'EnvironmentStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATING', 'UPDATING', 'DELETING', 'CREATE_FAILED', 'UPDATE_FAILED', 'DELETE_FAILED', 'VALIDATION_FAILED', 'SUSPENDED', 'DISABLED', 'EXPIRED', 'DELETED', 'INACCESSIBLE', ], ], 'EnvironmentSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentSummary', ], ], 'EnvironmentSummary' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'domainId', 'environmentProfileId', 'name', 'projectId', 'provider', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AwsAccountId', ], 'awsAccountRegion' => [ 'shape' => 'AwsRegion', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'environmentProfileId' => [ 'shape' => 'EnvironmentProfileId', ], 'id' => [ 'shape' => 'EnvironmentId', ], 'name' => [ 'shape' => 'EnvironmentName', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'provider' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'EnvironmentStatus', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ExternalIdentifier' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'sensitive' => true, ], 'FailureCause' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'attribute', 'value', ], 'members' => [ 'attribute' => [ 'shape' => 'Attribute', ], 'value' => [ 'shape' => 'FilterValueString', ], ], ], 'FilterClause' => [ 'type' => 'structure', 'members' => [ 'and' => [ 'shape' => 'FilterList', ], 'filter' => [ 'shape' => 'Filter', ], 'or' => [ 'shape' => 'FilterList', ], ], 'union' => true, ], 'FilterExpression' => [ 'type' => 'structure', 'required' => [ 'expression', 'type', ], 'members' => [ 'expression' => [ 'shape' => 'FilterExpressionExpressionString', ], 'type' => [ 'shape' => 'FilterExpressionType', ], ], ], 'FilterExpressionExpressionString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'FilterExpressionType' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'FilterExpressions' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterExpression', ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterClause', ], 'max' => 100, 'min' => 1, ], 'FilterValueString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'FirstName' => [ 'type' => 'string', 'sensitive' => true, ], 'Float' => [ 'type' => 'float', 'box' => true, ], 'FormEntryInput' => [ 'type' => 'structure', 'required' => [ 'typeIdentifier', 'typeRevision', ], 'members' => [ 'required' => [ 'shape' => 'Boolean', ], 'typeIdentifier' => [ 'shape' => 'FormTypeIdentifier', ], 'typeRevision' => [ 'shape' => 'Revision', ], ], ], 'FormEntryOutput' => [ 'type' => 'structure', 'required' => [ 'typeName', 'typeRevision', ], 'members' => [ 'required' => [ 'shape' => 'Boolean', ], 'typeName' => [ 'shape' => 'FormTypeName', ], 'typeRevision' => [ 'shape' => 'Revision', ], ], ], 'FormInput' => [ 'type' => 'structure', 'required' => [ 'formName', ], 'members' => [ 'content' => [ 'shape' => 'FormInputContentString', ], 'formName' => [ 'shape' => 'FormName', ], 'typeIdentifier' => [ 'shape' => 'FormTypeIdentifier', ], 'typeRevision' => [ 'shape' => 'Revision', ], ], 'sensitive' => true, ], 'FormInputContentString' => [ 'type' => 'string', 'max' => 75000, 'min' => 0, ], 'FormInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FormInput', ], 'max' => 10, 'min' => 0, 'sensitive' => true, ], 'FormName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?![0-9_])\\w+$|^_\\w*[a-zA-Z0-9]\\w*$', ], 'FormOutput' => [ 'type' => 'structure', 'required' => [ 'formName', ], 'members' => [ 'content' => [ 'shape' => 'String', ], 'formName' => [ 'shape' => 'FormName', ], 'typeName' => [ 'shape' => 'FormTypeName', ], 'typeRevision' => [ 'shape' => 'Revision', ], ], ], 'FormOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FormOutput', ], 'max' => 10, 'min' => 0, ], 'FormTypeData' => [ 'type' => 'structure', 'required' => [ 'domainId', 'name', 'revision', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'imports' => [ 'shape' => 'ImportList', ], 'model' => [ 'shape' => 'Model', ], 'name' => [ 'shape' => 'FormTypeName', ], 'originDomainId' => [ 'shape' => 'DomainId', ], 'originProjectId' => [ 'shape' => 'ProjectId', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'revision' => [ 'shape' => 'Revision', ], 'status' => [ 'shape' => 'FormTypeStatus', ], ], ], 'FormTypeIdentifier' => [ 'type' => 'string', 'max' => 385, 'min' => 1, 'pattern' => '^(?!\\.)[\\w\\.]*\\w$', ], 'FormTypeName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(amazon.datazone.)?(?![0-9_])\\w+$|^_\\w*[a-zA-Z0-9]\\w*$', 'sensitive' => true, ], 'FormTypeStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Forms' => [ 'type' => 'string', ], 'FormsInputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'FormName', ], 'value' => [ 'shape' => 'FormEntryInput', ], 'max' => 10, 'min' => 0, ], 'FormsOutputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'FormName', ], 'value' => [ 'shape' => 'FormEntryOutput', ], 'max' => 10, 'min' => 0, ], 'GetAssetInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'AssetIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], 'revision' => [ 'shape' => 'Revision', 'location' => 'querystring', 'locationName' => 'revision', ], ], ], 'GetAssetOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'formsOutput', 'id', 'name', 'owningProjectId', 'revision', 'typeIdentifier', 'typeRevision', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'externalIdentifier' => [ 'shape' => 'ExternalIdentifier', ], 'firstRevisionCreatedAt' => [ 'shape' => 'CreatedAt', ], 'firstRevisionCreatedBy' => [ 'shape' => 'CreatedBy', ], 'formsOutput' => [ 'shape' => 'FormOutputList', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'id' => [ 'shape' => 'AssetId', ], 'listing' => [ 'shape' => 'AssetListingDetails', ], 'name' => [ 'shape' => 'AssetName', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'readOnlyFormsOutput' => [ 'shape' => 'FormOutputList', ], 'revision' => [ 'shape' => 'Revision', ], 'typeIdentifier' => [ 'shape' => 'AssetTypeIdentifier', ], 'typeRevision' => [ 'shape' => 'Revision', ], ], ], 'GetAssetTypeInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'AssetTypeIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], 'revision' => [ 'shape' => 'Revision', 'location' => 'querystring', 'locationName' => 'revision', ], ], ], 'GetAssetTypeOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'formsOutput', 'name', 'owningProjectId', 'revision', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'formsOutput' => [ 'shape' => 'FormsOutputMap', ], 'name' => [ 'shape' => 'TypeName', ], 'originDomainId' => [ 'shape' => 'DomainId', ], 'originProjectId' => [ 'shape' => 'ProjectId', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'revision' => [ 'shape' => 'Revision', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetDataSourceInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetDataSourceOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'environmentId', 'id', 'name', 'projectId', ], 'members' => [ 'assetFormsOutput' => [ 'shape' => 'FormOutputList', ], 'configuration' => [ 'shape' => 'DataSourceConfigurationOutput', ], 'createdAt' => [ 'shape' => 'DateTime', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'enableSetting' => [ 'shape' => 'EnableSetting', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'errorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'id' => [ 'shape' => 'DataSourceId', ], 'lastRunAssetCount' => [ 'shape' => 'Integer', ], 'lastRunAt' => [ 'shape' => 'DateTime', ], 'lastRunErrorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'lastRunStatus' => [ 'shape' => 'DataSourceRunStatus', ], 'name' => [ 'shape' => 'Name', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'publishOnImport' => [ 'shape' => 'Boolean', ], 'recommendation' => [ 'shape' => 'RecommendationConfiguration', ], 'schedule' => [ 'shape' => 'ScheduleConfiguration', ], 'status' => [ 'shape' => 'DataSourceStatus', ], 'type' => [ 'shape' => 'DataSourceType', ], 'updatedAt' => [ 'shape' => 'DateTime', ], ], ], 'GetDataSourceRunInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'DataSourceRunId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetDataSourceRunOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'dataSourceId', 'domainId', 'id', 'projectId', 'status', 'type', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTime', ], 'dataSourceConfigurationSnapshot' => [ 'shape' => 'String', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'domainId' => [ 'shape' => 'DomainId', ], 'errorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'id' => [ 'shape' => 'DataSourceRunId', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'runStatisticsForAssets' => [ 'shape' => 'RunStatisticsForAssets', ], 'startedAt' => [ 'shape' => 'DateTime', ], 'status' => [ 'shape' => 'DataSourceRunStatus', ], 'stoppedAt' => [ 'shape' => 'DateTime', ], 'type' => [ 'shape' => 'DataSourceRunType', ], 'updatedAt' => [ 'shape' => 'DateTime', ], ], ], 'GetDomainInput' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetDomainOutput' => [ 'type' => 'structure', 'required' => [ 'domainExecutionRole', 'id', 'status', ], 'members' => [ 'arn' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'description' => [ 'shape' => 'String', ], 'domainExecutionRole' => [ 'shape' => 'RoleArn', ], 'id' => [ 'shape' => 'DomainId', ], 'kmsKeyIdentifier' => [ 'shape' => 'KmsKeyArn', ], 'lastUpdatedAt' => [ 'shape' => 'UpdatedAt', ], 'name' => [ 'shape' => 'String', ], 'portalUrl' => [ 'shape' => 'String', ], 'singleSignOn' => [ 'shape' => 'SingleSignOn', ], 'status' => [ 'shape' => 'DomainStatus', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'GetEnvironmentBlueprintConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'environmentBlueprintIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentBlueprintIdentifier' => [ 'shape' => 'EnvironmentBlueprintId', 'location' => 'uri', 'locationName' => 'environmentBlueprintIdentifier', ], ], ], 'GetEnvironmentBlueprintConfigurationOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'environmentBlueprintId', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'domainId' => [ 'shape' => 'DomainId', ], 'enabledRegions' => [ 'shape' => 'EnabledRegionList', ], 'environmentBlueprintId' => [ 'shape' => 'EnvironmentBlueprintId', ], 'manageAccessRoleArn' => [ 'shape' => 'RoleArn', ], 'provisioningRoleArn' => [ 'shape' => 'RoleArn', ], 'regionalParameters' => [ 'shape' => 'RegionalParameterMap', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'GetEnvironmentBlueprintInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'EnvironmentBlueprintId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetEnvironmentBlueprintOutput' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'provider', 'provisioningProperties', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'deploymentProperties' => [ 'shape' => 'DeploymentProperties', ], 'description' => [ 'shape' => 'Description', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'id' => [ 'shape' => 'EnvironmentBlueprintId', ], 'name' => [ 'shape' => 'EnvironmentBlueprintName', ], 'provider' => [ 'shape' => 'String', ], 'provisioningProperties' => [ 'shape' => 'ProvisioningProperties', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'userParameters' => [ 'shape' => 'CustomParameterList', ], ], ], 'GetEnvironmentInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetEnvironmentOutput' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'domainId', 'environmentProfileId', 'name', 'projectId', 'provider', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AwsAccountId', ], 'awsAccountRegion' => [ 'shape' => 'AwsRegion', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'String', ], 'deploymentProperties' => [ 'shape' => 'DeploymentProperties', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'environmentActions' => [ 'shape' => 'EnvironmentActionList', ], 'environmentBlueprintId' => [ 'shape' => 'EnvironmentBlueprintId', ], 'environmentProfileId' => [ 'shape' => 'EnvironmentProfileId', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'id' => [ 'shape' => 'EnvironmentId', ], 'lastDeployment' => [ 'shape' => 'Deployment', ], 'name' => [ 'shape' => 'EnvironmentName', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'provider' => [ 'shape' => 'String', ], 'provisionedResources' => [ 'shape' => 'ResourceList', ], 'provisioningProperties' => [ 'shape' => 'ProvisioningProperties', ], 'status' => [ 'shape' => 'EnvironmentStatus', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'userParameters' => [ 'shape' => 'CustomParameterList', ], ], ], 'GetEnvironmentProfileInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'EnvironmentProfileId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetEnvironmentProfileOutput' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'domainId', 'environmentBlueprintId', 'id', 'name', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AwsAccountId', ], 'awsAccountRegion' => [ 'shape' => 'AwsRegion', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'environmentBlueprintId' => [ 'shape' => 'EnvironmentBlueprintId', ], 'id' => [ 'shape' => 'EnvironmentProfileId', ], 'name' => [ 'shape' => 'EnvironmentProfileName', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'userParameters' => [ 'shape' => 'CustomParameterList', ], ], ], 'GetFormTypeInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'formTypeIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'formTypeIdentifier' => [ 'shape' => 'FormTypeIdentifier', 'location' => 'uri', 'locationName' => 'formTypeIdentifier', ], 'revision' => [ 'shape' => 'Revision', 'location' => 'querystring', 'locationName' => 'revision', ], ], ], 'GetFormTypeOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'model', 'name', 'revision', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'imports' => [ 'shape' => 'ImportList', ], 'model' => [ 'shape' => 'Model', ], 'name' => [ 'shape' => 'FormTypeName', ], 'originDomainId' => [ 'shape' => 'DomainId', ], 'originProjectId' => [ 'shape' => 'ProjectId', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'revision' => [ 'shape' => 'Revision', ], 'status' => [ 'shape' => 'FormTypeStatus', ], ], ], 'GetGlossaryInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'GlossaryId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetGlossaryOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'id', 'name', 'owningProjectId', 'status', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'GlossaryDescription', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'GlossaryId', ], 'name' => [ 'shape' => 'GlossaryName', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'status' => [ 'shape' => 'GlossaryStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetGlossaryTermInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'GlossaryTermId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetGlossaryTermOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'glossaryId', 'id', 'name', 'status', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'glossaryId' => [ 'shape' => 'GlossaryId', ], 'id' => [ 'shape' => 'GlossaryTermId', ], 'longDescription' => [ 'shape' => 'LongDescription', ], 'name' => [ 'shape' => 'GlossaryTermName', ], 'shortDescription' => [ 'shape' => 'ShortDescription', ], 'status' => [ 'shape' => 'GlossaryTermStatus', ], 'termRelations' => [ 'shape' => 'TermRelations', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetGroupProfileInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'groupIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'groupIdentifier' => [ 'shape' => 'GroupIdentifier', 'location' => 'uri', 'locationName' => 'groupIdentifier', ], ], ], 'GetGroupProfileOutput' => [ 'type' => 'structure', 'members' => [ 'domainId' => [ 'shape' => 'DomainId', ], 'groupName' => [ 'shape' => 'GroupProfileName', ], 'id' => [ 'shape' => 'GroupProfileId', ], 'status' => [ 'shape' => 'GroupProfileStatus', ], ], ], 'GetIamPortalLoginUrlInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], ], ], 'GetIamPortalLoginUrlOutput' => [ 'type' => 'structure', 'required' => [ 'userProfileId', ], 'members' => [ 'authCodeUrl' => [ 'shape' => 'String', ], 'userProfileId' => [ 'shape' => 'String', ], ], ], 'GetListingInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'ListingId', 'location' => 'uri', 'locationName' => 'identifier', ], 'listingRevision' => [ 'shape' => 'Revision', 'location' => 'querystring', 'locationName' => 'listingRevision', ], ], ], 'GetListingOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'id', 'listingRevision', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'ListingId', ], 'item' => [ 'shape' => 'ListingItem', ], 'listingRevision' => [ 'shape' => 'Revision', ], 'name' => [ 'shape' => 'ListingName', ], 'status' => [ 'shape' => 'ListingStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetProjectInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'ProjectId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetProjectOutput' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'domainId', 'id', 'name', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'id' => [ 'shape' => 'ProjectId', ], 'lastUpdatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'name' => [ 'shape' => 'ProjectName', ], ], ], 'GetSubscriptionGrantInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionGrantId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetSubscriptionGrantOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'grantedEntity', 'id', 'status', 'subscriptionTargetId', 'updatedAt', ], 'members' => [ 'assets' => [ 'shape' => 'SubscribedAssets', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'grantedEntity' => [ 'shape' => 'GrantedEntity', ], 'id' => [ 'shape' => 'SubscriptionGrantId', ], 'status' => [ 'shape' => 'SubscriptionGrantOverallStatus', ], 'subscriptionId' => [ 'shape' => 'SubscriptionId', ], 'subscriptionTargetId' => [ 'shape' => 'SubscriptionTargetId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetSubscriptionInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetSubscriptionOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'id', 'status', 'subscribedListing', 'subscribedPrincipal', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'SubscriptionId', ], 'retainPermissions' => [ 'shape' => 'Boolean', ], 'status' => [ 'shape' => 'SubscriptionStatus', ], 'subscribedListing' => [ 'shape' => 'SubscribedListing', ], 'subscribedPrincipal' => [ 'shape' => 'SubscribedPrincipal', ], 'subscriptionRequestId' => [ 'shape' => 'SubscriptionRequestId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetSubscriptionRequestDetailsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionRequestId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetSubscriptionRequestDetailsOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'id', 'requestReason', 'status', 'subscribedListings', 'subscribedPrincipals', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'decisionComment' => [ 'shape' => 'DecisionComment', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'SubscriptionRequestId', ], 'requestReason' => [ 'shape' => 'RequestReason', ], 'reviewerId' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'SubscriptionRequestStatus', ], 'subscribedListings' => [ 'shape' => 'GetSubscriptionRequestDetailsOutputSubscribedListingsList', ], 'subscribedPrincipals' => [ 'shape' => 'GetSubscriptionRequestDetailsOutputSubscribedPrincipalsList', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetSubscriptionRequestDetailsOutputSubscribedListingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedListing', ], 'max' => 1, 'min' => 1, ], 'GetSubscriptionRequestDetailsOutputSubscribedPrincipalsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedPrincipal', ], 'max' => 1, 'min' => 1, ], 'GetSubscriptionTargetInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'environmentIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionTargetId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetSubscriptionTargetOutput' => [ 'type' => 'structure', 'required' => [ 'applicableAssetTypes', 'authorizedPrincipals', 'createdAt', 'createdBy', 'domainId', 'environmentId', 'id', 'manageAccessRole', 'name', 'projectId', 'provider', 'subscriptionTargetConfig', 'type', ], 'members' => [ 'applicableAssetTypes' => [ 'shape' => 'ApplicableAssetTypes', ], 'authorizedPrincipals' => [ 'shape' => 'AuthorizedPrincipalIdentifiers', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'id' => [ 'shape' => 'SubscriptionTargetId', ], 'manageAccessRole' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'SubscriptionTargetName', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'provider' => [ 'shape' => 'String', ], 'subscriptionTargetConfig' => [ 'shape' => 'SubscriptionTargetForms', ], 'type' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetUserProfileInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'userIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'type' => [ 'shape' => 'UserProfileType', 'location' => 'querystring', 'locationName' => 'type', ], 'userIdentifier' => [ 'shape' => 'UserIdentifier', 'location' => 'uri', 'locationName' => 'userIdentifier', ], ], ], 'GetUserProfileOutput' => [ 'type' => 'structure', 'members' => [ 'details' => [ 'shape' => 'UserProfileDetails', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'UserProfileId', ], 'status' => [ 'shape' => 'UserProfileStatus', ], 'type' => [ 'shape' => 'UserProfileType', ], ], ], 'GlossaryDescription' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'sensitive' => true, ], 'GlossaryId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'GlossaryItem' => [ 'type' => 'structure', 'required' => [ 'domainId', 'id', 'name', 'owningProjectId', 'status', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'GlossaryDescription', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'GlossaryId', ], 'name' => [ 'shape' => 'GlossaryName', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'status' => [ 'shape' => 'GlossaryStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GlossaryName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'sensitive' => true, ], 'GlossaryStatus' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'GlossaryTermId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'GlossaryTermItem' => [ 'type' => 'structure', 'required' => [ 'domainId', 'glossaryId', 'id', 'name', 'status', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'glossaryId' => [ 'shape' => 'GlossaryId', ], 'id' => [ 'shape' => 'GlossaryTermId', ], 'longDescription' => [ 'shape' => 'LongDescription', ], 'name' => [ 'shape' => 'GlossaryTermName', ], 'shortDescription' => [ 'shape' => 'ShortDescription', ], 'status' => [ 'shape' => 'GlossaryTermStatus', ], 'termRelations' => [ 'shape' => 'TermRelations', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GlossaryTermName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'sensitive' => true, ], 'GlossaryTermStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'GlossaryTerms' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlossaryTermId', ], 'max' => 20, 'min' => 1, ], 'GlueRunConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'relationalFilterConfigurations', ], 'members' => [ 'dataAccessRole' => [ 'shape' => 'GlueRunConfigurationInputDataAccessRoleString', ], 'relationalFilterConfigurations' => [ 'shape' => 'RelationalFilterConfigurations', ], ], ], 'GlueRunConfigurationInputDataAccessRoleString' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:iam::\\d{12}:(role|role/service-role)/[\\w+=,.@-]{1,128}$', ], 'GlueRunConfigurationOutput' => [ 'type' => 'structure', 'required' => [ 'relationalFilterConfigurations', ], 'members' => [ 'accountId' => [ 'shape' => 'GlueRunConfigurationOutputAccountIdString', ], 'dataAccessRole' => [ 'shape' => 'GlueRunConfigurationOutputDataAccessRoleString', ], 'region' => [ 'shape' => 'GlueRunConfigurationOutputRegionString', ], 'relationalFilterConfigurations' => [ 'shape' => 'RelationalFilterConfigurations', ], ], ], 'GlueRunConfigurationOutputAccountIdString' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'GlueRunConfigurationOutputDataAccessRoleString' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:iam::\\d{12}:(role|role/service-role)/[\\w+=,.@-]{1,128}$', ], 'GlueRunConfigurationOutputRegionString' => [ 'type' => 'string', 'max' => 16, 'min' => 4, 'pattern' => '[a-z]{2}-?(iso|gov)?-{1}[a-z]*-{1}[0-9]', ], 'GrantedEntity' => [ 'type' => 'structure', 'members' => [ 'listing' => [ 'shape' => 'ListingRevision', ], ], 'union' => true, ], 'GrantedEntityInput' => [ 'type' => 'structure', 'members' => [ 'listing' => [ 'shape' => 'ListingRevisionInput', ], ], 'union' => true, ], 'GroupDetails' => [ 'type' => 'structure', 'required' => [ 'groupId', ], 'members' => [ 'groupId' => [ 'shape' => 'String', ], ], ], 'GroupIdentifier' => [ 'type' => 'string', 'pattern' => '(^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$|[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\t\\n\\r ]+)', ], 'GroupProfileId' => [ 'type' => 'string', 'pattern' => '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$', ], 'GroupProfileName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[a-zA-Z_0-9+=,.@-]+$', 'sensitive' => true, ], 'GroupProfileStatus' => [ 'type' => 'string', 'enum' => [ 'ASSIGNED', 'NOT_ASSIGNED', ], ], 'GroupProfileSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupProfileSummary', ], ], 'GroupProfileSummary' => [ 'type' => 'structure', 'members' => [ 'domainId' => [ 'shape' => 'DomainId', ], 'groupName' => [ 'shape' => 'GroupProfileName', ], 'id' => [ 'shape' => 'GroupProfileId', ], 'status' => [ 'shape' => 'GroupProfileStatus', ], ], ], 'GroupSearchText' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'GroupSearchType' => [ 'type' => 'string', 'enum' => [ 'SSO_GROUP', 'DATAZONE_SSO_GROUP', ], ], 'IamUserProfileDetails' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'String', ], ], ], 'Import' => [ 'type' => 'structure', 'required' => [ 'name', 'revision', ], 'members' => [ 'name' => [ 'shape' => 'FormTypeName', ], 'revision' => [ 'shape' => 'Revision', ], ], ], 'ImportList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Import', ], 'max' => 10, 'min' => 1, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'InventorySearchScope' => [ 'type' => 'string', 'enum' => [ 'ASSET', 'GLOSSARY', 'GLOSSARY_TERM', ], ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^arn:aws(|-cn|-us-gov):kms:[a-zA-Z0-9-]*:[0-9]{12}:key/[a-zA-Z0-9-]{36}$', ], 'LastName' => [ 'type' => 'string', 'sensitive' => true, ], 'ListAssetRevisionsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'AssetIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAssetRevisionsOutput' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'AssetRevisions', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataSourceRunActivitiesInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'DataSourceRunId', 'location' => 'uri', 'locationName' => 'identifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'status' => [ 'shape' => 'DataAssetActivityStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListDataSourceRunActivitiesOutput' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'DataSourceRunActivities', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataSourceRunsInput' => [ 'type' => 'structure', 'required' => [ 'dataSourceIdentifier', 'domainIdentifier', ], 'members' => [ 'dataSourceIdentifier' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceIdentifier', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'status' => [ 'shape' => 'DataSourceRunStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListDataSourceRunsOutput' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'DataSourceRunSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataSourcesInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'projectIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentIdentifier' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'environmentIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'name' => [ 'shape' => 'Name', 'location' => 'querystring', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'projectIdentifier' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'projectIdentifier', ], 'status' => [ 'shape' => 'DataSourceStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'type' => [ 'shape' => 'DataSourceType', 'location' => 'querystring', 'locationName' => 'type', ], ], ], 'ListDataSourcesOutput' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'DataSourceSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDomainsInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResultsForListDomains', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'status' => [ 'shape' => 'DomainStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListDomainsOutput' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'DomainSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListEnvironmentBlueprintConfigurationsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEnvironmentBlueprintConfigurationsOutput' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'EnvironmentBlueprintConfigurations', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListEnvironmentBlueprintsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'managed' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'managed', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'name' => [ 'shape' => 'EnvironmentBlueprintName', 'location' => 'querystring', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEnvironmentBlueprintsOutput' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'EnvironmentBlueprintSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListEnvironmentProfilesInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'querystring', 'locationName' => 'awsAccountId', ], 'awsAccountRegion' => [ 'shape' => 'AwsRegion', 'location' => 'querystring', 'locationName' => 'awsAccountRegion', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentBlueprintIdentifier' => [ 'shape' => 'EnvironmentBlueprintId', 'location' => 'querystring', 'locationName' => 'environmentBlueprintIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'name' => [ 'shape' => 'EnvironmentProfileName', 'location' => 'querystring', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'projectIdentifier' => [ 'shape' => 'ProjectId', 'location' => 'querystring', 'locationName' => 'projectIdentifier', ], ], ], 'ListEnvironmentProfilesOutput' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'EnvironmentProfileSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListEnvironmentsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'projectIdentifier', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'querystring', 'locationName' => 'awsAccountId', ], 'awsAccountRegion' => [ 'shape' => 'AwsRegion', 'location' => 'querystring', 'locationName' => 'awsAccountRegion', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentBlueprintIdentifier' => [ 'shape' => 'EnvironmentBlueprintId', 'location' => 'querystring', 'locationName' => 'environmentBlueprintIdentifier', ], 'environmentProfileIdentifier' => [ 'shape' => 'EnvironmentProfileId', 'location' => 'querystring', 'locationName' => 'environmentProfileIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'name' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'projectIdentifier' => [ 'shape' => 'ProjectId', 'location' => 'querystring', 'locationName' => 'projectIdentifier', ], 'provider' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'provider', ], 'status' => [ 'shape' => 'EnvironmentStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListEnvironmentsOutput' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'EnvironmentSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListNotificationsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'type', ], 'members' => [ 'afterTimestamp' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'afterTimestamp', ], 'beforeTimestamp' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'beforeTimestamp', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'subjects' => [ 'shape' => 'NotificationSubjects', 'location' => 'querystring', 'locationName' => 'subjects', ], 'taskStatus' => [ 'shape' => 'TaskStatus', 'location' => 'querystring', 'locationName' => 'taskStatus', ], 'type' => [ 'shape' => 'NotificationType', 'location' => 'querystring', 'locationName' => 'type', ], ], ], 'ListNotificationsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'notifications' => [ 'shape' => 'NotificationsList', ], ], ], 'ListProjectMembershipsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'projectIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'projectIdentifier' => [ 'shape' => 'ProjectId', 'location' => 'uri', 'locationName' => 'projectIdentifier', ], 'sortBy' => [ 'shape' => 'SortFieldProject', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], ], ], 'ListProjectMembershipsOutput' => [ 'type' => 'structure', 'required' => [ 'members', ], 'members' => [ 'members' => [ 'shape' => 'ProjectMembers', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListProjectsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'groupIdentifier' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'groupIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'name' => [ 'shape' => 'ProjectName', 'location' => 'querystring', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'userIdentifier' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'userIdentifier', ], ], ], 'ListProjectsOutput' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'ProjectSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSubscriptionGrantsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'querystring', 'locationName' => 'environmentId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'SortKey', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], 'subscribedListingId' => [ 'shape' => 'ListingId', 'location' => 'querystring', 'locationName' => 'subscribedListingId', ], 'subscriptionId' => [ 'shape' => 'SubscriptionId', 'location' => 'querystring', 'locationName' => 'subscriptionId', ], 'subscriptionTargetId' => [ 'shape' => 'SubscriptionTargetId', 'location' => 'querystring', 'locationName' => 'subscriptionTargetId', ], ], ], 'ListSubscriptionGrantsOutput' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'SubscriptionGrants', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSubscriptionRequestsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', ], 'members' => [ 'approverProjectId' => [ 'shape' => 'ProjectId', 'location' => 'querystring', 'locationName' => 'approverProjectId', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'owningProjectId' => [ 'shape' => 'ProjectId', 'location' => 'querystring', 'locationName' => 'owningProjectId', ], 'sortBy' => [ 'shape' => 'SortKey', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], 'status' => [ 'shape' => 'SubscriptionRequestStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'subscribedListingId' => [ 'shape' => 'ListingId', 'location' => 'querystring', 'locationName' => 'subscribedListingId', ], ], ], 'ListSubscriptionRequestsOutput' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'SubscriptionRequests', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSubscriptionTargetsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'environmentIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'SortKey', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], ], ], 'ListSubscriptionTargetsOutput' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'SubscriptionTargets', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSubscriptionsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', ], 'members' => [ 'approverProjectId' => [ 'shape' => 'ProjectId', 'location' => 'querystring', 'locationName' => 'approverProjectId', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'owningProjectId' => [ 'shape' => 'ProjectId', 'location' => 'querystring', 'locationName' => 'owningProjectId', ], 'sortBy' => [ 'shape' => 'SortKey', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], 'status' => [ 'shape' => 'SubscriptionStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'subscribedListingId' => [ 'shape' => 'ListingId', 'location' => 'querystring', 'locationName' => 'subscribedListingId', ], 'subscriptionRequestIdentifier' => [ 'shape' => 'SubscriptionRequestId', 'location' => 'querystring', 'locationName' => 'subscriptionRequestIdentifier', ], ], ], 'ListSubscriptionsOutput' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'Subscriptions', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'ListingId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'ListingItem' => [ 'type' => 'structure', 'members' => [ 'assetListing' => [ 'shape' => 'AssetListing', ], ], 'union' => true, ], 'ListingName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ListingRevision' => [ 'type' => 'structure', 'required' => [ 'id', 'revision', ], 'members' => [ 'id' => [ 'shape' => 'ListingId', ], 'revision' => [ 'shape' => 'Revision', ], ], ], 'ListingRevisionInput' => [ 'type' => 'structure', 'required' => [ 'identifier', 'revision', ], 'members' => [ 'identifier' => [ 'shape' => 'ListingId', ], 'revision' => [ 'shape' => 'Revision', ], ], ], 'ListingStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'INACTIVE', ], ], 'LongDescription' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'sensitive' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MaxResultsForListDomains' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'Member' => [ 'type' => 'structure', 'members' => [ 'groupIdentifier' => [ 'shape' => 'String', ], 'userIdentifier' => [ 'shape' => 'String', ], ], 'union' => true, ], 'MemberDetails' => [ 'type' => 'structure', 'members' => [ 'group' => [ 'shape' => 'GroupDetails', ], 'user' => [ 'shape' => 'UserDetails', ], ], 'union' => true, ], 'Message' => [ 'type' => 'string', 'max' => 16384, 'min' => 0, 'sensitive' => true, ], 'MetadataMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Model' => [ 'type' => 'structure', 'members' => [ 'smithy' => [ 'shape' => 'Smithy', ], ], 'sensitive' => true, 'union' => true, ], 'Name' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'sensitive' => true, ], 'NotificationOutput' => [ 'type' => 'structure', 'required' => [ 'actionLink', 'creationTimestamp', 'domainIdentifier', 'identifier', 'lastUpdatedTimestamp', 'message', 'title', 'topic', 'type', ], 'members' => [ 'actionLink' => [ 'shape' => 'ActionLink', ], 'creationTimestamp' => [ 'shape' => 'Timestamp', ], 'domainIdentifier' => [ 'shape' => 'DomainId', ], 'identifier' => [ 'shape' => 'TaskId', ], 'lastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'message' => [ 'shape' => 'Message', ], 'metadata' => [ 'shape' => 'MetadataMap', ], 'status' => [ 'shape' => 'TaskStatus', ], 'title' => [ 'shape' => 'Title', ], 'topic' => [ 'shape' => 'Topic', ], 'type' => [ 'shape' => 'NotificationType', ], ], ], 'NotificationResource' => [ 'type' => 'structure', 'required' => [ 'id', 'type', ], 'members' => [ 'id' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'NotificationResourceType', ], ], ], 'NotificationResourceType' => [ 'type' => 'string', 'enum' => [ 'PROJECT', ], ], 'NotificationRole' => [ 'type' => 'string', 'enum' => [ 'PROJECT_OWNER', 'PROJECT_CONTRIBUTOR', 'PROJECT_VIEWER', 'DOMAIN_OWNER', 'PROJECT_SUBSCRIBER', ], ], 'NotificationSubjects' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'NotificationType' => [ 'type' => 'string', 'enum' => [ 'TASK', 'EVENT', ], ], 'NotificationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationOutput', ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, ], 'PredictionChoices' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], ], 'PredictionConfiguration' => [ 'type' => 'structure', 'members' => [ 'businessNameGeneration' => [ 'shape' => 'BusinessNameGenerationConfiguration', ], ], ], 'ProjectId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'ProjectMember' => [ 'type' => 'structure', 'required' => [ 'designation', 'memberDetails', ], 'members' => [ 'designation' => [ 'shape' => 'UserDesignation', ], 'memberDetails' => [ 'shape' => 'MemberDetails', ], ], ], 'ProjectMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectMember', ], ], 'ProjectName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[\\w -]+$', 'sensitive' => true, ], 'ProjectSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectSummary', ], ], 'ProjectSummary' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'domainId', 'id', 'name', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'ProjectId', ], 'name' => [ 'shape' => 'ProjectName', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ProvisioningProperties' => [ 'type' => 'structure', 'members' => [ 'cloudFormation' => [ 'shape' => 'CloudFormationProperties', ], ], 'union' => true, ], 'PutEnvironmentBlueprintConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'enabledRegions', 'environmentBlueprintIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'enabledRegions' => [ 'shape' => 'EnabledRegionList', ], 'environmentBlueprintIdentifier' => [ 'shape' => 'EnvironmentBlueprintId', 'location' => 'uri', 'locationName' => 'environmentBlueprintIdentifier', ], 'manageAccessRoleArn' => [ 'shape' => 'RoleArn', ], 'provisioningRoleArn' => [ 'shape' => 'RoleArn', ], 'regionalParameters' => [ 'shape' => 'RegionalParameterMap', ], ], ], 'PutEnvironmentBlueprintConfigurationOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'environmentBlueprintId', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'domainId' => [ 'shape' => 'DomainId', ], 'enabledRegions' => [ 'shape' => 'EnabledRegionList', ], 'environmentBlueprintId' => [ 'shape' => 'EnvironmentBlueprintId', ], 'manageAccessRoleArn' => [ 'shape' => 'RoleArn', ], 'provisioningRoleArn' => [ 'shape' => 'RoleArn', ], 'regionalParameters' => [ 'shape' => 'RegionalParameterMap', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'RecommendationConfiguration' => [ 'type' => 'structure', 'members' => [ 'enableBusinessNameGeneration' => [ 'shape' => 'Boolean', ], ], ], 'RedshiftClusterStorage' => [ 'type' => 'structure', 'required' => [ 'clusterName', ], 'members' => [ 'clusterName' => [ 'shape' => 'RedshiftClusterStorageClusterNameString', ], ], ], 'RedshiftClusterStorageClusterNameString' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[0-9a-z].[a-z0-9\\-]*$', ], 'RedshiftCredentialConfiguration' => [ 'type' => 'structure', 'required' => [ 'secretManagerArn', ], 'members' => [ 'secretManagerArn' => [ 'shape' => 'RedshiftCredentialConfigurationSecretManagerArnString', ], ], ], 'RedshiftCredentialConfigurationSecretManagerArnString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^arn:aws[^:]*:secretsmanager:[a-z]{2}-?(iso|gov)?-{1}[a-z]*-{1}[0-9]:\\d{12}:secret:.*$', ], 'RedshiftRunConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'redshiftCredentialConfiguration', 'redshiftStorage', 'relationalFilterConfigurations', ], 'members' => [ 'dataAccessRole' => [ 'shape' => 'RedshiftRunConfigurationInputDataAccessRoleString', ], 'redshiftCredentialConfiguration' => [ 'shape' => 'RedshiftCredentialConfiguration', ], 'redshiftStorage' => [ 'shape' => 'RedshiftStorage', ], 'relationalFilterConfigurations' => [ 'shape' => 'RelationalFilterConfigurations', ], ], ], 'RedshiftRunConfigurationInputDataAccessRoleString' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:iam::\\d{12}:(role|role/service-role)/[\\w+=,.@-]{1,128}$', ], 'RedshiftRunConfigurationOutput' => [ 'type' => 'structure', 'required' => [ 'redshiftCredentialConfiguration', 'redshiftStorage', 'relationalFilterConfigurations', ], 'members' => [ 'accountId' => [ 'shape' => 'RedshiftRunConfigurationOutputAccountIdString', ], 'dataAccessRole' => [ 'shape' => 'RedshiftRunConfigurationOutputDataAccessRoleString', ], 'redshiftCredentialConfiguration' => [ 'shape' => 'RedshiftCredentialConfiguration', ], 'redshiftStorage' => [ 'shape' => 'RedshiftStorage', ], 'region' => [ 'shape' => 'RedshiftRunConfigurationOutputRegionString', ], 'relationalFilterConfigurations' => [ 'shape' => 'RelationalFilterConfigurations', ], ], ], 'RedshiftRunConfigurationOutputAccountIdString' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'RedshiftRunConfigurationOutputDataAccessRoleString' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:iam::\\d{12}:(role|role/service-role)/[\\w+=,.@-]{1,128}$', ], 'RedshiftRunConfigurationOutputRegionString' => [ 'type' => 'string', 'max' => 16, 'min' => 4, 'pattern' => '[a-z]{2}-?(iso|gov)?-{1}[a-z]*-{1}[0-9]', ], 'RedshiftServerlessStorage' => [ 'type' => 'structure', 'required' => [ 'workgroupName', ], 'members' => [ 'workgroupName' => [ 'shape' => 'RedshiftServerlessStorageWorkgroupNameString', ], ], ], 'RedshiftServerlessStorageWorkgroupNameString' => [ 'type' => 'string', 'max' => 64, 'min' => 3, 'pattern' => '^[a-z0-9-]+$', ], 'RedshiftStorage' => [ 'type' => 'structure', 'members' => [ 'redshiftClusterSource' => [ 'shape' => 'RedshiftClusterStorage', ], 'redshiftServerlessSource' => [ 'shape' => 'RedshiftServerlessStorage', ], ], 'union' => true, ], 'RegionName' => [ 'type' => 'string', 'max' => 16, 'min' => 4, 'pattern' => '^[a-z]{2}-?(iso|gov)?-{1}[a-z]*-{1}[0-9]$', ], 'RegionalParameter' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'RegionalParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'RegionName', ], 'value' => [ 'shape' => 'RegionalParameter', ], ], 'RejectChoice' => [ 'type' => 'structure', 'members' => [ 'predictionChoices' => [ 'shape' => 'PredictionChoices', ], 'predictionTarget' => [ 'shape' => 'String', ], ], ], 'RejectChoices' => [ 'type' => 'list', 'member' => [ 'shape' => 'RejectChoice', ], ], 'RejectPredictionsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'AssetIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], 'rejectChoices' => [ 'shape' => 'RejectChoices', ], 'rejectRule' => [ 'shape' => 'RejectRule', ], 'revision' => [ 'shape' => 'Revision', 'location' => 'querystring', 'locationName' => 'revision', ], ], ], 'RejectPredictionsOutput' => [ 'type' => 'structure', 'required' => [ 'assetId', 'assetRevision', 'domainId', ], 'members' => [ 'assetId' => [ 'shape' => 'AssetId', ], 'assetRevision' => [ 'shape' => 'Revision', ], 'domainId' => [ 'shape' => 'DomainId', ], ], ], 'RejectRule' => [ 'type' => 'structure', 'members' => [ 'rule' => [ 'shape' => 'RejectRuleBehavior', ], 'threshold' => [ 'shape' => 'Float', ], ], ], 'RejectRuleBehavior' => [ 'type' => 'string', 'enum' => [ 'ALL', 'NONE', ], ], 'RejectSubscriptionRequestInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'decisionComment' => [ 'shape' => 'DecisionComment', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionRequestId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'RejectSubscriptionRequestOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'id', 'requestReason', 'status', 'subscribedListings', 'subscribedPrincipals', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'decisionComment' => [ 'shape' => 'DecisionComment', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'SubscriptionRequestId', ], 'requestReason' => [ 'shape' => 'RequestReason', ], 'reviewerId' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'SubscriptionRequestStatus', ], 'subscribedListings' => [ 'shape' => 'RejectSubscriptionRequestOutputSubscribedListingsList', ], 'subscribedPrincipals' => [ 'shape' => 'RejectSubscriptionRequestOutputSubscribedPrincipalsList', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'RejectSubscriptionRequestOutputSubscribedListingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedListing', ], 'max' => 1, 'min' => 1, ], 'RejectSubscriptionRequestOutputSubscribedPrincipalsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedPrincipal', ], 'max' => 1, 'min' => 1, ], 'RelationalFilterConfiguration' => [ 'type' => 'structure', 'required' => [ 'databaseName', ], 'members' => [ 'databaseName' => [ 'shape' => 'RelationalFilterConfigurationDatabaseNameString', ], 'filterExpressions' => [ 'shape' => 'FilterExpressions', ], 'schemaName' => [ 'shape' => 'RelationalFilterConfigurationSchemaNameString', ], ], ], 'RelationalFilterConfigurationDatabaseNameString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'RelationalFilterConfigurationSchemaNameString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'RelationalFilterConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelationalFilterConfiguration', ], ], 'RequestReason' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'sensitive' => true, ], 'Resource' => [ 'type' => 'structure', 'required' => [ 'type', 'value', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'provider' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'ResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'Revision' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'RevokeSubscriptionInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionId', 'location' => 'uri', 'locationName' => 'identifier', ], 'retainPermissions' => [ 'shape' => 'Boolean', ], ], ], 'RevokeSubscriptionOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'id', 'status', 'subscribedListing', 'subscribedPrincipal', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'SubscriptionId', ], 'retainPermissions' => [ 'shape' => 'Boolean', ], 'status' => [ 'shape' => 'SubscriptionStatus', ], 'subscribedListing' => [ 'shape' => 'SubscribedListing', ], 'subscribedPrincipal' => [ 'shape' => 'SubscribedPrincipal', ], 'subscriptionRequestId' => [ 'shape' => 'SubscriptionRequestId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'RoleArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:iam::\\d{12}:(role|role/service-role)/[\\w+=,.@-]*$', ], 'RunStatisticsForAssets' => [ 'type' => 'structure', 'members' => [ 'added' => [ 'shape' => 'Integer', ], 'failed' => [ 'shape' => 'Integer', ], 'skipped' => [ 'shape' => 'Integer', ], 'unchanged' => [ 'shape' => 'Integer', ], 'updated' => [ 'shape' => 'Integer', ], ], ], 'ScheduleConfiguration' => [ 'type' => 'structure', 'members' => [ 'schedule' => [ 'shape' => 'CronString', ], 'timezone' => [ 'shape' => 'Timezone', ], ], 'sensitive' => true, ], 'SearchGroupProfilesInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'groupType', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'groupType' => [ 'shape' => 'GroupSearchType', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'searchText' => [ 'shape' => 'GroupSearchText', ], ], ], 'SearchGroupProfilesOutput' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'GroupProfileSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'SearchInItem' => [ 'type' => 'structure', 'required' => [ 'attribute', ], 'members' => [ 'attribute' => [ 'shape' => 'Attribute', ], ], ], 'SearchInList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchInItem', ], 'max' => 10, 'min' => 1, ], 'SearchInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'searchScope', ], 'members' => [ 'additionalAttributes' => [ 'shape' => 'SearchOutputAdditionalAttributes', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'filters' => [ 'shape' => 'FilterClause', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'owningProjectIdentifier' => [ 'shape' => 'ProjectId', ], 'searchIn' => [ 'shape' => 'SearchInList', ], 'searchScope' => [ 'shape' => 'InventorySearchScope', ], 'searchText' => [ 'shape' => 'SearchText', ], 'sort' => [ 'shape' => 'SearchSort', ], ], ], 'SearchInventoryResultItem' => [ 'type' => 'structure', 'members' => [ 'assetItem' => [ 'shape' => 'AssetItem', ], 'dataProductItem' => [ 'shape' => 'DataProductSummary', ], 'glossaryItem' => [ 'shape' => 'GlossaryItem', ], 'glossaryTermItem' => [ 'shape' => 'GlossaryTermItem', ], ], 'union' => true, ], 'SearchInventoryResultItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchInventoryResultItem', ], ], 'SearchListingsInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', ], 'members' => [ 'additionalAttributes' => [ 'shape' => 'SearchOutputAdditionalAttributes', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'filters' => [ 'shape' => 'FilterClause', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'searchIn' => [ 'shape' => 'SearchInList', ], 'searchText' => [ 'shape' => 'String', ], 'sort' => [ 'shape' => 'SearchSort', ], ], ], 'SearchListingsOutput' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'SearchResultItems', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'totalMatchCount' => [ 'shape' => 'Integer', ], ], ], 'SearchOutput' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'SearchInventoryResultItems', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'totalMatchCount' => [ 'shape' => 'Integer', ], ], ], 'SearchOutputAdditionalAttribute' => [ 'type' => 'string', 'enum' => [ 'FORMS', ], ], 'SearchOutputAdditionalAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchOutputAdditionalAttribute', ], ], 'SearchResultItem' => [ 'type' => 'structure', 'members' => [ 'assetListing' => [ 'shape' => 'AssetListingItem', ], ], 'union' => true, ], 'SearchResultItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchResultItem', ], ], 'SearchSort' => [ 'type' => 'structure', 'required' => [ 'attribute', ], 'members' => [ 'attribute' => [ 'shape' => 'Attribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'SearchText' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'SearchTypesInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'managed', 'searchScope', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'filters' => [ 'shape' => 'FilterClause', ], 'managed' => [ 'shape' => 'Boolean', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'searchIn' => [ 'shape' => 'SearchInList', ], 'searchScope' => [ 'shape' => 'TypesSearchScope', ], 'searchText' => [ 'shape' => 'SearchText', ], 'sort' => [ 'shape' => 'SearchSort', ], ], ], 'SearchTypesOutput' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'SearchTypesResultItems', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'totalMatchCount' => [ 'shape' => 'Integer', ], ], ], 'SearchTypesResultItem' => [ 'type' => 'structure', 'members' => [ 'assetTypeItem' => [ 'shape' => 'AssetTypeItem', ], 'formTypeItem' => [ 'shape' => 'FormTypeData', ], ], 'union' => true, ], 'SearchTypesResultItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchTypesResultItem', ], ], 'SearchUserProfilesInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'userType', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'searchText' => [ 'shape' => 'UserSearchText', ], 'userType' => [ 'shape' => 'UserSearchType', ], ], ], 'SearchUserProfilesOutput' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'UserProfileSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'ShortDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'SingleSignOn' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'AuthType', ], 'userAssignment' => [ 'shape' => 'UserAssignment', ], ], ], 'Smithy' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'SortFieldProject' => [ 'type' => 'string', 'enum' => [ 'NAME', ], ], 'SortKey' => [ 'type' => 'string', 'enum' => [ 'CREATED_AT', 'UPDATED_AT', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'SsoUserProfileDetails' => [ 'type' => 'structure', 'members' => [ 'firstName' => [ 'shape' => 'FirstName', ], 'lastName' => [ 'shape' => 'LastName', ], 'username' => [ 'shape' => 'UserProfileName', ], ], ], 'StartDataSourceRunInput' => [ 'type' => 'structure', 'required' => [ 'dataSourceIdentifier', 'domainIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'dataSourceIdentifier' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceIdentifier', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], ], ], 'StartDataSourceRunOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'dataSourceId', 'domainId', 'id', 'projectId', 'status', 'type', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTime', ], 'dataSourceConfigurationSnapshot' => [ 'shape' => 'String', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'domainId' => [ 'shape' => 'DomainId', ], 'errorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'id' => [ 'shape' => 'DataSourceRunId', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'runStatisticsForAssets' => [ 'shape' => 'RunStatisticsForAssets', ], 'startedAt' => [ 'shape' => 'DateTime', ], 'status' => [ 'shape' => 'DataSourceRunStatus', ], 'stoppedAt' => [ 'shape' => 'DateTime', ], 'type' => [ 'shape' => 'DataSourceRunType', ], 'updatedAt' => [ 'shape' => 'DateTime', ], ], ], 'String' => [ 'type' => 'string', ], 'SubscribedAsset' => [ 'type' => 'structure', 'required' => [ 'assetId', 'assetRevision', 'status', ], 'members' => [ 'assetId' => [ 'shape' => 'AssetId', ], 'assetRevision' => [ 'shape' => 'Revision', ], 'failureCause' => [ 'shape' => 'FailureCause', ], 'failureTimestamp' => [ 'shape' => 'Timestamp', ], 'grantedTimestamp' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'SubscriptionGrantStatus', ], 'targetName' => [ 'shape' => 'String', ], ], ], 'SubscribedAssetListing' => [ 'type' => 'structure', 'members' => [ 'entityId' => [ 'shape' => 'AssetId', ], 'entityRevision' => [ 'shape' => 'Revision', ], 'entityType' => [ 'shape' => 'TypeName', ], 'forms' => [ 'shape' => 'Forms', ], 'glossaryTerms' => [ 'shape' => 'DetailedGlossaryTerms', ], ], ], 'SubscribedAssets' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedAsset', ], ], 'SubscribedListing' => [ 'type' => 'structure', 'required' => [ 'description', 'id', 'item', 'name', 'ownerProjectId', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'id' => [ 'shape' => 'ListingId', ], 'item' => [ 'shape' => 'SubscribedListingItem', ], 'name' => [ 'shape' => 'ListingName', ], 'ownerProjectId' => [ 'shape' => 'ProjectId', ], 'ownerProjectName' => [ 'shape' => 'String', ], 'revision' => [ 'shape' => 'Revision', ], ], ], 'SubscribedListingInput' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ListingId', ], ], ], 'SubscribedListingInputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedListingInput', ], 'max' => 1, 'min' => 1, ], 'SubscribedListingItem' => [ 'type' => 'structure', 'members' => [ 'assetListing' => [ 'shape' => 'SubscribedAssetListing', ], ], 'union' => true, ], 'SubscribedPrincipal' => [ 'type' => 'structure', 'members' => [ 'project' => [ 'shape' => 'SubscribedProject', ], ], 'union' => true, ], 'SubscribedPrincipalInput' => [ 'type' => 'structure', 'members' => [ 'project' => [ 'shape' => 'SubscribedProjectInput', ], ], 'union' => true, ], 'SubscribedPrincipalInputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedPrincipalInput', ], 'max' => 1, 'min' => 1, ], 'SubscribedProject' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ProjectId', ], 'name' => [ 'shape' => 'ProjectName', ], ], ], 'SubscribedProjectInput' => [ 'type' => 'structure', 'members' => [ 'identifier' => [ 'shape' => 'ProjectId', ], ], ], 'SubscriptionGrantId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'SubscriptionGrantOverallStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'GRANT_FAILED', 'REVOKE_FAILED', 'GRANT_AND_REVOKE_FAILED', 'COMPLETED', 'INACCESSIBLE', ], ], 'SubscriptionGrantStatus' => [ 'type' => 'string', 'enum' => [ 'GRANT_PENDING', 'REVOKE_PENDING', 'GRANT_IN_PROGRESS', 'REVOKE_IN_PROGRESS', 'GRANTED', 'REVOKED', 'GRANT_FAILED', 'REVOKE_FAILED', ], ], 'SubscriptionGrantSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'grantedEntity', 'id', 'status', 'subscriptionTargetId', 'updatedAt', ], 'members' => [ 'assets' => [ 'shape' => 'SubscribedAssets', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'grantedEntity' => [ 'shape' => 'GrantedEntity', ], 'id' => [ 'shape' => 'SubscriptionGrantId', ], 'status' => [ 'shape' => 'SubscriptionGrantOverallStatus', ], 'subscriptionId' => [ 'shape' => 'SubscriptionId', ], 'subscriptionTargetId' => [ 'shape' => 'SubscriptionTargetId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'SubscriptionGrants' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscriptionGrantSummary', ], ], 'SubscriptionId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'SubscriptionRequestId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'SubscriptionRequestStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACCEPTED', 'REJECTED', ], ], 'SubscriptionRequestSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'id', 'requestReason', 'status', 'subscribedListings', 'subscribedPrincipals', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'decisionComment' => [ 'shape' => 'DecisionComment', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'SubscriptionRequestId', ], 'requestReason' => [ 'shape' => 'RequestReason', ], 'reviewerId' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'SubscriptionRequestStatus', ], 'subscribedListings' => [ 'shape' => 'SubscriptionRequestSummarySubscribedListingsList', ], 'subscribedPrincipals' => [ 'shape' => 'SubscriptionRequestSummarySubscribedPrincipalsList', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'SubscriptionRequestSummarySubscribedListingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedListing', ], 'max' => 1, 'min' => 1, ], 'SubscriptionRequestSummarySubscribedPrincipalsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedPrincipal', ], 'max' => 1, 'min' => 1, ], 'SubscriptionRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscriptionRequestSummary', ], ], 'SubscriptionStatus' => [ 'type' => 'string', 'enum' => [ 'APPROVED', 'REVOKED', 'CANCELLED', ], ], 'SubscriptionSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'id', 'status', 'subscribedListing', 'subscribedPrincipal', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'SubscriptionId', ], 'retainPermissions' => [ 'shape' => 'Boolean', ], 'status' => [ 'shape' => 'SubscriptionStatus', ], 'subscribedListing' => [ 'shape' => 'SubscribedListing', ], 'subscribedPrincipal' => [ 'shape' => 'SubscribedPrincipal', ], 'subscriptionRequestId' => [ 'shape' => 'SubscriptionRequestId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'SubscriptionTargetForm' => [ 'type' => 'structure', 'required' => [ 'content', 'formName', ], 'members' => [ 'content' => [ 'shape' => 'String', ], 'formName' => [ 'shape' => 'FormName', ], ], ], 'SubscriptionTargetForms' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscriptionTargetForm', ], ], 'SubscriptionTargetId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'SubscriptionTargetName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'sensitive' => true, ], 'SubscriptionTargetSummary' => [ 'type' => 'structure', 'required' => [ 'applicableAssetTypes', 'authorizedPrincipals', 'createdAt', 'createdBy', 'domainId', 'environmentId', 'id', 'manageAccessRole', 'name', 'projectId', 'provider', 'subscriptionTargetConfig', 'type', ], 'members' => [ 'applicableAssetTypes' => [ 'shape' => 'ApplicableAssetTypes', ], 'authorizedPrincipals' => [ 'shape' => 'AuthorizedPrincipalIdentifiers', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'id' => [ 'shape' => 'SubscriptionTargetId', ], 'manageAccessRole' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'SubscriptionTargetName', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'provider' => [ 'shape' => 'String', ], 'subscriptionTargetConfig' => [ 'shape' => 'SubscriptionTargetForms', ], 'type' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'SubscriptionTargets' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscriptionTargetSummary', ], ], 'Subscriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscriptionSummary', ], ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[\\w \\.:/=+@-]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[\\w \\.:/=+@-]*$', ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TaskId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_-]{1,36}$', ], 'TaskStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'TermRelations' => [ 'type' => 'structure', 'members' => [ 'classifies' => [ 'shape' => 'TermRelationsClassifiesList', ], 'isA' => [ 'shape' => 'TermRelationsIsAList', ], ], ], 'TermRelationsClassifiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlossaryTermId', ], 'max' => 10, 'min' => 1, ], 'TermRelationsIsAList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlossaryTermId', ], 'max' => 10, 'min' => 1, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Timezone' => [ 'type' => 'string', 'enum' => [ 'UTC', 'AFRICA_JOHANNESBURG', 'AMERICA_MONTREAL', 'AMERICA_SAO_PAULO', 'ASIA_BAHRAIN', 'ASIA_BANGKOK', 'ASIA_CALCUTTA', 'ASIA_DUBAI', 'ASIA_HONG_KONG', 'ASIA_JAKARTA', 'ASIA_KUALA_LUMPUR', 'ASIA_SEOUL', 'ASIA_SHANGHAI', 'ASIA_SINGAPORE', 'ASIA_TAIPEI', 'ASIA_TOKYO', 'AUSTRALIA_MELBOURNE', 'AUSTRALIA_SYDNEY', 'CANADA_CENTRAL', 'CET', 'CST6CDT', 'ETC_GMT', 'ETC_GMT0', 'ETC_GMT_ADD_0', 'ETC_GMT_ADD_1', 'ETC_GMT_ADD_10', 'ETC_GMT_ADD_11', 'ETC_GMT_ADD_12', 'ETC_GMT_ADD_2', 'ETC_GMT_ADD_3', 'ETC_GMT_ADD_4', 'ETC_GMT_ADD_5', 'ETC_GMT_ADD_6', 'ETC_GMT_ADD_7', 'ETC_GMT_ADD_8', 'ETC_GMT_ADD_9', 'ETC_GMT_NEG_0', 'ETC_GMT_NEG_1', 'ETC_GMT_NEG_10', 'ETC_GMT_NEG_11', 'ETC_GMT_NEG_12', 'ETC_GMT_NEG_13', 'ETC_GMT_NEG_14', 'ETC_GMT_NEG_2', 'ETC_GMT_NEG_3', 'ETC_GMT_NEG_4', 'ETC_GMT_NEG_5', 'ETC_GMT_NEG_6', 'ETC_GMT_NEG_7', 'ETC_GMT_NEG_8', 'ETC_GMT_NEG_9', 'EUROPE_DUBLIN', 'EUROPE_LONDON', 'EUROPE_PARIS', 'EUROPE_STOCKHOLM', 'EUROPE_ZURICH', 'ISRAEL', 'MEXICO_GENERAL', 'MST7MDT', 'PACIFIC_AUCKLAND', 'US_CENTRAL', 'US_EASTERN', 'US_MOUNTAIN', 'US_PACIFIC', ], ], 'Title' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'sensitive' => true, ], 'Topic' => [ 'type' => 'structure', 'required' => [ 'resource', 'role', 'subject', ], 'members' => [ 'resource' => [ 'shape' => 'NotificationResource', ], 'role' => [ 'shape' => 'NotificationRole', ], 'subject' => [ 'shape' => 'String', ], ], ], 'TypeName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[^\\.]*', ], 'TypesSearchScope' => [ 'type' => 'string', 'enum' => [ 'ASSET_TYPE', 'FORM_TYPE', ], ], 'UnauthorizedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 401, 'senderFault' => true, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDataSourceInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'assetFormsInput' => [ 'shape' => 'FormInputList', ], 'configuration' => [ 'shape' => 'DataSourceConfigurationInput', ], 'description' => [ 'shape' => 'Description', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'enableSetting' => [ 'shape' => 'EnableSetting', ], 'identifier' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'identifier', ], 'name' => [ 'shape' => 'Name', ], 'publishOnImport' => [ 'shape' => 'Boolean', ], 'recommendation' => [ 'shape' => 'RecommendationConfiguration', ], 'schedule' => [ 'shape' => 'ScheduleConfiguration', ], ], ], 'UpdateDataSourceOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'environmentId', 'id', 'name', 'projectId', ], 'members' => [ 'assetFormsOutput' => [ 'shape' => 'FormOutputList', ], 'configuration' => [ 'shape' => 'DataSourceConfigurationOutput', ], 'createdAt' => [ 'shape' => 'DateTime', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'enableSetting' => [ 'shape' => 'EnableSetting', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'errorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'id' => [ 'shape' => 'DataSourceId', ], 'lastRunAt' => [ 'shape' => 'DateTime', ], 'lastRunErrorMessage' => [ 'shape' => 'DataSourceErrorMessage', ], 'lastRunStatus' => [ 'shape' => 'DataSourceRunStatus', ], 'name' => [ 'shape' => 'Name', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'publishOnImport' => [ 'shape' => 'Boolean', ], 'recommendation' => [ 'shape' => 'RecommendationConfiguration', ], 'schedule' => [ 'shape' => 'ScheduleConfiguration', ], 'status' => [ 'shape' => 'DataSourceStatus', ], 'type' => [ 'shape' => 'DataSourceType', ], 'updatedAt' => [ 'shape' => 'DateTime', ], ], ], 'UpdateDomainInput' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'description' => [ 'shape' => 'String', ], 'domainExecutionRole' => [ 'shape' => 'RoleArn', ], 'identifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'identifier', ], 'name' => [ 'shape' => 'String', ], 'singleSignOn' => [ 'shape' => 'SingleSignOn', ], ], ], 'UpdateDomainOutput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'description' => [ 'shape' => 'String', ], 'domainExecutionRole' => [ 'shape' => 'RoleArn', ], 'id' => [ 'shape' => 'DomainId', ], 'lastUpdatedAt' => [ 'shape' => 'UpdatedAt', ], 'name' => [ 'shape' => 'String', ], 'singleSignOn' => [ 'shape' => 'SingleSignOn', ], ], ], 'UpdateEnvironmentInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'description' => [ 'shape' => 'String', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'identifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'identifier', ], 'name' => [ 'shape' => 'String', ], ], ], 'UpdateEnvironmentOutput' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'domainId', 'environmentProfileId', 'name', 'projectId', 'provider', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AwsAccountId', ], 'awsAccountRegion' => [ 'shape' => 'AwsRegion', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'String', ], 'deploymentProperties' => [ 'shape' => 'DeploymentProperties', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'environmentActions' => [ 'shape' => 'EnvironmentActionList', ], 'environmentBlueprintId' => [ 'shape' => 'EnvironmentBlueprintId', ], 'environmentProfileId' => [ 'shape' => 'EnvironmentProfileId', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'id' => [ 'shape' => 'EnvironmentId', ], 'lastDeployment' => [ 'shape' => 'Deployment', ], 'name' => [ 'shape' => 'EnvironmentName', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'provider' => [ 'shape' => 'String', ], 'provisionedResources' => [ 'shape' => 'ResourceList', ], 'provisioningProperties' => [ 'shape' => 'ProvisioningProperties', ], 'status' => [ 'shape' => 'EnvironmentStatus', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'userParameters' => [ 'shape' => 'CustomParameterList', ], ], ], 'UpdateEnvironmentProfileInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AwsAccountId', ], 'awsAccountRegion' => [ 'shape' => 'AwsRegion', ], 'description' => [ 'shape' => 'String', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'EnvironmentProfileId', 'location' => 'uri', 'locationName' => 'identifier', ], 'name' => [ 'shape' => 'EnvironmentProfileName', ], 'userParameters' => [ 'shape' => 'EnvironmentParametersList', ], ], ], 'UpdateEnvironmentProfileOutput' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'domainId', 'environmentBlueprintId', 'id', 'name', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AwsAccountId', ], 'awsAccountRegion' => [ 'shape' => 'AwsRegion', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'environmentBlueprintId' => [ 'shape' => 'EnvironmentBlueprintId', ], 'id' => [ 'shape' => 'EnvironmentProfileId', ], 'name' => [ 'shape' => 'EnvironmentProfileName', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'userParameters' => [ 'shape' => 'CustomParameterList', ], ], ], 'UpdateGlossaryInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'GlossaryDescription', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'GlossaryId', 'location' => 'uri', 'locationName' => 'identifier', ], 'name' => [ 'shape' => 'GlossaryName', ], 'status' => [ 'shape' => 'GlossaryStatus', ], ], ], 'UpdateGlossaryOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'id', 'name', 'owningProjectId', ], 'members' => [ 'description' => [ 'shape' => 'GlossaryDescription', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'GlossaryId', ], 'name' => [ 'shape' => 'GlossaryName', ], 'owningProjectId' => [ 'shape' => 'ProjectId', ], 'status' => [ 'shape' => 'GlossaryStatus', ], ], ], 'UpdateGlossaryTermInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'glossaryIdentifier' => [ 'shape' => 'GlossaryTermId', ], 'identifier' => [ 'shape' => 'GlossaryTermId', 'location' => 'uri', 'locationName' => 'identifier', ], 'longDescription' => [ 'shape' => 'LongDescription', ], 'name' => [ 'shape' => 'GlossaryTermName', ], 'shortDescription' => [ 'shape' => 'ShortDescription', ], 'status' => [ 'shape' => 'GlossaryTermStatus', ], 'termRelations' => [ 'shape' => 'TermRelations', ], ], ], 'UpdateGlossaryTermOutput' => [ 'type' => 'structure', 'required' => [ 'domainId', 'glossaryId', 'id', 'name', 'status', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', ], 'glossaryId' => [ 'shape' => 'GlossaryId', ], 'id' => [ 'shape' => 'GlossaryTermId', ], 'longDescription' => [ 'shape' => 'LongDescription', ], 'name' => [ 'shape' => 'GlossaryTermName', ], 'shortDescription' => [ 'shape' => 'ShortDescription', ], 'status' => [ 'shape' => 'GlossaryTermStatus', ], 'termRelations' => [ 'shape' => 'TermRelations', ], ], ], 'UpdateGroupProfileInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'groupIdentifier', 'status', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'groupIdentifier' => [ 'shape' => 'GroupIdentifier', 'location' => 'uri', 'locationName' => 'groupIdentifier', ], 'status' => [ 'shape' => 'GroupProfileStatus', ], ], ], 'UpdateGroupProfileOutput' => [ 'type' => 'structure', 'members' => [ 'domainId' => [ 'shape' => 'DomainId', ], 'groupName' => [ 'shape' => 'GroupProfileName', ], 'id' => [ 'shape' => 'GroupProfileId', ], 'status' => [ 'shape' => 'GroupProfileStatus', ], ], ], 'UpdateProjectInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'identifier' => [ 'shape' => 'ProjectId', 'location' => 'uri', 'locationName' => 'identifier', ], 'name' => [ 'shape' => 'ProjectName', ], ], ], 'UpdateProjectOutput' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'domainId', 'id', 'name', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'domainId' => [ 'shape' => 'DomainId', ], 'glossaryTerms' => [ 'shape' => 'GlossaryTerms', ], 'id' => [ 'shape' => 'ProjectId', ], 'lastUpdatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'name' => [ 'shape' => 'ProjectName', ], ], ], 'UpdateSubscriptionGrantStatusInput' => [ 'type' => 'structure', 'required' => [ 'assetIdentifier', 'domainIdentifier', 'identifier', 'status', ], 'members' => [ 'assetIdentifier' => [ 'shape' => 'AssetId', 'location' => 'uri', 'locationName' => 'assetIdentifier', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'failureCause' => [ 'shape' => 'FailureCause', ], 'identifier' => [ 'shape' => 'SubscriptionGrantId', 'location' => 'uri', 'locationName' => 'identifier', ], 'status' => [ 'shape' => 'SubscriptionGrantStatus', ], 'targetName' => [ 'shape' => 'String', ], ], ], 'UpdateSubscriptionGrantStatusOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'grantedEntity', 'id', 'status', 'subscriptionTargetId', 'updatedAt', ], 'members' => [ 'assets' => [ 'shape' => 'SubscribedAssets', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'grantedEntity' => [ 'shape' => 'GrantedEntity', ], 'id' => [ 'shape' => 'SubscriptionGrantId', ], 'status' => [ 'shape' => 'SubscriptionGrantOverallStatus', ], 'subscriptionId' => [ 'shape' => 'SubscriptionId', ], 'subscriptionTargetId' => [ 'shape' => 'SubscriptionTargetId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'UpdateSubscriptionRequestInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'identifier', 'requestReason', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionRequestId', 'location' => 'uri', 'locationName' => 'identifier', ], 'requestReason' => [ 'shape' => 'RequestReason', ], ], ], 'UpdateSubscriptionRequestOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'domainId', 'id', 'requestReason', 'status', 'subscribedListings', 'subscribedPrincipals', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'decisionComment' => [ 'shape' => 'DecisionComment', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'SubscriptionRequestId', ], 'requestReason' => [ 'shape' => 'RequestReason', ], 'reviewerId' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'SubscriptionRequestStatus', ], 'subscribedListings' => [ 'shape' => 'UpdateSubscriptionRequestOutputSubscribedListingsList', ], 'subscribedPrincipals' => [ 'shape' => 'UpdateSubscriptionRequestOutputSubscribedPrincipalsList', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'UpdateSubscriptionRequestOutputSubscribedListingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedListing', ], 'max' => 1, 'min' => 1, ], 'UpdateSubscriptionRequestOutputSubscribedPrincipalsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedPrincipal', ], 'max' => 1, 'min' => 1, ], 'UpdateSubscriptionTargetInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'environmentIdentifier', 'identifier', ], 'members' => [ 'applicableAssetTypes' => [ 'shape' => 'ApplicableAssetTypes', ], 'authorizedPrincipals' => [ 'shape' => 'AuthorizedPrincipalIdentifiers', ], 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'environmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentIdentifier', ], 'identifier' => [ 'shape' => 'SubscriptionTargetId', 'location' => 'uri', 'locationName' => 'identifier', ], 'manageAccessRole' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'SubscriptionTargetName', ], 'provider' => [ 'shape' => 'String', ], 'subscriptionTargetConfig' => [ 'shape' => 'SubscriptionTargetForms', ], ], ], 'UpdateSubscriptionTargetOutput' => [ 'type' => 'structure', 'required' => [ 'applicableAssetTypes', 'authorizedPrincipals', 'createdAt', 'createdBy', 'domainId', 'environmentId', 'id', 'manageAccessRole', 'name', 'projectId', 'provider', 'subscriptionTargetConfig', 'type', ], 'members' => [ 'applicableAssetTypes' => [ 'shape' => 'ApplicableAssetTypes', ], 'authorizedPrincipals' => [ 'shape' => 'AuthorizedPrincipalIdentifiers', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'domainId' => [ 'shape' => 'DomainId', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'id' => [ 'shape' => 'SubscriptionTargetId', ], 'manageAccessRole' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'SubscriptionTargetName', ], 'projectId' => [ 'shape' => 'ProjectId', ], 'provider' => [ 'shape' => 'String', ], 'subscriptionTargetConfig' => [ 'shape' => 'SubscriptionTargetForms', ], 'type' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'UpdateUserProfileInput' => [ 'type' => 'structure', 'required' => [ 'domainIdentifier', 'status', 'userIdentifier', ], 'members' => [ 'domainIdentifier' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainIdentifier', ], 'status' => [ 'shape' => 'UserProfileStatus', ], 'type' => [ 'shape' => 'UserProfileType', ], 'userIdentifier' => [ 'shape' => 'UserIdentifier', 'location' => 'uri', 'locationName' => 'userIdentifier', ], ], ], 'UpdateUserProfileOutput' => [ 'type' => 'structure', 'members' => [ 'details' => [ 'shape' => 'UserProfileDetails', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'UserProfileId', ], 'status' => [ 'shape' => 'UserProfileStatus', ], 'type' => [ 'shape' => 'UserProfileType', ], ], ], 'UpdatedAt' => [ 'type' => 'timestamp', ], 'UpdatedBy' => [ 'type' => 'string', ], 'UserAssignment' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC', 'MANUAL', ], ], 'UserDesignation' => [ 'type' => 'string', 'enum' => [ 'PROJECT_OWNER', 'PROJECT_CONTRIBUTOR', ], ], 'UserDetails' => [ 'type' => 'structure', 'required' => [ 'userId', ], 'members' => [ 'userId' => [ 'shape' => 'String', ], ], ], 'UserIdentifier' => [ 'type' => 'string', 'pattern' => '(^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$|^[a-zA-Z_0-9+=,.@-]+$|^arn:aws:iam::\\d{12}:.+$)', ], 'UserProfileDetails' => [ 'type' => 'structure', 'members' => [ 'iam' => [ 'shape' => 'IamUserProfileDetails', ], 'sso' => [ 'shape' => 'SsoUserProfileDetails', ], ], 'union' => true, ], 'UserProfileId' => [ 'type' => 'string', 'pattern' => '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$', ], 'UserProfileName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[a-zA-Z_0-9+=,.@-]+$', 'sensitive' => true, ], 'UserProfileStatus' => [ 'type' => 'string', 'enum' => [ 'ASSIGNED', 'NOT_ASSIGNED', 'ACTIVATED', 'DEACTIVATED', ], ], 'UserProfileSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserProfileSummary', ], ], 'UserProfileSummary' => [ 'type' => 'structure', 'members' => [ 'details' => [ 'shape' => 'UserProfileDetails', ], 'domainId' => [ 'shape' => 'DomainId', ], 'id' => [ 'shape' => 'UserProfileId', ], 'status' => [ 'shape' => 'UserProfileStatus', ], 'type' => [ 'shape' => 'UserProfileType', ], ], ], 'UserProfileType' => [ 'type' => 'string', 'enum' => [ 'IAM', 'SSO', ], ], 'UserSearchText' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'UserSearchType' => [ 'type' => 'string', 'enum' => [ 'SSO_USER', 'DATAZONE_USER', 'DATAZONE_SSO_USER', 'DATAZONE_IAM_USER', ], ], 'UserType' => [ 'type' => 'string', 'enum' => [ 'IAM_USER', 'IAM_ROLE', 'SSO_USER', ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
