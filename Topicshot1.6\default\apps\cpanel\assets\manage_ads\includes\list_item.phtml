<tr data-ads-item="<?php echo($cl['li']['id']); ?>">
    <td scope="row">
        <?php echo($cl['li']['id']); ?>
    </td>
    <td>
    	<div class="user-info-holder">
            <div class="avatar-holder">
                <img src="<?php echo($cl['li']['owner']['avatar']); ?>" alt="Avatar">
            </div>
            <div class="uname-holder">
                <b>
                    <span class="user-name-holder <?php if ($cl['li']['owner']['verified'] == '1') {echo('verified-badge');} ?>">
                        <?php echo($cl['li']['owner']['name']); ?>
                    </span>
                </b>
                <a href="<?php echo($cl['li']['owner']['url']); ?>">
                    <?php echo($cl['li']['owner']['username']); ?>
                </a>
            </div>
        </div>
    </td>
    <td>
    	<a target="_blank" href="<?php echo($cl['li']['target_url']); ?>">
            <?php echo($cl['li']['domain']); ?>   
        </a>
    </td>
    <td>
    	<b class="num">
    		<?php echo($cl['li']['budget']); ?>
    	</b>
    </td>
    <td>
    	<b class="num">
    		<?php echo($cl['li']['clicks']); ?>
    	</b>
    </td>
    <td>
    	<b class="num">
    		<?php echo($cl['li']['views']); ?>
    	</b>
    </td>
    <td>
        <?php if ($cl['li']['status'] == "active"): ?>
            <span class="badge bg-green">
                Active
            </span>
        <?php else: ?>
            <span class="badge bg-grey">
                Inactive
            </span>
        <?php endif; ?>
    </td>
    <td>
        <?php if ($cl['li']['approved'] == "Y"): ?>
            <span data-an="approval-status" class="badge bg-blue">
                Yes
            </span>
        <?php else: ?>
            <span data-an="approval-status" class="badge bg-grey">
                No
            </span>
        <?php endif; ?>
    </td>
    <td>
    	<time>
    		<?php echo($cl['li']['time']); ?>
    	</time>
    </td>
    <td>
    	<div class="dropdown">
            <a href="javascript:void(0);" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                <?php echo cl_ficon("chevron_down"); ?>
            </a>
            <div class="dropdown-menu dropdown-menu-right">
                <a target="_blank" href="<?php echo($cl['li']['ad_url']); ?>" class="dropdown-item">
                    Show
                </a>
                <?php if ($cl['li']['approved'] == "N"): ?>
                    <a onclick="SMC_CPanel.PS.approve('<?php echo($cl['li']['id']); ?>');" href="javascript:void(0);" class="dropdown-item">
                        Approve
                    </a>
                 <?php endif; ?>
                <a href="javascript:void(0);" class="dropdown-item" onclick="SMC_CPanel.PS.delete('<?php echo($cl['li']['id']); ?>');">
                    Delete
                </a>
            </ul>
        </div>
    </td>
</tr>