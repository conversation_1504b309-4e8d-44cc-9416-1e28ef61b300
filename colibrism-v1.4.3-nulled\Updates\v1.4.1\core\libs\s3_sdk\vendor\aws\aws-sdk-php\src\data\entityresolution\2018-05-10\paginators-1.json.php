<?php
// This file was auto-generated from sdk-root/src/data/entityresolution/2018-05-10/paginators-1.json
return [ 'pagination' => [ 'ListIdMappingJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'jobs', ], 'ListIdMappingWorkflows' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'workflowSummaries', ], 'ListMatchingJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'jobs', ], 'ListMatchingWorkflows' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'workflowSummaries', ], 'ListProviderServices' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'providerServiceSummaries', ], 'ListSchemaMappings' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'schemaList', ], ],];
