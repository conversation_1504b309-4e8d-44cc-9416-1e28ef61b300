<?php
// This file was auto-generated from sdk-root/src/data/inspector-scan/2023-08-08/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-08-08', 'endpointPrefix' => 'inspector-scan', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'inspector-scan', 'serviceFullName' => 'Inspector Scan', 'serviceId' => 'Inspector Scan', 'signatureVersion' => 'v4', 'signingName' => 'inspector-scan', 'uid' => 'inspector-scan-2023-08-08', ], 'operations' => [ 'ScanSbom' => [ 'name' => 'ScanSbom', 'http' => [ 'method' => 'POST', 'requestUri' => '/scan/sbom', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ScanSbomRequest', ], 'output' => [ 'shape' => 'ScanSbomResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'InternalServerExceptionReason', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'InternalServerExceptionReason' => [ 'type' => 'string', 'enum' => [ 'FAILED_TO_GENERATE_SBOM', 'OTHER', ], ], 'OutputFormat' => [ 'type' => 'string', 'enum' => [ 'CYCLONE_DX_1_5', 'INSPECTOR', ], ], 'Sbom' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'ScanSbomRequest' => [ 'type' => 'structure', 'required' => [ 'sbom', ], 'members' => [ 'sbom' => [ 'shape' => 'Sbom', ], 'outputFormat' => [ 'shape' => 'OutputFormat', ], ], ], 'ScanSbomResponse' => [ 'type' => 'structure', 'members' => [ 'sbom' => [ 'shape' => 'Sbom', ], ], ], 'String' => [ 'type' => 'string', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fields' => [ 'shape' => 'ValidationExceptionFields', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'UNSUPPORTED_SBOM_TYPE', 'OTHER', ], ], ],];
