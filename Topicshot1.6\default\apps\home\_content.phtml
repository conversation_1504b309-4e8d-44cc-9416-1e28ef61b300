<div class="timeline-container" data-app="homepage">
	<div class="timeline-header" data-el="tl-header" style="display: block; padding: 0;">
		<div class="timeline-header__botline" style="">
			<div class="lp">
				<div class="nav-link-holder h-mobile">
					<a href="<?php echo cl_link("/"); ?>" data-spa="true">
						<?php echo cl_translate("Home"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("/"); ?>" class="h-laptop">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
			</div>
		</div>
		<div>
			<!-- Tabs navs -->
			<ul class="nav nav-tabs nav-justified mb-3" id="myTab" role="tablist">
				<li class="nav-item" role="presentation">
					<a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab"
						aria-controls="home" aria-selected="true"><?php echo cl_translate("For You"); ?> <div
							class="css-1dbjc4n"></div></a>
				</li>
				<li class="nav-item" role="presentation">
					<a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab"
						aria-controls="profile" aria-selected="false">
						<?php echo cl_translate("Following"); ?>
						<div class="css-1dbjc4n"></div>
					</a>
				</li>
			</ul>
			<!-- Tabs navs -->
		</div>
	</div>
	<div class="homepage">
		<div class="main-timeline-pubbox-wrapper h-mobile">
			<?php $cl['pb_id'] = 1;
			echo cl_template('timeline/pubbox'); ?>
		</div>
		<?php if ($cl["config"]["swift_system_status"] == "on"): ?>
				<div class="homepage__swifts">
					<div class="swifts-slider">
						<div class="swiper" id="homepage-swifts-slider">
							<div class="swiper-wrapper">
								<div class="swiper-slide">
									<div class="swift-item" onclick="SMColibri.PS.new_swift.add_swift();">
										<div class="swift-item__body create-swift">
											<div class="swift-item__avatar">
												<img src="<?php echo ($me['avatar']); ?>" alt="Avatar">
											</div>
											<span class="add-ikon">
												<?php echo cl_ikon("plus"); ?>
											</span>
										</div>
										<div class="swift-item__footer">
											<?php echo cl_translate("Create new swift"); ?>
										</div>
									</div>
								</div>
								<?php if (not_empty($cl["tl_swifts"])): ?>
										<?php foreach ($cl["tl_swifts"] as $swift_id => $swift_data): ?>
												<div class="swiper-slide">
													<div data-an="swift-item-<?php echo ($swift_data['id']); ?>" class="swift-item <?php if (not_empty($swift_data['has_unseen'])) {
														   echo ('active');
													   } ?>" onclick="SMColibri.PS.play_swift.show('<?php echo ($swift_id); ?>');">
														<div class="swift-item__body">
															<div class="swift-item__avatar">
																<img src="<?php echo ($swift_data['avatar']); ?>" alt="Avatar">
															</div>
														</div>
														<div class="swift-item__footer">
															<?php if (not_empty($swift_data["is_user"])): ?>
																	<?php echo cl_translate("Your swifts"); ?>
															<?php else: ?>
																	<?php echo ($swift_data['name']); ?>
															<?php endif; ?>
														</div>
													</div>
												</div>
										<?php endforeach; ?>
								<?php endif; ?>
							</div>
						</div>
						<div class="swifts-slider__footer">
							<div class="swiper-scrollbar" id="homepage-swifts-scrollbar"></div>
						</div>
					</div>
				</div>
		<?php endif; ?>
	</div>

	<div class="tab-content" id="myTabContent">
		<div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
			<?php $count = 0; ?>
			<?php if (not_empty($cl["feed"])): ?>
					<div class="timeline-posts-container">
						<div class="timeline-posts-ls" data-an="entry-list">
							<?php foreach ($cl["feed"] as $cl['li']): ?>
									<?php $count++; ?>
									<?php echo cl_template('timeline/post'); ?>
									<?php if ($count == 3) {
										echo cl_template('home/follow');
									} ?>
							<?php endforeach; ?>
						</div>
						<?php if (count($cl["feed"]) == 30): ?>
								<div class="timeline-data-loader">
									<button class="btn btn-custom main-outline lg" data-an="load-more">
										<?php echo cl_translate("Show more"); ?>
									</button>
								</div>
						<?php endif; ?>
					</div>
			<?php else: ?>
					<div class="timeline-placeholder">
						<div class="icon">
							<div class="icon__bg">
								<?php echo cl_icon('feed'); ?>
							</div>
						</div>
						<div class="pl-message">
							<h4>
								<?php echo cl_translate("No data to display yet!"); ?>
							</h4>
							<p>
								<?php echo cl_translate("It looks like the site has no content to display yet. All featured posts will be displayed on this page"); ?>
							</p>
						</div>
					</div>
			<?php endif; ?>
		</div>

		<div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
			<div class="timeline-posts-container" style="margin-top: 0px;">
				<?php $count = 0; ?>
				<?php if (not_empty($cl["tl_feed"])): ?>
						<div class="timeline-posts-ls" data-an="entry-list">
							<?php foreach ($cl["tl_feed"] as $cl["li"]): ?>
									<?php $count++; ?>
									<?php echo cl_template('timeline/post'); ?>
									<?php if ($count == 3) {
										echo cl_template('home/follow');
									} ?>
							<?php endforeach; ?>
						</div>
						<?php if (count($cl["tl_feed"]) == 30): ?>
								<div class="timeline-data-loader">
									<button class="btn btn-custom main-outline lg" data-an="load-more">
										<?php echo cl_translate("Show more"); ?>
									</button>
								</div>
						<?php endif; ?>

				
			<?php else: ?>
					<div class="timeline-placeholder">
						<div class="icon">
							<div class="icon__bg">
								<?php echo cl_icon('feed'); ?>
							</div>
						</div>
						<div class="pl-message">
							<h4>
								<?php echo cl_translate("No data to display yet!"); ?>
							</h4>
							<p>
								<?php echo cl_translate("It looks like the site has no content to display yet. All featured posts will be displayed on this page"); ?>
							</p>
						</div>
					</div>
			<?php endif; ?>
			</div>
		</div>
	</div>

	<?php if (not_empty($cl["is_logged"]) && in_array($cl["pn"], array("conversation")) != true): ?>
			<div class="h-laptop">
				<div class="mobile-bottom-navbar2" data-app="mobile-navbar">
					<div class="mobile-bottom-navbar-inner">

						<div class="avatar-menu">
							<a href="#" onclick="SMColibri.toggleSB();"> <img src="<?php echo ($me['avatar']); ?>"
									alt="Avatar"></a>
						</div>

					</div>
				</div>
			</div>
	<?php endif; ?>

	<?php if ($cl["config"]["swift_system_status"] == "on"): ?>
			<?php echo cl_template("home/modals/swift_lightbox"); ?>
			<?php echo cl_template("home/modals/swift_pubbox"); ?>
	<?php endif; ?>

	<?php echo cl_template("main/includes/inline_statics/app_statics"); ?>
	<?php echo cl_template("home/scripts/app_master_script"); ?>
	<?php echo cl_template('feed/scripts/app_master_script'); ?>
</div>
<!-- MDB -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.2.0/mdb.min.css" rel="stylesheet" />
<!-- MDB -->
<style>
	body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-header {
		height: 107px;
		background-color: var(--smc-main-bg-color-3);
		backdrop-filter: blur(12px);
	}

	.timeline-header__botline {
		display: flex;
		justify-content: space-between;
		width: 100%;
		height: 53px !important;
		align-items: center;
		padding-left: 16px;
		padding-right: 16px;
	}

	.nav-tabs {

		border-bottom-color: var(--smc-main-border-color);
		border-bottom-style: solid;
		border-bottom-width: 1px;
	}

	.nav-fill .nav-item .nav-link,
	.nav-justified .nav-item .nav-link {
		width: auto;
		text-decoration: none;
		text-transform: none;
		color: var(--smc-main-text-color-lighten-5);
		font-weight: 500;
		font-size: 15px;
	}

	.nav-tabs .nav-item.show .nav-link,
	.nav-tabs .nav-link.active {
		color: var(--smc-main-text-color);
		border-bottom-color: var(--smc-main-border-color);
		border-bottom-width: 0px;
		font-weight: 700;
		background: transparent;
	}

	.nav-tabs .nav-item.show .nav-link,
	.nav-tabs .nav-link.active div.css-1dbjc4n {
		background-color: rgb(29, 155, 240);
		height: 4px;
		width: 56px;
		border-radius: 9999px;
		text-align: center;
		display: block;
		position: relative;
		bottom: 0;
		margin-left: auto;
		margin-right: auto;
		margin-top: 15px;
		margin-bottom: -15px;

	}
</style>
<script>
	// Initialise Carousel
	const cardSlider = new Carousel(document.querySelector("#cardSlider"), {
		Dots: false,
	})
	const cardSlider2 = new Carousel(document.querySelector("#cardSlider2"), {
		Dots: false,
	})
</script>
<script>
	"use strict";

	var offset = 30;

	jQuery(document).ready(function ($) {
		var _app = $('div[data-app="homepage"]');
		var feed_ls = _app.find('[data-an="entry-list"]');

		_app.find('button[data-an="load-more"]').on('click', function (event) {
			event.preventDefault();

			var _self = $(this);


			$.ajax({
				url: "<?php echo cl_link("native_api/home/<USER>"); ?>",
				type: 'GET',
				dataType: 'json',
				data: {
					offset: offset,
				},
				beforeSend: function () {
					_self.attr('disabled', 'true').text("<?php echo cl_translate("Please wait"); ?>");
				}
			}).done(function (data) {
				if (data.status == 200) {
					feed_ls.append(data.html);

					_self.removeAttr('disabled').text("<?php echo cl_translate("Show more"); ?>");

					offset += 30;
				}

				else {
					_self.text("<?php echo cl_translate("That is all for now!"); ?>");
				}
			});
		});
	});
</script>