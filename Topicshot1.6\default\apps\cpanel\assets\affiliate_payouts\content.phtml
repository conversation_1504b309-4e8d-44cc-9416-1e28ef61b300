<div class="cp-app-container" data-app="affiliate-payouts">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Affiliate payouts
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card" id="vue-cpanel-affiliate-payouts-app">
                <div class="header">
                    <h2>
                        Manage affiliate payout requests - ({{total_requests}})
                    </h2>
                </div>
                <div class="body">
                    <div class="inline-alertbox-wrapper">
                        <div class="inline-alertbox info">
                            <div class="icon">
                                <?php echo cl_ficon("info"); ?>
                            </div>
                            <div class="alert-message">
                                <p>
                                    Please note: When paying or refusing to pay, do not forget to send a notification to the user by email, informing him about the result of his request.
                                </p>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="regular-table">
                        <table class="table table-hover no-mb">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>
                                        Username
                                    </th>
                                    <th>
                                        E-mail address
                                    </th>
                                    <th>
                                        Paypal
                                    </th>
                                    <th>
                                        Amount
                                    </th>
                                    <th>
                                        Status
                                    </th>
                                    <th>
                                        Requested at
                                    </th>
                                    <th>
                                        Action
                                    </th>
                                </tr>
                            </thead>
                            <tbody data-an="requests-list">
                                <?php if (not_empty($cl['requests'])): ?>
                                    <?php foreach ($cl['requests'] as $cl['li']): ?>
                                        <?php echo cl_template('cpanel/assets/affiliate_payouts/includes/list_item'); ?>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8">
                                            <div class="empty-table">
                                                <h4><?php echo('No requests found'); ?></h4>
                                                <p>
                                                    <?php echo('It looks like there are no affiliate payout requests in your database yet. All affiliate payout requests will be displayed here.'); ?>
                                                </p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        
                        <div class="table-pagination">
                            <a v-if="show_ctrls" v-bind:class="{'disabled': dis_prev_ctrl}" v-on:click="paginate('up')" href="javascript:void(0);" class="pagination-ctrls prev">
                                <?php echo cl_ficon("arrow_left"); ?>
                            </a>
                            <a v-if="show_ctrls" v-bind:class="{'disabled': dis_next_ctrl}" v-on:click="paginate('down')" href="javascript:void(0);" class="pagination-ctrls next">
                                <?php echo cl_ficon("arrow_right"); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php echo cl_template('cpanel/assets/affiliate_payouts/scripts/app_master_script'); ?>