<div class="timeline-container">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("settings/social_links"); ?>" data-spa="true">
						<?php echo cl_translate("Social links"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("home"); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="profile-settings">
		<div class="profile-settings__content">
			<div class="settings-form">
				<form class="form" id="cl-social-settings-vue-app" v-on:submit="submit_form($event)">
	                <div class="form-group">
                        <label>
                            Facebook
                        </label>
                        <input v-model.trim="$v.facebook.$model" type="url" name="facebook" class="form-control" placeholder="<?php echo cl_translate("Enter your facebook URL"); ?>">
                        <div class="invalid-main-feedback" v-if="is_invalid_facebook">
                            {{invalid_feedback_facebook}}
                        </div>
                    </div>
	                <div class="form-group">
                        <label>
                            Twitter
                        </label>
                        <input v-model.trim="$v.twitter.$model" type="url" name="twitter" class="form-control" placeholder="<?php echo cl_translate("Enter your twitter URL"); ?>">
                        <div class="invalid-main-feedback" v-if="is_invalid_twitter">
                            {{invalid_feedback_twitter}}
                        </div>
                    </div>
	                <div class="form-group">
                        <label>
                            Youtube
                        </label>
                        <input v-model.trim="$v.youtube.$model" type="url" name="youtube" class="form-control" placeholder="<?php echo cl_translate("Enter your youtube URL"); ?>">
                        <div class="invalid-main-feedback" v-if="is_invalid_youtube">
                            {{invalid_feedback_youtube}}
                        </div>
                    </div>
	                <div class="form-group">
                        <label>
                            Instagram
                        </label>
                        <input v-model.trim="$v.instagram.$model" type="url" name="instagram" class="form-control" placeholder="<?php echo cl_translate("Enter your instagram URL"); ?>">
                        <div class="invalid-main-feedback" v-if="is_invalid_instagram">
                            {{invalid_feedback_instagram}}
                        </div>
                    </div>
	                <div class="form-group">
                        <label>
                            Vkontakte
                        </label>
                        <input v-model.trim="$v.vkontakte.$model" type="url" name="vkontakte" class="form-control" placeholder="<?php echo cl_translate("Enter your vkontakte URL"); ?>">
                        <div class="invalid-main-feedback" v-if="is_invalid_vkontakte">
                            {{invalid_feedback_vkontakte}}
                        </div>
                    </div>
	                <div class="form-group">
                        <label>
                            TikTok
                        </label>
                        <input v-model.trim="$v.tiktok.$model" type="url" name="tiktok" class="form-control" placeholder="<?php echo cl_translate("Enter your tiktok URL"); ?>">
                        <div class="invalid-main-feedback" v-if="is_invalid_tiktok">
                            {{invalid_feedback_tiktok}}
                        </div>
                    </div>
	                <div class="form-group">
                        <label>
                            Linked In
                        </label>
                        <input v-model.trim="$v.linkedin.$model" type="url" name="linkedin" class="form-control" placeholder="<?php echo cl_translate("Enter your linkedin URL"); ?>">
                        <div class="invalid-main-feedback" v-if="is_invalid_linkedin">
                            {{invalid_feedback_linkedin}}
                        </div>
                    </div>
                    <div class="form-group" v-if="unsuccessful_attempt">
                        <div class="invalid-main-feedback">
                            <?php echo cl_translate("Something went wrong while trying to save your changes, please try again later"); ?>
                        </div>
                    </div>
                    <div class="form-group" v-else>
                        <div class="form-info-label">
                        	<?php echo cl_translate("Provide links to your social networks so that users can see them on your profile"); ?>
                        </div>
                    </div>
                    <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    <div class="form-group no-mb d-flex">
                    	<button v-if="submitting != true" v-bind:disabled="$v.$invalid == true" type="submit" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Save changes"); ?>
	                    </button>
	                    <button v-else disabled="true" type="button" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Please wait"); ?>
	                    </button>
                    </div>
	            </form>
			</div>
		</div>
	</div>

	<script>
		"use strict";

		$(document).ready(function($) {
			Vue.use(window.vuelidate.default);
			var VueValids = window.validators;

			new Vue({
				el: "#cl-social-settings-vue-app",
				data: {
					submitting: false,
					unsuccessful_attempt: false,
					invalid_feedback_facebook: "",
					invalid_feedback_twitter: "",
					invalid_feedback_youtube: "",
					invalid_feedback_instagram: "",
					invalid_feedback_vkontakte: "",
					invalid_feedback_tiktok: "",
					invalid_feedback_linkedin: "",
					facebook: "<?php echo($me["facebook"]) ?>",
					twitter: "<?php echo($me["twitter"]) ?>",
					youtube: "<?php echo($me["youtube"]) ?>",
					instagram: "<?php echo($me["instagram"]) ?>",
					vkontakte: "<?php echo($me["vkontakte"]) ?>",
					tiktok: "<?php echo($me["tiktok"]) ?>",
					linkedin: "<?php echo($me["linkedin"]) ?>"
				},
				computed: {
					is_invalid_facebook: function() {
						if (this.$v.facebook.$error) {
							this.invalid_feedback_facebook = "<?php echo cl_translate("The facebook URL address you entered does not match the valid format."); ?>";
							return true;
						}

						else {
							this.invalid_feedback_facebook = "";
							return false;
						}
					},
					is_invalid_twitter: function() {
						if (this.$v.twitter.$error) {
							this.invalid_feedback_twitter = "<?php echo cl_translate("The twitter URL address you entered does not match the valid format."); ?>";
							return true;
						}

						else {
							this.invalid_feedback_twitter = "";
							return false;
						}
					},
					is_invalid_youtube: function() {
						if (this.$v.youtube.$error) {
							this.invalid_feedback_youtube = "<?php echo cl_translate("The youtube URL address you entered does not match the valid format."); ?>";
							return true;
						}

						else {
							this.invalid_feedback_youtube = "";
							return false;
						}
					},
					is_invalid_instagram: function() {
						if (this.$v.instagram.$error) {
							this.invalid_feedback_instagram = "<?php echo cl_translate("The instagram URL address you entered does not match the valid format."); ?>";
							return true;
						}

						else {
							this.invalid_feedback_instagram = "";
							return false;
						}
					},
					is_invalid_vkontakte: function() {
						if (this.$v.vkontakte.$error) {
							this.invalid_feedback_vkontakte = "<?php echo cl_translate("The vkontakte URL address you entered does not match the valid format."); ?>";
							return true;
						}

						else {
							this.invalid_feedback_vkontakte = "";
							return false;
						}
					},
					is_invalid_tiktok: function() {
						if (this.$v.tiktok.$error) {
							this.invalid_feedback_tiktok = "<?php echo cl_translate("The tiktok URL address you entered does not match the valid format."); ?>";
							return true;
						}

						else {
							this.invalid_feedback_tiktok = "";
							return false;
						}
					},
					is_invalid_linkedin: function() {
						if (this.$v.linkedin.$error) {
							this.invalid_feedback_linkedin = "<?php echo cl_translate("The linkedin URL address you entered does not match the valid format."); ?>";
							return true;
						}

						else {
							this.invalid_feedback_linkedin = "";
							return false;
						}
					}
				},
				validations: {
					facebook: {
						url: VueValids.url,
						max_length: VueValids.maxLength(115)
					},
					twitter: {
						url: VueValids.url,
						max_length: VueValids.maxLength(115)
					},
					youtube: {
						url: VueValids.url,
						max_length: VueValids.maxLength(115)
					},
					instagram: {
						url: VueValids.url,
						max_length: VueValids.maxLength(115)
					},
					vkontakte: {
						url: VueValids.url,
						max_length: VueValids.maxLength(115)
					},
					tiktok: {
						url: VueValids.url,
						max_length: VueValids.maxLength(115)
					},
					linkedin: {
						url: VueValids.url,
						max_length: VueValids.maxLength(115)
					}
				},
				methods: {
					submit_form: function(_self = null) {
						_self.preventDefault();

						var _app_ = this;

						$(_self.target).ajaxSubmit({
							url: "<?php echo cl_link("native_api/settings/save_social_networks"); ?>",
							type: 'POST',
							dataType: 'json',
							beforeSend: function() {
								_app_.submitting = true;
								_app_.unsuccessful_attempt = false;
							},
							success: function(data) {
								if (data.status == 200) {
									cl_bs_notify("<?php echo cl_translate("Your changes has been successfully saved!"); ?>", 5000, "success");
								}

								else {
									_app_.unsuccessful_attempt = true;
								}
							},
							complete: function() {
								_app_.submitting = false;
							}
						});
					}
				}
			});
		});
	</script>
</div>