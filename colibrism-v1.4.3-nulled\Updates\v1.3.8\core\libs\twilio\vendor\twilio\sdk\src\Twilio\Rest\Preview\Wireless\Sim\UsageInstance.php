<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Preview\Wireless\Sim;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;


/**
 * @property string|null $simSid
 * @property string|null $simUniqueName
 * @property string|null $accountSid
 * @property array|null $period
 * @property array|null $commandsUsage
 * @property array|null $commandsCosts
 * @property array|null $dataUsage
 * @property array|null $dataCosts
 * @property string|null $url
 */
class UsageInstance extends InstanceResource
{
    /**
     * Initialize the UsageInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $simSid 
     */
    public function __construct(Version $version, array $payload, string $simSid)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'simSid' => Values::array_get($payload, 'sim_sid'),
            'simUniqueName' => Values::array_get($payload, 'sim_unique_name'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'period' => Values::array_get($payload, 'period'),
            'commandsUsage' => Values::array_get($payload, 'commands_usage'),
            'commandsCosts' => Values::array_get($payload, 'commands_costs'),
            'dataUsage' => Values::array_get($payload, 'data_usage'),
            'dataCosts' => Values::array_get($payload, 'data_costs'),
            'url' => Values::array_get($payload, 'url'),
        ];

        $this->solution = ['simSid' => $simSid, ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return UsageContext Context for this UsageInstance
     */
    protected function proxy(): UsageContext
    {
        if (!$this->context) {
            $this->context = new UsageContext(
                $this->version,
                $this->solution['simSid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the UsageInstance
     *
     * @param array|Options $options Optional Arguments
     * @return UsageInstance Fetched UsageInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(array $options = []): UsageInstance
    {

        return $this->proxy()->fetch($options);
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Preview.Wireless.UsageInstance ' . \implode(' ', $context) . ']';
    }
}

