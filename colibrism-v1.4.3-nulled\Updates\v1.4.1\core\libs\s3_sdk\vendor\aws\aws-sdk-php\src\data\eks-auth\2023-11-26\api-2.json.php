<?php
// This file was auto-generated from sdk-root/src/data/eks-auth/2023-11-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-11-26', 'endpointPrefix' => 'eks-auth', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon EKS Auth', 'serviceId' => 'EKS Auth', 'signatureVersion' => 'v4', 'signingName' => 'eks-auth', 'uid' => 'eks-auth-2023-11-26', ], 'operations' => [ 'AssumeRoleForPodIdentity' => [ 'name' => 'AssumeRoleForPodIdentity', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{clusterName}/assume-role-for-pod-identity', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssumeRoleForPodIdentityRequest', ], 'output' => [ 'shape' => 'AssumeRoleForPodIdentityResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ExpiredTokenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'AssumeRoleForPodIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'token', ], 'members' => [ 'clusterName' => [ 'shape' => 'ClusterName', 'location' => 'uri', 'locationName' => 'clusterName', ], 'token' => [ 'shape' => 'JwtToken', ], ], ], 'AssumeRoleForPodIdentityResponse' => [ 'type' => 'structure', 'required' => [ 'subject', 'audience', 'podIdentityAssociation', 'assumedRoleUser', 'credentials', ], 'members' => [ 'subject' => [ 'shape' => 'Subject', ], 'audience' => [ 'shape' => 'String', ], 'podIdentityAssociation' => [ 'shape' => 'PodIdentityAssociation', ], 'assumedRoleUser' => [ 'shape' => 'AssumedRoleUser', ], 'credentials' => [ 'shape' => 'Credentials', ], ], ], 'AssumedRoleUser' => [ 'type' => 'structure', 'required' => [ 'arn', 'assumeRoleId', ], 'members' => [ 'arn' => [ 'shape' => 'String', ], 'assumeRoleId' => [ 'shape' => 'String', ], ], ], 'ClusterName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[0-9A-Za-z][A-Za-z0-9\\-_]*', ], 'Credentials' => [ 'type' => 'structure', 'required' => [ 'sessionToken', 'secretAccessKey', 'accessKeyId', 'expiration', ], 'members' => [ 'sessionToken' => [ 'shape' => 'String', ], 'secretAccessKey' => [ 'shape' => 'String', ], 'accessKeyId' => [ 'shape' => 'String', ], 'expiration' => [ 'shape' => 'Timestamp', ], ], 'sensitive' => true, ], 'ExpiredTokenException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidTokenException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'JwtToken' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[A-Za-z0-9-_=]+\\.[A-Za-z0-9-_=]+\\.[A-Za-z0-9-_=]+', 'sensitive' => true, ], 'PodIdentityAssociation' => [ 'type' => 'structure', 'required' => [ 'associationArn', 'associationId', ], 'members' => [ 'associationArn' => [ 'shape' => 'String', ], 'associationId' => [ 'shape' => 'String', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'String' => [ 'type' => 'string', ], 'Subject' => [ 'type' => 'structure', 'required' => [ 'namespace', 'serviceAccount', ], 'members' => [ 'namespace' => [ 'shape' => 'String', ], 'serviceAccount' => [ 'shape' => 'String', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], ],];
