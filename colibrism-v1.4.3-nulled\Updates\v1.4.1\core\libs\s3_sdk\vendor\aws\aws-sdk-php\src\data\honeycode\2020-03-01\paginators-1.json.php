<?php
// This file was auto-generated from sdk-root/src/data/honeycode/2020-03-01/paginators-1.json
return [ 'pagination' => [ 'ListTableColumns' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'result_key' => 'tableColumns', ], 'ListTableRows' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'rows', ], 'ListTables' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'tables', ], 'QueryTableRows' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'rows', ], ],];
