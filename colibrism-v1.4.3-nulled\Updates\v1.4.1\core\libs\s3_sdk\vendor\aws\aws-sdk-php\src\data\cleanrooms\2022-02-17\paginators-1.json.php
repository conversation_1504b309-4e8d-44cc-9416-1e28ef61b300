<?php
// This file was auto-generated from sdk-root/src/data/cleanrooms/2022-02-17/paginators-1.json
return [ 'pagination' => [ 'ListAnalysisTemplates' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'analysisTemplateSummaries', ], 'ListCollaborationAnalysisTemplates' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'collaborationAnalysisTemplateSummaries', ], 'ListCollaborations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'collaborationList', ], 'ListConfiguredTableAssociations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'configuredTableAssociationSummaries', ], 'ListConfiguredTables' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'configuredTableSummaries', ], 'ListMembers' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'memberSummaries', ], 'ListMemberships' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'membershipSummaries', ], 'ListProtectedQueries' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'protectedQueries', ], 'ListSchemas' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'schemaSummaries', ], ],];
