<div class="timeline-container" data-app="connections">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo ($cl['prof_user']['url']); ?>">
						<?php echo ($cl['prof_user']['name']); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link('/'); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="timeline-navbar">
		<?php if (not_empty($cl['prof_user']['followers'])): ?>
			<div class="timeline-navbar__item">
				<a href="<?php echo(cl_strf('%s/followers',$cl['prof_user']['url'])); ?>" data-spa="true">
					<button class="timeline-navbar__item-btn <?php if($cl['page_tab'] == 'followers') { echo 'active'; } ?>">
						<span class="btn-flex-inner">
							<?php echo cl_translate("Followers"); ?> (<?php echo cl_number($cl['prof_user']['followers']); ?>)
						</span>
					</button>
				</a>
			</div>
		<?php else: ?>
			<div class="timeline-navbar__item">
				<a href="javascript:void(0);">
					<button class="timeline-navbar__item-btn" disabled="true">
						<span class="btn-flex-inner">
							<?php echo cl_translate("Followers"); ?>
						</span>
					</button>
				</a>
			</div>
		<?php endif; ?>

		<?php if (not_empty($cl['prof_user']['following'])): ?>
			<div class="timeline-navbar__item">
				<a href="<?php echo(cl_strf('%s/following',$cl['prof_user']['url'])); ?>" data-spa="true">
					<button class="timeline-navbar__item-btn <?php if($cl['page_tab'] == 'following') { echo 'active'; } ?>">
						<span class="btn-flex-inner">
							<?php echo cl_translate("Following"); ?> (<?php echo cl_number($cl['prof_user']['following']); ?>)
						</span>
					</button>
				</a>
			</div>
		<?php else: ?>
			<div class="timeline-navbar__item">
				<a href="javascript:void(0);">
					<button class="timeline-navbar__item-btn" disabled="true">
						<span class="btn-flex-inner">
							<?php echo cl_translate("Following"); ?>
						</span>
					</button>
				</a>
			</div>
		<?php endif; ?>
		<?php if (not_empty($cl['prof_user']['owner'])): ?>
			<div class="timeline-navbar__item">
				<a href="<?php echo(cl_strf('%s/follow_requests', $cl['prof_user']['url'])); ?>" data-spa="true">
					<button class="timeline-navbar__item-btn <?php if($cl['page_tab'] == 'follow_requests') { echo 'active'; } ?>">
						<span class="btn-flex-inner">
							<?php echo cl_translate("Requests"); ?> (<span data-an="total-requests"><?php echo cl_number($cl['prof_user']['follow_requests']); ?></span>)
						</span>
					</button>
				</a>
			</div>
		<?php endif; ?>
	</div>

	<?php if (not_empty($cl["users_list"])): ?>
		<div class="timeline-users-container">
			<div class="timeline-user-ls" data-an="connectivity-list">
				<?php if (not_empty($cl["users_list"])): ?>
					<?php foreach ($cl["users_list"] as $cl['li']): ?>
						<?php echo cl_template('connections/includes/list_item'); ?>
					<?php endforeach; ?>
				<?php endif; ?>
			</div>
		</div>
		<?php if (count($cl["users_list"]) >= 25): ?>
			<div class="timeline-data-loader">
				<button class="timeline-data-loader__btn" onclick="CLFollowers.load_more(this);">
					<?php echo cl_translate("Show more"); ?>
				</button>
			</div>
		<?php endif; ?>
		<?php echo cl_template('connections/scripts/app_master_script'); ?>
	<?php else: ?>
		<?php echo cl_template('connections/includes/empty_list'); ?>
	<?php endif; ?>
</div>