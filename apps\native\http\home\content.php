<?php 
# @*************************************************************************@
# @ Software author: <PERSON><PERSON> (<PERSON>ur_TL)                               @
# @ UI/UX Designer & Web developer ;)                                       @
# @                                                                         @
# @*************************************************************************@
# @ Instagram: https://www.instagram.com/mansur_tl                          @
# @ VK: https://vk.com/mansur_tl_uiux                                       @
# @ Envato: http://codecanyon.net/user/mansur_tl                            @
# @ Behance: https://www.behance.net/mansur_tl                              @
# @ Telegram: https://t.me/mansurtl_contact                                 @
# @*************************************************************************@
# @ E-mail: <EMAIL>                                      @
# @ Website: https://www.mansurtl.com                                       @
# @*************************************************************************@
# @ ColibriSM - The Ultimate Social Network PHP Script                      @
# @ Copyright (c)  ColibriSM. All rights reserved                           @
# @*************************************************************************@

if (empty($cl["is_logged"])) {
	if ($cl["config"]["guest_page_status"] == "on") {
		cl_redirect("guest");
	}
	else{
		cl_redirect("feed");
	}
}
else {
	require_once(cl_full_path("core/apps/home/<USER>"));

	$cl["app_statics"] = array(
		"scripts" => array(
			cl_js_template("statics/js/libs/SwiperJS/swiper-bundle.min")
		)
	);

	$cl["page_title"]    = cl_translate("Homepage");
	$cl["page_desc"]     = $cl["config"]["description"];
	$cl["page_kw"]       = $cl["config"]["keywords"];
	$cl["pn"]            = "home";
	$cl["sbr"]           = true;
	$cl["sbl"]           = true;
	$cl["tl_feed"]       = cl_get_timeline_feed(30);
	$cl["tl_feed_total"] = 30;
	$cl["tl_swifts"]     = cl_timeline_swifts();
	$cl["admin_pinned_post"] = cl_get_admin_pinned_post();

	// Load follow suggestions for multiple appearances in timeline
	// Get more suggestions to distribute across 3 appearances (posts 4, 12, 20)
	$all_follow_suggestions = cl_get_follow_suggestions(30); // Get 30 suggestions

	// Prepare different sets for each appearance
	$cl["follow_suggestion"] = array();
	$cl["follow_suggestion_sets"] = array();

	if (not_empty($all_follow_suggestions)) {
		// Shuffle for randomization
		shuffle($all_follow_suggestions);

		// Create 3 sets of 5 users each, with remaining as backup
		$cl["follow_suggestion_sets"] = array(
			1 => array_slice($all_follow_suggestions, 0, 5),   // Posts 4: First 5 users
			2 => array_slice($all_follow_suggestions, 5, 5),   // Posts 12: Next 5 users
			3 => array_slice($all_follow_suggestions, 10, 5),  // Posts 20: Next 5 users
			'backup' => array_slice($all_follow_suggestions, 15) // Remaining users as backup
		);

		// Set default for backward compatibility
		$cl["follow_suggestion"] = $cl["follow_suggestion_sets"][1];
	}

	// Process follow suggestions data
	if (not_empty($cl["follow_suggestion"])) {
		foreach ($cl["follow_suggestion"] as $key => $row) {
			$udata = cl_raw_user_data($row["id"]);

			if (not_empty($udata)) {
				$cl["follow_suggestion"][$key] = array_merge($row, array(
					'name'              => cl_strf("%s %s", $udata['fname'], $udata['lname']),
					'avatar'            => cl_get_media($udata['avatar']),
					'url'               => cl_link($udata['username']),
					'username'          => $udata['username'],
					'verified'          => $udata['verified'],
					'follow_privacy'    => $udata['follow_privacy'],
					'follow_requested'  => cl_follow_requested($me['id'], $udata['id']),
					'common_follows'    => cl_get_common_follows($udata['id'])
				));
			}
		}
	}

	$cl["http_res"]      = cl_template("home/content");
}