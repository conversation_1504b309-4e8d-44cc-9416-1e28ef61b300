<?php
// This file was auto-generated from sdk-root/src/data/migration-hub-refactor-spaces/2021-10-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-10-26', 'endpointPrefix' => 'refactor-spaces', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Migration Hub Refactor Spaces', 'serviceId' => 'Migration Hub Refactor Spaces', 'signatureVersion' => 'v4', 'signingName' => 'refactor-spaces', 'uid' => 'migration-hub-refactor-spaces-2021-10-26', ], 'operations' => [ 'CreateApplication' => [ 'name' => 'CreateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateApplicationRequest', ], 'output' => [ 'shape' => 'CreateApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateEnvironment' => [ 'name' => 'CreateEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/environments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateEnvironmentRequest', ], 'output' => [ 'shape' => 'CreateEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateRoute' => [ 'name' => 'CreateRoute', 'http' => [ 'method' => 'POST', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications/{ApplicationIdentifier}/routes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateRouteRequest', ], 'output' => [ 'shape' => 'CreateRouteResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateService' => [ 'name' => 'CreateService', 'http' => [ 'method' => 'POST', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications/{ApplicationIdentifier}/services', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateServiceRequest', ], 'output' => [ 'shape' => 'CreateServiceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteApplication' => [ 'name' => 'DeleteApplication', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications/{ApplicationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteApplicationRequest', ], 'output' => [ 'shape' => 'DeleteApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteEnvironment' => [ 'name' => 'DeleteEnvironment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/environments/{EnvironmentIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEnvironmentRequest', ], 'output' => [ 'shape' => 'DeleteEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/resourcepolicy/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteRoute' => [ 'name' => 'DeleteRoute', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications/{ApplicationIdentifier}/routes/{RouteIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteRouteRequest', ], 'output' => [ 'shape' => 'DeleteRouteResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteService' => [ 'name' => 'DeleteService', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications/{ApplicationIdentifier}/services/{ServiceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteServiceRequest', ], 'output' => [ 'shape' => 'DeleteServiceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'GetApplication' => [ 'name' => 'GetApplication', 'http' => [ 'method' => 'GET', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications/{ApplicationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApplicationRequest', ], 'output' => [ 'shape' => 'GetApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetEnvironment' => [ 'name' => 'GetEnvironment', 'http' => [ 'method' => 'GET', 'requestUri' => '/environments/{EnvironmentIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEnvironmentRequest', ], 'output' => [ 'shape' => 'GetEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/resourcepolicy/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResourcePolicyRequest', ], 'output' => [ 'shape' => 'GetResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetRoute' => [ 'name' => 'GetRoute', 'http' => [ 'method' => 'GET', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications/{ApplicationIdentifier}/routes/{RouteIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRouteRequest', ], 'output' => [ 'shape' => 'GetRouteResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetService' => [ 'name' => 'GetService', 'http' => [ 'method' => 'GET', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications/{ApplicationIdentifier}/services/{ServiceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetServiceRequest', ], 'output' => [ 'shape' => 'GetServiceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListApplications' => [ 'name' => 'ListApplications', 'http' => [ 'method' => 'GET', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListApplicationsRequest', ], 'output' => [ 'shape' => 'ListApplicationsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListEnvironmentVpcs' => [ 'name' => 'ListEnvironmentVpcs', 'http' => [ 'method' => 'GET', 'requestUri' => '/environments/{EnvironmentIdentifier}/vpcs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEnvironmentVpcsRequest', ], 'output' => [ 'shape' => 'ListEnvironmentVpcsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListEnvironments' => [ 'name' => 'ListEnvironments', 'http' => [ 'method' => 'GET', 'requestUri' => '/environments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEnvironmentsRequest', ], 'output' => [ 'shape' => 'ListEnvironmentsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListRoutes' => [ 'name' => 'ListRoutes', 'http' => [ 'method' => 'GET', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications/{ApplicationIdentifier}/routes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRoutesRequest', ], 'output' => [ 'shape' => 'ListRoutesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListServices' => [ 'name' => 'ListServices', 'http' => [ 'method' => 'GET', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications/{ApplicationIdentifier}/services', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListServicesRequest', ], 'output' => [ 'shape' => 'ListServicesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/resourcepolicy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidResourcePolicyException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateRoute' => [ 'name' => 'UpdateRoute', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/environments/{EnvironmentIdentifier}/applications/{ApplicationIdentifier}/routes/{RouteIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRouteRequest', ], 'output' => [ 'shape' => 'UpdateRouteResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'AdditionalDetails' => [ 'type' => 'map', 'key' => [ 'shape' => 'AdditionalDetailsKey', ], 'value' => [ 'shape' => 'AdditionalDetailsValue', ], ], 'AdditionalDetailsKey' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'AdditionalDetailsValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ApiGatewayEndpointType' => [ 'type' => 'string', 'enum' => [ 'REGIONAL', 'PRIVATE', ], ], 'ApiGatewayId' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^[a-z0-9]{10}$', ], 'ApiGatewayProxyConfig' => [ 'type' => 'structure', 'members' => [ 'ApiGatewayId' => [ 'shape' => 'ApiGatewayId', ], 'EndpointType' => [ 'shape' => 'ApiGatewayEndpointType', ], 'NlbArn' => [ 'shape' => 'NlbArn', ], 'NlbName' => [ 'shape' => 'NlbName', ], 'ProxyUrl' => [ 'shape' => 'Uri', ], 'StageName' => [ 'shape' => 'StageName', ], 'VpcLinkId' => [ 'shape' => 'VpcLinkId', ], ], ], 'ApiGatewayProxyInput' => [ 'type' => 'structure', 'members' => [ 'EndpointType' => [ 'shape' => 'ApiGatewayEndpointType', ], 'StageName' => [ 'shape' => 'StageName', ], ], ], 'ApiGatewayProxySummary' => [ 'type' => 'structure', 'members' => [ 'ApiGatewayId' => [ 'shape' => 'ApiGatewayId', ], 'EndpointType' => [ 'shape' => 'ApiGatewayEndpointType', ], 'NlbArn' => [ 'shape' => 'NlbArn', ], 'NlbName' => [ 'shape' => 'NlbName', ], 'ProxyUrl' => [ 'shape' => 'Uri', ], 'StageName' => [ 'shape' => 'StageName', ], 'VpcLinkId' => [ 'shape' => 'VpcLinkId', ], ], ], 'ApplicationId' => [ 'type' => 'string', 'max' => 14, 'min' => 14, 'pattern' => '^app-[0-9A-Za-z]{10}$', ], 'ApplicationName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^(?!app-)[a-zA-Z0-9]+[a-zA-Z0-9-_ ]+$', ], 'ApplicationState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'UPDATING', ], ], 'ApplicationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationSummary', ], ], 'ApplicationSummary' => [ 'type' => 'structure', 'members' => [ 'ApiGatewayProxy' => [ 'shape' => 'ApiGatewayProxySummary', ], 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'CreatedByAccountId' => [ 'shape' => 'AccountId', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'Error' => [ 'shape' => 'ErrorResponse', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'ApplicationName', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'ProxyType' => [ 'shape' => 'ProxyType', ], 'State' => [ 'shape' => 'ApplicationState', ], 'Tags' => [ 'shape' => 'TagMap', ], 'VpcId' => [ 'shape' => 'VpcId', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CidrBlock' => [ 'type' => 'string', ], 'CidrBlocks' => [ 'type' => 'list', 'member' => [ 'shape' => 'CidrBlock', ], 'min' => 1, ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[\\x20-\\x7E]{1,64}$', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'EnvironmentIdentifier', 'Name', 'ProxyType', 'VpcId', ], 'members' => [ 'ApiGatewayProxy' => [ 'shape' => 'ApiGatewayProxyInput', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], 'Name' => [ 'shape' => 'ApplicationName', ], 'ProxyType' => [ 'shape' => 'ProxyType', ], 'Tags' => [ 'shape' => 'TagMap', ], 'VpcId' => [ 'shape' => 'VpcId', ], ], ], 'CreateApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'ApiGatewayProxy' => [ 'shape' => 'ApiGatewayProxyInput', ], 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'CreatedByAccountId' => [ 'shape' => 'AccountId', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'ApplicationName', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'ProxyType' => [ 'shape' => 'ProxyType', ], 'State' => [ 'shape' => 'ApplicationState', ], 'Tags' => [ 'shape' => 'TagMap', ], 'VpcId' => [ 'shape' => 'VpcId', ], ], ], 'CreateEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'NetworkFabricType', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Description' => [ 'shape' => 'Description', ], 'Name' => [ 'shape' => 'EnvironmentName', ], 'NetworkFabricType' => [ 'shape' => 'NetworkFabricType', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateEnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ResourceArn', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'EnvironmentName', ], 'NetworkFabricType' => [ 'shape' => 'NetworkFabricType', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'State' => [ 'shape' => 'EnvironmentState', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateRouteRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifier', 'EnvironmentIdentifier', 'RouteType', 'ServiceIdentifier', ], 'members' => [ 'ApplicationIdentifier' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'DefaultRoute' => [ 'shape' => 'DefaultRouteInput', ], 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], 'RouteType' => [ 'shape' => 'RouteType', ], 'ServiceIdentifier' => [ 'shape' => 'ServiceId', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UriPathRoute' => [ 'shape' => 'UriPathRouteInput', ], ], ], 'CreateRouteResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'CreatedByAccountId' => [ 'shape' => 'AccountId', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'RouteId' => [ 'shape' => 'RouteId', ], 'RouteType' => [ 'shape' => 'RouteType', ], 'ServiceId' => [ 'shape' => 'ServiceId', ], 'State' => [ 'shape' => 'RouteState', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UriPathRoute' => [ 'shape' => 'UriPathRouteInput', ], ], ], 'CreateServiceRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifier', 'EndpointType', 'EnvironmentIdentifier', 'Name', ], 'members' => [ 'ApplicationIdentifier' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Description' => [ 'shape' => 'Description', ], 'EndpointType' => [ 'shape' => 'ServiceEndpointType', ], 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], 'LambdaEndpoint' => [ 'shape' => 'LambdaEndpointInput', ], 'Name' => [ 'shape' => 'ServiceName', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UrlEndpoint' => [ 'shape' => 'UrlEndpointInput', ], 'VpcId' => [ 'shape' => 'VpcId', ], ], ], 'CreateServiceResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'CreatedByAccountId' => [ 'shape' => 'AccountId', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'EndpointType' => [ 'shape' => 'ServiceEndpointType', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'LambdaEndpoint' => [ 'shape' => 'LambdaEndpointInput', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'ServiceName', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'ServiceId' => [ 'shape' => 'ServiceId', ], 'State' => [ 'shape' => 'ServiceState', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UrlEndpoint' => [ 'shape' => 'UrlEndpointInput', ], 'VpcId' => [ 'shape' => 'VpcId', ], ], ], 'DefaultRouteInput' => [ 'type' => 'structure', 'members' => [ 'ActivationState' => [ 'shape' => 'RouteActivationState', ], ], ], 'DeleteApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifier', 'EnvironmentIdentifier', ], 'members' => [ 'ApplicationIdentifier' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], ], ], 'DeleteApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'ApplicationName', ], 'State' => [ 'shape' => 'ApplicationState', ], ], ], 'DeleteEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'EnvironmentIdentifier', ], 'members' => [ 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], ], ], 'DeleteEnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ResourceArn', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'EnvironmentName', ], 'State' => [ 'shape' => 'EnvironmentState', ], ], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ResourcePolicyIdentifier', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRouteRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifier', 'EnvironmentIdentifier', 'RouteIdentifier', ], 'members' => [ 'ApplicationIdentifier' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], 'RouteIdentifier' => [ 'shape' => 'RouteId', 'location' => 'uri', 'locationName' => 'RouteIdentifier', ], ], ], 'DeleteRouteResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'RouteId' => [ 'shape' => 'RouteId', ], 'ServiceId' => [ 'shape' => 'ServiceId', ], 'State' => [ 'shape' => 'RouteState', ], ], ], 'DeleteServiceRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifier', 'EnvironmentIdentifier', 'ServiceIdentifier', ], 'members' => [ 'ApplicationIdentifier' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], 'ServiceIdentifier' => [ 'shape' => 'ServiceId', 'location' => 'uri', 'locationName' => 'ServiceIdentifier', ], ], ], 'DeleteServiceResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'ServiceName', ], 'ServiceId' => [ 'shape' => 'ServiceId', ], 'State' => [ 'shape' => 'ServiceState', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_\\s\\.\\!\\*\\#\\@\\\']+$', ], 'Ec2TagValue' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^.*$', ], 'EnvironmentId' => [ 'type' => 'string', 'max' => 14, 'min' => 14, 'pattern' => '^env-[0-9A-Za-z]{10}$', ], 'EnvironmentName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^(?!env-)[a-zA-Z0-9]+[a-zA-Z0-9-_ ]+$', ], 'EnvironmentState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', ], ], 'EnvironmentSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentSummary', ], ], 'EnvironmentSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ResourceArn', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'Error' => [ 'shape' => 'ErrorResponse', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'EnvironmentName', ], 'NetworkFabricType' => [ 'shape' => 'NetworkFabricType', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'State' => [ 'shape' => 'EnvironmentState', ], 'Tags' => [ 'shape' => 'TagMap', ], 'TransitGatewayId' => [ 'shape' => 'TransitGatewayId', ], ], ], 'EnvironmentVpc' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'CidrBlocks' => [ 'shape' => 'CidrBlocks', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'VpcName' => [ 'shape' => 'Ec2TagValue', ], ], ], 'EnvironmentVpcs' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentVpc', ], ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'INVALID_RESOURCE_STATE', 'RESOURCE_LIMIT_EXCEEDED', 'RESOURCE_CREATION_FAILURE', 'RESOURCE_UPDATE_FAILURE', 'SERVICE_ENDPOINT_HEALTH_CHECK_FAILURE', 'RESOURCE_DELETION_FAILURE', 'RESOURCE_RETRIEVAL_FAILURE', 'RESOURCE_IN_USE', 'RESOURCE_NOT_FOUND', 'STATE_TRANSITION_FAILURE', 'REQUEST_LIMIT_EXCEEDED', 'NOT_AUTHORIZED', ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[\\p{Alnum}\\p{Punct}\\p{Blank}]*$', ], 'ErrorResourceType' => [ 'type' => 'string', 'enum' => [ 'ENVIRONMENT', 'APPLICATION', 'ROUTE', 'SERVICE', 'TRANSIT_GATEWAY', 'TRANSIT_GATEWAY_ATTACHMENT', 'API_GATEWAY', 'NLB', 'TARGET_GROUP', 'LOAD_BALANCER_LISTENER', 'VPC_LINK', 'LAMBDA', 'VPC', 'SUBNET', 'ROUTE_TABLE', 'SECURITY_GROUP', 'VPC_ENDPOINT_SERVICE_CONFIGURATION', 'RESOURCE_SHARE', 'IAM_ROLE', ], ], 'ErrorResponse' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'AdditionalDetails' => [ 'shape' => 'AdditionalDetails', ], 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'ErrorMessage', ], 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'ErrorResourceType', ], ], ], 'GetApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifier', 'EnvironmentIdentifier', ], 'members' => [ 'ApplicationIdentifier' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], ], ], 'GetApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'ApiGatewayProxy' => [ 'shape' => 'ApiGatewayProxyConfig', ], 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'CreatedByAccountId' => [ 'shape' => 'AccountId', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'Error' => [ 'shape' => 'ErrorResponse', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'ApplicationName', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'ProxyType' => [ 'shape' => 'ProxyType', ], 'State' => [ 'shape' => 'ApplicationState', ], 'Tags' => [ 'shape' => 'TagMap', ], 'VpcId' => [ 'shape' => 'VpcId', ], ], ], 'GetEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'EnvironmentIdentifier', ], 'members' => [ 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], ], ], 'GetEnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ResourceArn', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'Error' => [ 'shape' => 'ErrorResponse', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'EnvironmentName', ], 'NetworkFabricType' => [ 'shape' => 'NetworkFabricType', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'State' => [ 'shape' => 'EnvironmentState', ], 'Tags' => [ 'shape' => 'TagMap', ], 'TransitGatewayId' => [ 'shape' => 'TransitGatewayId', ], ], ], 'GetResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ResourcePolicyIdentifier', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'PolicyString', ], ], ], 'GetRouteRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifier', 'EnvironmentIdentifier', 'RouteIdentifier', ], 'members' => [ 'ApplicationIdentifier' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], 'RouteIdentifier' => [ 'shape' => 'RouteId', 'location' => 'uri', 'locationName' => 'RouteIdentifier', ], ], ], 'GetRouteResponse' => [ 'type' => 'structure', 'members' => [ 'AppendSourcePath' => [ 'shape' => 'Boolean', ], 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'CreatedByAccountId' => [ 'shape' => 'AccountId', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'Error' => [ 'shape' => 'ErrorResponse', ], 'IncludeChildPaths' => [ 'shape' => 'Boolean', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Methods' => [ 'shape' => 'HttpMethods', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'PathResourceToId' => [ 'shape' => 'PathResourceToId', ], 'RouteId' => [ 'shape' => 'RouteId', ], 'RouteType' => [ 'shape' => 'RouteType', ], 'ServiceId' => [ 'shape' => 'ServiceId', ], 'SourcePath' => [ 'shape' => 'UriPath', ], 'State' => [ 'shape' => 'RouteState', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'GetServiceRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifier', 'EnvironmentIdentifier', 'ServiceIdentifier', ], 'members' => [ 'ApplicationIdentifier' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], 'ServiceIdentifier' => [ 'shape' => 'ServiceId', 'location' => 'uri', 'locationName' => 'ServiceIdentifier', ], ], ], 'GetServiceResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'CreatedByAccountId' => [ 'shape' => 'AccountId', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'EndpointType' => [ 'shape' => 'ServiceEndpointType', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'Error' => [ 'shape' => 'ErrorResponse', ], 'LambdaEndpoint' => [ 'shape' => 'LambdaEndpointConfig', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'ServiceName', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'ServiceId' => [ 'shape' => 'ServiceId', ], 'State' => [ 'shape' => 'ServiceState', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UrlEndpoint' => [ 'shape' => 'UrlEndpointConfig', ], 'VpcId' => [ 'shape' => 'VpcId', ], ], ], 'HttpMethod' => [ 'type' => 'string', 'enum' => [ 'DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT', ], ], 'HttpMethods' => [ 'type' => 'list', 'member' => [ 'shape' => 'HttpMethod', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidResourcePolicyException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'LambdaArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?$', ], 'LambdaEndpointConfig' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'LambdaArn', ], ], ], 'LambdaEndpointInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'LambdaArn', ], ], ], 'LambdaEndpointSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'LambdaArn', ], ], ], 'ListApplicationsRequest' => [ 'type' => 'structure', 'required' => [ 'EnvironmentIdentifier', ], 'members' => [ 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListApplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationSummaryList' => [ 'shape' => 'ApplicationSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEnvironmentVpcsRequest' => [ 'type' => 'structure', 'required' => [ 'EnvironmentIdentifier', ], 'members' => [ 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEnvironmentVpcsResponse' => [ 'type' => 'structure', 'members' => [ 'EnvironmentVpcList' => [ 'shape' => 'EnvironmentVpcs', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEnvironmentsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEnvironmentsResponse' => [ 'type' => 'structure', 'members' => [ 'EnvironmentSummaryList' => [ 'shape' => 'EnvironmentSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRoutesRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifier', 'EnvironmentIdentifier', ], 'members' => [ 'ApplicationIdentifier' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListRoutesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'RouteSummaryList' => [ 'shape' => 'RouteSummaries', ], ], ], 'ListServicesRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifier', 'EnvironmentIdentifier', ], 'members' => [ 'ApplicationIdentifier' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListServicesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ServiceSummaryList' => [ 'shape' => 'ServiceSummaries', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NetworkFabricType' => [ 'type' => 'string', 'enum' => [ 'TRANSIT_GATEWAY', 'NONE', ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[a-zA-Z0-9/\\+\\=]{0,2048}$', ], 'NlbArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws:elasticloadbalancing:[a-zA-Z0-9\\-]+:\\w{12}:[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'NlbName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^(?!internal-)[a-zA-Z0-9]+[a-zA-Z0-9-_ ]+.*[^-]$', ], 'PathResourceToId' => [ 'type' => 'map', 'key' => [ 'shape' => 'PathResourceToIdKey', ], 'value' => [ 'shape' => 'PathResourceToIdValue', ], ], 'PathResourceToIdKey' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'PathResourceToIdValue' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^[a-z0-9]{10}$', ], 'PolicyString' => [ 'type' => 'string', 'max' => 300000, 'min' => 1, 'pattern' => '^.*\\S.*$', ], 'ProxyType' => [ 'type' => 'string', 'enum' => [ 'API_GATEWAY', ], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Policy', 'ResourceArn', ], 'members' => [ 'Policy' => [ 'shape' => 'PolicyString', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws:refactor-spaces:[a-zA-Z0-9\\-]+:\\w{12}:[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'ResourceIdentifier' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '(^(env|svc|pxy|rte|app)-([0-9A-Za-z]{10}$))', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourcePolicyIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws:refactor-spaces:[a-zA-Z0-9\\-]+:\\w{12}:[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'RetryAfterSeconds' => [ 'type' => 'integer', ], 'RouteActivationState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'RouteId' => [ 'type' => 'string', 'max' => 14, 'min' => 14, 'pattern' => '^rte-[0-9A-Za-z]{10}$', ], 'RouteState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'UPDATING', 'INACTIVE', ], ], 'RouteSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteSummary', ], ], 'RouteSummary' => [ 'type' => 'structure', 'members' => [ 'AppendSourcePath' => [ 'shape' => 'Boolean', ], 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'CreatedByAccountId' => [ 'shape' => 'AccountId', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'Error' => [ 'shape' => 'ErrorResponse', ], 'IncludeChildPaths' => [ 'shape' => 'Boolean', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Methods' => [ 'shape' => 'HttpMethods', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'PathResourceToId' => [ 'shape' => 'PathResourceToId', ], 'RouteId' => [ 'shape' => 'RouteId', ], 'RouteType' => [ 'shape' => 'RouteType', ], 'ServiceId' => [ 'shape' => 'ServiceId', ], 'SourcePath' => [ 'shape' => 'UriPath', ], 'State' => [ 'shape' => 'RouteState', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'RouteType' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'URI_PATH', ], ], 'ServiceEndpointType' => [ 'type' => 'string', 'enum' => [ 'LAMBDA', 'URL', ], ], 'ServiceId' => [ 'type' => 'string', 'max' => 14, 'min' => 14, 'pattern' => '^svc-[0-9A-Za-z]{10}$', ], 'ServiceName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^(?!svc-)[a-zA-Z0-9]+[a-zA-Z0-9-_ ]+$', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', 'ServiceCode', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'QuotaCode' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], 'ServiceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'ServiceState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', ], ], 'ServiceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceSummary', ], ], 'ServiceSummary' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'CreatedByAccountId' => [ 'shape' => 'AccountId', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'EndpointType' => [ 'shape' => 'ServiceEndpointType', ], 'EnvironmentId' => [ 'shape' => 'EnvironmentId', ], 'Error' => [ 'shape' => 'ErrorResponse', ], 'LambdaEndpoint' => [ 'shape' => 'LambdaEndpointSummary', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'ServiceName', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'ServiceId' => [ 'shape' => 'ServiceId', ], 'State' => [ 'shape' => 'ServiceState', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UrlEndpoint' => [ 'shape' => 'UrlEndpointSummary', ], 'VpcId' => [ 'shape' => 'VpcId', ], ], ], 'StageName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[-a-zA-Z0-9_]*$', ], 'String' => [ 'type' => 'string', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'sensitive' => true, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagMapKeyString', ], 'value' => [ 'shape' => 'TagMapValueString', ], 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'TagMapKeyString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:).+', ], 'TagMapValueString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'QuotaCode' => [ 'shape' => 'String', ], 'RetryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], 'ServiceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TransitGatewayId' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^tgw-[-a-f0-9]{17}$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRouteRequest' => [ 'type' => 'structure', 'required' => [ 'ActivationState', 'ApplicationIdentifier', 'EnvironmentIdentifier', 'RouteIdentifier', ], 'members' => [ 'ActivationState' => [ 'shape' => 'RouteActivationState', ], 'ApplicationIdentifier' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'EnvironmentIdentifier' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'EnvironmentIdentifier', ], 'RouteIdentifier' => [ 'shape' => 'RouteId', 'location' => 'uri', 'locationName' => 'RouteIdentifier', ], ], ], 'UpdateRouteResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => 'ApplicationId', ], 'Arn' => [ 'shape' => 'ResourceArn', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'RouteId' => [ 'shape' => 'RouteId', ], 'ServiceId' => [ 'shape' => 'ServiceId', ], 'State' => [ 'shape' => 'RouteState', ], ], ], 'Uri' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^https?://[-a-zA-Z0-9+\\x38@#/%?=~_|!:,.;]*[-a-zA-Z0-9+\\x38@#/%=~_|]$', ], 'UriPath' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(/([a-zA-Z0-9._:-]+|\\{[a-zA-Z0-9._:-]+\\}))+$', ], 'UriPathRouteInput' => [ 'type' => 'structure', 'required' => [ 'ActivationState', 'SourcePath', ], 'members' => [ 'ActivationState' => [ 'shape' => 'RouteActivationState', ], 'AppendSourcePath' => [ 'shape' => 'Boolean', ], 'IncludeChildPaths' => [ 'shape' => 'Boolean', ], 'Methods' => [ 'shape' => 'HttpMethods', ], 'SourcePath' => [ 'shape' => 'UriPath', ], ], ], 'UrlEndpointConfig' => [ 'type' => 'structure', 'members' => [ 'HealthUrl' => [ 'shape' => 'Uri', ], 'Url' => [ 'shape' => 'Uri', ], ], ], 'UrlEndpointInput' => [ 'type' => 'structure', 'required' => [ 'Url', ], 'members' => [ 'HealthUrl' => [ 'shape' => 'Uri', ], 'Url' => [ 'shape' => 'Uri', ], ], ], 'UrlEndpointSummary' => [ 'type' => 'structure', 'members' => [ 'HealthUrl' => [ 'shape' => 'Uri', ], 'Url' => [ 'shape' => 'Uri', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VpcId' => [ 'type' => 'string', 'max' => 21, 'min' => 12, 'pattern' => '^vpc-[-a-f0-9]{8}([-a-f0-9]{9})?$', ], 'VpcLinkId' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^[a-z0-9]{10}$', ], ],];
