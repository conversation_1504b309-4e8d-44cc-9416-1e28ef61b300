<?php
// This file was auto-generated from sdk-root/src/data/lightsail/2016-11-28/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-11-28', 'endpointPrefix' => 'lightsail', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Amazon Lightsail', 'serviceId' => 'Lightsail', 'signatureVersion' => 'v4', 'targetPrefix' => 'Lightsail_20161128', 'uid' => 'lightsail-2016-11-28', ], 'operations' => [ 'AllocateStaticIp' => [ 'name' => 'AllocateStaticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AllocateStaticIpRequest', ], 'output' => [ 'shape' => 'AllocateStaticIpResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'AttachCertificateToDistribution' => [ 'name' => 'AttachCertificateToDistribution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachCertificateToDistributionRequest', ], 'output' => [ 'shape' => 'AttachCertificateToDistributionResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'AttachDisk' => [ 'name' => 'AttachDisk', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachDiskRequest', ], 'output' => [ 'shape' => 'AttachDiskResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'AttachInstancesToLoadBalancer' => [ 'name' => 'AttachInstancesToLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachInstancesToLoadBalancerRequest', ], 'output' => [ 'shape' => 'AttachInstancesToLoadBalancerResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'AttachLoadBalancerTlsCertificate' => [ 'name' => 'AttachLoadBalancerTlsCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachLoadBalancerTlsCertificateRequest', ], 'output' => [ 'shape' => 'AttachLoadBalancerTlsCertificateResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'AttachStaticIp' => [ 'name' => 'AttachStaticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachStaticIpRequest', ], 'output' => [ 'shape' => 'AttachStaticIpResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CloseInstancePublicPorts' => [ 'name' => 'CloseInstancePublicPorts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CloseInstancePublicPortsRequest', ], 'output' => [ 'shape' => 'CloseInstancePublicPortsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CopySnapshot' => [ 'name' => 'CopySnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopySnapshotRequest', ], 'output' => [ 'shape' => 'CopySnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateBucket' => [ 'name' => 'CreateBucket', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBucketRequest', ], 'output' => [ 'shape' => 'CreateBucketResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateBucketAccessKey' => [ 'name' => 'CreateBucketAccessKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBucketAccessKeyRequest', ], 'output' => [ 'shape' => 'CreateBucketAccessKeyResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateCertificate' => [ 'name' => 'CreateCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCertificateRequest', ], 'output' => [ 'shape' => 'CreateCertificateResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateCloudFormationStack' => [ 'name' => 'CreateCloudFormationStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCloudFormationStackRequest', ], 'output' => [ 'shape' => 'CreateCloudFormationStackResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateContactMethod' => [ 'name' => 'CreateContactMethod', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateContactMethodRequest', ], 'output' => [ 'shape' => 'CreateContactMethodResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateContainerService' => [ 'name' => 'CreateContainerService', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateContainerServiceRequest', ], 'output' => [ 'shape' => 'CreateContainerServiceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateContainerServiceDeployment' => [ 'name' => 'CreateContainerServiceDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateContainerServiceDeploymentRequest', ], 'output' => [ 'shape' => 'CreateContainerServiceDeploymentResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateContainerServiceRegistryLogin' => [ 'name' => 'CreateContainerServiceRegistryLogin', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateContainerServiceRegistryLoginRequest', ], 'output' => [ 'shape' => 'CreateContainerServiceRegistryLoginResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateDisk' => [ 'name' => 'CreateDisk', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDiskRequest', ], 'output' => [ 'shape' => 'CreateDiskResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateDiskFromSnapshot' => [ 'name' => 'CreateDiskFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDiskFromSnapshotRequest', ], 'output' => [ 'shape' => 'CreateDiskFromSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateDiskSnapshot' => [ 'name' => 'CreateDiskSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDiskSnapshotRequest', ], 'output' => [ 'shape' => 'CreateDiskSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateDistribution' => [ 'name' => 'CreateDistribution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDistributionRequest', ], 'output' => [ 'shape' => 'CreateDistributionResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateDomain' => [ 'name' => 'CreateDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDomainRequest', ], 'output' => [ 'shape' => 'CreateDomainResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateDomainEntry' => [ 'name' => 'CreateDomainEntry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDomainEntryRequest', ], 'output' => [ 'shape' => 'CreateDomainEntryResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateGUISessionAccessDetails' => [ 'name' => 'CreateGUISessionAccessDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGUISessionAccessDetailsRequest', ], 'output' => [ 'shape' => 'CreateGUISessionAccessDetailsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateInstanceSnapshot' => [ 'name' => 'CreateInstanceSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInstanceSnapshotRequest', ], 'output' => [ 'shape' => 'CreateInstanceSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateInstances' => [ 'name' => 'CreateInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInstancesRequest', ], 'output' => [ 'shape' => 'CreateInstancesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateInstancesFromSnapshot' => [ 'name' => 'CreateInstancesFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInstancesFromSnapshotRequest', ], 'output' => [ 'shape' => 'CreateInstancesFromSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateKeyPair' => [ 'name' => 'CreateKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateKeyPairRequest', ], 'output' => [ 'shape' => 'CreateKeyPairResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateLoadBalancer' => [ 'name' => 'CreateLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLoadBalancerRequest', ], 'output' => [ 'shape' => 'CreateLoadBalancerResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateLoadBalancerTlsCertificate' => [ 'name' => 'CreateLoadBalancerTlsCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLoadBalancerTlsCertificateRequest', ], 'output' => [ 'shape' => 'CreateLoadBalancerTlsCertificateResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateRelationalDatabase' => [ 'name' => 'CreateRelationalDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRelationalDatabaseRequest', ], 'output' => [ 'shape' => 'CreateRelationalDatabaseResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateRelationalDatabaseFromSnapshot' => [ 'name' => 'CreateRelationalDatabaseFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRelationalDatabaseFromSnapshotRequest', ], 'output' => [ 'shape' => 'CreateRelationalDatabaseFromSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateRelationalDatabaseSnapshot' => [ 'name' => 'CreateRelationalDatabaseSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRelationalDatabaseSnapshotRequest', ], 'output' => [ 'shape' => 'CreateRelationalDatabaseSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteAlarm' => [ 'name' => 'DeleteAlarm', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAlarmRequest', ], 'output' => [ 'shape' => 'DeleteAlarmResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'UnauthenticatedException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], ], ], 'DeleteAutoSnapshot' => [ 'name' => 'DeleteAutoSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAutoSnapshotRequest', ], 'output' => [ 'shape' => 'DeleteAutoSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteBucket' => [ 'name' => 'DeleteBucket', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBucketRequest', ], 'output' => [ 'shape' => 'DeleteBucketResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteBucketAccessKey' => [ 'name' => 'DeleteBucketAccessKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBucketAccessKeyRequest', ], 'output' => [ 'shape' => 'DeleteBucketAccessKeyResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteCertificate' => [ 'name' => 'DeleteCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCertificateRequest', ], 'output' => [ 'shape' => 'DeleteCertificateResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteContactMethod' => [ 'name' => 'DeleteContactMethod', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteContactMethodRequest', ], 'output' => [ 'shape' => 'DeleteContactMethodResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'UnauthenticatedException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], ], ], 'DeleteContainerImage' => [ 'name' => 'DeleteContainerImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteContainerImageRequest', ], 'output' => [ 'shape' => 'DeleteContainerImageResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteContainerService' => [ 'name' => 'DeleteContainerService', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteContainerServiceRequest', ], 'output' => [ 'shape' => 'DeleteContainerServiceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteDisk' => [ 'name' => 'DeleteDisk', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDiskRequest', ], 'output' => [ 'shape' => 'DeleteDiskResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteDiskSnapshot' => [ 'name' => 'DeleteDiskSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDiskSnapshotRequest', ], 'output' => [ 'shape' => 'DeleteDiskSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteDistribution' => [ 'name' => 'DeleteDistribution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDistributionRequest', ], 'output' => [ 'shape' => 'DeleteDistributionResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteDomain' => [ 'name' => 'DeleteDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDomainRequest', ], 'output' => [ 'shape' => 'DeleteDomainResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteDomainEntry' => [ 'name' => 'DeleteDomainEntry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDomainEntryRequest', ], 'output' => [ 'shape' => 'DeleteDomainEntryResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteInstance' => [ 'name' => 'DeleteInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInstanceRequest', ], 'output' => [ 'shape' => 'DeleteInstanceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteInstanceSnapshot' => [ 'name' => 'DeleteInstanceSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInstanceSnapshotRequest', ], 'output' => [ 'shape' => 'DeleteInstanceSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteKeyPair' => [ 'name' => 'DeleteKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteKeyPairRequest', ], 'output' => [ 'shape' => 'DeleteKeyPairResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteKnownHostKeys' => [ 'name' => 'DeleteKnownHostKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteKnownHostKeysRequest', ], 'output' => [ 'shape' => 'DeleteKnownHostKeysResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteLoadBalancer' => [ 'name' => 'DeleteLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLoadBalancerRequest', ], 'output' => [ 'shape' => 'DeleteLoadBalancerResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteLoadBalancerTlsCertificate' => [ 'name' => 'DeleteLoadBalancerTlsCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLoadBalancerTlsCertificateRequest', ], 'output' => [ 'shape' => 'DeleteLoadBalancerTlsCertificateResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteRelationalDatabase' => [ 'name' => 'DeleteRelationalDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRelationalDatabaseRequest', ], 'output' => [ 'shape' => 'DeleteRelationalDatabaseResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteRelationalDatabaseSnapshot' => [ 'name' => 'DeleteRelationalDatabaseSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRelationalDatabaseSnapshotRequest', ], 'output' => [ 'shape' => 'DeleteRelationalDatabaseSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DetachCertificateFromDistribution' => [ 'name' => 'DetachCertificateFromDistribution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachCertificateFromDistributionRequest', ], 'output' => [ 'shape' => 'DetachCertificateFromDistributionResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DetachDisk' => [ 'name' => 'DetachDisk', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachDiskRequest', ], 'output' => [ 'shape' => 'DetachDiskResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DetachInstancesFromLoadBalancer' => [ 'name' => 'DetachInstancesFromLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachInstancesFromLoadBalancerRequest', ], 'output' => [ 'shape' => 'DetachInstancesFromLoadBalancerResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DetachStaticIp' => [ 'name' => 'DetachStaticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachStaticIpRequest', ], 'output' => [ 'shape' => 'DetachStaticIpResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DisableAddOn' => [ 'name' => 'DisableAddOn', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableAddOnRequest', ], 'output' => [ 'shape' => 'DisableAddOnResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DownloadDefaultKeyPair' => [ 'name' => 'DownloadDefaultKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DownloadDefaultKeyPairRequest', ], 'output' => [ 'shape' => 'DownloadDefaultKeyPairResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'EnableAddOn' => [ 'name' => 'EnableAddOn', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableAddOnRequest', ], 'output' => [ 'shape' => 'EnableAddOnResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'ExportSnapshot' => [ 'name' => 'ExportSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExportSnapshotRequest', ], 'output' => [ 'shape' => 'ExportSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetActiveNames' => [ 'name' => 'GetActiveNames', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetActiveNamesRequest', ], 'output' => [ 'shape' => 'GetActiveNamesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetAlarms' => [ 'name' => 'GetAlarms', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAlarmsRequest', ], 'output' => [ 'shape' => 'GetAlarmsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'UnauthenticatedException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetAutoSnapshots' => [ 'name' => 'GetAutoSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAutoSnapshotsRequest', ], 'output' => [ 'shape' => 'GetAutoSnapshotsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetBlueprints' => [ 'name' => 'GetBlueprints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBlueprintsRequest', ], 'output' => [ 'shape' => 'GetBlueprintsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetBucketAccessKeys' => [ 'name' => 'GetBucketAccessKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBucketAccessKeysRequest', ], 'output' => [ 'shape' => 'GetBucketAccessKeysResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetBucketBundles' => [ 'name' => 'GetBucketBundles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBucketBundlesRequest', ], 'output' => [ 'shape' => 'GetBucketBundlesResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetBucketMetricData' => [ 'name' => 'GetBucketMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBucketMetricDataRequest', ], 'output' => [ 'shape' => 'GetBucketMetricDataResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetBuckets' => [ 'name' => 'GetBuckets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBucketsRequest', ], 'output' => [ 'shape' => 'GetBucketsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetBundles' => [ 'name' => 'GetBundles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBundlesRequest', ], 'output' => [ 'shape' => 'GetBundlesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetCertificates' => [ 'name' => 'GetCertificates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCertificatesRequest', ], 'output' => [ 'shape' => 'GetCertificatesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetCloudFormationStackRecords' => [ 'name' => 'GetCloudFormationStackRecords', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCloudFormationStackRecordsRequest', ], 'output' => [ 'shape' => 'GetCloudFormationStackRecordsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetContactMethods' => [ 'name' => 'GetContactMethods', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetContactMethodsRequest', ], 'output' => [ 'shape' => 'GetContactMethodsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetContainerAPIMetadata' => [ 'name' => 'GetContainerAPIMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetContainerAPIMetadataRequest', ], 'output' => [ 'shape' => 'GetContainerAPIMetadataResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetContainerImages' => [ 'name' => 'GetContainerImages', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetContainerImagesRequest', ], 'output' => [ 'shape' => 'GetContainerImagesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetContainerLog' => [ 'name' => 'GetContainerLog', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetContainerLogRequest', ], 'output' => [ 'shape' => 'GetContainerLogResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetContainerServiceDeployments' => [ 'name' => 'GetContainerServiceDeployments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetContainerServiceDeploymentsRequest', ], 'output' => [ 'shape' => 'GetContainerServiceDeploymentsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetContainerServiceMetricData' => [ 'name' => 'GetContainerServiceMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetContainerServiceMetricDataRequest', ], 'output' => [ 'shape' => 'GetContainerServiceMetricDataResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetContainerServicePowers' => [ 'name' => 'GetContainerServicePowers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetContainerServicePowersRequest', ], 'output' => [ 'shape' => 'GetContainerServicePowersResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetContainerServices' => [ 'name' => 'GetContainerServices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetContainerServicesRequest', ], 'output' => [ 'shape' => 'ContainerServicesListResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetCostEstimate' => [ 'name' => 'GetCostEstimate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCostEstimateRequest', ], 'output' => [ 'shape' => 'GetCostEstimateResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDisk' => [ 'name' => 'GetDisk', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDiskRequest', ], 'output' => [ 'shape' => 'GetDiskResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDiskSnapshot' => [ 'name' => 'GetDiskSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDiskSnapshotRequest', ], 'output' => [ 'shape' => 'GetDiskSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDiskSnapshots' => [ 'name' => 'GetDiskSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDiskSnapshotsRequest', ], 'output' => [ 'shape' => 'GetDiskSnapshotsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDisks' => [ 'name' => 'GetDisks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDisksRequest', ], 'output' => [ 'shape' => 'GetDisksResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDistributionBundles' => [ 'name' => 'GetDistributionBundles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDistributionBundlesRequest', ], 'output' => [ 'shape' => 'GetDistributionBundlesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDistributionLatestCacheReset' => [ 'name' => 'GetDistributionLatestCacheReset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDistributionLatestCacheResetRequest', ], 'output' => [ 'shape' => 'GetDistributionLatestCacheResetResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDistributionMetricData' => [ 'name' => 'GetDistributionMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDistributionMetricDataRequest', ], 'output' => [ 'shape' => 'GetDistributionMetricDataResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDistributions' => [ 'name' => 'GetDistributions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDistributionsRequest', ], 'output' => [ 'shape' => 'GetDistributionsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDomain' => [ 'name' => 'GetDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDomainRequest', ], 'output' => [ 'shape' => 'GetDomainResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDomains' => [ 'name' => 'GetDomains', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDomainsRequest', ], 'output' => [ 'shape' => 'GetDomainsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetExportSnapshotRecords' => [ 'name' => 'GetExportSnapshotRecords', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetExportSnapshotRecordsRequest', ], 'output' => [ 'shape' => 'GetExportSnapshotRecordsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstance' => [ 'name' => 'GetInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceRequest', ], 'output' => [ 'shape' => 'GetInstanceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstanceAccessDetails' => [ 'name' => 'GetInstanceAccessDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceAccessDetailsRequest', ], 'output' => [ 'shape' => 'GetInstanceAccessDetailsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstanceMetricData' => [ 'name' => 'GetInstanceMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceMetricDataRequest', ], 'output' => [ 'shape' => 'GetInstanceMetricDataResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstancePortStates' => [ 'name' => 'GetInstancePortStates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstancePortStatesRequest', ], 'output' => [ 'shape' => 'GetInstancePortStatesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstanceSnapshot' => [ 'name' => 'GetInstanceSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceSnapshotRequest', ], 'output' => [ 'shape' => 'GetInstanceSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstanceSnapshots' => [ 'name' => 'GetInstanceSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceSnapshotsRequest', ], 'output' => [ 'shape' => 'GetInstanceSnapshotsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstanceState' => [ 'name' => 'GetInstanceState', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceStateRequest', ], 'output' => [ 'shape' => 'GetInstanceStateResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstances' => [ 'name' => 'GetInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstancesRequest', ], 'output' => [ 'shape' => 'GetInstancesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetKeyPair' => [ 'name' => 'GetKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetKeyPairRequest', ], 'output' => [ 'shape' => 'GetKeyPairResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetKeyPairs' => [ 'name' => 'GetKeyPairs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetKeyPairsRequest', ], 'output' => [ 'shape' => 'GetKeyPairsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetLoadBalancer' => [ 'name' => 'GetLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLoadBalancerRequest', ], 'output' => [ 'shape' => 'GetLoadBalancerResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetLoadBalancerMetricData' => [ 'name' => 'GetLoadBalancerMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLoadBalancerMetricDataRequest', ], 'output' => [ 'shape' => 'GetLoadBalancerMetricDataResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetLoadBalancerTlsCertificates' => [ 'name' => 'GetLoadBalancerTlsCertificates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLoadBalancerTlsCertificatesRequest', ], 'output' => [ 'shape' => 'GetLoadBalancerTlsCertificatesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetLoadBalancerTlsPolicies' => [ 'name' => 'GetLoadBalancerTlsPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLoadBalancerTlsPoliciesRequest', ], 'output' => [ 'shape' => 'GetLoadBalancerTlsPoliciesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetLoadBalancers' => [ 'name' => 'GetLoadBalancers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLoadBalancersRequest', ], 'output' => [ 'shape' => 'GetLoadBalancersResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetOperation' => [ 'name' => 'GetOperation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOperationRequest', ], 'output' => [ 'shape' => 'GetOperationResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetOperations' => [ 'name' => 'GetOperations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOperationsRequest', ], 'output' => [ 'shape' => 'GetOperationsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetOperationsForResource' => [ 'name' => 'GetOperationsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOperationsForResourceRequest', ], 'output' => [ 'shape' => 'GetOperationsForResourceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRegions' => [ 'name' => 'GetRegions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRegionsRequest', ], 'output' => [ 'shape' => 'GetRegionsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRelationalDatabase' => [ 'name' => 'GetRelationalDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelationalDatabaseRequest', ], 'output' => [ 'shape' => 'GetRelationalDatabaseResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRelationalDatabaseBlueprints' => [ 'name' => 'GetRelationalDatabaseBlueprints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelationalDatabaseBlueprintsRequest', ], 'output' => [ 'shape' => 'GetRelationalDatabaseBlueprintsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRelationalDatabaseBundles' => [ 'name' => 'GetRelationalDatabaseBundles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelationalDatabaseBundlesRequest', ], 'output' => [ 'shape' => 'GetRelationalDatabaseBundlesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRelationalDatabaseEvents' => [ 'name' => 'GetRelationalDatabaseEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelationalDatabaseEventsRequest', ], 'output' => [ 'shape' => 'GetRelationalDatabaseEventsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRelationalDatabaseLogEvents' => [ 'name' => 'GetRelationalDatabaseLogEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelationalDatabaseLogEventsRequest', ], 'output' => [ 'shape' => 'GetRelationalDatabaseLogEventsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRelationalDatabaseLogStreams' => [ 'name' => 'GetRelationalDatabaseLogStreams', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelationalDatabaseLogStreamsRequest', ], 'output' => [ 'shape' => 'GetRelationalDatabaseLogStreamsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRelationalDatabaseMasterUserPassword' => [ 'name' => 'GetRelationalDatabaseMasterUserPassword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelationalDatabaseMasterUserPasswordRequest', ], 'output' => [ 'shape' => 'GetRelationalDatabaseMasterUserPasswordResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRelationalDatabaseMetricData' => [ 'name' => 'GetRelationalDatabaseMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelationalDatabaseMetricDataRequest', ], 'output' => [ 'shape' => 'GetRelationalDatabaseMetricDataResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRelationalDatabaseParameters' => [ 'name' => 'GetRelationalDatabaseParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelationalDatabaseParametersRequest', ], 'output' => [ 'shape' => 'GetRelationalDatabaseParametersResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRelationalDatabaseSnapshot' => [ 'name' => 'GetRelationalDatabaseSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelationalDatabaseSnapshotRequest', ], 'output' => [ 'shape' => 'GetRelationalDatabaseSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRelationalDatabaseSnapshots' => [ 'name' => 'GetRelationalDatabaseSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelationalDatabaseSnapshotsRequest', ], 'output' => [ 'shape' => 'GetRelationalDatabaseSnapshotsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRelationalDatabases' => [ 'name' => 'GetRelationalDatabases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelationalDatabasesRequest', ], 'output' => [ 'shape' => 'GetRelationalDatabasesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetStaticIp' => [ 'name' => 'GetStaticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetStaticIpRequest', ], 'output' => [ 'shape' => 'GetStaticIpResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetStaticIps' => [ 'name' => 'GetStaticIps', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetStaticIpsRequest', ], 'output' => [ 'shape' => 'GetStaticIpsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'ImportKeyPair' => [ 'name' => 'ImportKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportKeyPairRequest', ], 'output' => [ 'shape' => 'ImportKeyPairResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'IsVpcPeered' => [ 'name' => 'IsVpcPeered', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'IsVpcPeeredRequest', ], 'output' => [ 'shape' => 'IsVpcPeeredResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'OpenInstancePublicPorts' => [ 'name' => 'OpenInstancePublicPorts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'OpenInstancePublicPortsRequest', ], 'output' => [ 'shape' => 'OpenInstancePublicPortsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'PeerVpc' => [ 'name' => 'PeerVpc', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PeerVpcRequest', ], 'output' => [ 'shape' => 'PeerVpcResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'PutAlarm' => [ 'name' => 'PutAlarm', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutAlarmRequest', ], 'output' => [ 'shape' => 'PutAlarmResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], [ 'shape' => 'NotFoundException', ], ], ], 'PutInstancePublicPorts' => [ 'name' => 'PutInstancePublicPorts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutInstancePublicPortsRequest', ], 'output' => [ 'shape' => 'PutInstancePublicPortsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'RebootInstance' => [ 'name' => 'RebootInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebootInstanceRequest', ], 'output' => [ 'shape' => 'RebootInstanceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'RebootRelationalDatabase' => [ 'name' => 'RebootRelationalDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebootRelationalDatabaseRequest', ], 'output' => [ 'shape' => 'RebootRelationalDatabaseResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'RegisterContainerImage' => [ 'name' => 'RegisterContainerImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterContainerImageRequest', ], 'output' => [ 'shape' => 'RegisterContainerImageResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'ReleaseStaticIp' => [ 'name' => 'ReleaseStaticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ReleaseStaticIpRequest', ], 'output' => [ 'shape' => 'ReleaseStaticIpResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'ResetDistributionCache' => [ 'name' => 'ResetDistributionCache', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetDistributionCacheRequest', ], 'output' => [ 'shape' => 'ResetDistributionCacheResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'SendContactMethodVerification' => [ 'name' => 'SendContactMethodVerification', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendContactMethodVerificationRequest', ], 'output' => [ 'shape' => 'SendContactMethodVerificationResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'UnauthenticatedException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], ], ], 'SetIpAddressType' => [ 'name' => 'SetIpAddressType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetIpAddressTypeRequest', ], 'output' => [ 'shape' => 'SetIpAddressTypeResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'SetResourceAccessForBucket' => [ 'name' => 'SetResourceAccessForBucket', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetResourceAccessForBucketRequest', ], 'output' => [ 'shape' => 'SetResourceAccessForBucketResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'StartGUISession' => [ 'name' => 'StartGUISession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartGUISessionRequest', ], 'output' => [ 'shape' => 'StartGUISessionResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'StartInstance' => [ 'name' => 'StartInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartInstanceRequest', ], 'output' => [ 'shape' => 'StartInstanceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'StartRelationalDatabase' => [ 'name' => 'StartRelationalDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartRelationalDatabaseRequest', ], 'output' => [ 'shape' => 'StartRelationalDatabaseResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'StopGUISession' => [ 'name' => 'StopGUISession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopGUISessionRequest', ], 'output' => [ 'shape' => 'StopGUISessionResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'StopInstance' => [ 'name' => 'StopInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopInstanceRequest', ], 'output' => [ 'shape' => 'StopInstanceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'StopRelationalDatabase' => [ 'name' => 'StopRelationalDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopRelationalDatabaseRequest', ], 'output' => [ 'shape' => 'StopRelationalDatabaseResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'TestAlarm' => [ 'name' => 'TestAlarm', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestAlarmRequest', ], 'output' => [ 'shape' => 'TestAlarmResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'UnauthenticatedException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], ], ], 'UnpeerVpc' => [ 'name' => 'UnpeerVpc', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UnpeerVpcRequest', ], 'output' => [ 'shape' => 'UnpeerVpcResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UpdateBucket' => [ 'name' => 'UpdateBucket', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateBucketRequest', ], 'output' => [ 'shape' => 'UpdateBucketResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UpdateBucketBundle' => [ 'name' => 'UpdateBucketBundle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateBucketBundleRequest', ], 'output' => [ 'shape' => 'UpdateBucketBundleResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UpdateContainerService' => [ 'name' => 'UpdateContainerService', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateContainerServiceRequest', ], 'output' => [ 'shape' => 'UpdateContainerServiceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UpdateDistribution' => [ 'name' => 'UpdateDistribution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDistributionRequest', ], 'output' => [ 'shape' => 'UpdateDistributionResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UpdateDistributionBundle' => [ 'name' => 'UpdateDistributionBundle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDistributionBundleRequest', ], 'output' => [ 'shape' => 'UpdateDistributionBundleResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UpdateDomainEntry' => [ 'name' => 'UpdateDomainEntry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDomainEntryRequest', ], 'output' => [ 'shape' => 'UpdateDomainEntryResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UpdateInstanceMetadataOptions' => [ 'name' => 'UpdateInstanceMetadataOptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateInstanceMetadataOptionsRequest', ], 'output' => [ 'shape' => 'UpdateInstanceMetadataOptionsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UpdateLoadBalancerAttribute' => [ 'name' => 'UpdateLoadBalancerAttribute', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLoadBalancerAttributeRequest', ], 'output' => [ 'shape' => 'UpdateLoadBalancerAttributeResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UpdateRelationalDatabase' => [ 'name' => 'UpdateRelationalDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRelationalDatabaseRequest', ], 'output' => [ 'shape' => 'UpdateRelationalDatabaseResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UpdateRelationalDatabaseParameters' => [ 'name' => 'UpdateRelationalDatabaseParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRelationalDatabaseParametersRequest', ], 'output' => [ 'shape' => 'UpdateRelationalDatabaseParametersResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'AccessDirection' => [ 'type' => 'string', 'enum' => [ 'inbound', 'outbound', ], ], 'AccessKey' => [ 'type' => 'structure', 'members' => [ 'accessKeyId' => [ 'shape' => 'IAMAccessKeyId', ], 'secretAccessKey' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'StatusType', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'lastUsed' => [ 'shape' => 'AccessKeyLastUsed', ], ], ], 'AccessKeyLastUsed' => [ 'type' => 'structure', 'members' => [ 'lastUsedDate' => [ 'shape' => 'IsoDate', ], 'region' => [ 'shape' => 'string', ], 'serviceName' => [ 'shape' => 'string', ], ], ], 'AccessKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessKey', ], ], 'AccessReceiverList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceReceivingAccess', ], ], 'AccessRules' => [ 'type' => 'structure', 'members' => [ 'getObject' => [ 'shape' => 'AccessType', ], 'allowPublicOverrides' => [ 'shape' => 'boolean', ], ], ], 'AccessType' => [ 'type' => 'string', 'enum' => [ 'public', 'private', ], ], 'AccountLevelBpaSync' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'AccountLevelBpaSyncStatus', ], 'lastSyncedAt' => [ 'shape' => 'IsoDate', ], 'message' => [ 'shape' => 'BPAStatusMessage', ], 'bpaImpactsLightsail' => [ 'shape' => 'boolean', ], ], ], 'AccountLevelBpaSyncStatus' => [ 'type' => 'string', 'enum' => [ 'InSync', 'Failed', 'NeverSynced', 'Defaulted', ], ], 'AccountSetupInProgressException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'AddOn' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'string', ], 'status' => [ 'shape' => 'string', ], 'snapshotTimeOfDay' => [ 'shape' => 'TimeOfDay', ], 'nextSnapshotTimeOfDay' => [ 'shape' => 'TimeOfDay', ], 'threshold' => [ 'shape' => 'string', ], 'duration' => [ 'shape' => 'string', ], ], ], 'AddOnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddOn', ], ], 'AddOnRequest' => [ 'type' => 'structure', 'required' => [ 'addOnType', ], 'members' => [ 'addOnType' => [ 'shape' => 'AddOnType', ], 'autoSnapshotAddOnRequest' => [ 'shape' => 'AutoSnapshotAddOnRequest', ], 'stopInstanceOnIdleRequest' => [ 'shape' => 'StopInstanceOnIdleRequest', ], ], ], 'AddOnRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddOnRequest', ], ], 'AddOnType' => [ 'type' => 'string', 'enum' => [ 'AutoSnapshot', 'StopInstanceOnIdle', ], ], 'Alarm' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'supportCode' => [ 'shape' => 'string', ], 'monitoredResourceInfo' => [ 'shape' => 'MonitoredResourceInfo', ], 'comparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'evaluationPeriods' => [ 'shape' => 'integer', ], 'period' => [ 'shape' => 'MetricPeriod', ], 'threshold' => [ 'shape' => 'double', ], 'datapointsToAlarm' => [ 'shape' => 'integer', ], 'treatMissingData' => [ 'shape' => 'TreatMissingData', ], 'statistic' => [ 'shape' => 'MetricStatistic', ], 'metricName' => [ 'shape' => 'MetricName', ], 'state' => [ 'shape' => 'AlarmState', ], 'unit' => [ 'shape' => 'MetricUnit', ], 'contactProtocols' => [ 'shape' => 'ContactProtocolsList', ], 'notificationTriggers' => [ 'shape' => 'NotificationTriggerList', ], 'notificationEnabled' => [ 'shape' => 'boolean', ], ], ], 'AlarmState' => [ 'type' => 'string', 'enum' => [ 'OK', 'ALARM', 'INSUFFICIENT_DATA', ], ], 'AlarmsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Alarm', ], ], 'AllocateStaticIpRequest' => [ 'type' => 'structure', 'required' => [ 'staticIpName', ], 'members' => [ 'staticIpName' => [ 'shape' => 'ResourceName', ], ], ], 'AllocateStaticIpResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'AppCategory' => [ 'type' => 'string', 'enum' => [ 'LfR', ], ], 'AppCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppCategory', ], ], 'AttachCertificateToDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'distributionName', 'certificateName', ], 'members' => [ 'distributionName' => [ 'shape' => 'ResourceName', ], 'certificateName' => [ 'shape' => 'ResourceName', ], ], ], 'AttachCertificateToDistributionResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'AttachDiskRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', 'instanceName', 'diskPath', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], 'instanceName' => [ 'shape' => 'ResourceName', ], 'diskPath' => [ 'shape' => 'NonEmptyString', ], 'autoMounting' => [ 'shape' => 'boolean', ], ], ], 'AttachDiskResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'AttachInstancesToLoadBalancerRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'instanceNames', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'instanceNames' => [ 'shape' => 'ResourceNameList', ], ], ], 'AttachInstancesToLoadBalancerResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'AttachLoadBalancerTlsCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'certificateName', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'certificateName' => [ 'shape' => 'ResourceName', ], ], ], 'AttachLoadBalancerTlsCertificateResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'AttachStaticIpRequest' => [ 'type' => 'structure', 'required' => [ 'staticIpName', 'instanceName', ], 'members' => [ 'staticIpName' => [ 'shape' => 'ResourceName', ], 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'AttachStaticIpResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'AttachedDisk' => [ 'type' => 'structure', 'members' => [ 'path' => [ 'shape' => 'string', ], 'sizeInGb' => [ 'shape' => 'integer', ], ], ], 'AttachedDiskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachedDisk', ], ], 'AttachedDiskMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceName', ], 'value' => [ 'shape' => 'DiskMapList', ], ], 'AutoMountStatus' => [ 'type' => 'string', 'enum' => [ 'Failed', 'Pending', 'Mounted', 'NotMounted', ], ], 'AutoSnapshotAddOnRequest' => [ 'type' => 'structure', 'members' => [ 'snapshotTimeOfDay' => [ 'shape' => 'TimeOfDay', ], ], ], 'AutoSnapshotDate' => [ 'type' => 'string', 'pattern' => '^[0-9]{4}-[0-9]{2}-[0-9]{2}$', ], 'AutoSnapshotDetails' => [ 'type' => 'structure', 'members' => [ 'date' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'status' => [ 'shape' => 'AutoSnapshotStatus', ], 'fromAttachedDisks' => [ 'shape' => 'AttachedDiskList', ], ], ], 'AutoSnapshotDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoSnapshotDetails', ], ], 'AutoSnapshotStatus' => [ 'type' => 'string', 'enum' => [ 'Success', 'Failed', 'InProgress', 'NotFound', ], ], 'AvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'zoneName' => [ 'shape' => 'NonEmptyString', ], 'state' => [ 'shape' => 'NonEmptyString', ], ], ], 'AvailabilityZoneList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', ], ], 'BPAStatusMessage' => [ 'type' => 'string', 'enum' => [ 'DEFAULTED_FOR_SLR_MISSING', 'SYNC_ON_HOLD', 'DEFAULTED_FOR_SLR_MISSING_ON_HOLD', 'Unknown', ], ], 'Base64' => [ 'type' => 'string', ], 'BehaviorEnum' => [ 'type' => 'string', 'enum' => [ 'dont-cache', 'cache', ], ], 'Blueprint' => [ 'type' => 'structure', 'members' => [ 'blueprintId' => [ 'shape' => 'NonEmptyString', ], 'name' => [ 'shape' => 'ResourceName', ], 'group' => [ 'shape' => 'NonEmptyString', ], 'type' => [ 'shape' => 'BlueprintType', ], 'description' => [ 'shape' => 'string', ], 'isActive' => [ 'shape' => 'boolean', ], 'minPower' => [ 'shape' => 'integer', ], 'version' => [ 'shape' => 'string', ], 'versionCode' => [ 'shape' => 'string', ], 'productUrl' => [ 'shape' => 'string', ], 'licenseUrl' => [ 'shape' => 'string', ], 'platform' => [ 'shape' => 'InstancePlatform', ], 'appCategory' => [ 'shape' => 'AppCategory', ], ], ], 'BlueprintList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Blueprint', ], ], 'BlueprintType' => [ 'type' => 'string', 'enum' => [ 'os', 'app', ], ], 'Bucket' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'NonEmptyString', ], 'accessRules' => [ 'shape' => 'AccessRules', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'bundleId' => [ 'shape' => 'NonEmptyString', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'url' => [ 'shape' => 'NonEmptyString', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'name' => [ 'shape' => 'BucketName', ], 'supportCode' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagList', ], 'objectVersioning' => [ 'shape' => 'NonEmptyString', ], 'ableToUpdateBundle' => [ 'shape' => 'boolean', ], 'readonlyAccessAccounts' => [ 'shape' => 'PartnerIdList', ], 'resourcesReceivingAccess' => [ 'shape' => 'AccessReceiverList', ], 'state' => [ 'shape' => 'BucketState', ], 'accessLogConfig' => [ 'shape' => 'BucketAccessLogConfig', ], ], ], 'BucketAccessLogConfig' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'boolean', ], 'destination' => [ 'shape' => 'BucketName', ], 'prefix' => [ 'shape' => 'BucketAccessLogPrefix', ], ], ], 'BucketAccessLogPrefix' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[\\w/!.*\')(-]+$', ], 'BucketBundle' => [ 'type' => 'structure', 'members' => [ 'bundleId' => [ 'shape' => 'NonEmptyString', ], 'name' => [ 'shape' => 'NonEmptyString', ], 'price' => [ 'shape' => 'float', ], 'storagePerMonthInGb' => [ 'shape' => 'integer', ], 'transferPerMonthInGb' => [ 'shape' => 'integer', ], 'isActive' => [ 'shape' => 'boolean', ], ], ], 'BucketBundleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BucketBundle', ], ], 'BucketList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Bucket', ], ], 'BucketMetricName' => [ 'type' => 'string', 'enum' => [ 'BucketSizeBytes', 'NumberOfObjects', ], ], 'BucketName' => [ 'type' => 'string', 'max' => 54, 'min' => 3, 'pattern' => '^[a-z0-9][a-z0-9-]{1,52}[a-z0-9]$', ], 'BucketState' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'NonEmptyString', ], 'message' => [ 'shape' => 'string', ], ], ], 'Bundle' => [ 'type' => 'structure', 'members' => [ 'price' => [ 'shape' => 'float', ], 'cpuCount' => [ 'shape' => 'integer', ], 'diskSizeInGb' => [ 'shape' => 'integer', ], 'bundleId' => [ 'shape' => 'NonEmptyString', ], 'instanceType' => [ 'shape' => 'string', ], 'isActive' => [ 'shape' => 'boolean', ], 'name' => [ 'shape' => 'string', ], 'power' => [ 'shape' => 'integer', ], 'ramSizeInGb' => [ 'shape' => 'float', ], 'transferPerMonthInGb' => [ 'shape' => 'integer', ], 'supportedPlatforms' => [ 'shape' => 'InstancePlatformList', ], 'supportedAppCategories' => [ 'shape' => 'AppCategoryList', ], ], ], 'BundleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Bundle', ], ], 'CacheBehavior' => [ 'type' => 'structure', 'members' => [ 'behavior' => [ 'shape' => 'BehaviorEnum', ], ], ], 'CacheBehaviorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheBehaviorPerPath', ], ], 'CacheBehaviorPerPath' => [ 'type' => 'structure', 'members' => [ 'path' => [ 'shape' => 'string', ], 'behavior' => [ 'shape' => 'BehaviorEnum', ], ], ], 'CacheSettings' => [ 'type' => 'structure', 'members' => [ 'defaultTTL' => [ 'shape' => 'long', ], 'minimumTTL' => [ 'shape' => 'long', ], 'maximumTTL' => [ 'shape' => 'long', ], 'allowedHTTPMethods' => [ 'shape' => 'NonEmptyString', ], 'cachedHTTPMethods' => [ 'shape' => 'NonEmptyString', ], 'forwardedCookies' => [ 'shape' => 'CookieObject', ], 'forwardedHeaders' => [ 'shape' => 'HeaderObject', ], 'forwardedQueryStrings' => [ 'shape' => 'QueryStringObject', ], ], ], 'Certificate' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], 'name' => [ 'shape' => 'CertificateName', ], 'domainName' => [ 'shape' => 'DomainName', ], 'status' => [ 'shape' => 'CertificateStatus', ], 'serialNumber' => [ 'shape' => 'SerialNumber', ], 'subjectAlternativeNames' => [ 'shape' => 'SubjectAlternativeNameList', ], 'domainValidationRecords' => [ 'shape' => 'DomainValidationRecordList', ], 'requestFailureReason' => [ 'shape' => 'RequestFailureReason', ], 'inUseResourceCount' => [ 'shape' => 'InUseResourceCount', ], 'keyAlgorithm' => [ 'shape' => 'KeyAlgorithm', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'issuedAt' => [ 'shape' => 'IsoDate', ], 'issuerCA' => [ 'shape' => 'IssuerCA', ], 'notBefore' => [ 'shape' => 'IsoDate', ], 'notAfter' => [ 'shape' => 'IsoDate', ], 'eligibleToRenew' => [ 'shape' => 'EligibleToRenew', ], 'renewalSummary' => [ 'shape' => 'RenewalSummary', ], 'revokedAt' => [ 'shape' => 'IsoDate', ], 'revocationReason' => [ 'shape' => 'RevocationReason', ], 'tags' => [ 'shape' => 'TagList', ], 'supportCode' => [ 'shape' => 'string', ], ], ], 'CertificateDomainValidationStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_VALIDATION', 'FAILED', 'SUCCESS', ], ], 'CertificateName' => [ 'type' => 'string', ], 'CertificateStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_VALIDATION', 'ISSUED', 'INACTIVE', 'EXPIRED', 'VALIDATION_TIMED_OUT', 'REVOKED', 'FAILED', ], ], 'CertificateStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CertificateStatus', ], ], 'CertificateSummary' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'NonEmptyString', ], 'certificateName' => [ 'shape' => 'CertificateName', ], 'domainName' => [ 'shape' => 'DomainName', ], 'certificateDetail' => [ 'shape' => 'Certificate', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CertificateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CertificateSummary', ], ], 'CloseInstancePublicPortsRequest' => [ 'type' => 'structure', 'required' => [ 'portInfo', 'instanceName', ], 'members' => [ 'portInfo' => [ 'shape' => 'PortInfo', ], 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'CloseInstancePublicPortsResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'CloudFormationStackRecord' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'state' => [ 'shape' => 'RecordState', ], 'sourceInfo' => [ 'shape' => 'CloudFormationStackRecordSourceInfoList', ], 'destinationInfo' => [ 'shape' => 'DestinationInfo', ], ], ], 'CloudFormationStackRecordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CloudFormationStackRecord', ], ], 'CloudFormationStackRecordSourceInfo' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'CloudFormationStackRecordSourceType', ], 'name' => [ 'shape' => 'NonEmptyString', ], 'arn' => [ 'shape' => 'NonEmptyString', ], ], ], 'CloudFormationStackRecordSourceInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CloudFormationStackRecordSourceInfo', ], ], 'CloudFormationStackRecordSourceType' => [ 'type' => 'string', 'enum' => [ 'ExportSnapshotRecord', ], ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'GreaterThanOrEqualToThreshold', 'GreaterThanThreshold', 'LessThanThreshold', 'LessThanOrEqualToThreshold', ], ], 'ContactMethod' => [ 'type' => 'structure', 'members' => [ 'contactEndpoint' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'ContactMethodStatus', ], 'protocol' => [ 'shape' => 'ContactProtocol', ], 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'supportCode' => [ 'shape' => 'string', ], ], ], 'ContactMethodStatus' => [ 'type' => 'string', 'enum' => [ 'PendingVerification', 'Valid', 'Invalid', ], ], 'ContactMethodVerificationProtocol' => [ 'type' => 'string', 'enum' => [ 'Email', ], ], 'ContactMethodsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactMethod', ], ], 'ContactProtocol' => [ 'type' => 'string', 'enum' => [ 'Email', 'SMS', ], ], 'ContactProtocolsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactProtocol', ], ], 'Container' => [ 'type' => 'structure', 'members' => [ 'image' => [ 'shape' => 'string', ], 'command' => [ 'shape' => 'StringList', ], 'environment' => [ 'shape' => 'Environment', ], 'ports' => [ 'shape' => 'PortMap', ], ], ], 'ContainerImage' => [ 'type' => 'structure', 'members' => [ 'image' => [ 'shape' => 'string', ], 'digest' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], ], ], 'ContainerImageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerImage', ], ], 'ContainerLabel' => [ 'type' => 'string', 'max' => 53, 'min' => 1, 'pattern' => '^[a-z0-9]{1,2}|[a-z0-9][a-z0-9-]+[a-z0-9]$', ], 'ContainerMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ContainerName', ], 'value' => [ 'shape' => 'Container', ], ], 'ContainerName' => [ 'type' => 'string', 'max' => 53, 'min' => 1, 'pattern' => '^[a-z0-9]{1,2}|[a-z0-9][a-z0-9-]+[a-z0-9]$', ], 'ContainerService' => [ 'type' => 'structure', 'members' => [ 'containerServiceName' => [ 'shape' => 'ContainerServiceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'tags' => [ 'shape' => 'TagList', ], 'power' => [ 'shape' => 'ContainerServicePowerName', ], 'powerId' => [ 'shape' => 'string', ], 'state' => [ 'shape' => 'ContainerServiceState', ], 'stateDetail' => [ 'shape' => 'ContainerServiceStateDetail', ], 'scale' => [ 'shape' => 'ContainerServiceScale', ], 'currentDeployment' => [ 'shape' => 'ContainerServiceDeployment', ], 'nextDeployment' => [ 'shape' => 'ContainerServiceDeployment', ], 'isDisabled' => [ 'shape' => 'boolean', ], 'principalArn' => [ 'shape' => 'string', ], 'privateDomainName' => [ 'shape' => 'string', ], 'publicDomainNames' => [ 'shape' => 'ContainerServicePublicDomains', ], 'url' => [ 'shape' => 'string', ], 'privateRegistryAccess' => [ 'shape' => 'PrivateRegistryAccess', ], ], ], 'ContainerServiceDeployment' => [ 'type' => 'structure', 'members' => [ 'version' => [ 'shape' => 'integer', ], 'state' => [ 'shape' => 'ContainerServiceDeploymentState', ], 'containers' => [ 'shape' => 'ContainerMap', ], 'publicEndpoint' => [ 'shape' => 'ContainerServiceEndpoint', ], 'createdAt' => [ 'shape' => 'IsoDate', ], ], ], 'ContainerServiceDeploymentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerServiceDeployment', ], ], 'ContainerServiceDeploymentRequest' => [ 'type' => 'structure', 'members' => [ 'containers' => [ 'shape' => 'ContainerMap', ], 'publicEndpoint' => [ 'shape' => 'EndpointRequest', ], ], ], 'ContainerServiceDeploymentState' => [ 'type' => 'string', 'enum' => [ 'ACTIVATING', 'ACTIVE', 'INACTIVE', 'FAILED', ], ], 'ContainerServiceECRImagePullerRole' => [ 'type' => 'structure', 'members' => [ 'isActive' => [ 'shape' => 'boolean', ], 'principalArn' => [ 'shape' => 'string', ], ], ], 'ContainerServiceECRImagePullerRoleRequest' => [ 'type' => 'structure', 'members' => [ 'isActive' => [ 'shape' => 'boolean', ], ], ], 'ContainerServiceEndpoint' => [ 'type' => 'structure', 'members' => [ 'containerName' => [ 'shape' => 'string', ], 'containerPort' => [ 'shape' => 'integer', ], 'healthCheck' => [ 'shape' => 'ContainerServiceHealthCheckConfig', ], ], ], 'ContainerServiceHealthCheckConfig' => [ 'type' => 'structure', 'members' => [ 'healthyThreshold' => [ 'shape' => 'integer', ], 'unhealthyThreshold' => [ 'shape' => 'integer', ], 'timeoutSeconds' => [ 'shape' => 'integer', ], 'intervalSeconds' => [ 'shape' => 'integer', ], 'path' => [ 'shape' => 'string', ], 'successCodes' => [ 'shape' => 'string', ], ], ], 'ContainerServiceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerService', ], ], 'ContainerServiceLogEvent' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'IsoDate', ], 'message' => [ 'shape' => 'string', ], ], ], 'ContainerServiceLogEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerServiceLogEvent', ], ], 'ContainerServiceMetadataEntry' => [ 'type' => 'map', 'key' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'string', ], ], 'ContainerServiceMetadataEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerServiceMetadataEntry', ], ], 'ContainerServiceMetricName' => [ 'type' => 'string', 'enum' => [ 'CPUUtilization', 'MemoryUtilization', ], ], 'ContainerServiceName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-z0-9]{1,2}|[a-z0-9][a-z0-9-]+[a-z0-9]$', ], 'ContainerServicePower' => [ 'type' => 'structure', 'members' => [ 'powerId' => [ 'shape' => 'string', ], 'price' => [ 'shape' => 'float', ], 'cpuCount' => [ 'shape' => 'float', ], 'ramSizeInGb' => [ 'shape' => 'float', ], 'name' => [ 'shape' => 'string', ], 'isActive' => [ 'shape' => 'boolean', ], ], ], 'ContainerServicePowerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerServicePower', ], ], 'ContainerServicePowerName' => [ 'type' => 'string', 'enum' => [ 'nano', 'micro', 'small', 'medium', 'large', 'xlarge', ], ], 'ContainerServiceProtocol' => [ 'type' => 'string', 'enum' => [ 'HTTP', 'HTTPS', 'TCP', 'UDP', ], ], 'ContainerServicePublicDomains' => [ 'type' => 'map', 'key' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'ContainerServicePublicDomainsList', ], ], 'ContainerServicePublicDomainsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], ], 'ContainerServiceRegistryLogin' => [ 'type' => 'structure', 'members' => [ 'username' => [ 'shape' => 'string', ], 'password' => [ 'shape' => 'string', ], 'expiresAt' => [ 'shape' => 'IsoDate', ], 'registry' => [ 'shape' => 'string', ], ], ], 'ContainerServiceScale' => [ 'type' => 'integer', 'max' => 20, 'min' => 1, ], 'ContainerServiceState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'READY', 'RUNNING', 'UPDATING', 'DELETING', 'DISABLED', 'DEPLOYING', ], ], 'ContainerServiceStateDetail' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'ContainerServiceStateDetailCode', ], 'message' => [ 'shape' => 'string', ], ], ], 'ContainerServiceStateDetailCode' => [ 'type' => 'string', 'enum' => [ 'CREATING_SYSTEM_RESOURCES', 'CREATING_NETWORK_INFRASTRUCTURE', 'PROVISIONING_CERTIFICATE', 'PROVISIONING_SERVICE', 'CREATING_DEPLOYMENT', 'EVALUATING_HEALTH_CHECK', 'ACTIVATING_DEPLOYMENT', 'CERTIFICATE_LIMIT_EXCEEDED', 'UNKNOWN_ERROR', ], ], 'ContainerServicesListResult' => [ 'type' => 'structure', 'members' => [ 'containerServices' => [ 'shape' => 'ContainerServiceList', ], ], ], 'CookieObject' => [ 'type' => 'structure', 'members' => [ 'option' => [ 'shape' => 'ForwardValues', ], 'cookiesAllowList' => [ 'shape' => 'StringList', ], ], ], 'CopySnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'targetSnapshotName', 'sourceRegion', ], 'members' => [ 'sourceSnapshotName' => [ 'shape' => 'ResourceName', ], 'sourceResourceName' => [ 'shape' => 'string', ], 'restoreDate' => [ 'shape' => 'string', ], 'useLatestRestorableAutoSnapshot' => [ 'shape' => 'boolean', ], 'targetSnapshotName' => [ 'shape' => 'ResourceName', ], 'sourceRegion' => [ 'shape' => 'RegionName', ], ], ], 'CopySnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CostEstimate' => [ 'type' => 'structure', 'members' => [ 'usageType' => [ 'shape' => 'NonEmptyString', ], 'resultsByTime' => [ 'shape' => 'EstimatesByTime', ], ], ], 'CostEstimates' => [ 'type' => 'list', 'member' => [ 'shape' => 'CostEstimate', ], ], 'CreateBucketAccessKeyRequest' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], ], ], 'CreateBucketAccessKeyResult' => [ 'type' => 'structure', 'members' => [ 'accessKey' => [ 'shape' => 'AccessKey', ], 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateBucketRequest' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'bundleId', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'bundleId' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagList', ], 'enableObjectVersioning' => [ 'shape' => 'boolean', ], ], ], 'CreateBucketResult' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'Bucket', ], 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateName', 'domainName', ], 'members' => [ 'certificateName' => [ 'shape' => 'CertificateName', ], 'domainName' => [ 'shape' => 'DomainName', ], 'subjectAlternativeNames' => [ 'shape' => 'SubjectAlternativeNameList', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateCertificateResult' => [ 'type' => 'structure', 'members' => [ 'certificate' => [ 'shape' => 'CertificateSummary', ], 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateCloudFormationStackRequest' => [ 'type' => 'structure', 'required' => [ 'instances', ], 'members' => [ 'instances' => [ 'shape' => 'InstanceEntryList', ], ], ], 'CreateCloudFormationStackResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateContactMethodRequest' => [ 'type' => 'structure', 'required' => [ 'protocol', 'contactEndpoint', ], 'members' => [ 'protocol' => [ 'shape' => 'ContactProtocol', ], 'contactEndpoint' => [ 'shape' => 'StringMax256', ], ], ], 'CreateContactMethodResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateContainerServiceDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'serviceName' => [ 'shape' => 'ContainerServiceName', ], 'containers' => [ 'shape' => 'ContainerMap', ], 'publicEndpoint' => [ 'shape' => 'EndpointRequest', ], ], ], 'CreateContainerServiceDeploymentResult' => [ 'type' => 'structure', 'members' => [ 'containerService' => [ 'shape' => 'ContainerService', ], ], ], 'CreateContainerServiceRegistryLoginRequest' => [ 'type' => 'structure', 'members' => [], ], 'CreateContainerServiceRegistryLoginResult' => [ 'type' => 'structure', 'members' => [ 'registryLogin' => [ 'shape' => 'ContainerServiceRegistryLogin', ], ], ], 'CreateContainerServiceRequest' => [ 'type' => 'structure', 'required' => [ 'serviceName', 'power', 'scale', ], 'members' => [ 'serviceName' => [ 'shape' => 'ContainerServiceName', ], 'power' => [ 'shape' => 'ContainerServicePowerName', ], 'scale' => [ 'shape' => 'ContainerServiceScale', ], 'tags' => [ 'shape' => 'TagList', ], 'publicDomainNames' => [ 'shape' => 'ContainerServicePublicDomains', ], 'deployment' => [ 'shape' => 'ContainerServiceDeploymentRequest', ], 'privateRegistryAccess' => [ 'shape' => 'PrivateRegistryAccessRequest', ], ], ], 'CreateContainerServiceResult' => [ 'type' => 'structure', 'members' => [ 'containerService' => [ 'shape' => 'ContainerService', ], ], ], 'CreateDiskFromSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', 'availabilityZone', 'sizeInGb', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], 'diskSnapshotName' => [ 'shape' => 'ResourceName', ], 'availabilityZone' => [ 'shape' => 'NonEmptyString', ], 'sizeInGb' => [ 'shape' => 'integer', ], 'tags' => [ 'shape' => 'TagList', ], 'addOns' => [ 'shape' => 'AddOnRequestList', ], 'sourceDiskName' => [ 'shape' => 'string', ], 'restoreDate' => [ 'shape' => 'string', ], 'useLatestRestorableAutoSnapshot' => [ 'shape' => 'boolean', ], ], ], 'CreateDiskFromSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateDiskRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', 'availabilityZone', 'sizeInGb', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], 'availabilityZone' => [ 'shape' => 'NonEmptyString', ], 'sizeInGb' => [ 'shape' => 'integer', ], 'tags' => [ 'shape' => 'TagList', ], 'addOns' => [ 'shape' => 'AddOnRequestList', ], ], ], 'CreateDiskResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateDiskSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'diskSnapshotName', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], 'diskSnapshotName' => [ 'shape' => 'ResourceName', ], 'instanceName' => [ 'shape' => 'ResourceName', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDiskSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'distributionName', 'origin', 'defaultCacheBehavior', 'bundleId', ], 'members' => [ 'distributionName' => [ 'shape' => 'ResourceName', ], 'origin' => [ 'shape' => 'InputOrigin', ], 'defaultCacheBehavior' => [ 'shape' => 'CacheBehavior', ], 'cacheBehaviorSettings' => [ 'shape' => 'CacheSettings', ], 'cacheBehaviors' => [ 'shape' => 'CacheBehaviorList', ], 'bundleId' => [ 'shape' => 'string', ], 'ipAddressType' => [ 'shape' => 'IpAddressType', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDistributionResult' => [ 'type' => 'structure', 'members' => [ 'distribution' => [ 'shape' => 'LightsailDistribution', ], 'operation' => [ 'shape' => 'Operation', ], ], ], 'CreateDomainEntryRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', 'domainEntry', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'domainEntry' => [ 'shape' => 'DomainEntry', ], ], ], 'CreateDomainEntryResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'CreateDomainRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDomainResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'CreateGUISessionAccessDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'resourceName', ], 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateGUISessionAccessDetailsResult' => [ 'type' => 'structure', 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], 'status' => [ 'shape' => 'Status', ], 'percentageComplete' => [ 'shape' => 'integer', ], 'failureReason' => [ 'shape' => 'string', ], 'sessions' => [ 'shape' => 'Sessions', ], ], ], 'CreateInstanceSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'instanceSnapshotName', 'instanceName', ], 'members' => [ 'instanceSnapshotName' => [ 'shape' => 'ResourceName', ], 'instanceName' => [ 'shape' => 'ResourceName', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateInstanceSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateInstancesFromSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'instanceNames', 'availabilityZone', 'bundleId', ], 'members' => [ 'instanceNames' => [ 'shape' => 'StringList', ], 'attachedDiskMapping' => [ 'shape' => 'AttachedDiskMap', ], 'availabilityZone' => [ 'shape' => 'string', ], 'instanceSnapshotName' => [ 'shape' => 'ResourceName', ], 'bundleId' => [ 'shape' => 'NonEmptyString', ], 'userData' => [ 'shape' => 'string', ], 'keyPairName' => [ 'shape' => 'ResourceName', ], 'tags' => [ 'shape' => 'TagList', ], 'addOns' => [ 'shape' => 'AddOnRequestList', ], 'ipAddressType' => [ 'shape' => 'IpAddressType', ], 'sourceInstanceName' => [ 'shape' => 'string', ], 'restoreDate' => [ 'shape' => 'string', ], 'useLatestRestorableAutoSnapshot' => [ 'shape' => 'boolean', ], ], ], 'CreateInstancesFromSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateInstancesRequest' => [ 'type' => 'structure', 'required' => [ 'instanceNames', 'availabilityZone', 'blueprintId', 'bundleId', ], 'members' => [ 'instanceNames' => [ 'shape' => 'StringList', ], 'availabilityZone' => [ 'shape' => 'string', ], 'customImageName' => [ 'shape' => 'ResourceName', 'deprecated' => true, ], 'blueprintId' => [ 'shape' => 'NonEmptyString', ], 'bundleId' => [ 'shape' => 'NonEmptyString', ], 'userData' => [ 'shape' => 'string', ], 'keyPairName' => [ 'shape' => 'ResourceName', ], 'tags' => [ 'shape' => 'TagList', ], 'addOns' => [ 'shape' => 'AddOnRequestList', ], 'ipAddressType' => [ 'shape' => 'IpAddressType', ], ], ], 'CreateInstancesResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateKeyPairRequest' => [ 'type' => 'structure', 'required' => [ 'keyPairName', ], 'members' => [ 'keyPairName' => [ 'shape' => 'ResourceName', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateKeyPairResult' => [ 'type' => 'structure', 'members' => [ 'keyPair' => [ 'shape' => 'KeyPair', ], 'publicKeyBase64' => [ 'shape' => 'Base64', ], 'privateKeyBase64' => [ 'shape' => 'Base64', ], 'operation' => [ 'shape' => 'Operation', ], ], ], 'CreateLoadBalancerRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'instancePort', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'instancePort' => [ 'shape' => 'Port', ], 'healthCheckPath' => [ 'shape' => 'string', ], 'certificateName' => [ 'shape' => 'ResourceName', ], 'certificateDomainName' => [ 'shape' => 'DomainName', ], 'certificateAlternativeNames' => [ 'shape' => 'DomainNameList', ], 'tags' => [ 'shape' => 'TagList', ], 'ipAddressType' => [ 'shape' => 'IpAddressType', ], 'tlsPolicyName' => [ 'shape' => 'string', ], ], ], 'CreateLoadBalancerResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateLoadBalancerTlsCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'certificateName', 'certificateDomainName', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'certificateName' => [ 'shape' => 'ResourceName', ], 'certificateDomainName' => [ 'shape' => 'DomainName', ], 'certificateAlternativeNames' => [ 'shape' => 'DomainNameList', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateLoadBalancerTlsCertificateResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateRelationalDatabaseFromSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'availabilityZone' => [ 'shape' => 'string', ], 'publiclyAccessible' => [ 'shape' => 'boolean', ], 'relationalDatabaseSnapshotName' => [ 'shape' => 'ResourceName', ], 'relationalDatabaseBundleId' => [ 'shape' => 'string', ], 'sourceRelationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'restoreTime' => [ 'shape' => 'IsoDate', ], 'useLatestRestorableTime' => [ 'shape' => 'boolean', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRelationalDatabaseFromSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateRelationalDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', 'relationalDatabaseBlueprintId', 'relationalDatabaseBundleId', 'masterDatabaseName', 'masterUsername', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'availabilityZone' => [ 'shape' => 'string', ], 'relationalDatabaseBlueprintId' => [ 'shape' => 'string', ], 'relationalDatabaseBundleId' => [ 'shape' => 'string', ], 'masterDatabaseName' => [ 'shape' => 'string', ], 'masterUsername' => [ 'shape' => 'string', ], 'masterUserPassword' => [ 'shape' => 'SensitiveString', ], 'preferredBackupWindow' => [ 'shape' => 'string', ], 'preferredMaintenanceWindow' => [ 'shape' => 'string', ], 'publiclyAccessible' => [ 'shape' => 'boolean', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRelationalDatabaseResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateRelationalDatabaseSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', 'relationalDatabaseSnapshotName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'relationalDatabaseSnapshotName' => [ 'shape' => 'ResourceName', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRelationalDatabaseSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'Currency' => [ 'type' => 'string', 'enum' => [ 'USD', ], ], 'DeleteAlarmRequest' => [ 'type' => 'structure', 'required' => [ 'alarmName', ], 'members' => [ 'alarmName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteAlarmResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteAutoSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'resourceName', 'date', ], 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], 'date' => [ 'shape' => 'AutoSnapshotDate', ], ], ], 'DeleteAutoSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteBucketAccessKeyRequest' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'accessKeyId', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'accessKeyId' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteBucketAccessKeyResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteBucketRequest' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'forceDelete' => [ 'shape' => 'boolean', ], ], ], 'DeleteBucketResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateName', ], 'members' => [ 'certificateName' => [ 'shape' => 'CertificateName', ], ], ], 'DeleteCertificateResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteContactMethodRequest' => [ 'type' => 'structure', 'required' => [ 'protocol', ], 'members' => [ 'protocol' => [ 'shape' => 'ContactProtocol', ], ], ], 'DeleteContactMethodResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteContainerImageRequest' => [ 'type' => 'structure', 'required' => [ 'serviceName', 'image', ], 'members' => [ 'serviceName' => [ 'shape' => 'ContainerServiceName', ], 'image' => [ 'shape' => 'string', ], ], ], 'DeleteContainerImageResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContainerServiceRequest' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'serviceName' => [ 'shape' => 'ContainerServiceName', ], ], ], 'DeleteContainerServiceResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDiskRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], 'forceDeleteAddOns' => [ 'shape' => 'boolean', ], ], ], 'DeleteDiskResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteDiskSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'diskSnapshotName', ], 'members' => [ 'diskSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteDiskSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteDistributionRequest' => [ 'type' => 'structure', 'members' => [ 'distributionName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteDistributionResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'DeleteDomainEntryRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', 'domainEntry', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'domainEntry' => [ 'shape' => 'DomainEntry', ], ], ], 'DeleteDomainEntryResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'DeleteDomainRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], ], ], 'DeleteDomainResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'DeleteInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], 'forceDeleteAddOns' => [ 'shape' => 'boolean', ], ], ], 'DeleteInstanceResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteInstanceSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'instanceSnapshotName', ], 'members' => [ 'instanceSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteInstanceSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteKeyPairRequest' => [ 'type' => 'structure', 'required' => [ 'keyPairName', ], 'members' => [ 'keyPairName' => [ 'shape' => 'ResourceName', ], 'expectedFingerprint' => [ 'shape' => 'string', ], ], ], 'DeleteKeyPairResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'DeleteKnownHostKeysRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteKnownHostKeysResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteLoadBalancerRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteLoadBalancerResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteLoadBalancerTlsCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'certificateName', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'certificateName' => [ 'shape' => 'ResourceName', ], 'force' => [ 'shape' => 'boolean', ], ], ], 'DeleteLoadBalancerTlsCertificateResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteRelationalDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'skipFinalSnapshot' => [ 'shape' => 'boolean', ], 'finalRelationalDatabaseSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteRelationalDatabaseResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteRelationalDatabaseSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseSnapshotName', ], 'members' => [ 'relationalDatabaseSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteRelationalDatabaseSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DestinationInfo' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'service' => [ 'shape' => 'NonEmptyString', ], ], ], 'DetachCertificateFromDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'distributionName', ], 'members' => [ 'distributionName' => [ 'shape' => 'ResourceName', ], ], ], 'DetachCertificateFromDistributionResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'DetachDiskRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], ], ], 'DetachDiskResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DetachInstancesFromLoadBalancerRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'instanceNames', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'instanceNames' => [ 'shape' => 'ResourceNameList', ], ], ], 'DetachInstancesFromLoadBalancerResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DetachStaticIpRequest' => [ 'type' => 'structure', 'required' => [ 'staticIpName', ], 'members' => [ 'staticIpName' => [ 'shape' => 'ResourceName', ], ], ], 'DetachStaticIpResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DisableAddOnRequest' => [ 'type' => 'structure', 'required' => [ 'addOnType', 'resourceName', ], 'members' => [ 'addOnType' => [ 'shape' => 'AddOnType', ], 'resourceName' => [ 'shape' => 'ResourceName', ], ], ], 'DisableAddOnResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'Disk' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'tags' => [ 'shape' => 'TagList', ], 'addOns' => [ 'shape' => 'AddOnList', ], 'sizeInGb' => [ 'shape' => 'integer', ], 'isSystemDisk' => [ 'shape' => 'boolean', ], 'iops' => [ 'shape' => 'integer', ], 'path' => [ 'shape' => 'string', ], 'state' => [ 'shape' => 'DiskState', ], 'attachedTo' => [ 'shape' => 'ResourceName', ], 'isAttached' => [ 'shape' => 'boolean', ], 'attachmentState' => [ 'shape' => 'string', 'deprecated' => true, ], 'gbInUse' => [ 'shape' => 'integer', 'deprecated' => true, ], 'autoMountStatus' => [ 'shape' => 'AutoMountStatus', ], ], ], 'DiskInfo' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'string', ], 'path' => [ 'shape' => 'NonEmptyString', ], 'sizeInGb' => [ 'shape' => 'integer', ], 'isSystemDisk' => [ 'shape' => 'boolean', ], ], ], 'DiskInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DiskInfo', ], ], 'DiskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Disk', ], ], 'DiskMap' => [ 'type' => 'structure', 'members' => [ 'originalDiskPath' => [ 'shape' => 'NonEmptyString', ], 'newDiskName' => [ 'shape' => 'ResourceName', ], ], ], 'DiskMapList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DiskMap', ], ], 'DiskSnapshot' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'tags' => [ 'shape' => 'TagList', ], 'sizeInGb' => [ 'shape' => 'integer', ], 'state' => [ 'shape' => 'DiskSnapshotState', ], 'progress' => [ 'shape' => 'string', ], 'fromDiskName' => [ 'shape' => 'ResourceName', ], 'fromDiskArn' => [ 'shape' => 'NonEmptyString', ], 'fromInstanceName' => [ 'shape' => 'ResourceName', ], 'fromInstanceArn' => [ 'shape' => 'NonEmptyString', ], 'isFromAutoSnapshot' => [ 'shape' => 'boolean', ], ], ], 'DiskSnapshotInfo' => [ 'type' => 'structure', 'members' => [ 'sizeInGb' => [ 'shape' => 'integer', ], ], ], 'DiskSnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DiskSnapshot', ], ], 'DiskSnapshotState' => [ 'type' => 'string', 'enum' => [ 'pending', 'completed', 'error', 'unknown', ], ], 'DiskState' => [ 'type' => 'string', 'enum' => [ 'pending', 'error', 'available', 'in-use', 'unknown', ], ], 'DistributionBundle' => [ 'type' => 'structure', 'members' => [ 'bundleId' => [ 'shape' => 'string', ], 'name' => [ 'shape' => 'string', ], 'price' => [ 'shape' => 'float', ], 'transferPerMonthInGb' => [ 'shape' => 'integer', ], 'isActive' => [ 'shape' => 'boolean', ], ], ], 'DistributionBundleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DistributionBundle', ], ], 'DistributionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LightsailDistribution', ], ], 'DistributionMetricName' => [ 'type' => 'string', 'enum' => [ 'Requests', 'BytesDownloaded', 'BytesUploaded', 'TotalErrorRate', 'Http4xxErrorRate', 'Http5xxErrorRate', ], ], 'DnsRecordCreationState' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'DnsRecordCreationStateCode', ], 'message' => [ 'shape' => 'string', ], ], ], 'DnsRecordCreationStateCode' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'STARTED', 'FAILED', ], ], 'Domain' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'tags' => [ 'shape' => 'TagList', ], 'domainEntries' => [ 'shape' => 'DomainEntryList', ], 'registeredDomainDelegationInfo' => [ 'shape' => 'RegisteredDomainDelegationInfo', ], ], ], 'DomainEntry' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'name' => [ 'shape' => 'DomainName', ], 'target' => [ 'shape' => 'string', ], 'isAlias' => [ 'shape' => 'boolean', ], 'type' => [ 'shape' => 'DomainEntryType', ], 'options' => [ 'shape' => 'DomainEntryOptions', 'deprecated' => true, ], ], ], 'DomainEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainEntry', ], ], 'DomainEntryOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'DomainEntryOptionsKeys', ], 'value' => [ 'shape' => 'string', ], ], 'DomainEntryOptionsKeys' => [ 'type' => 'string', ], 'DomainEntryType' => [ 'type' => 'string', ], 'DomainList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Domain', ], ], 'DomainName' => [ 'type' => 'string', ], 'DomainNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainName', ], ], 'DomainValidationRecord' => [ 'type' => 'structure', 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'resourceRecord' => [ 'shape' => 'ResourceRecord', ], 'dnsRecordCreationState' => [ 'shape' => 'DnsRecordCreationState', ], 'validationStatus' => [ 'shape' => 'CertificateDomainValidationStatus', ], ], ], 'DomainValidationRecordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainValidationRecord', ], ], 'DownloadDefaultKeyPairRequest' => [ 'type' => 'structure', 'members' => [], ], 'DownloadDefaultKeyPairResult' => [ 'type' => 'structure', 'members' => [ 'publicKeyBase64' => [ 'shape' => 'Base64', ], 'privateKeyBase64' => [ 'shape' => 'Base64', ], 'createdAt' => [ 'shape' => 'IsoDate', ], ], ], 'EligibleToRenew' => [ 'type' => 'string', ], 'EnableAddOnRequest' => [ 'type' => 'structure', 'required' => [ 'resourceName', 'addOnRequest', ], 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], 'addOnRequest' => [ 'shape' => 'AddOnRequest', ], ], ], 'EnableAddOnResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'EndpointRequest' => [ 'type' => 'structure', 'required' => [ 'containerName', 'containerPort', ], 'members' => [ 'containerName' => [ 'shape' => 'string', ], 'containerPort' => [ 'shape' => 'integer', ], 'healthCheck' => [ 'shape' => 'ContainerServiceHealthCheckConfig', ], ], ], 'Environment' => [ 'type' => 'map', 'key' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'string', ], ], 'EstimateByTime' => [ 'type' => 'structure', 'members' => [ 'usageCost' => [ 'shape' => 'double', ], 'pricingUnit' => [ 'shape' => 'PricingUnit', ], 'unit' => [ 'shape' => 'double', ], 'currency' => [ 'shape' => 'Currency', ], 'timePeriod' => [ 'shape' => 'TimePeriod', ], ], ], 'EstimatesByTime' => [ 'type' => 'list', 'member' => [ 'shape' => 'EstimateByTime', ], ], 'ExportSnapshotRecord' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'state' => [ 'shape' => 'RecordState', ], 'sourceInfo' => [ 'shape' => 'ExportSnapshotRecordSourceInfo', ], 'destinationInfo' => [ 'shape' => 'DestinationInfo', ], ], ], 'ExportSnapshotRecordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportSnapshotRecord', ], ], 'ExportSnapshotRecordSourceInfo' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'ExportSnapshotRecordSourceType', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'name' => [ 'shape' => 'NonEmptyString', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'fromResourceName' => [ 'shape' => 'NonEmptyString', ], 'fromResourceArn' => [ 'shape' => 'NonEmptyString', ], 'instanceSnapshotInfo' => [ 'shape' => 'InstanceSnapshotInfo', ], 'diskSnapshotInfo' => [ 'shape' => 'DiskSnapshotInfo', ], ], ], 'ExportSnapshotRecordSourceType' => [ 'type' => 'string', 'enum' => [ 'InstanceSnapshot', 'DiskSnapshot', ], ], 'ExportSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'sourceSnapshotName', ], 'members' => [ 'sourceSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'ExportSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'ForwardValues' => [ 'type' => 'string', 'enum' => [ 'none', 'allow-list', 'all', ], ], 'GetActiveNamesRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetActiveNamesResult' => [ 'type' => 'structure', 'members' => [ 'activeNames' => [ 'shape' => 'StringList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetAlarmsRequest' => [ 'type' => 'structure', 'members' => [ 'alarmName' => [ 'shape' => 'ResourceName', ], 'pageToken' => [ 'shape' => 'string', ], 'monitoredResourceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetAlarmsResult' => [ 'type' => 'structure', 'members' => [ 'alarms' => [ 'shape' => 'AlarmsList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetAutoSnapshotsRequest' => [ 'type' => 'structure', 'required' => [ 'resourceName', ], 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetAutoSnapshotsResult' => [ 'type' => 'structure', 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'autoSnapshots' => [ 'shape' => 'AutoSnapshotDetailsList', ], ], ], 'GetBlueprintsRequest' => [ 'type' => 'structure', 'members' => [ 'includeInactive' => [ 'shape' => 'boolean', ], 'pageToken' => [ 'shape' => 'string', ], 'appCategory' => [ 'shape' => 'AppCategory', ], ], ], 'GetBlueprintsResult' => [ 'type' => 'structure', 'members' => [ 'blueprints' => [ 'shape' => 'BlueprintList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetBucketAccessKeysRequest' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], ], ], 'GetBucketAccessKeysResult' => [ 'type' => 'structure', 'members' => [ 'accessKeys' => [ 'shape' => 'AccessKeyList', ], ], ], 'GetBucketBundlesRequest' => [ 'type' => 'structure', 'members' => [ 'includeInactive' => [ 'shape' => 'boolean', ], ], ], 'GetBucketBundlesResult' => [ 'type' => 'structure', 'members' => [ 'bundles' => [ 'shape' => 'BucketBundleList', ], ], ], 'GetBucketMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'metricName', 'startTime', 'endTime', 'period', 'statistics', 'unit', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'metricName' => [ 'shape' => 'BucketMetricName', ], 'startTime' => [ 'shape' => 'IsoDate', ], 'endTime' => [ 'shape' => 'IsoDate', ], 'period' => [ 'shape' => 'MetricPeriod', ], 'statistics' => [ 'shape' => 'MetricStatisticList', ], 'unit' => [ 'shape' => 'MetricUnit', ], ], ], 'GetBucketMetricDataResult' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'BucketMetricName', ], 'metricData' => [ 'shape' => 'MetricDatapointList', ], ], ], 'GetBucketsRequest' => [ 'type' => 'structure', 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'pageToken' => [ 'shape' => 'string', ], 'includeConnectedResources' => [ 'shape' => 'boolean', ], ], ], 'GetBucketsResult' => [ 'type' => 'structure', 'members' => [ 'buckets' => [ 'shape' => 'BucketList', ], 'nextPageToken' => [ 'shape' => 'string', ], 'accountLevelBpaSync' => [ 'shape' => 'AccountLevelBpaSync', ], ], ], 'GetBundlesRequest' => [ 'type' => 'structure', 'members' => [ 'includeInactive' => [ 'shape' => 'boolean', ], 'pageToken' => [ 'shape' => 'string', ], 'appCategory' => [ 'shape' => 'AppCategory', ], ], ], 'GetBundlesResult' => [ 'type' => 'structure', 'members' => [ 'bundles' => [ 'shape' => 'BundleList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetCertificatesRequest' => [ 'type' => 'structure', 'members' => [ 'certificateStatuses' => [ 'shape' => 'CertificateStatusList', ], 'includeCertificateDetails' => [ 'shape' => 'IncludeCertificateDetails', ], 'certificateName' => [ 'shape' => 'CertificateName', ], 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetCertificatesResult' => [ 'type' => 'structure', 'members' => [ 'certificates' => [ 'shape' => 'CertificateSummaryList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetCloudFormationStackRecordsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetCloudFormationStackRecordsResult' => [ 'type' => 'structure', 'members' => [ 'cloudFormationStackRecords' => [ 'shape' => 'CloudFormationStackRecordList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetContactMethodsRequest' => [ 'type' => 'structure', 'members' => [ 'protocols' => [ 'shape' => 'ContactProtocolsList', ], ], ], 'GetContactMethodsResult' => [ 'type' => 'structure', 'members' => [ 'contactMethods' => [ 'shape' => 'ContactMethodsList', ], ], ], 'GetContainerAPIMetadataRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetContainerAPIMetadataResult' => [ 'type' => 'structure', 'members' => [ 'metadata' => [ 'shape' => 'ContainerServiceMetadataEntryList', ], ], ], 'GetContainerImagesRequest' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'serviceName' => [ 'shape' => 'ContainerServiceName', ], ], ], 'GetContainerImagesResult' => [ 'type' => 'structure', 'members' => [ 'containerImages' => [ 'shape' => 'ContainerImageList', ], ], ], 'GetContainerLogRequest' => [ 'type' => 'structure', 'required' => [ 'serviceName', 'containerName', ], 'members' => [ 'serviceName' => [ 'shape' => 'ContainerServiceName', ], 'containerName' => [ 'shape' => 'string', ], 'startTime' => [ 'shape' => 'IsoDate', ], 'endTime' => [ 'shape' => 'IsoDate', ], 'filterPattern' => [ 'shape' => 'string', ], 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetContainerLogResult' => [ 'type' => 'structure', 'members' => [ 'logEvents' => [ 'shape' => 'ContainerServiceLogEventList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetContainerServiceDeploymentsRequest' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'serviceName' => [ 'shape' => 'ContainerServiceName', ], ], ], 'GetContainerServiceDeploymentsResult' => [ 'type' => 'structure', 'members' => [ 'deployments' => [ 'shape' => 'ContainerServiceDeploymentList', ], ], ], 'GetContainerServiceMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'serviceName', 'metricName', 'startTime', 'endTime', 'period', 'statistics', ], 'members' => [ 'serviceName' => [ 'shape' => 'ContainerServiceName', ], 'metricName' => [ 'shape' => 'ContainerServiceMetricName', ], 'startTime' => [ 'shape' => 'IsoDate', ], 'endTime' => [ 'shape' => 'IsoDate', ], 'period' => [ 'shape' => 'MetricPeriod', ], 'statistics' => [ 'shape' => 'MetricStatisticList', ], ], ], 'GetContainerServiceMetricDataResult' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'ContainerServiceMetricName', ], 'metricData' => [ 'shape' => 'MetricDatapointList', ], ], ], 'GetContainerServicePowersRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetContainerServicePowersResult' => [ 'type' => 'structure', 'members' => [ 'powers' => [ 'shape' => 'ContainerServicePowerList', ], ], ], 'GetContainerServicesRequest' => [ 'type' => 'structure', 'members' => [ 'serviceName' => [ 'shape' => 'ContainerServiceName', ], ], ], 'GetCostEstimateRequest' => [ 'type' => 'structure', 'required' => [ 'resourceName', 'startTime', 'endTime', ], 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], 'startTime' => [ 'shape' => 'IsoDate', ], 'endTime' => [ 'shape' => 'IsoDate', ], ], ], 'GetCostEstimateResult' => [ 'type' => 'structure', 'members' => [ 'resourcesBudgetEstimate' => [ 'shape' => 'ResourcesBudgetEstimate', ], ], ], 'GetDiskRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], ], ], 'GetDiskResult' => [ 'type' => 'structure', 'members' => [ 'disk' => [ 'shape' => 'Disk', ], ], ], 'GetDiskSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'diskSnapshotName', ], 'members' => [ 'diskSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'GetDiskSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'diskSnapshot' => [ 'shape' => 'DiskSnapshot', ], ], ], 'GetDiskSnapshotsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetDiskSnapshotsResult' => [ 'type' => 'structure', 'members' => [ 'diskSnapshots' => [ 'shape' => 'DiskSnapshotList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetDisksRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetDisksResult' => [ 'type' => 'structure', 'members' => [ 'disks' => [ 'shape' => 'DiskList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetDistributionBundlesRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetDistributionBundlesResult' => [ 'type' => 'structure', 'members' => [ 'bundles' => [ 'shape' => 'DistributionBundleList', ], ], ], 'GetDistributionLatestCacheResetRequest' => [ 'type' => 'structure', 'members' => [ 'distributionName' => [ 'shape' => 'ResourceName', ], ], ], 'GetDistributionLatestCacheResetResult' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'string', ], 'createTime' => [ 'shape' => 'IsoDate', ], ], ], 'GetDistributionMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'distributionName', 'metricName', 'startTime', 'endTime', 'period', 'unit', 'statistics', ], 'members' => [ 'distributionName' => [ 'shape' => 'ResourceName', ], 'metricName' => [ 'shape' => 'DistributionMetricName', ], 'startTime' => [ 'shape' => 'timestamp', ], 'endTime' => [ 'shape' => 'timestamp', ], 'period' => [ 'shape' => 'MetricPeriod', ], 'unit' => [ 'shape' => 'MetricUnit', ], 'statistics' => [ 'shape' => 'MetricStatisticList', ], ], ], 'GetDistributionMetricDataResult' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'DistributionMetricName', ], 'metricData' => [ 'shape' => 'MetricDatapointList', ], ], ], 'GetDistributionsRequest' => [ 'type' => 'structure', 'members' => [ 'distributionName' => [ 'shape' => 'ResourceName', ], 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetDistributionsResult' => [ 'type' => 'structure', 'members' => [ 'distributions' => [ 'shape' => 'DistributionList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetDomainRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], ], ], 'GetDomainResult' => [ 'type' => 'structure', 'members' => [ 'domain' => [ 'shape' => 'Domain', ], ], ], 'GetDomainsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetDomainsResult' => [ 'type' => 'structure', 'members' => [ 'domains' => [ 'shape' => 'DomainList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetExportSnapshotRecordsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetExportSnapshotRecordsResult' => [ 'type' => 'structure', 'members' => [ 'exportSnapshotRecords' => [ 'shape' => 'ExportSnapshotRecordList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetInstanceAccessDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], 'protocol' => [ 'shape' => 'InstanceAccessProtocol', ], ], ], 'GetInstanceAccessDetailsResult' => [ 'type' => 'structure', 'members' => [ 'accessDetails' => [ 'shape' => 'InstanceAccessDetails', ], ], ], 'GetInstanceMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', 'metricName', 'period', 'startTime', 'endTime', 'unit', 'statistics', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], 'metricName' => [ 'shape' => 'InstanceMetricName', ], 'period' => [ 'shape' => 'MetricPeriod', ], 'startTime' => [ 'shape' => 'timestamp', ], 'endTime' => [ 'shape' => 'timestamp', ], 'unit' => [ 'shape' => 'MetricUnit', ], 'statistics' => [ 'shape' => 'MetricStatisticList', ], ], ], 'GetInstanceMetricDataResult' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'InstanceMetricName', ], 'metricData' => [ 'shape' => 'MetricDatapointList', ], ], ], 'GetInstancePortStatesRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetInstancePortStatesResult' => [ 'type' => 'structure', 'members' => [ 'portStates' => [ 'shape' => 'InstancePortStateList', ], ], ], 'GetInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetInstanceResult' => [ 'type' => 'structure', 'members' => [ 'instance' => [ 'shape' => 'Instance', ], ], ], 'GetInstanceSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'instanceSnapshotName', ], 'members' => [ 'instanceSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'GetInstanceSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'instanceSnapshot' => [ 'shape' => 'InstanceSnapshot', ], ], ], 'GetInstanceSnapshotsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetInstanceSnapshotsResult' => [ 'type' => 'structure', 'members' => [ 'instanceSnapshots' => [ 'shape' => 'InstanceSnapshotList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetInstanceStateRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetInstanceStateResult' => [ 'type' => 'structure', 'members' => [ 'state' => [ 'shape' => 'InstanceState', ], ], ], 'GetInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetInstancesResult' => [ 'type' => 'structure', 'members' => [ 'instances' => [ 'shape' => 'InstanceList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetKeyPairRequest' => [ 'type' => 'structure', 'required' => [ 'keyPairName', ], 'members' => [ 'keyPairName' => [ 'shape' => 'ResourceName', ], ], ], 'GetKeyPairResult' => [ 'type' => 'structure', 'members' => [ 'keyPair' => [ 'shape' => 'KeyPair', ], ], ], 'GetKeyPairsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], 'includeDefaultKeyPair' => [ 'shape' => 'boolean', ], ], ], 'GetKeyPairsResult' => [ 'type' => 'structure', 'members' => [ 'keyPairs' => [ 'shape' => 'KeyPairList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetLoadBalancerMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'metricName', 'period', 'startTime', 'endTime', 'unit', 'statistics', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'metricName' => [ 'shape' => 'LoadBalancerMetricName', ], 'period' => [ 'shape' => 'MetricPeriod', ], 'startTime' => [ 'shape' => 'timestamp', ], 'endTime' => [ 'shape' => 'timestamp', ], 'unit' => [ 'shape' => 'MetricUnit', ], 'statistics' => [ 'shape' => 'MetricStatisticList', ], ], ], 'GetLoadBalancerMetricDataResult' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'LoadBalancerMetricName', ], 'metricData' => [ 'shape' => 'MetricDatapointList', ], ], ], 'GetLoadBalancerRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], ], ], 'GetLoadBalancerResult' => [ 'type' => 'structure', 'members' => [ 'loadBalancer' => [ 'shape' => 'LoadBalancer', ], ], ], 'GetLoadBalancerTlsCertificatesRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], ], ], 'GetLoadBalancerTlsCertificatesResult' => [ 'type' => 'structure', 'members' => [ 'tlsCertificates' => [ 'shape' => 'LoadBalancerTlsCertificateList', ], ], ], 'GetLoadBalancerTlsPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetLoadBalancerTlsPoliciesResult' => [ 'type' => 'structure', 'members' => [ 'tlsPolicies' => [ 'shape' => 'LoadBalancerTlsPolicyList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetLoadBalancersRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetLoadBalancersResult' => [ 'type' => 'structure', 'members' => [ 'loadBalancers' => [ 'shape' => 'LoadBalancerList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetOperationRequest' => [ 'type' => 'structure', 'required' => [ 'operationId', ], 'members' => [ 'operationId' => [ 'shape' => 'NonEmptyString', ], ], ], 'GetOperationResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'GetOperationsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceName', ], 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetOperationsForResourceResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], 'nextPageCount' => [ 'shape' => 'string', 'deprecated' => true, ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetOperationsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetOperationsResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetRegionsRequest' => [ 'type' => 'structure', 'members' => [ 'includeAvailabilityZones' => [ 'shape' => 'boolean', ], 'includeRelationalDatabaseAvailabilityZones' => [ 'shape' => 'boolean', ], ], ], 'GetRegionsResult' => [ 'type' => 'structure', 'members' => [ 'regions' => [ 'shape' => 'RegionList', ], ], ], 'GetRelationalDatabaseBlueprintsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetRelationalDatabaseBlueprintsResult' => [ 'type' => 'structure', 'members' => [ 'blueprints' => [ 'shape' => 'RelationalDatabaseBlueprintList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetRelationalDatabaseBundlesRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], 'includeInactive' => [ 'shape' => 'boolean', ], ], ], 'GetRelationalDatabaseBundlesResult' => [ 'type' => 'structure', 'members' => [ 'bundles' => [ 'shape' => 'RelationalDatabaseBundleList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetRelationalDatabaseEventsRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'durationInMinutes' => [ 'shape' => 'integer', ], 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetRelationalDatabaseEventsResult' => [ 'type' => 'structure', 'members' => [ 'relationalDatabaseEvents' => [ 'shape' => 'RelationalDatabaseEventList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetRelationalDatabaseLogEventsRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', 'logStreamName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'logStreamName' => [ 'shape' => 'string', ], 'startTime' => [ 'shape' => 'IsoDate', ], 'endTime' => [ 'shape' => 'IsoDate', ], 'startFromHead' => [ 'shape' => 'boolean', ], 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetRelationalDatabaseLogEventsResult' => [ 'type' => 'structure', 'members' => [ 'resourceLogEvents' => [ 'shape' => 'LogEventList', ], 'nextBackwardToken' => [ 'shape' => 'string', ], 'nextForwardToken' => [ 'shape' => 'string', ], ], ], 'GetRelationalDatabaseLogStreamsRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], ], ], 'GetRelationalDatabaseLogStreamsResult' => [ 'type' => 'structure', 'members' => [ 'logStreams' => [ 'shape' => 'StringList', ], ], ], 'GetRelationalDatabaseMasterUserPasswordRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'passwordVersion' => [ 'shape' => 'RelationalDatabasePasswordVersion', ], ], ], 'GetRelationalDatabaseMasterUserPasswordResult' => [ 'type' => 'structure', 'members' => [ 'masterUserPassword' => [ 'shape' => 'SensitiveString', ], 'createdAt' => [ 'shape' => 'IsoDate', ], ], ], 'GetRelationalDatabaseMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', 'metricName', 'period', 'startTime', 'endTime', 'unit', 'statistics', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'metricName' => [ 'shape' => 'RelationalDatabaseMetricName', ], 'period' => [ 'shape' => 'MetricPeriod', ], 'startTime' => [ 'shape' => 'IsoDate', ], 'endTime' => [ 'shape' => 'IsoDate', ], 'unit' => [ 'shape' => 'MetricUnit', ], 'statistics' => [ 'shape' => 'MetricStatisticList', ], ], ], 'GetRelationalDatabaseMetricDataResult' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'RelationalDatabaseMetricName', ], 'metricData' => [ 'shape' => 'MetricDatapointList', ], ], ], 'GetRelationalDatabaseParametersRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetRelationalDatabaseParametersResult' => [ 'type' => 'structure', 'members' => [ 'parameters' => [ 'shape' => 'RelationalDatabaseParameterList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetRelationalDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], ], ], 'GetRelationalDatabaseResult' => [ 'type' => 'structure', 'members' => [ 'relationalDatabase' => [ 'shape' => 'RelationalDatabase', ], ], ], 'GetRelationalDatabaseSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseSnapshotName', ], 'members' => [ 'relationalDatabaseSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'GetRelationalDatabaseSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'relationalDatabaseSnapshot' => [ 'shape' => 'RelationalDatabaseSnapshot', ], ], ], 'GetRelationalDatabaseSnapshotsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetRelationalDatabaseSnapshotsResult' => [ 'type' => 'structure', 'members' => [ 'relationalDatabaseSnapshots' => [ 'shape' => 'RelationalDatabaseSnapshotList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetRelationalDatabasesRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetRelationalDatabasesResult' => [ 'type' => 'structure', 'members' => [ 'relationalDatabases' => [ 'shape' => 'RelationalDatabaseList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetStaticIpRequest' => [ 'type' => 'structure', 'required' => [ 'staticIpName', ], 'members' => [ 'staticIpName' => [ 'shape' => 'ResourceName', ], ], ], 'GetStaticIpResult' => [ 'type' => 'structure', 'members' => [ 'staticIp' => [ 'shape' => 'StaticIp', ], ], ], 'GetStaticIpsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetStaticIpsResult' => [ 'type' => 'structure', 'members' => [ 'staticIps' => [ 'shape' => 'StaticIpList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'HeaderEnum' => [ 'type' => 'string', 'enum' => [ 'Accept', 'Accept-Charset', 'Accept-Datetime', 'Accept-Encoding', 'Accept-Language', 'Authorization', 'CloudFront-Forwarded-Proto', 'CloudFront-Is-Desktop-Viewer', 'CloudFront-Is-Mobile-Viewer', 'CloudFront-Is-SmartTV-Viewer', 'CloudFront-Is-Tablet-Viewer', 'CloudFront-Viewer-Country', 'Host', 'Origin', 'Referer', ], ], 'HeaderForwardList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HeaderEnum', ], ], 'HeaderObject' => [ 'type' => 'structure', 'members' => [ 'option' => [ 'shape' => 'ForwardValues', ], 'headersAllowList' => [ 'shape' => 'HeaderForwardList', ], ], ], 'HostKeyAttributes' => [ 'type' => 'structure', 'members' => [ 'algorithm' => [ 'shape' => 'string', ], 'publicKey' => [ 'shape' => 'string', ], 'witnessedAt' => [ 'shape' => 'IsoDate', ], 'fingerprintSHA1' => [ 'shape' => 'string', ], 'fingerprintSHA256' => [ 'shape' => 'string', ], 'notValidBefore' => [ 'shape' => 'IsoDate', ], 'notValidAfter' => [ 'shape' => 'IsoDate', ], ], ], 'HostKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HostKeyAttributes', ], ], 'HttpEndpoint' => [ 'type' => 'string', 'enum' => [ 'disabled', 'enabled', ], ], 'HttpProtocolIpv6' => [ 'type' => 'string', 'enum' => [ 'disabled', 'enabled', ], ], 'HttpTokens' => [ 'type' => 'string', 'enum' => [ 'optional', 'required', ], ], 'IAMAccessKeyId' => [ 'type' => 'string', 'max' => 20, 'min' => 20, 'pattern' => '^[A-Z0-9]{20}$', 'sensitive' => true, ], 'ImportKeyPairRequest' => [ 'type' => 'structure', 'required' => [ 'keyPairName', 'publicKeyBase64', ], 'members' => [ 'keyPairName' => [ 'shape' => 'ResourceName', ], 'publicKeyBase64' => [ 'shape' => 'Base64', ], ], ], 'ImportKeyPairResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'InUseResourceCount' => [ 'type' => 'integer', ], 'IncludeCertificateDetails' => [ 'type' => 'boolean', ], 'InputOrigin' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'regionName' => [ 'shape' => 'RegionName', ], 'protocolPolicy' => [ 'shape' => 'OriginProtocolPolicyEnum', ], ], ], 'Instance' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'tags' => [ 'shape' => 'TagList', ], 'blueprintId' => [ 'shape' => 'NonEmptyString', ], 'blueprintName' => [ 'shape' => 'NonEmptyString', ], 'bundleId' => [ 'shape' => 'NonEmptyString', ], 'addOns' => [ 'shape' => 'AddOnList', ], 'isStaticIp' => [ 'shape' => 'boolean', ], 'privateIpAddress' => [ 'shape' => 'IpAddress', ], 'publicIpAddress' => [ 'shape' => 'IpAddress', ], 'ipv6Addresses' => [ 'shape' => 'Ipv6AddressList', ], 'ipAddressType' => [ 'shape' => 'IpAddressType', ], 'hardware' => [ 'shape' => 'InstanceHardware', ], 'networking' => [ 'shape' => 'InstanceNetworking', ], 'state' => [ 'shape' => 'InstanceState', ], 'username' => [ 'shape' => 'NonEmptyString', ], 'sshKeyName' => [ 'shape' => 'ResourceName', ], 'metadataOptions' => [ 'shape' => 'InstanceMetadataOptions', ], ], ], 'InstanceAccessDetails' => [ 'type' => 'structure', 'members' => [ 'certKey' => [ 'shape' => 'string', ], 'expiresAt' => [ 'shape' => 'IsoDate', ], 'ipAddress' => [ 'shape' => 'IpAddress', ], 'password' => [ 'shape' => 'string', ], 'passwordData' => [ 'shape' => 'PasswordData', ], 'privateKey' => [ 'shape' => 'string', ], 'protocol' => [ 'shape' => 'InstanceAccessProtocol', ], 'instanceName' => [ 'shape' => 'ResourceName', ], 'username' => [ 'shape' => 'string', ], 'hostKeys' => [ 'shape' => 'HostKeysList', ], ], ], 'InstanceAccessProtocol' => [ 'type' => 'string', 'enum' => [ 'ssh', 'rdp', ], ], 'InstanceEntry' => [ 'type' => 'structure', 'required' => [ 'sourceName', 'instanceType', 'portInfoSource', 'availabilityZone', ], 'members' => [ 'sourceName' => [ 'shape' => 'ResourceName', ], 'instanceType' => [ 'shape' => 'NonEmptyString', ], 'portInfoSource' => [ 'shape' => 'PortInfoSourceType', ], 'userData' => [ 'shape' => 'string', ], 'availabilityZone' => [ 'shape' => 'string', ], ], ], 'InstanceEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceEntry', ], ], 'InstanceHardware' => [ 'type' => 'structure', 'members' => [ 'cpuCount' => [ 'shape' => 'integer', ], 'disks' => [ 'shape' => 'DiskList', ], 'ramSizeInGb' => [ 'shape' => 'float', ], ], ], 'InstanceHealthReason' => [ 'type' => 'string', 'enum' => [ 'Lb.RegistrationInProgress', 'Lb.InitialHealthChecking', 'Lb.InternalError', 'Instance.ResponseCodeMismatch', 'Instance.Timeout', 'Instance.FailedHealthChecks', 'Instance.NotRegistered', 'Instance.NotInUse', 'Instance.DeregistrationInProgress', 'Instance.InvalidState', 'Instance.IpUnusable', ], ], 'InstanceHealthState' => [ 'type' => 'string', 'enum' => [ 'initial', 'healthy', 'unhealthy', 'unused', 'draining', 'unavailable', ], ], 'InstanceHealthSummary' => [ 'type' => 'structure', 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], 'instanceHealth' => [ 'shape' => 'InstanceHealthState', ], 'instanceHealthReason' => [ 'shape' => 'InstanceHealthReason', ], ], ], 'InstanceHealthSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceHealthSummary', ], ], 'InstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Instance', ], ], 'InstanceMetadataOptions' => [ 'type' => 'structure', 'members' => [ 'state' => [ 'shape' => 'InstanceMetadataState', ], 'httpTokens' => [ 'shape' => 'HttpTokens', ], 'httpEndpoint' => [ 'shape' => 'HttpEndpoint', ], 'httpPutResponseHopLimit' => [ 'shape' => 'integer', ], 'httpProtocolIpv6' => [ 'shape' => 'HttpProtocolIpv6', ], ], ], 'InstanceMetadataState' => [ 'type' => 'string', 'enum' => [ 'pending', 'applied', ], ], 'InstanceMetricName' => [ 'type' => 'string', 'enum' => [ 'CPUUtilization', 'NetworkIn', 'NetworkOut', 'StatusCheckFailed', 'StatusCheckFailed_Instance', 'StatusCheckFailed_System', 'BurstCapacityTime', 'BurstCapacityPercentage', 'MetadataNoToken', ], ], 'InstanceNetworking' => [ 'type' => 'structure', 'members' => [ 'monthlyTransfer' => [ 'shape' => 'MonthlyTransfer', ], 'ports' => [ 'shape' => 'InstancePortInfoList', ], ], ], 'InstancePlatform' => [ 'type' => 'string', 'enum' => [ 'LINUX_UNIX', 'WINDOWS', ], ], 'InstancePlatformList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstancePlatform', ], ], 'InstancePortInfo' => [ 'type' => 'structure', 'members' => [ 'fromPort' => [ 'shape' => 'Port', ], 'toPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'NetworkProtocol', ], 'accessFrom' => [ 'shape' => 'string', ], 'accessType' => [ 'shape' => 'PortAccessType', ], 'commonName' => [ 'shape' => 'string', ], 'accessDirection' => [ 'shape' => 'AccessDirection', ], 'cidrs' => [ 'shape' => 'StringList', ], 'ipv6Cidrs' => [ 'shape' => 'StringList', ], 'cidrListAliases' => [ 'shape' => 'StringList', ], ], ], 'InstancePortInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstancePortInfo', ], ], 'InstancePortState' => [ 'type' => 'structure', 'members' => [ 'fromPort' => [ 'shape' => 'Port', ], 'toPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'NetworkProtocol', ], 'state' => [ 'shape' => 'PortState', ], 'cidrs' => [ 'shape' => 'StringList', ], 'ipv6Cidrs' => [ 'shape' => 'StringList', ], 'cidrListAliases' => [ 'shape' => 'StringList', ], ], ], 'InstancePortStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstancePortState', ], ], 'InstanceSnapshot' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'tags' => [ 'shape' => 'TagList', ], 'state' => [ 'shape' => 'InstanceSnapshotState', ], 'progress' => [ 'shape' => 'string', ], 'fromAttachedDisks' => [ 'shape' => 'DiskList', ], 'fromInstanceName' => [ 'shape' => 'ResourceName', ], 'fromInstanceArn' => [ 'shape' => 'NonEmptyString', ], 'fromBlueprintId' => [ 'shape' => 'string', ], 'fromBundleId' => [ 'shape' => 'string', ], 'isFromAutoSnapshot' => [ 'shape' => 'boolean', ], 'sizeInGb' => [ 'shape' => 'integer', ], ], ], 'InstanceSnapshotInfo' => [ 'type' => 'structure', 'members' => [ 'fromBundleId' => [ 'shape' => 'NonEmptyString', ], 'fromBlueprintId' => [ 'shape' => 'NonEmptyString', ], 'fromDiskInfo' => [ 'shape' => 'DiskInfoList', ], ], ], 'InstanceSnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceSnapshot', ], ], 'InstanceSnapshotState' => [ 'type' => 'string', 'enum' => [ 'pending', 'error', 'available', ], ], 'InstanceState' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'integer', ], 'name' => [ 'shape' => 'string', ], ], ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'IpAddress' => [ 'type' => 'string', 'pattern' => '([0-9]{1,3}\\.){3}[0-9]{1,3}', ], 'IpAddressType' => [ 'type' => 'string', 'enum' => [ 'dualstack', 'ipv4', ], ], 'Ipv6Address' => [ 'type' => 'string', 'pattern' => '([A-F0-9]{1,4}:){7}[A-F0-9]{1,4}', ], 'Ipv6AddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ipv6Address', ], ], 'IsVpcPeeredRequest' => [ 'type' => 'structure', 'members' => [], ], 'IsVpcPeeredResult' => [ 'type' => 'structure', 'members' => [ 'isPeered' => [ 'shape' => 'boolean', ], ], ], 'IsoDate' => [ 'type' => 'timestamp', ], 'IssuerCA' => [ 'type' => 'string', ], 'KeyAlgorithm' => [ 'type' => 'string', ], 'KeyPair' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'tags' => [ 'shape' => 'TagList', ], 'fingerprint' => [ 'shape' => 'Base64', ], ], ], 'KeyPairList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyPair', ], ], 'LightsailDistribution' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'alternativeDomainNames' => [ 'shape' => 'StringList', ], 'status' => [ 'shape' => 'string', ], 'isEnabled' => [ 'shape' => 'boolean', ], 'domainName' => [ 'shape' => 'string', ], 'bundleId' => [ 'shape' => 'string', ], 'certificateName' => [ 'shape' => 'ResourceName', ], 'origin' => [ 'shape' => 'Origin', ], 'originPublicDNS' => [ 'shape' => 'string', ], 'defaultCacheBehavior' => [ 'shape' => 'CacheBehavior', ], 'cacheBehaviorSettings' => [ 'shape' => 'CacheSettings', ], 'cacheBehaviors' => [ 'shape' => 'CacheBehaviorList', ], 'ableToUpdateBundle' => [ 'shape' => 'boolean', ], 'ipAddressType' => [ 'shape' => 'IpAddressType', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'LoadBalancer' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'tags' => [ 'shape' => 'TagList', ], 'dnsName' => [ 'shape' => 'NonEmptyString', ], 'state' => [ 'shape' => 'LoadBalancerState', ], 'protocol' => [ 'shape' => 'LoadBalancerProtocol', ], 'publicPorts' => [ 'shape' => 'PortList', ], 'healthCheckPath' => [ 'shape' => 'NonEmptyString', ], 'instancePort' => [ 'shape' => 'integer', ], 'instanceHealthSummary' => [ 'shape' => 'InstanceHealthSummaryList', ], 'tlsCertificateSummaries' => [ 'shape' => 'LoadBalancerTlsCertificateSummaryList', ], 'configurationOptions' => [ 'shape' => 'LoadBalancerConfigurationOptions', ], 'ipAddressType' => [ 'shape' => 'IpAddressType', ], 'httpsRedirectionEnabled' => [ 'shape' => 'boolean', ], 'tlsPolicyName' => [ 'shape' => 'ResourceName', ], ], ], 'LoadBalancerAttributeName' => [ 'type' => 'string', 'enum' => [ 'HealthCheckPath', 'SessionStickinessEnabled', 'SessionStickiness_LB_CookieDurationSeconds', 'HttpsRedirectionEnabled', 'TlsPolicyName', ], ], 'LoadBalancerConfigurationOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'LoadBalancerAttributeName', ], 'value' => [ 'shape' => 'string', ], ], 'LoadBalancerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancer', ], ], 'LoadBalancerMetricName' => [ 'type' => 'string', 'enum' => [ 'ClientTLSNegotiationErrorCount', 'HealthyHostCount', 'UnhealthyHostCount', 'HTTPCode_LB_4XX_Count', 'HTTPCode_LB_5XX_Count', 'HTTPCode_Instance_2XX_Count', 'HTTPCode_Instance_3XX_Count', 'HTTPCode_Instance_4XX_Count', 'HTTPCode_Instance_5XX_Count', 'InstanceResponseTime', 'RejectedConnectionCount', 'RequestCount', ], ], 'LoadBalancerProtocol' => [ 'type' => 'string', 'enum' => [ 'HTTP_HTTPS', 'HTTP', ], ], 'LoadBalancerState' => [ 'type' => 'string', 'enum' => [ 'active', 'provisioning', 'active_impaired', 'failed', 'unknown', ], ], 'LoadBalancerTlsCertificate' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'tags' => [ 'shape' => 'TagList', ], 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'isAttached' => [ 'shape' => 'boolean', ], 'status' => [ 'shape' => 'LoadBalancerTlsCertificateStatus', ], 'domainName' => [ 'shape' => 'DomainName', ], 'domainValidationRecords' => [ 'shape' => 'LoadBalancerTlsCertificateDomainValidationRecordList', ], 'failureReason' => [ 'shape' => 'LoadBalancerTlsCertificateFailureReason', ], 'issuedAt' => [ 'shape' => 'IsoDate', ], 'issuer' => [ 'shape' => 'NonEmptyString', ], 'keyAlgorithm' => [ 'shape' => 'NonEmptyString', ], 'notAfter' => [ 'shape' => 'IsoDate', ], 'notBefore' => [ 'shape' => 'IsoDate', ], 'renewalSummary' => [ 'shape' => 'LoadBalancerTlsCertificateRenewalSummary', ], 'revocationReason' => [ 'shape' => 'LoadBalancerTlsCertificateRevocationReason', ], 'revokedAt' => [ 'shape' => 'IsoDate', ], 'serial' => [ 'shape' => 'NonEmptyString', ], 'signatureAlgorithm' => [ 'shape' => 'NonEmptyString', ], 'subject' => [ 'shape' => 'NonEmptyString', ], 'subjectAlternativeNames' => [ 'shape' => 'StringList', ], ], ], 'LoadBalancerTlsCertificateDnsRecordCreationState' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LoadBalancerTlsCertificateDnsRecordCreationStateCode', ], 'message' => [ 'shape' => 'string', ], ], ], 'LoadBalancerTlsCertificateDnsRecordCreationStateCode' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'STARTED', 'FAILED', ], ], 'LoadBalancerTlsCertificateDomainStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_VALIDATION', 'FAILED', 'SUCCESS', ], ], 'LoadBalancerTlsCertificateDomainValidationOption' => [ 'type' => 'structure', 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'validationStatus' => [ 'shape' => 'LoadBalancerTlsCertificateDomainStatus', ], ], ], 'LoadBalancerTlsCertificateDomainValidationOptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerTlsCertificateDomainValidationOption', ], ], 'LoadBalancerTlsCertificateDomainValidationRecord' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'type' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], 'validationStatus' => [ 'shape' => 'LoadBalancerTlsCertificateDomainStatus', ], 'domainName' => [ 'shape' => 'DomainName', ], 'dnsRecordCreationState' => [ 'shape' => 'LoadBalancerTlsCertificateDnsRecordCreationState', ], ], ], 'LoadBalancerTlsCertificateDomainValidationRecordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerTlsCertificateDomainValidationRecord', ], ], 'LoadBalancerTlsCertificateFailureReason' => [ 'type' => 'string', 'enum' => [ 'NO_AVAILABLE_CONTACTS', 'ADDITIONAL_VERIFICATION_REQUIRED', 'DOMAIN_NOT_ALLOWED', 'INVALID_PUBLIC_DOMAIN', 'OTHER', ], ], 'LoadBalancerTlsCertificateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerTlsCertificate', ], ], 'LoadBalancerTlsCertificateRenewalStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_AUTO_RENEWAL', 'PENDING_VALIDATION', 'SUCCESS', 'FAILED', ], ], 'LoadBalancerTlsCertificateRenewalSummary' => [ 'type' => 'structure', 'members' => [ 'renewalStatus' => [ 'shape' => 'LoadBalancerTlsCertificateRenewalStatus', ], 'domainValidationOptions' => [ 'shape' => 'LoadBalancerTlsCertificateDomainValidationOptionList', ], ], ], 'LoadBalancerTlsCertificateRevocationReason' => [ 'type' => 'string', 'enum' => [ 'UNSPECIFIED', 'KEY_COMPROMISE', 'CA_COMPROMISE', 'AFFILIATION_CHANGED', 'SUPERCEDED', 'CESSATION_OF_OPERATION', 'CERTIFICATE_HOLD', 'REMOVE_FROM_CRL', 'PRIVILEGE_WITHDRAWN', 'A_A_COMPROMISE', ], ], 'LoadBalancerTlsCertificateStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_VALIDATION', 'ISSUED', 'INACTIVE', 'EXPIRED', 'VALIDATION_TIMED_OUT', 'REVOKED', 'FAILED', 'UNKNOWN', ], ], 'LoadBalancerTlsCertificateSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'isAttached' => [ 'shape' => 'boolean', ], ], ], 'LoadBalancerTlsCertificateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerTlsCertificateSummary', ], ], 'LoadBalancerTlsPolicy' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'isDefault' => [ 'shape' => 'boolean', ], 'description' => [ 'shape' => 'string', ], 'protocols' => [ 'shape' => 'StringList', ], 'ciphers' => [ 'shape' => 'StringList', ], ], ], 'LoadBalancerTlsPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerTlsPolicy', ], ], 'LogEvent' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'IsoDate', ], 'message' => [ 'shape' => 'string', ], ], ], 'LogEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogEvent', ], ], 'MetricDatapoint' => [ 'type' => 'structure', 'members' => [ 'average' => [ 'shape' => 'double', ], 'maximum' => [ 'shape' => 'double', ], 'minimum' => [ 'shape' => 'double', ], 'sampleCount' => [ 'shape' => 'double', ], 'sum' => [ 'shape' => 'double', ], 'timestamp' => [ 'shape' => 'timestamp', ], 'unit' => [ 'shape' => 'MetricUnit', ], ], ], 'MetricDatapointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDatapoint', ], ], 'MetricName' => [ 'type' => 'string', 'enum' => [ 'CPUUtilization', 'NetworkIn', 'NetworkOut', 'StatusCheckFailed', 'StatusCheckFailed_Instance', 'StatusCheckFailed_System', 'ClientTLSNegotiationErrorCount', 'HealthyHostCount', 'UnhealthyHostCount', 'HTTPCode_LB_4XX_Count', 'HTTPCode_LB_5XX_Count', 'HTTPCode_Instance_2XX_Count', 'HTTPCode_Instance_3XX_Count', 'HTTPCode_Instance_4XX_Count', 'HTTPCode_Instance_5XX_Count', 'InstanceResponseTime', 'RejectedConnectionCount', 'RequestCount', 'DatabaseConnections', 'DiskQueueDepth', 'FreeStorageSpace', 'NetworkReceiveThroughput', 'NetworkTransmitThroughput', 'BurstCapacityTime', 'BurstCapacityPercentage', ], ], 'MetricPeriod' => [ 'type' => 'integer', 'max' => 86400, 'min' => 60, ], 'MetricStatistic' => [ 'type' => 'string', 'enum' => [ 'Minimum', 'Maximum', 'Sum', 'Average', 'SampleCount', ], ], 'MetricStatisticList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricStatistic', ], ], 'MetricUnit' => [ 'type' => 'string', 'enum' => [ 'Seconds', 'Microseconds', 'Milliseconds', 'Bytes', 'Kilobytes', 'Megabytes', 'Gigabytes', 'Terabytes', 'Bits', 'Kilobits', 'Megabits', 'Gigabits', 'Terabits', 'Percent', 'Count', 'Bytes/Second', 'Kilobytes/Second', 'Megabytes/Second', 'Gigabytes/Second', 'Terabytes/Second', 'Bits/Second', 'Kilobits/Second', 'Megabits/Second', 'Gigabits/Second', 'Terabits/Second', 'Count/Second', 'None', ], ], 'MonitoredResourceInfo' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], ], 'MonthlyTransfer' => [ 'type' => 'structure', 'members' => [ 'gbPerMonthAllocated' => [ 'shape' => 'integer', ], ], ], 'NameServersUpdateState' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'NameServersUpdateStateCode', ], 'message' => [ 'shape' => 'string', ], ], ], 'NameServersUpdateStateCode' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'PENDING', 'FAILED', 'STARTED', ], ], 'NetworkProtocol' => [ 'type' => 'string', 'enum' => [ 'tcp', 'all', 'udp', 'icmp', ], ], 'NonEmptyString' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'NotificationTriggerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlarmState', ], ], 'OpenInstancePublicPortsRequest' => [ 'type' => 'structure', 'required' => [ 'portInfo', 'instanceName', ], 'members' => [ 'portInfo' => [ 'shape' => 'PortInfo', ], 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'OpenInstancePublicPortsResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'Operation' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'resourceName' => [ 'shape' => 'ResourceName', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'isTerminal' => [ 'shape' => 'boolean', ], 'operationDetails' => [ 'shape' => 'string', ], 'operationType' => [ 'shape' => 'OperationType', ], 'status' => [ 'shape' => 'OperationStatus', ], 'statusChangedAt' => [ 'shape' => 'IsoDate', ], 'errorCode' => [ 'shape' => 'string', ], 'errorDetails' => [ 'shape' => 'string', ], ], ], 'OperationFailureException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'OperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Operation', ], ], 'OperationStatus' => [ 'type' => 'string', 'enum' => [ 'NotStarted', 'Started', 'Failed', 'Completed', 'Succeeded', ], ], 'OperationType' => [ 'type' => 'string', 'enum' => [ 'DeleteKnownHostKeys', 'DeleteInstance', 'CreateInstance', 'StopInstance', 'StartInstance', 'RebootInstance', 'OpenInstancePublicPorts', 'PutInstancePublicPorts', 'CloseInstancePublicPorts', 'AllocateStaticIp', 'ReleaseStaticIp', 'AttachStaticIp', 'DetachStaticIp', 'UpdateDomainEntry', 'DeleteDomainEntry', 'CreateDomain', 'DeleteDomain', 'CreateInstanceSnapshot', 'DeleteInstanceSnapshot', 'CreateInstancesFromSnapshot', 'CreateLoadBalancer', 'DeleteLoadBalancer', 'AttachInstancesToLoadBalancer', 'DetachInstancesFromLoadBalancer', 'UpdateLoadBalancerAttribute', 'CreateLoadBalancerTlsCertificate', 'DeleteLoadBalancerTlsCertificate', 'AttachLoadBalancerTlsCertificate', 'CreateDisk', 'DeleteDisk', 'AttachDisk', 'DetachDisk', 'CreateDiskSnapshot', 'DeleteDiskSnapshot', 'CreateDiskFromSnapshot', 'CreateRelationalDatabase', 'UpdateRelationalDatabase', 'DeleteRelationalDatabase', 'CreateRelationalDatabaseFromSnapshot', 'CreateRelationalDatabaseSnapshot', 'DeleteRelationalDatabaseSnapshot', 'UpdateRelationalDatabaseParameters', 'StartRelationalDatabase', 'RebootRelationalDatabase', 'StopRelationalDatabase', 'EnableAddOn', 'DisableAddOn', 'PutAlarm', 'GetAlarms', 'DeleteAlarm', 'TestAlarm', 'CreateContactMethod', 'GetContactMethods', 'SendContactMethodVerification', 'DeleteContactMethod', 'CreateDistribution', 'UpdateDistribution', 'DeleteDistribution', 'ResetDistributionCache', 'AttachCertificateToDistribution', 'DetachCertificateFromDistribution', 'UpdateDistributionBundle', 'SetIpAddressType', 'CreateCertificate', 'DeleteCertificate', 'CreateContainerService', 'UpdateContainerService', 'DeleteContainerService', 'CreateContainerServiceDeployment', 'CreateContainerServiceRegistryLogin', 'RegisterContainerImage', 'DeleteContainerImage', 'CreateBucket', 'DeleteBucket', 'CreateBucketAccessKey', 'DeleteBucketAccessKey', 'UpdateBucketBundle', 'UpdateBucket', 'SetResourceAccessForBucket', 'UpdateInstanceMetadataOptions', 'StartGUISession', 'StopGUISession', ], ], 'Origin' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'regionName' => [ 'shape' => 'RegionName', ], 'protocolPolicy' => [ 'shape' => 'OriginProtocolPolicyEnum', ], ], ], 'OriginProtocolPolicyEnum' => [ 'type' => 'string', 'enum' => [ 'http-only', 'https-only', ], ], 'PartnerIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 10, ], 'PasswordData' => [ 'type' => 'structure', 'members' => [ 'ciphertext' => [ 'shape' => 'string', ], 'keyPairName' => [ 'shape' => 'ResourceName', ], ], ], 'PeerVpcRequest' => [ 'type' => 'structure', 'members' => [], ], 'PeerVpcResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'PendingMaintenanceAction' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'NonEmptyString', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'currentApplyDate' => [ 'shape' => 'IsoDate', ], ], ], 'PendingMaintenanceActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PendingMaintenanceAction', ], ], 'PendingModifiedRelationalDatabaseValues' => [ 'type' => 'structure', 'members' => [ 'masterUserPassword' => [ 'shape' => 'string', ], 'engineVersion' => [ 'shape' => 'string', ], 'backupRetentionEnabled' => [ 'shape' => 'boolean', ], ], ], 'Port' => [ 'type' => 'integer', 'max' => 65535, 'min' => -1, ], 'PortAccessType' => [ 'type' => 'string', 'enum' => [ 'Public', 'Private', ], ], 'PortInfo' => [ 'type' => 'structure', 'members' => [ 'fromPort' => [ 'shape' => 'Port', ], 'toPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'NetworkProtocol', ], 'cidrs' => [ 'shape' => 'StringList', ], 'ipv6Cidrs' => [ 'shape' => 'StringList', ], 'cidrListAliases' => [ 'shape' => 'StringList', ], ], ], 'PortInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortInfo', ], ], 'PortInfoSourceType' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'INSTANCE', 'NONE', 'CLOSED', ], ], 'PortList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Port', ], ], 'PortMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'ContainerServiceProtocol', ], ], 'PortState' => [ 'type' => 'string', 'enum' => [ 'open', 'closed', ], ], 'PricingUnit' => [ 'type' => 'string', 'enum' => [ 'GB', 'Hrs', 'GB-Mo', 'Bundles', 'Queries', ], ], 'PrivateRegistryAccess' => [ 'type' => 'structure', 'members' => [ 'ecrImagePullerRole' => [ 'shape' => 'ContainerServiceECRImagePullerRole', ], ], ], 'PrivateRegistryAccessRequest' => [ 'type' => 'structure', 'members' => [ 'ecrImagePullerRole' => [ 'shape' => 'ContainerServiceECRImagePullerRoleRequest', ], ], ], 'PutAlarmRequest' => [ 'type' => 'structure', 'required' => [ 'alarmName', 'metricName', 'monitoredResourceName', 'comparisonOperator', 'threshold', 'evaluationPeriods', ], 'members' => [ 'alarmName' => [ 'shape' => 'ResourceName', ], 'metricName' => [ 'shape' => 'MetricName', ], 'monitoredResourceName' => [ 'shape' => 'ResourceName', ], 'comparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'threshold' => [ 'shape' => 'double', ], 'evaluationPeriods' => [ 'shape' => 'integer', ], 'datapointsToAlarm' => [ 'shape' => 'integer', ], 'treatMissingData' => [ 'shape' => 'TreatMissingData', ], 'contactProtocols' => [ 'shape' => 'ContactProtocolsList', ], 'notificationTriggers' => [ 'shape' => 'NotificationTriggerList', ], 'notificationEnabled' => [ 'shape' => 'boolean', ], ], ], 'PutAlarmResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'PutInstancePublicPortsRequest' => [ 'type' => 'structure', 'required' => [ 'portInfos', 'instanceName', ], 'members' => [ 'portInfos' => [ 'shape' => 'PortInfoList', ], 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'PutInstancePublicPortsResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'QueryStringObject' => [ 'type' => 'structure', 'members' => [ 'option' => [ 'shape' => 'boolean', ], 'queryStringsAllowList' => [ 'shape' => 'StringList', ], ], ], 'R53HostedZoneDeletionState' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'R53HostedZoneDeletionStateCode', ], 'message' => [ 'shape' => 'string', ], ], ], 'R53HostedZoneDeletionStateCode' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'PENDING', 'FAILED', 'STARTED', ], ], 'RebootInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'RebootInstanceResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'RebootRelationalDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], ], ], 'RebootRelationalDatabaseResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'RecordState' => [ 'type' => 'string', 'enum' => [ 'Started', 'Succeeded', 'Failed', ], ], 'Region' => [ 'type' => 'structure', 'members' => [ 'continentCode' => [ 'shape' => 'string', ], 'description' => [ 'shape' => 'string', ], 'displayName' => [ 'shape' => 'string', ], 'name' => [ 'shape' => 'RegionName', ], 'availabilityZones' => [ 'shape' => 'AvailabilityZoneList', ], 'relationalDatabaseAvailabilityZones' => [ 'shape' => 'AvailabilityZoneList', ], ], ], 'RegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', ], ], 'RegionName' => [ 'type' => 'string', 'enum' => [ 'us-east-1', 'us-east-2', 'us-west-1', 'us-west-2', 'eu-west-1', 'eu-west-2', 'eu-west-3', 'eu-central-1', 'ca-central-1', 'ap-south-1', 'ap-southeast-1', 'ap-southeast-2', 'ap-northeast-1', 'ap-northeast-2', 'eu-north-1', ], ], 'RegisterContainerImageRequest' => [ 'type' => 'structure', 'required' => [ 'serviceName', 'label', 'digest', ], 'members' => [ 'serviceName' => [ 'shape' => 'ContainerServiceName', ], 'label' => [ 'shape' => 'ContainerLabel', ], 'digest' => [ 'shape' => 'string', ], ], ], 'RegisterContainerImageResult' => [ 'type' => 'structure', 'members' => [ 'containerImage' => [ 'shape' => 'ContainerImage', ], ], ], 'RegisteredDomainDelegationInfo' => [ 'type' => 'structure', 'members' => [ 'nameServersUpdateState' => [ 'shape' => 'NameServersUpdateState', ], 'r53HostedZoneDeletionState' => [ 'shape' => 'R53HostedZoneDeletionState', ], ], ], 'RelationalDatabase' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'tags' => [ 'shape' => 'TagList', ], 'relationalDatabaseBlueprintId' => [ 'shape' => 'NonEmptyString', ], 'relationalDatabaseBundleId' => [ 'shape' => 'NonEmptyString', ], 'masterDatabaseName' => [ 'shape' => 'string', ], 'hardware' => [ 'shape' => 'RelationalDatabaseHardware', ], 'state' => [ 'shape' => 'NonEmptyString', ], 'secondaryAvailabilityZone' => [ 'shape' => 'string', ], 'backupRetentionEnabled' => [ 'shape' => 'boolean', ], 'pendingModifiedValues' => [ 'shape' => 'PendingModifiedRelationalDatabaseValues', ], 'engine' => [ 'shape' => 'NonEmptyString', ], 'engineVersion' => [ 'shape' => 'NonEmptyString', ], 'latestRestorableTime' => [ 'shape' => 'IsoDate', ], 'masterUsername' => [ 'shape' => 'NonEmptyString', ], 'parameterApplyStatus' => [ 'shape' => 'NonEmptyString', ], 'preferredBackupWindow' => [ 'shape' => 'NonEmptyString', ], 'preferredMaintenanceWindow' => [ 'shape' => 'NonEmptyString', ], 'publiclyAccessible' => [ 'shape' => 'boolean', ], 'masterEndpoint' => [ 'shape' => 'RelationalDatabaseEndpoint', ], 'pendingMaintenanceActions' => [ 'shape' => 'PendingMaintenanceActionList', ], 'caCertificateIdentifier' => [ 'shape' => 'string', ], ], ], 'RelationalDatabaseBlueprint' => [ 'type' => 'structure', 'members' => [ 'blueprintId' => [ 'shape' => 'string', ], 'engine' => [ 'shape' => 'RelationalDatabaseEngine', ], 'engineVersion' => [ 'shape' => 'string', ], 'engineDescription' => [ 'shape' => 'string', ], 'engineVersionDescription' => [ 'shape' => 'string', ], 'isEngineDefault' => [ 'shape' => 'boolean', ], ], ], 'RelationalDatabaseBlueprintList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelationalDatabaseBlueprint', ], ], 'RelationalDatabaseBundle' => [ 'type' => 'structure', 'members' => [ 'bundleId' => [ 'shape' => 'string', ], 'name' => [ 'shape' => 'string', ], 'price' => [ 'shape' => 'float', ], 'ramSizeInGb' => [ 'shape' => 'float', ], 'diskSizeInGb' => [ 'shape' => 'integer', ], 'transferPerMonthInGb' => [ 'shape' => 'integer', ], 'cpuCount' => [ 'shape' => 'integer', ], 'isEncrypted' => [ 'shape' => 'boolean', ], 'isActive' => [ 'shape' => 'boolean', ], ], ], 'RelationalDatabaseBundleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelationalDatabaseBundle', ], ], 'RelationalDatabaseEndpoint' => [ 'type' => 'structure', 'members' => [ 'port' => [ 'shape' => 'integer', ], 'address' => [ 'shape' => 'NonEmptyString', ], ], ], 'RelationalDatabaseEngine' => [ 'type' => 'string', 'enum' => [ 'mysql', ], ], 'RelationalDatabaseEvent' => [ 'type' => 'structure', 'members' => [ 'resource' => [ 'shape' => 'ResourceName', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'message' => [ 'shape' => 'string', ], 'eventCategories' => [ 'shape' => 'StringList', ], ], ], 'RelationalDatabaseEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelationalDatabaseEvent', ], ], 'RelationalDatabaseHardware' => [ 'type' => 'structure', 'members' => [ 'cpuCount' => [ 'shape' => 'integer', ], 'diskSizeInGb' => [ 'shape' => 'integer', ], 'ramSizeInGb' => [ 'shape' => 'float', ], ], ], 'RelationalDatabaseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelationalDatabase', ], ], 'RelationalDatabaseMetricName' => [ 'type' => 'string', 'enum' => [ 'CPUUtilization', 'DatabaseConnections', 'DiskQueueDepth', 'FreeStorageSpace', 'NetworkReceiveThroughput', 'NetworkTransmitThroughput', ], ], 'RelationalDatabaseParameter' => [ 'type' => 'structure', 'members' => [ 'allowedValues' => [ 'shape' => 'string', ], 'applyMethod' => [ 'shape' => 'string', ], 'applyType' => [ 'shape' => 'string', ], 'dataType' => [ 'shape' => 'string', ], 'description' => [ 'shape' => 'string', ], 'isModifiable' => [ 'shape' => 'boolean', ], 'parameterName' => [ 'shape' => 'string', ], 'parameterValue' => [ 'shape' => 'string', ], ], ], 'RelationalDatabaseParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelationalDatabaseParameter', ], ], 'RelationalDatabasePasswordVersion' => [ 'type' => 'string', 'enum' => [ 'CURRENT', 'PREVIOUS', 'PENDING', ], ], 'RelationalDatabaseSnapshot' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'tags' => [ 'shape' => 'TagList', ], 'engine' => [ 'shape' => 'NonEmptyString', ], 'engineVersion' => [ 'shape' => 'NonEmptyString', ], 'sizeInGb' => [ 'shape' => 'integer', ], 'state' => [ 'shape' => 'NonEmptyString', ], 'fromRelationalDatabaseName' => [ 'shape' => 'NonEmptyString', ], 'fromRelationalDatabaseArn' => [ 'shape' => 'NonEmptyString', ], 'fromRelationalDatabaseBundleId' => [ 'shape' => 'string', ], 'fromRelationalDatabaseBlueprintId' => [ 'shape' => 'string', ], ], ], 'RelationalDatabaseSnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelationalDatabaseSnapshot', ], ], 'ReleaseStaticIpRequest' => [ 'type' => 'structure', 'required' => [ 'staticIpName', ], 'members' => [ 'staticIpName' => [ 'shape' => 'ResourceName', ], ], ], 'ReleaseStaticIpResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'RenewalStatus' => [ 'type' => 'string', 'enum' => [ 'PendingAutoRenewal', 'PendingValidation', 'Success', 'Failed', ], ], 'RenewalStatusReason' => [ 'type' => 'string', ], 'RenewalSummary' => [ 'type' => 'structure', 'members' => [ 'domainValidationRecords' => [ 'shape' => 'DomainValidationRecordList', ], 'renewalStatus' => [ 'shape' => 'RenewalStatus', ], 'renewalStatusReason' => [ 'shape' => 'RenewalStatusReason', ], 'updatedAt' => [ 'shape' => 'IsoDate', ], ], ], 'RequestFailureReason' => [ 'type' => 'string', ], 'ResetDistributionCacheRequest' => [ 'type' => 'structure', 'members' => [ 'distributionName' => [ 'shape' => 'ResourceName', ], ], ], 'ResetDistributionCacheResult' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'string', ], 'createTime' => [ 'shape' => 'IsoDate', ], 'operation' => [ 'shape' => 'Operation', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws[^:]*):([a-zA-Z0-9-]+):([a-z0-9-]+):([0-9]+):([a-zA-Z]+)/([a-zA-Z0-9-]+)$', ], 'ResourceBucketAccess' => [ 'type' => 'string', 'enum' => [ 'allow', 'deny', ], ], 'ResourceBudgetEstimate' => [ 'type' => 'structure', 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'costEstimates' => [ 'shape' => 'CostEstimates', ], 'startTime' => [ 'shape' => 'IsoDate', ], 'endTime' => [ 'shape' => 'IsoDate', ], ], ], 'ResourceLocation' => [ 'type' => 'structure', 'members' => [ 'availabilityZone' => [ 'shape' => 'string', ], 'regionName' => [ 'shape' => 'RegionName', ], ], ], 'ResourceName' => [ 'type' => 'string', 'pattern' => '\\w[\\w\\-]*\\w', ], 'ResourceNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceName', ], ], 'ResourceReceivingAccess' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'resourceType' => [ 'shape' => 'NonEmptyString', ], ], ], 'ResourceRecord' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'string', ], 'type' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'string', ], ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'ContainerService', 'Instance', 'StaticIp', 'KeyPair', 'InstanceSnapshot', 'Domain', 'PeeredVpc', 'LoadBalancer', 'LoadBalancerTlsCertificate', 'Disk', 'DiskSnapshot', 'RelationalDatabase', 'RelationalDatabaseSnapshot', 'ExportSnapshotRecord', 'CloudFormationStackRecord', 'Alarm', 'ContactMethod', 'Distribution', 'Certificate', 'Bucket', ], ], 'ResourcesBudgetEstimate' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceBudgetEstimate', ], ], 'RevocationReason' => [ 'type' => 'string', ], 'SendContactMethodVerificationRequest' => [ 'type' => 'structure', 'required' => [ 'protocol', ], 'members' => [ 'protocol' => [ 'shape' => 'ContactMethodVerificationProtocol', ], ], ], 'SendContactMethodVerificationResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'SensitiveNonEmptyString' => [ 'type' => 'string', 'pattern' => '.*\\S.*', 'sensitive' => true, ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'SerialNumber' => [ 'type' => 'string', ], 'ServiceException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, 'fault' => true, ], 'Session' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'url' => [ 'shape' => 'SensitiveNonEmptyString', ], 'isPrimary' => [ 'shape' => 'boolean', ], ], ], 'Sessions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Session', ], ], 'SetIpAddressTypeRequest' => [ 'type' => 'structure', 'required' => [ 'resourceType', 'resourceName', 'ipAddressType', ], 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceName' => [ 'shape' => 'ResourceName', ], 'ipAddressType' => [ 'shape' => 'IpAddressType', ], ], ], 'SetIpAddressTypeResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'SetResourceAccessForBucketRequest' => [ 'type' => 'structure', 'required' => [ 'resourceName', 'bucketName', 'access', ], 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], 'bucketName' => [ 'shape' => 'BucketName', ], 'access' => [ 'shape' => 'ResourceBucketAccess', ], ], ], 'SetResourceAccessForBucketResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'StartGUISessionRequest' => [ 'type' => 'structure', 'required' => [ 'resourceName', ], 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], ], ], 'StartGUISessionResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'StartInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'StartInstanceResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'StartRelationalDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], ], ], 'StartRelationalDatabaseResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'StaticIp' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'ipAddress' => [ 'shape' => 'IpAddress', ], 'attachedTo' => [ 'shape' => 'ResourceName', ], 'isAttached' => [ 'shape' => 'boolean', ], ], ], 'StaticIpList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StaticIp', ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'startExpired', 'notStarted', 'started', 'starting', 'stopped', 'stopping', 'settingUpInstance', 'failedInstanceCreation', 'failedStartingGUISession', 'failedStoppingGUISession', ], ], 'StatusType' => [ 'type' => 'string', 'enum' => [ 'Active', 'Inactive', ], ], 'StopGUISessionRequest' => [ 'type' => 'structure', 'required' => [ 'resourceName', ], 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], ], ], 'StopGUISessionResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'StopInstanceOnIdleRequest' => [ 'type' => 'structure', 'members' => [ 'threshold' => [ 'shape' => 'string', ], 'duration' => [ 'shape' => 'string', ], ], ], 'StopInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], 'force' => [ 'shape' => 'boolean', ], ], ], 'StopInstanceResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'StopRelationalDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'relationalDatabaseSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'StopRelationalDatabaseResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], ], 'StringMax256' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SubjectAlternativeNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainName', ], ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceName', 'tags', ], 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'TagValue' => [ 'type' => 'string', ], 'TestAlarmRequest' => [ 'type' => 'structure', 'required' => [ 'alarmName', 'state', ], 'members' => [ 'alarmName' => [ 'shape' => 'ResourceName', ], 'state' => [ 'shape' => 'AlarmState', ], ], ], 'TestAlarmResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'TimeOfDay' => [ 'type' => 'string', 'pattern' => '^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$', ], 'TimePeriod' => [ 'type' => 'structure', 'members' => [ 'start' => [ 'shape' => 'IsoDate', ], 'end' => [ 'shape' => 'IsoDate', ], ], ], 'TreatMissingData' => [ 'type' => 'string', 'enum' => [ 'breaching', 'notBreaching', 'ignore', 'missing', ], ], 'UnauthenticatedException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'UnpeerVpcRequest' => [ 'type' => 'structure', 'members' => [], ], 'UnpeerVpcResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceName', 'tagKeys', ], 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'UpdateBucketBundleRequest' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'bundleId', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'bundleId' => [ 'shape' => 'NonEmptyString', ], ], ], 'UpdateBucketBundleResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'UpdateBucketRequest' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'accessRules' => [ 'shape' => 'AccessRules', ], 'versioning' => [ 'shape' => 'NonEmptyString', ], 'readonlyAccessAccounts' => [ 'shape' => 'PartnerIdList', ], 'accessLogConfig' => [ 'shape' => 'BucketAccessLogConfig', ], ], ], 'UpdateBucketResult' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'Bucket', ], 'operations' => [ 'shape' => 'OperationList', ], ], ], 'UpdateContainerServiceRequest' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'serviceName' => [ 'shape' => 'ContainerServiceName', ], 'power' => [ 'shape' => 'ContainerServicePowerName', ], 'scale' => [ 'shape' => 'ContainerServiceScale', ], 'isDisabled' => [ 'shape' => 'boolean', ], 'publicDomainNames' => [ 'shape' => 'ContainerServicePublicDomains', ], 'privateRegistryAccess' => [ 'shape' => 'PrivateRegistryAccessRequest', ], ], ], 'UpdateContainerServiceResult' => [ 'type' => 'structure', 'members' => [ 'containerService' => [ 'shape' => 'ContainerService', ], ], ], 'UpdateDistributionBundleRequest' => [ 'type' => 'structure', 'members' => [ 'distributionName' => [ 'shape' => 'ResourceName', ], 'bundleId' => [ 'shape' => 'string', ], ], ], 'UpdateDistributionBundleResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'UpdateDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'distributionName', ], 'members' => [ 'distributionName' => [ 'shape' => 'ResourceName', ], 'origin' => [ 'shape' => 'InputOrigin', ], 'defaultCacheBehavior' => [ 'shape' => 'CacheBehavior', ], 'cacheBehaviorSettings' => [ 'shape' => 'CacheSettings', ], 'cacheBehaviors' => [ 'shape' => 'CacheBehaviorList', ], 'isEnabled' => [ 'shape' => 'boolean', ], ], ], 'UpdateDistributionResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'UpdateDomainEntryRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', 'domainEntry', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'domainEntry' => [ 'shape' => 'DomainEntry', ], ], ], 'UpdateDomainEntryResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'UpdateInstanceMetadataOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], 'httpTokens' => [ 'shape' => 'HttpTokens', ], 'httpEndpoint' => [ 'shape' => 'HttpEndpoint', ], 'httpPutResponseHopLimit' => [ 'shape' => 'integer', ], 'httpProtocolIpv6' => [ 'shape' => 'HttpProtocolIpv6', ], ], ], 'UpdateInstanceMetadataOptionsResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'UpdateLoadBalancerAttributeRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'attributeName', 'attributeValue', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'attributeName' => [ 'shape' => 'LoadBalancerAttributeName', ], 'attributeValue' => [ 'shape' => 'StringMax256', ], ], ], 'UpdateLoadBalancerAttributeResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'UpdateRelationalDatabaseParametersRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', 'parameters', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'parameters' => [ 'shape' => 'RelationalDatabaseParameterList', ], ], ], 'UpdateRelationalDatabaseParametersResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'UpdateRelationalDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'relationalDatabaseName', ], 'members' => [ 'relationalDatabaseName' => [ 'shape' => 'ResourceName', ], 'masterUserPassword' => [ 'shape' => 'SensitiveString', ], 'rotateMasterUserPassword' => [ 'shape' => 'boolean', ], 'preferredBackupWindow' => [ 'shape' => 'string', ], 'preferredMaintenanceWindow' => [ 'shape' => 'string', ], 'enableBackupRetention' => [ 'shape' => 'boolean', ], 'disableBackupRetention' => [ 'shape' => 'boolean', ], 'publiclyAccessible' => [ 'shape' => 'boolean', ], 'applyImmediately' => [ 'shape' => 'boolean', ], 'caCertificateIdentifier' => [ 'shape' => 'string', ], ], ], 'UpdateRelationalDatabaseResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'boolean' => [ 'type' => 'boolean', ], 'double' => [ 'type' => 'double', ], 'float' => [ 'type' => 'float', ], 'integer' => [ 'type' => 'integer', ], 'long' => [ 'type' => 'long', ], 'string' => [ 'type' => 'string', ], 'timestamp' => [ 'type' => 'timestamp', ], ],];
