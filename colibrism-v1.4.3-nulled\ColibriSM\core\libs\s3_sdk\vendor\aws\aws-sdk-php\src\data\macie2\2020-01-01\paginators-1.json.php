<?php
// This file was auto-generated from sdk-root/src/data/macie2/2020-01-01/paginators-1.json
return [ 'pagination' => [ 'DescribeBuckets' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'buckets', ], 'GetUsageStatistics' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'records', ], 'ListClassificationJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'items', ], 'ListClassificationScopes' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'result_key' => 'classificationScopes', ], 'ListCustomDataIdentifiers' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'items', ], 'ListFindings' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'findingIds', ], 'ListFindingsFilters' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'findingsFilterListItems', ], 'ListInvitations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'invitations', ], 'ListMembers' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'members', ], 'ListOrganizationAdminAccounts' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'adminAccounts', ], 'SearchResources' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'matchingResources', ], 'ListAllowLists' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'allowLists', ], 'ListManagedDataIdentifiers' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'result_key' => 'items', ], 'ListResourceProfileDetections' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'detections', ], 'ListSensitivityInspectionTemplates' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'sensitivityInspectionTemplates', ], 'ListResourceProfileArtifacts' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'result_key' => 'artifacts', ], ],];
