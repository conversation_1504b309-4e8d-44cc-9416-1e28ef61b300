<?php
// This file was auto-generated from sdk-root/src/data/comprehend/2017-11-27/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-11-27', 'endpointPrefix' => 'comprehend', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Amazon Comprehend', 'serviceId' => 'Comprehend', 'signatureVersion' => 'v4', 'signingName' => 'comprehend', 'targetPrefix' => 'Comprehend_20171127', 'uid' => 'comprehend-2017-11-27', ], 'operations' => [ 'BatchDetectDominantLanguage' => [ 'name' => 'BatchDetectDominantLanguage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDetectDominantLanguageRequest', ], 'output' => [ 'shape' => 'BatchDetectDominantLanguageResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'BatchSizeLimitExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchDetectEntities' => [ 'name' => 'BatchDetectEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDetectEntitiesRequest', ], 'output' => [ 'shape' => 'BatchDetectEntitiesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'BatchSizeLimitExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchDetectKeyPhrases' => [ 'name' => 'BatchDetectKeyPhrases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDetectKeyPhrasesRequest', ], 'output' => [ 'shape' => 'BatchDetectKeyPhrasesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'BatchSizeLimitExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchDetectSentiment' => [ 'name' => 'BatchDetectSentiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDetectSentimentRequest', ], 'output' => [ 'shape' => 'BatchDetectSentimentResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'BatchSizeLimitExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchDetectSyntax' => [ 'name' => 'BatchDetectSyntax', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDetectSyntaxRequest', ], 'output' => [ 'shape' => 'BatchDetectSyntaxResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'BatchSizeLimitExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchDetectTargetedSentiment' => [ 'name' => 'BatchDetectTargetedSentiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDetectTargetedSentimentRequest', ], 'output' => [ 'shape' => 'BatchDetectTargetedSentimentResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'BatchSizeLimitExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ClassifyDocument' => [ 'name' => 'ClassifyDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ClassifyDocumentRequest', ], 'output' => [ 'shape' => 'ClassifyDocumentResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ContainsPiiEntities' => [ 'name' => 'ContainsPiiEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ContainsPiiEntitiesRequest', ], 'output' => [ 'shape' => 'ContainsPiiEntitiesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateDataset' => [ 'name' => 'CreateDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetRequest', ], 'output' => [ 'shape' => 'CreateDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateDocumentClassifier' => [ 'name' => 'CreateDocumentClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDocumentClassifierRequest', ], 'output' => [ 'shape' => 'CreateDocumentClassifierResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateEndpoint' => [ 'name' => 'CreateEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEndpointRequest', ], 'output' => [ 'shape' => 'CreateEndpointResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateEntityRecognizer' => [ 'name' => 'CreateEntityRecognizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEntityRecognizerRequest', ], 'output' => [ 'shape' => 'CreateEntityRecognizerResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateFlywheel' => [ 'name' => 'CreateFlywheel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFlywheelRequest', ], 'output' => [ 'shape' => 'CreateFlywheelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteDocumentClassifier' => [ 'name' => 'DeleteDocumentClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDocumentClassifierRequest', ], 'output' => [ 'shape' => 'DeleteDocumentClassifierResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteEndpoint' => [ 'name' => 'DeleteEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEndpointRequest', ], 'output' => [ 'shape' => 'DeleteEndpointResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteEntityRecognizer' => [ 'name' => 'DeleteEntityRecognizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEntityRecognizerRequest', ], 'output' => [ 'shape' => 'DeleteEntityRecognizerResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteFlywheel' => [ 'name' => 'DeleteFlywheel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFlywheelRequest', ], 'output' => [ 'shape' => 'DeleteFlywheelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeDataset' => [ 'name' => 'DescribeDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetRequest', ], 'output' => [ 'shape' => 'DescribeDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeDocumentClassificationJob' => [ 'name' => 'DescribeDocumentClassificationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDocumentClassificationJobRequest', ], 'output' => [ 'shape' => 'DescribeDocumentClassificationJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeDocumentClassifier' => [ 'name' => 'DescribeDocumentClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDocumentClassifierRequest', ], 'output' => [ 'shape' => 'DescribeDocumentClassifierResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeDominantLanguageDetectionJob' => [ 'name' => 'DescribeDominantLanguageDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDominantLanguageDetectionJobRequest', ], 'output' => [ 'shape' => 'DescribeDominantLanguageDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeEndpoint' => [ 'name' => 'DescribeEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEndpointRequest', ], 'output' => [ 'shape' => 'DescribeEndpointResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeEntitiesDetectionJob' => [ 'name' => 'DescribeEntitiesDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEntitiesDetectionJobRequest', ], 'output' => [ 'shape' => 'DescribeEntitiesDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeEntityRecognizer' => [ 'name' => 'DescribeEntityRecognizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEntityRecognizerRequest', ], 'output' => [ 'shape' => 'DescribeEntityRecognizerResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeEventsDetectionJob' => [ 'name' => 'DescribeEventsDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventsDetectionJobRequest', ], 'output' => [ 'shape' => 'DescribeEventsDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeFlywheel' => [ 'name' => 'DescribeFlywheel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFlywheelRequest', ], 'output' => [ 'shape' => 'DescribeFlywheelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeFlywheelIteration' => [ 'name' => 'DescribeFlywheelIteration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFlywheelIterationRequest', ], 'output' => [ 'shape' => 'DescribeFlywheelIterationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeKeyPhrasesDetectionJob' => [ 'name' => 'DescribeKeyPhrasesDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeKeyPhrasesDetectionJobRequest', ], 'output' => [ 'shape' => 'DescribeKeyPhrasesDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribePiiEntitiesDetectionJob' => [ 'name' => 'DescribePiiEntitiesDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePiiEntitiesDetectionJobRequest', ], 'output' => [ 'shape' => 'DescribePiiEntitiesDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeResourcePolicy' => [ 'name' => 'DescribeResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeResourcePolicyRequest', ], 'output' => [ 'shape' => 'DescribeResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeSentimentDetectionJob' => [ 'name' => 'DescribeSentimentDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSentimentDetectionJobRequest', ], 'output' => [ 'shape' => 'DescribeSentimentDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeTargetedSentimentDetectionJob' => [ 'name' => 'DescribeTargetedSentimentDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTargetedSentimentDetectionJobRequest', ], 'output' => [ 'shape' => 'DescribeTargetedSentimentDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeTopicsDetectionJob' => [ 'name' => 'DescribeTopicsDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTopicsDetectionJobRequest', ], 'output' => [ 'shape' => 'DescribeTopicsDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DetectDominantLanguage' => [ 'name' => 'DetectDominantLanguage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectDominantLanguageRequest', ], 'output' => [ 'shape' => 'DetectDominantLanguageResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DetectEntities' => [ 'name' => 'DetectEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectEntitiesRequest', ], 'output' => [ 'shape' => 'DetectEntitiesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DetectKeyPhrases' => [ 'name' => 'DetectKeyPhrases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectKeyPhrasesRequest', ], 'output' => [ 'shape' => 'DetectKeyPhrasesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DetectPiiEntities' => [ 'name' => 'DetectPiiEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectPiiEntitiesRequest', ], 'output' => [ 'shape' => 'DetectPiiEntitiesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DetectSentiment' => [ 'name' => 'DetectSentiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectSentimentRequest', ], 'output' => [ 'shape' => 'DetectSentimentResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DetectSyntax' => [ 'name' => 'DetectSyntax', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectSyntaxRequest', ], 'output' => [ 'shape' => 'DetectSyntaxResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DetectTargetedSentiment' => [ 'name' => 'DetectTargetedSentiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectTargetedSentimentRequest', ], 'output' => [ 'shape' => 'DetectTargetedSentimentResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DetectToxicContent' => [ 'name' => 'DetectToxicContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectToxicContentRequest', ], 'output' => [ 'shape' => 'DetectToxicContentResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TextSizeLimitExceededException', ], [ 'shape' => 'UnsupportedLanguageException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ImportModel' => [ 'name' => 'ImportModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportModelRequest', ], 'output' => [ 'shape' => 'ImportModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDatasets' => [ 'name' => 'ListDatasets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetsRequest', ], 'output' => [ 'shape' => 'ListDatasetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDocumentClassificationJobs' => [ 'name' => 'ListDocumentClassificationJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDocumentClassificationJobsRequest', ], 'output' => [ 'shape' => 'ListDocumentClassificationJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDocumentClassifierSummaries' => [ 'name' => 'ListDocumentClassifierSummaries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDocumentClassifierSummariesRequest', ], 'output' => [ 'shape' => 'ListDocumentClassifierSummariesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDocumentClassifiers' => [ 'name' => 'ListDocumentClassifiers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDocumentClassifiersRequest', ], 'output' => [ 'shape' => 'ListDocumentClassifiersResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDominantLanguageDetectionJobs' => [ 'name' => 'ListDominantLanguageDetectionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDominantLanguageDetectionJobsRequest', ], 'output' => [ 'shape' => 'ListDominantLanguageDetectionJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEndpoints' => [ 'name' => 'ListEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEndpointsRequest', ], 'output' => [ 'shape' => 'ListEndpointsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEntitiesDetectionJobs' => [ 'name' => 'ListEntitiesDetectionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEntitiesDetectionJobsRequest', ], 'output' => [ 'shape' => 'ListEntitiesDetectionJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEntityRecognizerSummaries' => [ 'name' => 'ListEntityRecognizerSummaries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEntityRecognizerSummariesRequest', ], 'output' => [ 'shape' => 'ListEntityRecognizerSummariesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEntityRecognizers' => [ 'name' => 'ListEntityRecognizers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEntityRecognizersRequest', ], 'output' => [ 'shape' => 'ListEntityRecognizersResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEventsDetectionJobs' => [ 'name' => 'ListEventsDetectionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEventsDetectionJobsRequest', ], 'output' => [ 'shape' => 'ListEventsDetectionJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListFlywheelIterationHistory' => [ 'name' => 'ListFlywheelIterationHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFlywheelIterationHistoryRequest', ], 'output' => [ 'shape' => 'ListFlywheelIterationHistoryResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListFlywheels' => [ 'name' => 'ListFlywheels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFlywheelsRequest', ], 'output' => [ 'shape' => 'ListFlywheelsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListKeyPhrasesDetectionJobs' => [ 'name' => 'ListKeyPhrasesDetectionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListKeyPhrasesDetectionJobsRequest', ], 'output' => [ 'shape' => 'ListKeyPhrasesDetectionJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPiiEntitiesDetectionJobs' => [ 'name' => 'ListPiiEntitiesDetectionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPiiEntitiesDetectionJobsRequest', ], 'output' => [ 'shape' => 'ListPiiEntitiesDetectionJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSentimentDetectionJobs' => [ 'name' => 'ListSentimentDetectionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSentimentDetectionJobsRequest', ], 'output' => [ 'shape' => 'ListSentimentDetectionJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTargetedSentimentDetectionJobs' => [ 'name' => 'ListTargetedSentimentDetectionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTargetedSentimentDetectionJobsRequest', ], 'output' => [ 'shape' => 'ListTargetedSentimentDetectionJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTopicsDetectionJobs' => [ 'name' => 'ListTopicsDetectionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTopicsDetectionJobsRequest', ], 'output' => [ 'shape' => 'ListTopicsDetectionJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidFilterException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartDocumentClassificationJob' => [ 'name' => 'StartDocumentClassificationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDocumentClassificationJobRequest', ], 'output' => [ 'shape' => 'StartDocumentClassificationJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartDominantLanguageDetectionJob' => [ 'name' => 'StartDominantLanguageDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDominantLanguageDetectionJobRequest', ], 'output' => [ 'shape' => 'StartDominantLanguageDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartEntitiesDetectionJob' => [ 'name' => 'StartEntitiesDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartEntitiesDetectionJobRequest', ], 'output' => [ 'shape' => 'StartEntitiesDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartEventsDetectionJob' => [ 'name' => 'StartEventsDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartEventsDetectionJobRequest', ], 'output' => [ 'shape' => 'StartEventsDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartFlywheelIteration' => [ 'name' => 'StartFlywheelIteration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartFlywheelIterationRequest', ], 'output' => [ 'shape' => 'StartFlywheelIterationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartKeyPhrasesDetectionJob' => [ 'name' => 'StartKeyPhrasesDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartKeyPhrasesDetectionJobRequest', ], 'output' => [ 'shape' => 'StartKeyPhrasesDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartPiiEntitiesDetectionJob' => [ 'name' => 'StartPiiEntitiesDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartPiiEntitiesDetectionJobRequest', ], 'output' => [ 'shape' => 'StartPiiEntitiesDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartSentimentDetectionJob' => [ 'name' => 'StartSentimentDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartSentimentDetectionJobRequest', ], 'output' => [ 'shape' => 'StartSentimentDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartTargetedSentimentDetectionJob' => [ 'name' => 'StartTargetedSentimentDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartTargetedSentimentDetectionJobRequest', ], 'output' => [ 'shape' => 'StartTargetedSentimentDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartTopicsDetectionJob' => [ 'name' => 'StartTopicsDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartTopicsDetectionJobRequest', ], 'output' => [ 'shape' => 'StartTopicsDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopDominantLanguageDetectionJob' => [ 'name' => 'StopDominantLanguageDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopDominantLanguageDetectionJobRequest', ], 'output' => [ 'shape' => 'StopDominantLanguageDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopEntitiesDetectionJob' => [ 'name' => 'StopEntitiesDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopEntitiesDetectionJobRequest', ], 'output' => [ 'shape' => 'StopEntitiesDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopEventsDetectionJob' => [ 'name' => 'StopEventsDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopEventsDetectionJobRequest', ], 'output' => [ 'shape' => 'StopEventsDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopKeyPhrasesDetectionJob' => [ 'name' => 'StopKeyPhrasesDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopKeyPhrasesDetectionJobRequest', ], 'output' => [ 'shape' => 'StopKeyPhrasesDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopPiiEntitiesDetectionJob' => [ 'name' => 'StopPiiEntitiesDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopPiiEntitiesDetectionJobRequest', ], 'output' => [ 'shape' => 'StopPiiEntitiesDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopSentimentDetectionJob' => [ 'name' => 'StopSentimentDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopSentimentDetectionJobRequest', ], 'output' => [ 'shape' => 'StopSentimentDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopTargetedSentimentDetectionJob' => [ 'name' => 'StopTargetedSentimentDetectionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopTargetedSentimentDetectionJobRequest', ], 'output' => [ 'shape' => 'StopTargetedSentimentDetectionJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'JobNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopTrainingDocumentClassifier' => [ 'name' => 'StopTrainingDocumentClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopTrainingDocumentClassifierRequest', ], 'output' => [ 'shape' => 'StopTrainingDocumentClassifierResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopTrainingEntityRecognizer' => [ 'name' => 'StopTrainingEntityRecognizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopTrainingEntityRecognizerRequest', ], 'output' => [ 'shape' => 'StopTrainingEntityRecognizerResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'TooManyTagKeysException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateEndpoint' => [ 'name' => 'UpdateEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEndpointRequest', ], 'output' => [ 'shape' => 'UpdateEndpointResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateFlywheel' => [ 'name' => 'UpdateFlywheel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFlywheelRequest', ], 'output' => [ 'shape' => 'UpdateFlywheelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'KmsKeyValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AnyLengthString' => [ 'type' => 'string', ], 'AttributeNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeNamesListItem', ], ], 'AttributeNamesListItem' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'AugmentedManifestsDocumentTypeFormat' => [ 'type' => 'string', 'enum' => [ 'PLAIN_TEXT_DOCUMENT', 'SEMI_STRUCTURED_DOCUMENT', ], ], 'AugmentedManifestsListItem' => [ 'type' => 'structure', 'required' => [ 'S3Uri', 'AttributeNames', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'Split' => [ 'shape' => 'Split', ], 'AttributeNames' => [ 'shape' => 'AttributeNamesList', ], 'AnnotationDataS3Uri' => [ 'shape' => 'S3Uri', ], 'SourceDocumentsS3Uri' => [ 'shape' => 'S3Uri', ], 'DocumentType' => [ 'shape' => 'AugmentedManifestsDocumentTypeFormat', ], ], ], 'BatchDetectDominantLanguageItemResult' => [ 'type' => 'structure', 'members' => [ 'Index' => [ 'shape' => 'Integer', ], 'Languages' => [ 'shape' => 'ListOfDominantLanguages', ], ], ], 'BatchDetectDominantLanguageRequest' => [ 'type' => 'structure', 'required' => [ 'TextList', ], 'members' => [ 'TextList' => [ 'shape' => 'CustomerInputStringList', ], ], ], 'BatchDetectDominantLanguageResponse' => [ 'type' => 'structure', 'required' => [ 'ResultList', 'ErrorList', ], 'members' => [ 'ResultList' => [ 'shape' => 'ListOfDetectDominantLanguageResult', ], 'ErrorList' => [ 'shape' => 'BatchItemErrorList', ], ], 'sensitive' => true, ], 'BatchDetectEntitiesItemResult' => [ 'type' => 'structure', 'members' => [ 'Index' => [ 'shape' => 'Integer', ], 'Entities' => [ 'shape' => 'ListOfEntities', ], ], ], 'BatchDetectEntitiesRequest' => [ 'type' => 'structure', 'required' => [ 'TextList', 'LanguageCode', ], 'members' => [ 'TextList' => [ 'shape' => 'CustomerInputStringList', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'BatchDetectEntitiesResponse' => [ 'type' => 'structure', 'required' => [ 'ResultList', 'ErrorList', ], 'members' => [ 'ResultList' => [ 'shape' => 'ListOfDetectEntitiesResult', ], 'ErrorList' => [ 'shape' => 'BatchItemErrorList', ], ], 'sensitive' => true, ], 'BatchDetectKeyPhrasesItemResult' => [ 'type' => 'structure', 'members' => [ 'Index' => [ 'shape' => 'Integer', ], 'KeyPhrases' => [ 'shape' => 'ListOfKeyPhrases', ], ], ], 'BatchDetectKeyPhrasesRequest' => [ 'type' => 'structure', 'required' => [ 'TextList', 'LanguageCode', ], 'members' => [ 'TextList' => [ 'shape' => 'CustomerInputStringList', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'BatchDetectKeyPhrasesResponse' => [ 'type' => 'structure', 'required' => [ 'ResultList', 'ErrorList', ], 'members' => [ 'ResultList' => [ 'shape' => 'ListOfDetectKeyPhrasesResult', ], 'ErrorList' => [ 'shape' => 'BatchItemErrorList', ], ], 'sensitive' => true, ], 'BatchDetectSentimentItemResult' => [ 'type' => 'structure', 'members' => [ 'Index' => [ 'shape' => 'Integer', ], 'Sentiment' => [ 'shape' => 'SentimentType', ], 'SentimentScore' => [ 'shape' => 'SentimentScore', ], ], ], 'BatchDetectSentimentRequest' => [ 'type' => 'structure', 'required' => [ 'TextList', 'LanguageCode', ], 'members' => [ 'TextList' => [ 'shape' => 'CustomerInputStringList', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'BatchDetectSentimentResponse' => [ 'type' => 'structure', 'required' => [ 'ResultList', 'ErrorList', ], 'members' => [ 'ResultList' => [ 'shape' => 'ListOfDetectSentimentResult', ], 'ErrorList' => [ 'shape' => 'BatchItemErrorList', ], ], 'sensitive' => true, ], 'BatchDetectSyntaxItemResult' => [ 'type' => 'structure', 'members' => [ 'Index' => [ 'shape' => 'Integer', ], 'SyntaxTokens' => [ 'shape' => 'ListOfSyntaxTokens', ], ], ], 'BatchDetectSyntaxRequest' => [ 'type' => 'structure', 'required' => [ 'TextList', 'LanguageCode', ], 'members' => [ 'TextList' => [ 'shape' => 'CustomerInputStringList', ], 'LanguageCode' => [ 'shape' => 'SyntaxLanguageCode', ], ], ], 'BatchDetectSyntaxResponse' => [ 'type' => 'structure', 'required' => [ 'ResultList', 'ErrorList', ], 'members' => [ 'ResultList' => [ 'shape' => 'ListOfDetectSyntaxResult', ], 'ErrorList' => [ 'shape' => 'BatchItemErrorList', ], ], 'sensitive' => true, ], 'BatchDetectTargetedSentimentItemResult' => [ 'type' => 'structure', 'members' => [ 'Index' => [ 'shape' => 'Integer', ], 'Entities' => [ 'shape' => 'ListOfTargetedSentimentEntities', ], ], ], 'BatchDetectTargetedSentimentRequest' => [ 'type' => 'structure', 'required' => [ 'TextList', 'LanguageCode', ], 'members' => [ 'TextList' => [ 'shape' => 'CustomerInputStringList', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'BatchDetectTargetedSentimentResponse' => [ 'type' => 'structure', 'required' => [ 'ResultList', 'ErrorList', ], 'members' => [ 'ResultList' => [ 'shape' => 'ListOfDetectTargetedSentimentResult', ], 'ErrorList' => [ 'shape' => 'BatchItemErrorList', ], ], 'sensitive' => true, ], 'BatchItemError' => [ 'type' => 'structure', 'members' => [ 'Index' => [ 'shape' => 'Integer', ], 'ErrorCode' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'BatchItemErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchItemError', ], ], 'BatchSizeLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Block' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'String', ], 'BlockType' => [ 'shape' => 'BlockType', ], 'Text' => [ 'shape' => 'String', ], 'Page' => [ 'shape' => 'Integer', ], 'Geometry' => [ 'shape' => 'Geometry', ], 'Relationships' => [ 'shape' => 'ListOfRelationships', ], ], ], 'BlockReference' => [ 'type' => 'structure', 'members' => [ 'BlockId' => [ 'shape' => 'String', ], 'BeginOffset' => [ 'shape' => 'Integer', ], 'EndOffset' => [ 'shape' => 'Integer', ], 'ChildBlocks' => [ 'shape' => 'ListOfChildBlocks', ], ], ], 'BlockType' => [ 'type' => 'string', 'enum' => [ 'LINE', 'WORD', ], ], 'BoundingBox' => [ 'type' => 'structure', 'members' => [ 'Height' => [ 'shape' => 'Float', ], 'Left' => [ 'shape' => 'Float', ], 'Top' => [ 'shape' => 'Float', ], 'Width' => [ 'shape' => 'Float', ], ], ], 'ChildBlock' => [ 'type' => 'structure', 'members' => [ 'ChildBlockId' => [ 'shape' => 'String', ], 'BeginOffset' => [ 'shape' => 'Integer', ], 'EndOffset' => [ 'shape' => 'Integer', ], ], ], 'ClassifierEvaluationMetrics' => [ 'type' => 'structure', 'members' => [ 'Accuracy' => [ 'shape' => 'Double', ], 'Precision' => [ 'shape' => 'Double', ], 'Recall' => [ 'shape' => 'Double', ], 'F1Score' => [ 'shape' => 'Double', ], 'MicroPrecision' => [ 'shape' => 'Double', ], 'MicroRecall' => [ 'shape' => 'Double', ], 'MicroF1Score' => [ 'shape' => 'Double', ], 'HammingLoss' => [ 'shape' => 'Double', ], ], ], 'ClassifierMetadata' => [ 'type' => 'structure', 'members' => [ 'NumberOfLabels' => [ 'shape' => 'Integer', ], 'NumberOfTrainedDocuments' => [ 'shape' => 'Integer', ], 'NumberOfTestDocuments' => [ 'shape' => 'Integer', ], 'EvaluationMetrics' => [ 'shape' => 'ClassifierEvaluationMetrics', ], ], 'sensitive' => true, ], 'ClassifyDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'Text' => [ 'shape' => 'CustomerInputString', ], 'EndpointArn' => [ 'shape' => 'DocumentClassifierEndpointArn', ], 'Bytes' => [ 'shape' => 'SemiStructuredDocumentBlob', ], 'DocumentReaderConfig' => [ 'shape' => 'DocumentReaderConfig', ], ], ], 'ClassifyDocumentResponse' => [ 'type' => 'structure', 'members' => [ 'Classes' => [ 'shape' => 'ListOfClasses', ], 'Labels' => [ 'shape' => 'ListOfLabels', ], 'DocumentMetadata' => [ 'shape' => 'DocumentMetadata', ], 'DocumentType' => [ 'shape' => 'ListOfDocumentType', ], 'Errors' => [ 'shape' => 'ListOfErrors', ], 'Warnings' => [ 'shape' => 'ListOfWarnings', ], ], 'sensitive' => true, ], 'ClientRequestTokenString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'ComprehendArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws(-[^:]+)?:comprehend:[a-zA-Z0-9-]*:[0-9]{12}:[a-zA-Z0-9-]{1,64}/[a-zA-Z0-9](-*[a-zA-Z0-9])*((/dataset/[a-zA-Z0-9](-*[a-zA-Z0-9])*)|(/version/[a-zA-Z0-9](-*[a-zA-Z0-9])*))?', ], 'ComprehendArnName' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*$', ], 'ComprehendDatasetArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws(-[^:]+)?:comprehend:[a-zA-Z0-9-]*:[0-9]{12}:flywheel/[a-zA-Z0-9](-*[a-zA-Z0-9])*/dataset/[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'ComprehendEndpointArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws(-[^:]+)?:comprehend:[a-zA-Z0-9-]*:[0-9]{12}:(document-classifier-endpoint|entity-recognizer-endpoint)/[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'ComprehendEndpointName' => [ 'type' => 'string', 'max' => 40, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*$', ], 'ComprehendFlywheelArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws(-[^:]+)?:comprehend:[a-zA-Z0-9-]*:[0-9]{12}:flywheel/[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'ComprehendModelArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws(-[^:]+)?:comprehend:[a-zA-Z0-9-]*:[0-9]{12}:(document-classifier|entity-recognizer)/[a-zA-Z0-9](-*[a-zA-Z0-9])*(/version/[a-zA-Z0-9](-*[a-zA-Z0-9])*)?', ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ContainsPiiEntitiesRequest' => [ 'type' => 'structure', 'required' => [ 'Text', 'LanguageCode', ], 'members' => [ 'Text' => [ 'shape' => 'String', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'ContainsPiiEntitiesResponse' => [ 'type' => 'structure', 'members' => [ 'Labels' => [ 'shape' => 'ListOfEntityLabels', ], ], ], 'CreateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'FlywheelArn', 'DatasetName', 'InputDataConfig', ], 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], 'DatasetName' => [ 'shape' => 'ComprehendArnName', ], 'DatasetType' => [ 'shape' => 'DatasetType', ], 'Description' => [ 'shape' => 'Description', ], 'InputDataConfig' => [ 'shape' => 'DatasetInputDataConfig', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetArn' => [ 'shape' => 'ComprehendDatasetArn', ], ], ], 'CreateDocumentClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentClassifierName', 'DataAccessRoleArn', 'InputDataConfig', 'LanguageCode', ], 'members' => [ 'DocumentClassifierName' => [ 'shape' => 'ComprehendArnName', ], 'VersionName' => [ 'shape' => 'VersionName', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'InputDataConfig' => [ 'shape' => 'DocumentClassifierInputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'DocumentClassifierOutputDataConfig', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'Mode' => [ 'shape' => 'DocumentClassifierMode', ], 'ModelKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'ModelPolicy' => [ 'shape' => 'Policy', ], ], ], 'CreateDocumentClassifierResponse' => [ 'type' => 'structure', 'members' => [ 'DocumentClassifierArn' => [ 'shape' => 'DocumentClassifierArn', ], ], ], 'CreateEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', 'DesiredInferenceUnits', ], 'members' => [ 'EndpointName' => [ 'shape' => 'ComprehendEndpointName', ], 'ModelArn' => [ 'shape' => 'ComprehendModelArn', ], 'DesiredInferenceUnits' => [ 'shape' => 'InferenceUnitsInteger', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], ], ], 'CreateEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointArn' => [ 'shape' => 'ComprehendEndpointArn', ], 'ModelArn' => [ 'shape' => 'ComprehendModelArn', ], ], ], 'CreateEntityRecognizerRequest' => [ 'type' => 'structure', 'required' => [ 'RecognizerName', 'DataAccessRoleArn', 'InputDataConfig', 'LanguageCode', ], 'members' => [ 'RecognizerName' => [ 'shape' => 'ComprehendArnName', ], 'VersionName' => [ 'shape' => 'VersionName', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'InputDataConfig' => [ 'shape' => 'EntityRecognizerInputDataConfig', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'ModelKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'ModelPolicy' => [ 'shape' => 'Policy', ], ], ], 'CreateEntityRecognizerResponse' => [ 'type' => 'structure', 'members' => [ 'EntityRecognizerArn' => [ 'shape' => 'EntityRecognizerArn', ], ], ], 'CreateFlywheelRequest' => [ 'type' => 'structure', 'required' => [ 'FlywheelName', 'DataAccessRoleArn', 'DataLakeS3Uri', ], 'members' => [ 'FlywheelName' => [ 'shape' => 'ComprehendArnName', ], 'ActiveModelArn' => [ 'shape' => 'ComprehendModelArn', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'TaskConfig' => [ 'shape' => 'TaskConfig', ], 'ModelType' => [ 'shape' => 'ModelType', ], 'DataLakeS3Uri' => [ 'shape' => 'FlywheelS3Uri', ], 'DataSecurityConfig' => [ 'shape' => 'DataSecurityConfig', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateFlywheelResponse' => [ 'type' => 'structure', 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], 'ActiveModelArn' => [ 'shape' => 'ComprehendModelArn', ], ], ], 'CustomerInputString' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'CustomerInputStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomerInputString', ], 'min' => 1, 'sensitive' => true, ], 'DataSecurityConfig' => [ 'type' => 'structure', 'members' => [ 'ModelKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'DataLakeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'DatasetAugmentedManifestsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetAugmentedManifestsListItem', ], ], 'DatasetAugmentedManifestsListItem' => [ 'type' => 'structure', 'required' => [ 'AttributeNames', 'S3Uri', ], 'members' => [ 'AttributeNames' => [ 'shape' => 'AttributeNamesList', ], 'S3Uri' => [ 'shape' => 'S3Uri', ], 'AnnotationDataS3Uri' => [ 'shape' => 'S3Uri', ], 'SourceDocumentsS3Uri' => [ 'shape' => 'S3Uri', ], 'DocumentType' => [ 'shape' => 'AugmentedManifestsDocumentTypeFormat', ], ], ], 'DatasetDataFormat' => [ 'type' => 'string', 'enum' => [ 'COMPREHEND_CSV', 'AUGMENTED_MANIFEST', ], ], 'DatasetDocumentClassifierInputDataConfig' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'LabelDelimiter' => [ 'shape' => 'LabelDelimiter', ], ], ], 'DatasetEntityRecognizerAnnotations' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'DatasetEntityRecognizerDocuments' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'InputFormat' => [ 'shape' => 'InputFormat', ], ], ], 'DatasetEntityRecognizerEntityList' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'DatasetEntityRecognizerInputDataConfig' => [ 'type' => 'structure', 'required' => [ 'Documents', ], 'members' => [ 'Annotations' => [ 'shape' => 'DatasetEntityRecognizerAnnotations', ], 'Documents' => [ 'shape' => 'DatasetEntityRecognizerDocuments', ], 'EntityList' => [ 'shape' => 'DatasetEntityRecognizerEntityList', ], ], ], 'DatasetFilter' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'DatasetStatus', ], 'DatasetType' => [ 'shape' => 'DatasetType', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], ], ], 'DatasetInputDataConfig' => [ 'type' => 'structure', 'members' => [ 'AugmentedManifests' => [ 'shape' => 'DatasetAugmentedManifestsList', ], 'DataFormat' => [ 'shape' => 'DatasetDataFormat', ], 'DocumentClassifierInputDataConfig' => [ 'shape' => 'DatasetDocumentClassifierInputDataConfig', ], 'EntityRecognizerInputDataConfig' => [ 'shape' => 'DatasetEntityRecognizerInputDataConfig', ], ], ], 'DatasetProperties' => [ 'type' => 'structure', 'members' => [ 'DatasetArn' => [ 'shape' => 'ComprehendDatasetArn', ], 'DatasetName' => [ 'shape' => 'ComprehendArnName', ], 'DatasetType' => [ 'shape' => 'DatasetType', ], 'DatasetS3Uri' => [ 'shape' => 'S3Uri', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'DatasetStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'NumberOfDocuments' => [ 'shape' => 'NumberOfDocuments', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'DatasetPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetProperties', ], ], 'DatasetStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'COMPLETED', 'FAILED', ], ], 'DatasetType' => [ 'type' => 'string', 'enum' => [ 'TRAIN', 'TEST', ], ], 'DeleteDocumentClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentClassifierArn', ], 'members' => [ 'DocumentClassifierArn' => [ 'shape' => 'DocumentClassifierArn', ], ], ], 'DeleteDocumentClassifierResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'ComprehendEndpointArn', ], ], ], 'DeleteEndpointResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEntityRecognizerRequest' => [ 'type' => 'structure', 'required' => [ 'EntityRecognizerArn', ], 'members' => [ 'EntityRecognizerArn' => [ 'shape' => 'EntityRecognizerArn', ], ], ], 'DeleteEntityRecognizerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFlywheelRequest' => [ 'type' => 'structure', 'required' => [ 'FlywheelArn', ], 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], ], ], 'DeleteFlywheelResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ComprehendModelArn', ], 'PolicyRevisionId' => [ 'shape' => 'PolicyRevisionId', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetArn', ], 'members' => [ 'DatasetArn' => [ 'shape' => 'ComprehendDatasetArn', ], ], ], 'DescribeDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetProperties' => [ 'shape' => 'DatasetProperties', ], ], ], 'DescribeDocumentClassificationJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'DescribeDocumentClassificationJobResponse' => [ 'type' => 'structure', 'members' => [ 'DocumentClassificationJobProperties' => [ 'shape' => 'DocumentClassificationJobProperties', ], ], ], 'DescribeDocumentClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentClassifierArn', ], 'members' => [ 'DocumentClassifierArn' => [ 'shape' => 'DocumentClassifierArn', ], ], ], 'DescribeDocumentClassifierResponse' => [ 'type' => 'structure', 'members' => [ 'DocumentClassifierProperties' => [ 'shape' => 'DocumentClassifierProperties', ], ], ], 'DescribeDominantLanguageDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'DescribeDominantLanguageDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'DominantLanguageDetectionJobProperties' => [ 'shape' => 'DominantLanguageDetectionJobProperties', ], ], ], 'DescribeEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'ComprehendEndpointArn', ], ], ], 'DescribeEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointProperties' => [ 'shape' => 'EndpointProperties', ], ], ], 'DescribeEntitiesDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'DescribeEntitiesDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'EntitiesDetectionJobProperties' => [ 'shape' => 'EntitiesDetectionJobProperties', ], ], ], 'DescribeEntityRecognizerRequest' => [ 'type' => 'structure', 'required' => [ 'EntityRecognizerArn', ], 'members' => [ 'EntityRecognizerArn' => [ 'shape' => 'EntityRecognizerArn', ], ], ], 'DescribeEntityRecognizerResponse' => [ 'type' => 'structure', 'members' => [ 'EntityRecognizerProperties' => [ 'shape' => 'EntityRecognizerProperties', ], ], ], 'DescribeEventsDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'DescribeEventsDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'EventsDetectionJobProperties' => [ 'shape' => 'EventsDetectionJobProperties', ], ], ], 'DescribeFlywheelIterationRequest' => [ 'type' => 'structure', 'required' => [ 'FlywheelArn', 'FlywheelIterationId', ], 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], 'FlywheelIterationId' => [ 'shape' => 'FlywheelIterationId', ], ], ], 'DescribeFlywheelIterationResponse' => [ 'type' => 'structure', 'members' => [ 'FlywheelIterationProperties' => [ 'shape' => 'FlywheelIterationProperties', ], ], ], 'DescribeFlywheelRequest' => [ 'type' => 'structure', 'required' => [ 'FlywheelArn', ], 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], ], ], 'DescribeFlywheelResponse' => [ 'type' => 'structure', 'members' => [ 'FlywheelProperties' => [ 'shape' => 'FlywheelProperties', ], ], ], 'DescribeKeyPhrasesDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'DescribeKeyPhrasesDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'KeyPhrasesDetectionJobProperties' => [ 'shape' => 'KeyPhrasesDetectionJobProperties', ], ], ], 'DescribePiiEntitiesDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'DescribePiiEntitiesDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'PiiEntitiesDetectionJobProperties' => [ 'shape' => 'PiiEntitiesDetectionJobProperties', ], ], ], 'DescribeResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ComprehendModelArn', ], ], ], 'DescribeResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'ResourcePolicy' => [ 'shape' => 'Policy', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'PolicyRevisionId' => [ 'shape' => 'PolicyRevisionId', ], ], ], 'DescribeSentimentDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'DescribeSentimentDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'SentimentDetectionJobProperties' => [ 'shape' => 'SentimentDetectionJobProperties', ], ], ], 'DescribeTargetedSentimentDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'DescribeTargetedSentimentDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'TargetedSentimentDetectionJobProperties' => [ 'shape' => 'TargetedSentimentDetectionJobProperties', ], ], ], 'DescribeTopicsDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'DescribeTopicsDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'TopicsDetectionJobProperties' => [ 'shape' => 'TopicsDetectionJobProperties', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '^([a-zA-Z0-9_])[\\\\a-zA-Z0-9_@#%*+=:?./!\\s-]*$', ], 'DetectDominantLanguageRequest' => [ 'type' => 'structure', 'required' => [ 'Text', ], 'members' => [ 'Text' => [ 'shape' => 'CustomerInputString', ], ], ], 'DetectDominantLanguageResponse' => [ 'type' => 'structure', 'members' => [ 'Languages' => [ 'shape' => 'ListOfDominantLanguages', ], ], 'sensitive' => true, ], 'DetectEntitiesRequest' => [ 'type' => 'structure', 'members' => [ 'Text' => [ 'shape' => 'CustomerInputString', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'EndpointArn' => [ 'shape' => 'EntityRecognizerEndpointArn', ], 'Bytes' => [ 'shape' => 'SemiStructuredDocumentBlob', ], 'DocumentReaderConfig' => [ 'shape' => 'DocumentReaderConfig', ], ], ], 'DetectEntitiesResponse' => [ 'type' => 'structure', 'members' => [ 'Entities' => [ 'shape' => 'ListOfEntities', ], 'DocumentMetadata' => [ 'shape' => 'DocumentMetadata', ], 'DocumentType' => [ 'shape' => 'ListOfDocumentType', ], 'Blocks' => [ 'shape' => 'ListOfBlocks', ], 'Errors' => [ 'shape' => 'ListOfErrors', ], ], 'sensitive' => true, ], 'DetectKeyPhrasesRequest' => [ 'type' => 'structure', 'required' => [ 'Text', 'LanguageCode', ], 'members' => [ 'Text' => [ 'shape' => 'CustomerInputString', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'DetectKeyPhrasesResponse' => [ 'type' => 'structure', 'members' => [ 'KeyPhrases' => [ 'shape' => 'ListOfKeyPhrases', ], ], 'sensitive' => true, ], 'DetectPiiEntitiesRequest' => [ 'type' => 'structure', 'required' => [ 'Text', 'LanguageCode', ], 'members' => [ 'Text' => [ 'shape' => 'String', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'DetectPiiEntitiesResponse' => [ 'type' => 'structure', 'members' => [ 'Entities' => [ 'shape' => 'ListOfPiiEntities', ], ], ], 'DetectSentimentRequest' => [ 'type' => 'structure', 'required' => [ 'Text', 'LanguageCode', ], 'members' => [ 'Text' => [ 'shape' => 'CustomerInputString', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'DetectSentimentResponse' => [ 'type' => 'structure', 'members' => [ 'Sentiment' => [ 'shape' => 'SentimentType', ], 'SentimentScore' => [ 'shape' => 'SentimentScore', ], ], 'sensitive' => true, ], 'DetectSyntaxRequest' => [ 'type' => 'structure', 'required' => [ 'Text', 'LanguageCode', ], 'members' => [ 'Text' => [ 'shape' => 'CustomerInputString', ], 'LanguageCode' => [ 'shape' => 'SyntaxLanguageCode', ], ], ], 'DetectSyntaxResponse' => [ 'type' => 'structure', 'members' => [ 'SyntaxTokens' => [ 'shape' => 'ListOfSyntaxTokens', ], ], 'sensitive' => true, ], 'DetectTargetedSentimentRequest' => [ 'type' => 'structure', 'required' => [ 'Text', 'LanguageCode', ], 'members' => [ 'Text' => [ 'shape' => 'CustomerInputString', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'DetectTargetedSentimentResponse' => [ 'type' => 'structure', 'members' => [ 'Entities' => [ 'shape' => 'ListOfTargetedSentimentEntities', ], ], 'sensitive' => true, ], 'DetectToxicContentRequest' => [ 'type' => 'structure', 'required' => [ 'TextSegments', 'LanguageCode', ], 'members' => [ 'TextSegments' => [ 'shape' => 'ListOfTextSegments', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'DetectToxicContentResponse' => [ 'type' => 'structure', 'members' => [ 'ResultList' => [ 'shape' => 'ListOfToxicLabels', ], ], ], 'DocumentClass' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Score' => [ 'shape' => 'Float', ], 'Page' => [ 'shape' => 'Integer', ], ], ], 'DocumentClassificationConfig' => [ 'type' => 'structure', 'required' => [ 'Mode', ], 'members' => [ 'Mode' => [ 'shape' => 'DocumentClassifierMode', ], 'Labels' => [ 'shape' => 'LabelsList', ], ], ], 'DocumentClassificationJobFilter' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'SubmitTimeBefore' => [ 'shape' => 'Timestamp', ], 'SubmitTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'DocumentClassificationJobProperties' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'SubmitTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'DocumentClassifierArn' => [ 'shape' => 'DocumentClassifierArn', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], ], ], 'DocumentClassificationJobPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentClassificationJobProperties', ], ], 'DocumentClassifierArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws(-[^:]+)?:comprehend:[a-zA-Z0-9-]*:[0-9]{12}:document-classifier/[a-zA-Z0-9](-*[a-zA-Z0-9])*(/version/[a-zA-Z0-9](-*[a-zA-Z0-9])*)?', ], 'DocumentClassifierAugmentedManifestsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AugmentedManifestsListItem', ], ], 'DocumentClassifierDataFormat' => [ 'type' => 'string', 'enum' => [ 'COMPREHEND_CSV', 'AUGMENTED_MANIFEST', ], ], 'DocumentClassifierDocumentTypeFormat' => [ 'type' => 'string', 'enum' => [ 'PLAIN_TEXT_DOCUMENT', 'SEMI_STRUCTURED_DOCUMENT', ], ], 'DocumentClassifierDocuments' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'TestS3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'DocumentClassifierEndpointArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws(-[^:]+)?:comprehend:[a-zA-Z0-9-]*:([0-9]{12}|aws):document-classifier-endpoint/[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'DocumentClassifierFilter' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'ModelStatus', ], 'DocumentClassifierName' => [ 'shape' => 'ComprehendArnName', ], 'SubmitTimeBefore' => [ 'shape' => 'Timestamp', ], 'SubmitTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'DocumentClassifierInputDataConfig' => [ 'type' => 'structure', 'members' => [ 'DataFormat' => [ 'shape' => 'DocumentClassifierDataFormat', ], 'S3Uri' => [ 'shape' => 'S3Uri', ], 'TestS3Uri' => [ 'shape' => 'S3Uri', ], 'LabelDelimiter' => [ 'shape' => 'LabelDelimiter', ], 'AugmentedManifests' => [ 'shape' => 'DocumentClassifierAugmentedManifestsList', ], 'DocumentType' => [ 'shape' => 'DocumentClassifierDocumentTypeFormat', ], 'Documents' => [ 'shape' => 'DocumentClassifierDocuments', ], 'DocumentReaderConfig' => [ 'shape' => 'DocumentReaderConfig', ], ], ], 'DocumentClassifierMode' => [ 'type' => 'string', 'enum' => [ 'MULTI_CLASS', 'MULTI_LABEL', ], ], 'DocumentClassifierOutputDataConfig' => [ 'type' => 'structure', 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'FlywheelStatsS3Prefix' => [ 'shape' => 'S3Uri', ], ], ], 'DocumentClassifierProperties' => [ 'type' => 'structure', 'members' => [ 'DocumentClassifierArn' => [ 'shape' => 'DocumentClassifierArn', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'Status' => [ 'shape' => 'ModelStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'SubmitTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'TrainingStartTime' => [ 'shape' => 'Timestamp', ], 'TrainingEndTime' => [ 'shape' => 'Timestamp', ], 'InputDataConfig' => [ 'shape' => 'DocumentClassifierInputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'DocumentClassifierOutputDataConfig', ], 'ClassifierMetadata' => [ 'shape' => 'ClassifierMetadata', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'Mode' => [ 'shape' => 'DocumentClassifierMode', ], 'ModelKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VersionName' => [ 'shape' => 'VersionName', ], 'SourceModelArn' => [ 'shape' => 'DocumentClassifierArn', ], 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], ], ], 'DocumentClassifierPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentClassifierProperties', ], ], 'DocumentClassifierSummariesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentClassifierSummary', ], ], 'DocumentClassifierSummary' => [ 'type' => 'structure', 'members' => [ 'DocumentClassifierName' => [ 'shape' => 'ComprehendArnName', ], 'NumberOfVersions' => [ 'shape' => 'Integer', ], 'LatestVersionCreatedAt' => [ 'shape' => 'Timestamp', ], 'LatestVersionName' => [ 'shape' => 'VersionName', ], 'LatestVersionStatus' => [ 'shape' => 'ModelStatus', ], ], ], 'DocumentLabel' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Score' => [ 'shape' => 'Float', ], 'Page' => [ 'shape' => 'Integer', ], ], ], 'DocumentMetadata' => [ 'type' => 'structure', 'members' => [ 'Pages' => [ 'shape' => 'Integer', ], 'ExtractedCharacters' => [ 'shape' => 'ListOfExtractedCharacters', ], ], ], 'DocumentReadAction' => [ 'type' => 'string', 'enum' => [ 'TEXTRACT_DETECT_DOCUMENT_TEXT', 'TEXTRACT_ANALYZE_DOCUMENT', ], ], 'DocumentReadFeatureTypes' => [ 'type' => 'string', 'enum' => [ 'TABLES', 'FORMS', ], ], 'DocumentReadMode' => [ 'type' => 'string', 'enum' => [ 'SERVICE_DEFAULT', 'FORCE_DOCUMENT_READ_ACTION', ], ], 'DocumentReaderConfig' => [ 'type' => 'structure', 'required' => [ 'DocumentReadAction', ], 'members' => [ 'DocumentReadAction' => [ 'shape' => 'DocumentReadAction', ], 'DocumentReadMode' => [ 'shape' => 'DocumentReadMode', ], 'FeatureTypes' => [ 'shape' => 'ListOfDocumentReadFeatureTypes', ], ], ], 'DocumentType' => [ 'type' => 'string', 'enum' => [ 'NATIVE_PDF', 'SCANNED_PDF', 'MS_WORD', 'IMAGE', 'PLAIN_TEXT', 'TEXTRACT_DETECT_DOCUMENT_TEXT_JSON', 'TEXTRACT_ANALYZE_DOCUMENT_JSON', ], ], 'DocumentTypeListItem' => [ 'type' => 'structure', 'members' => [ 'Page' => [ 'shape' => 'Integer', ], 'Type' => [ 'shape' => 'DocumentType', ], ], ], 'DominantLanguage' => [ 'type' => 'structure', 'members' => [ 'LanguageCode' => [ 'shape' => 'String', ], 'Score' => [ 'shape' => 'Float', ], ], ], 'DominantLanguageDetectionJobFilter' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'SubmitTimeBefore' => [ 'shape' => 'Timestamp', ], 'SubmitTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'DominantLanguageDetectionJobProperties' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'SubmitTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'DominantLanguageDetectionJobPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DominantLanguageDetectionJobProperties', ], ], 'Double' => [ 'type' => 'double', ], 'EndpointFilter' => [ 'type' => 'structure', 'members' => [ 'ModelArn' => [ 'shape' => 'ComprehendModelArn', ], 'Status' => [ 'shape' => 'EndpointStatus', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'EndpointProperties' => [ 'type' => 'structure', 'members' => [ 'EndpointArn' => [ 'shape' => 'ComprehendEndpointArn', ], 'Status' => [ 'shape' => 'EndpointStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'ModelArn' => [ 'shape' => 'ComprehendModelArn', ], 'DesiredModelArn' => [ 'shape' => 'ComprehendModelArn', ], 'DesiredInferenceUnits' => [ 'shape' => 'InferenceUnitsInteger', ], 'CurrentInferenceUnits' => [ 'shape' => 'InferenceUnitsInteger', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'DesiredDataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], ], ], 'EndpointPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointProperties', ], ], 'EndpointStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'DELETING', 'FAILED', 'IN_SERVICE', 'UPDATING', ], ], 'EntitiesDetectionJobFilter' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'SubmitTimeBefore' => [ 'shape' => 'Timestamp', ], 'SubmitTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'EntitiesDetectionJobProperties' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'SubmitTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'EntityRecognizerArn' => [ 'shape' => 'EntityRecognizerArn', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], ], ], 'EntitiesDetectionJobPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntitiesDetectionJobProperties', ], ], 'Entity' => [ 'type' => 'structure', 'members' => [ 'Score' => [ 'shape' => 'Float', ], 'Type' => [ 'shape' => 'EntityType', ], 'Text' => [ 'shape' => 'String', ], 'BeginOffset' => [ 'shape' => 'Integer', ], 'EndOffset' => [ 'shape' => 'Integer', ], 'BlockReferences' => [ 'shape' => 'ListOfBlockReferences', ], ], ], 'EntityLabel' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PiiEntityType', ], 'Score' => [ 'shape' => 'Float', ], ], ], 'EntityRecognitionConfig' => [ 'type' => 'structure', 'required' => [ 'EntityTypes', ], 'members' => [ 'EntityTypes' => [ 'shape' => 'EntityTypesList', ], ], ], 'EntityRecognizerAnnotations' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'TestS3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'EntityRecognizerArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws(-[^:]+)?:comprehend:[a-zA-Z0-9-]*:[0-9]{12}:entity-recognizer/[a-zA-Z0-9](-*[a-zA-Z0-9])*(/version/[a-zA-Z0-9](-*[a-zA-Z0-9])*)?', ], 'EntityRecognizerAugmentedManifestsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AugmentedManifestsListItem', ], ], 'EntityRecognizerDataFormat' => [ 'type' => 'string', 'enum' => [ 'COMPREHEND_CSV', 'AUGMENTED_MANIFEST', ], ], 'EntityRecognizerDocuments' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'TestS3Uri' => [ 'shape' => 'S3Uri', ], 'InputFormat' => [ 'shape' => 'InputFormat', ], ], ], 'EntityRecognizerEndpointArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws(-[^:]+)?:comprehend:[a-zA-Z0-9-]*:[0-9]{12}:entity-recognizer-endpoint/[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'EntityRecognizerEntityList' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'EntityRecognizerEvaluationMetrics' => [ 'type' => 'structure', 'members' => [ 'Precision' => [ 'shape' => 'Double', ], 'Recall' => [ 'shape' => 'Double', ], 'F1Score' => [ 'shape' => 'Double', ], ], ], 'EntityRecognizerFilter' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'ModelStatus', ], 'RecognizerName' => [ 'shape' => 'ComprehendArnName', ], 'SubmitTimeBefore' => [ 'shape' => 'Timestamp', ], 'SubmitTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'EntityRecognizerInputDataConfig' => [ 'type' => 'structure', 'required' => [ 'EntityTypes', ], 'members' => [ 'DataFormat' => [ 'shape' => 'EntityRecognizerDataFormat', ], 'EntityTypes' => [ 'shape' => 'EntityTypesList', ], 'Documents' => [ 'shape' => 'EntityRecognizerDocuments', ], 'Annotations' => [ 'shape' => 'EntityRecognizerAnnotations', ], 'EntityList' => [ 'shape' => 'EntityRecognizerEntityList', ], 'AugmentedManifests' => [ 'shape' => 'EntityRecognizerAugmentedManifestsList', ], ], ], 'EntityRecognizerMetadata' => [ 'type' => 'structure', 'members' => [ 'NumberOfTrainedDocuments' => [ 'shape' => 'Integer', ], 'NumberOfTestDocuments' => [ 'shape' => 'Integer', ], 'EvaluationMetrics' => [ 'shape' => 'EntityRecognizerEvaluationMetrics', ], 'EntityTypes' => [ 'shape' => 'EntityRecognizerMetadataEntityTypesList', ], ], 'sensitive' => true, ], 'EntityRecognizerMetadataEntityTypesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityRecognizerMetadataEntityTypesListItem', ], ], 'EntityRecognizerMetadataEntityTypesListItem' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'AnyLengthString', ], 'EvaluationMetrics' => [ 'shape' => 'EntityTypesEvaluationMetrics', ], 'NumberOfTrainMentions' => [ 'shape' => 'Integer', ], ], ], 'EntityRecognizerOutputDataConfig' => [ 'type' => 'structure', 'members' => [ 'FlywheelStatsS3Prefix' => [ 'shape' => 'S3Uri', ], ], ], 'EntityRecognizerProperties' => [ 'type' => 'structure', 'members' => [ 'EntityRecognizerArn' => [ 'shape' => 'EntityRecognizerArn', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'Status' => [ 'shape' => 'ModelStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'SubmitTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'TrainingStartTime' => [ 'shape' => 'Timestamp', ], 'TrainingEndTime' => [ 'shape' => 'Timestamp', ], 'InputDataConfig' => [ 'shape' => 'EntityRecognizerInputDataConfig', ], 'RecognizerMetadata' => [ 'shape' => 'EntityRecognizerMetadata', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'ModelKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VersionName' => [ 'shape' => 'VersionName', ], 'SourceModelArn' => [ 'shape' => 'EntityRecognizerArn', ], 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], 'OutputDataConfig' => [ 'shape' => 'EntityRecognizerOutputDataConfig', ], ], ], 'EntityRecognizerPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityRecognizerProperties', ], ], 'EntityRecognizerSummariesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityRecognizerSummary', ], ], 'EntityRecognizerSummary' => [ 'type' => 'structure', 'members' => [ 'RecognizerName' => [ 'shape' => 'ComprehendArnName', ], 'NumberOfVersions' => [ 'shape' => 'Integer', ], 'LatestVersionCreatedAt' => [ 'shape' => 'Timestamp', ], 'LatestVersionName' => [ 'shape' => 'VersionName', ], 'LatestVersionStatus' => [ 'shape' => 'ModelStatus', ], ], ], 'EntityType' => [ 'type' => 'string', 'enum' => [ 'PERSON', 'LOCATION', 'ORGANIZATION', 'COMMERCIAL_ITEM', 'EVENT', 'DATE', 'QUANTITY', 'TITLE', 'OTHER', ], ], 'EntityTypeName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '^(?![^\\n\\r\\t,]*\\\\n|\\\\r|\\\\t)[^\\n\\r\\t,]+$', ], 'EntityTypesEvaluationMetrics' => [ 'type' => 'structure', 'members' => [ 'Precision' => [ 'shape' => 'Double', ], 'Recall' => [ 'shape' => 'Double', ], 'F1Score' => [ 'shape' => 'Double', ], ], ], 'EntityTypesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityTypesListItem', ], ], 'EntityTypesListItem' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'EntityTypeName', ], ], ], 'ErrorsListItem' => [ 'type' => 'structure', 'members' => [ 'Page' => [ 'shape' => 'Integer', ], 'ErrorCode' => [ 'shape' => 'PageBasedErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'EventTypeString' => [ 'type' => 'string', 'max' => 40, 'min' => 1, 'pattern' => '[A-Z_]*', ], 'EventsDetectionJobFilter' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'SubmitTimeBefore' => [ 'shape' => 'Timestamp', ], 'SubmitTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'EventsDetectionJobProperties' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'SubmitTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'TargetEventTypes' => [ 'shape' => 'TargetEventTypes', ], ], ], 'EventsDetectionJobPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventsDetectionJobProperties', ], ], 'ExtractedCharactersListItem' => [ 'type' => 'structure', 'members' => [ 'Page' => [ 'shape' => 'Integer', ], 'Count' => [ 'shape' => 'Integer', ], ], ], 'Float' => [ 'type' => 'float', ], 'FlywheelFilter' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'FlywheelStatus', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], ], ], 'FlywheelIterationFilter' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], ], ], 'FlywheelIterationId' => [ 'type' => 'string', 'max' => 63, 'pattern' => '[0-9]{8}T[0-9]{6}Z', ], 'FlywheelIterationProperties' => [ 'type' => 'structure', 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], 'FlywheelIterationId' => [ 'shape' => 'FlywheelIterationId', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'FlywheelIterationStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'EvaluatedModelArn' => [ 'shape' => 'ComprehendModelArn', ], 'EvaluatedModelMetrics' => [ 'shape' => 'FlywheelModelEvaluationMetrics', ], 'TrainedModelArn' => [ 'shape' => 'ComprehendModelArn', ], 'TrainedModelMetrics' => [ 'shape' => 'FlywheelModelEvaluationMetrics', ], 'EvaluationManifestS3Prefix' => [ 'shape' => 'S3Uri', ], ], ], 'FlywheelIterationPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlywheelIterationProperties', ], ], 'FlywheelIterationStatus' => [ 'type' => 'string', 'enum' => [ 'TRAINING', 'EVALUATING', 'COMPLETED', 'FAILED', 'STOP_REQUESTED', 'STOPPED', ], ], 'FlywheelModelEvaluationMetrics' => [ 'type' => 'structure', 'members' => [ 'AverageF1Score' => [ 'shape' => 'Double', ], 'AveragePrecision' => [ 'shape' => 'Double', ], 'AverageRecall' => [ 'shape' => 'Double', ], 'AverageAccuracy' => [ 'shape' => 'Double', ], ], ], 'FlywheelProperties' => [ 'type' => 'structure', 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], 'ActiveModelArn' => [ 'shape' => 'ComprehendModelArn', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'TaskConfig' => [ 'shape' => 'TaskConfig', ], 'DataLakeS3Uri' => [ 'shape' => 'S3Uri', ], 'DataSecurityConfig' => [ 'shape' => 'DataSecurityConfig', ], 'Status' => [ 'shape' => 'FlywheelStatus', ], 'ModelType' => [ 'shape' => 'ModelType', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LatestFlywheelIteration' => [ 'shape' => 'FlywheelIterationId', ], ], ], 'FlywheelS3Uri' => [ 'type' => 'string', 'max' => 512, 'pattern' => 's3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/.*)?', ], 'FlywheelStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'DELETING', 'FAILED', ], ], 'FlywheelSummary' => [ 'type' => 'structure', 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], 'ActiveModelArn' => [ 'shape' => 'ComprehendModelArn', ], 'DataLakeS3Uri' => [ 'shape' => 'S3Uri', ], 'Status' => [ 'shape' => 'FlywheelStatus', ], 'ModelType' => [ 'shape' => 'ModelType', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LatestFlywheelIteration' => [ 'shape' => 'FlywheelIterationId', ], ], ], 'FlywheelSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlywheelSummary', ], ], 'Geometry' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Polygon' => [ 'shape' => 'Polygon', ], ], ], 'IamRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:iam::[0-9]{12}:role/.+', ], 'ImportModelRequest' => [ 'type' => 'structure', 'required' => [ 'SourceModelArn', ], 'members' => [ 'SourceModelArn' => [ 'shape' => 'ComprehendModelArn', ], 'ModelName' => [ 'shape' => 'ComprehendArnName', ], 'VersionName' => [ 'shape' => 'VersionName', ], 'ModelKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ImportModelResponse' => [ 'type' => 'structure', 'members' => [ 'ModelArn' => [ 'shape' => 'ComprehendModelArn', ], ], ], 'InferenceUnitsInteger' => [ 'type' => 'integer', 'min' => 1, ], 'InputDataConfig' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'InputFormat' => [ 'shape' => 'InputFormat', ], 'DocumentReaderConfig' => [ 'shape' => 'DocumentReaderConfig', ], ], ], 'InputFormat' => [ 'type' => 'string', 'enum' => [ 'ONE_DOC_PER_FILE', 'ONE_DOC_PER_LINE', ], ], 'Integer' => [ 'type' => 'integer', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, ], 'InvalidFilterException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidRequestDetail' => [ 'type' => 'structure', 'members' => [ 'Reason' => [ 'shape' => 'InvalidRequestDetailReason', ], ], ], 'InvalidRequestDetailReason' => [ 'type' => 'string', 'enum' => [ 'DOCUMENT_SIZE_EXCEEDED', 'UNSUPPORTED_DOC_TYPE', 'PAGE_LIMIT_EXCEEDED', 'TEXTRACT_ACCESS_DENIED', ], ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'InvalidRequestReason', ], 'Detail' => [ 'shape' => 'InvalidRequestDetail', ], ], 'exception' => true, ], 'InvalidRequestReason' => [ 'type' => 'string', 'enum' => [ 'INVALID_DOCUMENT', ], ], 'JobId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-%@]*)$', ], 'JobName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-%@]*)$', ], 'JobNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'STOP_REQUESTED', 'STOPPED', ], ], 'KeyPhrase' => [ 'type' => 'structure', 'members' => [ 'Score' => [ 'shape' => 'Float', ], 'Text' => [ 'shape' => 'String', ], 'BeginOffset' => [ 'shape' => 'Integer', ], 'EndOffset' => [ 'shape' => 'Integer', ], ], ], 'KeyPhrasesDetectionJobFilter' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'SubmitTimeBefore' => [ 'shape' => 'Timestamp', ], 'SubmitTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'KeyPhrasesDetectionJobProperties' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'SubmitTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'KeyPhrasesDetectionJobPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyPhrasesDetectionJobProperties', ], ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '^\\p{ASCII}+$', ], 'KmsKeyValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'LabelDelimiter' => [ 'type' => 'string', 'max' => 1, 'min' => 1, 'pattern' => '^[ ~!@#$%^*\\-_+=|\\\\:;\\t>?/]$', ], 'LabelListItem' => [ 'type' => 'string', 'max' => 5000, 'pattern' => '^\\P{C}*$', ], 'LabelsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LabelListItem', ], 'max' => 1000, ], 'LanguageCode' => [ 'type' => 'string', 'enum' => [ 'en', 'es', 'fr', 'de', 'it', 'pt', 'ar', 'hi', 'ja', 'ko', 'zh', 'zh-TW', ], ], 'ListDatasetsRequest' => [ 'type' => 'structure', 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], 'Filter' => [ 'shape' => 'DatasetFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListDatasetsResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetPropertiesList' => [ 'shape' => 'DatasetPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListDocumentClassificationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'DocumentClassificationJobFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListDocumentClassificationJobsResponse' => [ 'type' => 'structure', 'members' => [ 'DocumentClassificationJobPropertiesList' => [ 'shape' => 'DocumentClassificationJobPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListDocumentClassifierSummariesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListDocumentClassifierSummariesResponse' => [ 'type' => 'structure', 'members' => [ 'DocumentClassifierSummariesList' => [ 'shape' => 'DocumentClassifierSummariesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListDocumentClassifiersRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'DocumentClassifierFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListDocumentClassifiersResponse' => [ 'type' => 'structure', 'members' => [ 'DocumentClassifierPropertiesList' => [ 'shape' => 'DocumentClassifierPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListDominantLanguageDetectionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'DominantLanguageDetectionJobFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListDominantLanguageDetectionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'DominantLanguageDetectionJobPropertiesList' => [ 'shape' => 'DominantLanguageDetectionJobPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'EndpointFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointPropertiesList' => [ 'shape' => 'EndpointPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListEntitiesDetectionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'EntitiesDetectionJobFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListEntitiesDetectionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'EntitiesDetectionJobPropertiesList' => [ 'shape' => 'EntitiesDetectionJobPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListEntityRecognizerSummariesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListEntityRecognizerSummariesResponse' => [ 'type' => 'structure', 'members' => [ 'EntityRecognizerSummariesList' => [ 'shape' => 'EntityRecognizerSummariesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListEntityRecognizersRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'EntityRecognizerFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListEntityRecognizersResponse' => [ 'type' => 'structure', 'members' => [ 'EntityRecognizerPropertiesList' => [ 'shape' => 'EntityRecognizerPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListEventsDetectionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'EventsDetectionJobFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListEventsDetectionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'EventsDetectionJobPropertiesList' => [ 'shape' => 'EventsDetectionJobPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListFlywheelIterationHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'FlywheelArn', ], 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], 'Filter' => [ 'shape' => 'FlywheelIterationFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListFlywheelIterationHistoryResponse' => [ 'type' => 'structure', 'members' => [ 'FlywheelIterationPropertiesList' => [ 'shape' => 'FlywheelIterationPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListFlywheelsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'FlywheelFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListFlywheelsResponse' => [ 'type' => 'structure', 'members' => [ 'FlywheelSummaryList' => [ 'shape' => 'FlywheelSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListKeyPhrasesDetectionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'KeyPhrasesDetectionJobFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListKeyPhrasesDetectionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'KeyPhrasesDetectionJobPropertiesList' => [ 'shape' => 'KeyPhrasesDetectionJobPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListOfBlockReferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlockReference', ], ], 'ListOfBlocks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Block', ], ], 'ListOfChildBlocks' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChildBlock', ], ], 'ListOfClasses' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentClass', ], ], 'ListOfDescriptiveMentionIndices' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], ], 'ListOfDetectDominantLanguageResult' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDetectDominantLanguageItemResult', ], ], 'ListOfDetectEntitiesResult' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDetectEntitiesItemResult', ], ], 'ListOfDetectKeyPhrasesResult' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDetectKeyPhrasesItemResult', ], ], 'ListOfDetectSentimentResult' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDetectSentimentItemResult', ], ], 'ListOfDetectSyntaxResult' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDetectSyntaxItemResult', ], ], 'ListOfDetectTargetedSentimentResult' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDetectTargetedSentimentItemResult', ], ], 'ListOfDocumentReadFeatureTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentReadFeatureTypes', ], 'max' => 2, 'min' => 1, ], 'ListOfDocumentType' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentTypeListItem', ], ], 'ListOfDominantLanguages' => [ 'type' => 'list', 'member' => [ 'shape' => 'DominantLanguage', ], ], 'ListOfEntities' => [ 'type' => 'list', 'member' => [ 'shape' => 'Entity', ], ], 'ListOfEntityLabels' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityLabel', ], ], 'ListOfErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorsListItem', ], ], 'ListOfExtractedCharacters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExtractedCharactersListItem', ], ], 'ListOfKeyPhrases' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyPhrase', ], ], 'ListOfLabels' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentLabel', ], ], 'ListOfMentions' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetedSentimentMention', ], ], 'ListOfPiiEntities' => [ 'type' => 'list', 'member' => [ 'shape' => 'PiiEntity', ], ], 'ListOfPiiEntityTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'PiiEntityType', ], ], 'ListOfRelationships' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelationshipsListItem', ], ], 'ListOfSyntaxTokens' => [ 'type' => 'list', 'member' => [ 'shape' => 'SyntaxToken', ], ], 'ListOfTargetedSentimentEntities' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetedSentimentEntity', ], ], 'ListOfTextSegments' => [ 'type' => 'list', 'member' => [ 'shape' => 'TextSegment', ], 'min' => 1, 'sensitive' => true, ], 'ListOfToxicContent' => [ 'type' => 'list', 'member' => [ 'shape' => 'ToxicContent', ], ], 'ListOfToxicLabels' => [ 'type' => 'list', 'member' => [ 'shape' => 'ToxicLabels', ], ], 'ListOfWarnings' => [ 'type' => 'list', 'member' => [ 'shape' => 'WarningsListItem', ], ], 'ListPiiEntitiesDetectionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'PiiEntitiesDetectionJobFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListPiiEntitiesDetectionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'PiiEntitiesDetectionJobPropertiesList' => [ 'shape' => 'PiiEntitiesDetectionJobPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListSentimentDetectionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'SentimentDetectionJobFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListSentimentDetectionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'SentimentDetectionJobPropertiesList' => [ 'shape' => 'SentimentDetectionJobPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ComprehendArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ComprehendArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTargetedSentimentDetectionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'TargetedSentimentDetectionJobFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListTargetedSentimentDetectionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'TargetedSentimentDetectionJobPropertiesList' => [ 'shape' => 'TargetedSentimentDetectionJobPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTopicsDetectionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'TopicsDetectionJobFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], ], ], 'ListTopicsDetectionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'TopicsDetectionJobPropertiesList' => [ 'shape' => 'TopicsDetectionJobPropertiesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'MaskCharacter' => [ 'type' => 'string', 'max' => 1, 'min' => 1, 'pattern' => '[!@#$%&*]', ], 'MaxResultsInteger' => [ 'type' => 'integer', 'max' => 500, 'min' => 1, ], 'MentionSentiment' => [ 'type' => 'structure', 'members' => [ 'Sentiment' => [ 'shape' => 'SentimentType', ], 'SentimentScore' => [ 'shape' => 'SentimentScore', ], ], ], 'ModelStatus' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'TRAINING', 'DELETING', 'STOP_REQUESTED', 'STOPPED', 'IN_ERROR', 'TRAINED', 'TRAINED_WITH_WARNING', ], ], 'ModelType' => [ 'type' => 'string', 'enum' => [ 'DOCUMENT_CLASSIFIER', 'ENTITY_RECOGNIZER', ], ], 'NumberOfDocuments' => [ 'type' => 'long', ], 'NumberOfTopicsInteger' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'OutputDataConfig' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'PageBasedErrorCode' => [ 'type' => 'string', 'enum' => [ 'TEXTRACT_BAD_PAGE', 'TEXTRACT_PROVISIONED_THROUGHPUT_EXCEEDED', 'PAGE_CHARACTERS_EXCEEDED', 'PAGE_SIZE_EXCEEDED', 'INTERNAL_SERVER_ERROR', ], ], 'PageBasedWarningCode' => [ 'type' => 'string', 'enum' => [ 'INFERENCING_PLAINTEXT_WITH_NATIVE_TRAINED_MODEL', 'INFERENCING_NATIVE_DOCUMENT_WITH_PLAINTEXT_TRAINED_MODEL', ], ], 'PartOfSpeechTag' => [ 'type' => 'structure', 'members' => [ 'Tag' => [ 'shape' => 'PartOfSpeechTagType', ], 'Score' => [ 'shape' => 'Float', ], ], ], 'PartOfSpeechTagType' => [ 'type' => 'string', 'enum' => [ 'ADJ', 'ADP', 'ADV', 'AUX', 'CONJ', 'CCONJ', 'DET', 'INTJ', 'NOUN', 'NUM', 'O', 'PART', 'PRON', 'PROPN', 'PUNCT', 'SCONJ', 'SYM', 'VERB', ], ], 'PiiEntitiesDetectionJobFilter' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'SubmitTimeBefore' => [ 'shape' => 'Timestamp', ], 'SubmitTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'PiiEntitiesDetectionJobProperties' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'SubmitTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'PiiOutputDataConfig', ], 'RedactionConfig' => [ 'shape' => 'RedactionConfig', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'Mode' => [ 'shape' => 'PiiEntitiesDetectionMode', ], ], ], 'PiiEntitiesDetectionJobPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PiiEntitiesDetectionJobProperties', ], ], 'PiiEntitiesDetectionMaskMode' => [ 'type' => 'string', 'enum' => [ 'MASK', 'REPLACE_WITH_PII_ENTITY_TYPE', ], ], 'PiiEntitiesDetectionMode' => [ 'type' => 'string', 'enum' => [ 'ONLY_REDACTION', 'ONLY_OFFSETS', ], ], 'PiiEntity' => [ 'type' => 'structure', 'members' => [ 'Score' => [ 'shape' => 'Float', ], 'Type' => [ 'shape' => 'PiiEntityType', ], 'BeginOffset' => [ 'shape' => 'Integer', ], 'EndOffset' => [ 'shape' => 'Integer', ], ], ], 'PiiEntityType' => [ 'type' => 'string', 'enum' => [ 'BANK_ACCOUNT_NUMBER', 'BANK_ROUTING', 'CREDIT_DEBIT_NUMBER', 'CREDIT_DEBIT_CVV', 'CREDIT_DEBIT_EXPIRY', 'PIN', 'EMAIL', 'ADDRESS', 'NAME', 'PHONE', 'SSN', 'DATE_TIME', 'PASSPORT_NUMBER', 'DRIVER_ID', 'URL', 'AGE', 'USERNAME', 'PASSWORD', 'AWS_ACCESS_KEY', 'AWS_SECRET_KEY', 'IP_ADDRESS', 'MAC_ADDRESS', 'ALL', 'LICENSE_PLATE', 'VEHICLE_IDENTIFICATION_NUMBER', 'UK_NATIONAL_INSURANCE_NUMBER', 'CA_SOCIAL_INSURANCE_NUMBER', 'US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER', 'UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER', 'IN_PERMANENT_ACCOUNT_NUMBER', 'IN_NREGA', 'INTERNATIONAL_BANK_ACCOUNT_NUMBER', 'SWIFT_CODE', 'UK_NATIONAL_HEALTH_SERVICE_NUMBER', 'CA_HEALTH_NUMBER', 'IN_AADHAAR', 'IN_VOTER_NUMBER', ], ], 'PiiOutputDataConfig' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'Point' => [ 'type' => 'structure', 'members' => [ 'X' => [ 'shape' => 'Float', ], 'Y' => [ 'shape' => 'Float', ], ], ], 'Policy' => [ 'type' => 'string', 'max' => 20000, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u00FF]+', ], 'PolicyRevisionId' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[0-9A-Fa-f]+', ], 'Polygon' => [ 'type' => 'list', 'member' => [ 'shape' => 'Point', ], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'ResourcePolicy', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ComprehendModelArn', ], 'ResourcePolicy' => [ 'shape' => 'Policy', ], 'PolicyRevisionId' => [ 'shape' => 'PolicyRevisionId', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyRevisionId' => [ 'shape' => 'PolicyRevisionId', ], ], ], 'RedactionConfig' => [ 'type' => 'structure', 'members' => [ 'PiiEntityTypes' => [ 'shape' => 'ListOfPiiEntityTypes', ], 'MaskMode' => [ 'shape' => 'PiiEntitiesDetectionMaskMode', ], 'MaskCharacter' => [ 'shape' => 'MaskCharacter', ], ], ], 'RelationshipType' => [ 'type' => 'string', 'enum' => [ 'CHILD', ], ], 'RelationshipsListItem' => [ 'type' => 'structure', 'members' => [ 'Ids' => [ 'shape' => 'StringList', ], 'Type' => [ 'shape' => 'RelationshipType', ], ], ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'S3Uri' => [ 'type' => 'string', 'max' => 1024, 'pattern' => 's3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/.*)?', ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[-0-9a-zA-Z]+', ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 1, ], 'SemiStructuredDocumentBlob' => [ 'type' => 'blob', 'min' => 1, ], 'SentimentDetectionJobFilter' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'SubmitTimeBefore' => [ 'shape' => 'Timestamp', ], 'SubmitTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'SentimentDetectionJobProperties' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'SubmitTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'SentimentDetectionJobPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SentimentDetectionJobProperties', ], ], 'SentimentScore' => [ 'type' => 'structure', 'members' => [ 'Positive' => [ 'shape' => 'Float', ], 'Negative' => [ 'shape' => 'Float', ], 'Neutral' => [ 'shape' => 'Float', ], 'Mixed' => [ 'shape' => 'Float', ], ], ], 'SentimentType' => [ 'type' => 'string', 'enum' => [ 'POSITIVE', 'NEGATIVE', 'NEUTRAL', 'MIXED', ], ], 'Split' => [ 'type' => 'string', 'enum' => [ 'TRAIN', 'TEST', ], ], 'StartDocumentClassificationJobRequest' => [ 'type' => 'structure', 'required' => [ 'InputDataConfig', 'OutputDataConfig', 'DataAccessRoleArn', ], 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'DocumentClassifierArn' => [ 'shape' => 'DocumentClassifierArn', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'Tags' => [ 'shape' => 'TagList', ], 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], ], ], 'StartDocumentClassificationJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'DocumentClassifierArn' => [ 'shape' => 'DocumentClassifierArn', ], ], ], 'StartDominantLanguageDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'InputDataConfig', 'OutputDataConfig', 'DataAccessRoleArn', ], 'members' => [ 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartDominantLanguageDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StartEntitiesDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'InputDataConfig', 'OutputDataConfig', 'DataAccessRoleArn', 'LanguageCode', ], 'members' => [ 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'EntityRecognizerArn' => [ 'shape' => 'EntityRecognizerArn', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'Tags' => [ 'shape' => 'TagList', ], 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], ], ], 'StartEntitiesDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'EntityRecognizerArn' => [ 'shape' => 'EntityRecognizerArn', ], ], ], 'StartEventsDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'InputDataConfig', 'OutputDataConfig', 'DataAccessRoleArn', 'LanguageCode', 'TargetEventTypes', ], 'members' => [ 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'TargetEventTypes' => [ 'shape' => 'TargetEventTypes', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartEventsDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StartFlywheelIterationRequest' => [ 'type' => 'structure', 'required' => [ 'FlywheelArn', ], 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', ], ], ], 'StartFlywheelIterationResponse' => [ 'type' => 'structure', 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], 'FlywheelIterationId' => [ 'shape' => 'FlywheelIterationId', ], ], ], 'StartKeyPhrasesDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'InputDataConfig', 'OutputDataConfig', 'DataAccessRoleArn', 'LanguageCode', ], 'members' => [ 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartKeyPhrasesDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StartPiiEntitiesDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'InputDataConfig', 'OutputDataConfig', 'Mode', 'DataAccessRoleArn', 'LanguageCode', ], 'members' => [ 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'Mode' => [ 'shape' => 'PiiEntitiesDetectionMode', ], 'RedactionConfig' => [ 'shape' => 'RedactionConfig', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartPiiEntitiesDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StartSentimentDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'InputDataConfig', 'OutputDataConfig', 'DataAccessRoleArn', 'LanguageCode', ], 'members' => [ 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartSentimentDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StartTargetedSentimentDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'InputDataConfig', 'OutputDataConfig', 'DataAccessRoleArn', 'LanguageCode', ], 'members' => [ 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartTargetedSentimentDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StartTopicsDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'InputDataConfig', 'OutputDataConfig', 'DataAccessRoleArn', ], 'members' => [ 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'NumberOfTopics' => [ 'shape' => 'NumberOfTopicsInteger', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestTokenString', 'idempotencyToken' => true, ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartTopicsDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StopDominantLanguageDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StopDominantLanguageDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StopEntitiesDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StopEntitiesDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StopEventsDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StopEventsDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StopKeyPhrasesDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StopKeyPhrasesDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StopPiiEntitiesDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StopPiiEntitiesDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StopSentimentDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StopSentimentDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StopTargetedSentimentDetectionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StopTargetedSentimentDetectionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'StopTrainingDocumentClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentClassifierArn', ], 'members' => [ 'DocumentClassifierArn' => [ 'shape' => 'DocumentClassifierArn', ], ], ], 'StopTrainingDocumentClassifierResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopTrainingEntityRecognizerRequest' => [ 'type' => 'structure', 'required' => [ 'EntityRecognizerArn', ], 'members' => [ 'EntityRecognizerArn' => [ 'shape' => 'EntityRecognizerArn', ], ], ], 'StopTrainingEntityRecognizerResponse' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', 'min' => 1, ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SubnetId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[-0-9a-zA-Z]+', ], 'Subnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 16, 'min' => 1, ], 'SyntaxLanguageCode' => [ 'type' => 'string', 'enum' => [ 'en', 'es', 'fr', 'de', 'it', 'pt', ], ], 'SyntaxToken' => [ 'type' => 'structure', 'members' => [ 'TokenId' => [ 'shape' => 'Integer', ], 'Text' => [ 'shape' => 'String', ], 'BeginOffset' => [ 'shape' => 'Integer', ], 'EndOffset' => [ 'shape' => 'Integer', ], 'PartOfSpeech' => [ 'shape' => 'PartOfSpeechTag', ], ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ComprehendArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TargetEventTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventTypeString', ], 'min' => 1, ], 'TargetedSentimentDetectionJobFilter' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'SubmitTimeBefore' => [ 'shape' => 'Timestamp', ], 'SubmitTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'TargetedSentimentDetectionJobProperties' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'SubmitTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'TargetedSentimentDetectionJobPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetedSentimentDetectionJobProperties', ], ], 'TargetedSentimentEntity' => [ 'type' => 'structure', 'members' => [ 'DescriptiveMentionIndex' => [ 'shape' => 'ListOfDescriptiveMentionIndices', ], 'Mentions' => [ 'shape' => 'ListOfMentions', ], ], ], 'TargetedSentimentEntityType' => [ 'type' => 'string', 'enum' => [ 'PERSON', 'LOCATION', 'ORGANIZATION', 'FACILITY', 'BRAND', 'COMMERCIAL_ITEM', 'MOVIE', 'MUSIC', 'BOOK', 'SOFTWARE', 'GAME', 'PERSONAL_TITLE', 'EVENT', 'DATE', 'QUANTITY', 'ATTRIBUTE', 'OTHER', ], ], 'TargetedSentimentMention' => [ 'type' => 'structure', 'members' => [ 'Score' => [ 'shape' => 'Float', ], 'GroupScore' => [ 'shape' => 'Float', ], 'Text' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'TargetedSentimentEntityType', ], 'MentionSentiment' => [ 'shape' => 'MentionSentiment', ], 'BeginOffset' => [ 'shape' => 'Integer', ], 'EndOffset' => [ 'shape' => 'Integer', ], ], ], 'TaskConfig' => [ 'type' => 'structure', 'required' => [ 'LanguageCode', ], 'members' => [ 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'DocumentClassificationConfig' => [ 'shape' => 'DocumentClassificationConfig', ], 'EntityRecognitionConfig' => [ 'shape' => 'EntityRecognitionConfig', ], ], ], 'TextSegment' => [ 'type' => 'structure', 'required' => [ 'Text', ], 'members' => [ 'Text' => [ 'shape' => 'CustomerInputString', ], ], ], 'TextSizeLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'TooManyTagKeysException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'TopicsDetectionJobFilter' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'SubmitTimeBefore' => [ 'shape' => 'Timestamp', ], 'SubmitTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'TopicsDetectionJobProperties' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobArn' => [ 'shape' => 'ComprehendArn', ], 'JobName' => [ 'shape' => 'JobName', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'Message' => [ 'shape' => 'AnyLengthString', ], 'SubmitTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'NumberOfTopics' => [ 'shape' => 'Integer', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'TopicsDetectionJobPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TopicsDetectionJobProperties', ], ], 'ToxicContent' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ToxicContentType', ], 'Score' => [ 'shape' => 'Float', ], ], ], 'ToxicContentType' => [ 'type' => 'string', 'enum' => [ 'GRAPHIC', 'HARASSMENT_OR_ABUSE', 'HATE_SPEECH', 'INSULT', 'PROFANITY', 'SEXUAL', 'VIOLENCE_OR_THREAT', ], ], 'ToxicLabels' => [ 'type' => 'structure', 'members' => [ 'Labels' => [ 'shape' => 'ListOfToxicContent', ], 'Toxicity' => [ 'shape' => 'Float', ], ], ], 'UnsupportedLanguageException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ComprehendArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDataSecurityConfig' => [ 'type' => 'structure', 'members' => [ 'ModelKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'UpdateEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'ComprehendEndpointArn', ], 'DesiredModelArn' => [ 'shape' => 'ComprehendModelArn', ], 'DesiredInferenceUnits' => [ 'shape' => 'InferenceUnitsInteger', ], 'DesiredDataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], ], ], 'UpdateEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'DesiredModelArn' => [ 'shape' => 'ComprehendModelArn', ], ], ], 'UpdateFlywheelRequest' => [ 'type' => 'structure', 'required' => [ 'FlywheelArn', ], 'members' => [ 'FlywheelArn' => [ 'shape' => 'ComprehendFlywheelArn', ], 'ActiveModelArn' => [ 'shape' => 'ComprehendModelArn', ], 'DataAccessRoleArn' => [ 'shape' => 'IamRoleArn', ], 'DataSecurityConfig' => [ 'shape' => 'UpdateDataSecurityConfig', ], ], ], 'UpdateFlywheelResponse' => [ 'type' => 'structure', 'members' => [ 'FlywheelProperties' => [ 'shape' => 'FlywheelProperties', ], ], ], 'VersionName' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*$', ], 'VpcConfig' => [ 'type' => 'structure', 'required' => [ 'SecurityGroupIds', 'Subnets', ], 'members' => [ 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'Subnets' => [ 'shape' => 'Subnets', ], ], ], 'WarningsListItem' => [ 'type' => 'structure', 'members' => [ 'Page' => [ 'shape' => 'Integer', ], 'WarnCode' => [ 'shape' => 'PageBasedWarningCode', ], 'WarnMessage' => [ 'shape' => 'String', ], ], ], ],];
