<?php
// This file was auto-generated from sdk-root/src/data/logs/2014-03-28/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2014-03-28', 'endpointPrefix' => 'logs', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Amazon CloudWatch Logs', 'serviceId' => 'CloudWatch Logs', 'signatureVersion' => 'v4', 'targetPrefix' => 'Logs_20140328', 'uid' => 'logs-2014-03-28', ], 'operations' => [ 'AssociateKmsKey' => [ 'name' => 'AssociateKmsKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateKmsKeyRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CancelExportTask' => [ 'name' => 'CancelExportTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelExportTaskRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateDelivery' => [ 'name' => 'CreateDelivery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDeliveryRequest', ], 'output' => [ 'shape' => 'CreateDeliveryResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateExportTask' => [ 'name' => 'CreateExportTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateExportTaskRequest', ], 'output' => [ 'shape' => 'CreateExportTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'CreateLogAnomalyDetector' => [ 'name' => 'CreateLogAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLogAnomalyDetectorRequest', ], 'output' => [ 'shape' => 'CreateLogAnomalyDetectorResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateLogGroup' => [ 'name' => 'CreateLogGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLogGroupRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateLogStream' => [ 'name' => 'CreateLogStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLogStreamRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteAccountPolicy' => [ 'name' => 'DeleteAccountPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAccountPolicyRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'OperationAbortedException', ], ], ], 'DeleteDataProtectionPolicy' => [ 'name' => 'DeleteDataProtectionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDataProtectionPolicyRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteDelivery' => [ 'name' => 'DeleteDelivery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDeliveryRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteDeliveryDestination' => [ 'name' => 'DeleteDeliveryDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDeliveryDestinationRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteDeliveryDestinationPolicy' => [ 'name' => 'DeleteDeliveryDestinationPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDeliveryDestinationPolicyRequest', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteDeliverySource' => [ 'name' => 'DeleteDeliverySource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDeliverySourceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteDestination' => [ 'name' => 'DeleteDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDestinationRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteLogAnomalyDetector' => [ 'name' => 'DeleteLogAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLogAnomalyDetectorRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'OperationAbortedException', ], ], ], 'DeleteLogGroup' => [ 'name' => 'DeleteLogGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLogGroupRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteLogStream' => [ 'name' => 'DeleteLogStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLogStreamRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteMetricFilter' => [ 'name' => 'DeleteMetricFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMetricFilterRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteQueryDefinition' => [ 'name' => 'DeleteQueryDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteQueryDefinitionRequest', ], 'output' => [ 'shape' => 'DeleteQueryDefinitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteRetentionPolicy' => [ 'name' => 'DeleteRetentionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRetentionPolicyRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteSubscriptionFilter' => [ 'name' => 'DeleteSubscriptionFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSubscriptionFilterRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeAccountPolicies' => [ 'name' => 'DescribeAccountPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountPoliciesRequest', ], 'output' => [ 'shape' => 'DescribeAccountPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeDeliveries' => [ 'name' => 'DescribeDeliveries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDeliveriesRequest', ], 'output' => [ 'shape' => 'DescribeDeliveriesResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeDeliveryDestinations' => [ 'name' => 'DescribeDeliveryDestinations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDeliveryDestinationsRequest', ], 'output' => [ 'shape' => 'DescribeDeliveryDestinationsResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeDeliverySources' => [ 'name' => 'DescribeDeliverySources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDeliverySourcesRequest', ], 'output' => [ 'shape' => 'DescribeDeliverySourcesResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeDestinations' => [ 'name' => 'DescribeDestinations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDestinationsRequest', ], 'output' => [ 'shape' => 'DescribeDestinationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeExportTasks' => [ 'name' => 'DescribeExportTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeExportTasksRequest', ], 'output' => [ 'shape' => 'DescribeExportTasksResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeLogGroups' => [ 'name' => 'DescribeLogGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLogGroupsRequest', ], 'output' => [ 'shape' => 'DescribeLogGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeLogStreams' => [ 'name' => 'DescribeLogStreams', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLogStreamsRequest', ], 'output' => [ 'shape' => 'DescribeLogStreamsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeMetricFilters' => [ 'name' => 'DescribeMetricFilters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMetricFiltersRequest', ], 'output' => [ 'shape' => 'DescribeMetricFiltersResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeQueries' => [ 'name' => 'DescribeQueries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeQueriesRequest', ], 'output' => [ 'shape' => 'DescribeQueriesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeQueryDefinitions' => [ 'name' => 'DescribeQueryDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeQueryDefinitionsRequest', ], 'output' => [ 'shape' => 'DescribeQueryDefinitionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeResourcePolicies' => [ 'name' => 'DescribeResourcePolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeResourcePoliciesRequest', ], 'output' => [ 'shape' => 'DescribeResourcePoliciesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeSubscriptionFilters' => [ 'name' => 'DescribeSubscriptionFilters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSubscriptionFiltersRequest', ], 'output' => [ 'shape' => 'DescribeSubscriptionFiltersResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DisassociateKmsKey' => [ 'name' => 'DisassociateKmsKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateKmsKeyRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'FilterLogEvents' => [ 'name' => 'FilterLogEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'FilterLogEventsRequest', ], 'output' => [ 'shape' => 'FilterLogEventsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetDataProtectionPolicy' => [ 'name' => 'GetDataProtectionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataProtectionPolicyRequest', ], 'output' => [ 'shape' => 'GetDataProtectionPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetDelivery' => [ 'name' => 'GetDelivery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeliveryRequest', ], 'output' => [ 'shape' => 'GetDeliveryResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetDeliveryDestination' => [ 'name' => 'GetDeliveryDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeliveryDestinationRequest', ], 'output' => [ 'shape' => 'GetDeliveryDestinationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetDeliveryDestinationPolicy' => [ 'name' => 'GetDeliveryDestinationPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeliveryDestinationPolicyRequest', ], 'output' => [ 'shape' => 'GetDeliveryDestinationPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetDeliverySource' => [ 'name' => 'GetDeliverySource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeliverySourceRequest', ], 'output' => [ 'shape' => 'GetDeliverySourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetLogAnomalyDetector' => [ 'name' => 'GetLogAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLogAnomalyDetectorRequest', ], 'output' => [ 'shape' => 'GetLogAnomalyDetectorResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'OperationAbortedException', ], ], ], 'GetLogEvents' => [ 'name' => 'GetLogEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLogEventsRequest', ], 'output' => [ 'shape' => 'GetLogEventsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetLogGroupFields' => [ 'name' => 'GetLogGroupFields', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLogGroupFieldsRequest', ], 'output' => [ 'shape' => 'GetLogGroupFieldsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetLogRecord' => [ 'name' => 'GetLogRecord', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLogRecordRequest', ], 'output' => [ 'shape' => 'GetLogRecordResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetQueryResults' => [ 'name' => 'GetQueryResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetQueryResultsRequest', ], 'output' => [ 'shape' => 'GetQueryResultsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListAnomalies' => [ 'name' => 'ListAnomalies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAnomaliesRequest', ], 'output' => [ 'shape' => 'ListAnomaliesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'OperationAbortedException', ], ], ], 'ListLogAnomalyDetectors' => [ 'name' => 'ListLogAnomalyDetectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLogAnomalyDetectorsRequest', ], 'output' => [ 'shape' => 'ListLogAnomalyDetectorsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'OperationAbortedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListTagsLogGroup' => [ 'name' => 'ListTagsLogGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsLogGroupRequest', ], 'output' => [ 'shape' => 'ListTagsLogGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Please use the generic tagging API ListTagsForResource', ], 'PutAccountPolicy' => [ 'name' => 'PutAccountPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutAccountPolicyRequest', ], 'output' => [ 'shape' => 'PutAccountPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'PutDataProtectionPolicy' => [ 'name' => 'PutDataProtectionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDataProtectionPolicyRequest', ], 'output' => [ 'shape' => 'PutDataProtectionPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'PutDeliveryDestination' => [ 'name' => 'PutDeliveryDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDeliveryDestinationRequest', ], 'output' => [ 'shape' => 'PutDeliveryDestinationResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'PutDeliveryDestinationPolicy' => [ 'name' => 'PutDeliveryDestinationPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDeliveryDestinationPolicyRequest', ], 'output' => [ 'shape' => 'PutDeliveryDestinationPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutDeliverySource' => [ 'name' => 'PutDeliverySource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDeliverySourceRequest', ], 'output' => [ 'shape' => 'PutDeliverySourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'PutDestination' => [ 'name' => 'PutDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDestinationRequest', ], 'output' => [ 'shape' => 'PutDestinationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'PutDestinationPolicy' => [ 'name' => 'PutDestinationPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDestinationPolicyRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'PutLogEvents' => [ 'name' => 'PutLogEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutLogEventsRequest', ], 'output' => [ 'shape' => 'PutLogEventsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidSequenceTokenException', ], [ 'shape' => 'DataAlreadyAcceptedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnrecognizedClientException', ], ], ], 'PutMetricFilter' => [ 'name' => 'PutMetricFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutMetricFilterRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'PutQueryDefinition' => [ 'name' => 'PutQueryDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutQueryDefinitionRequest', ], 'output' => [ 'shape' => 'PutQueryDefinitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'PutRetentionPolicy' => [ 'name' => 'PutRetentionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRetentionPolicyRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'PutSubscriptionFilter' => [ 'name' => 'PutSubscriptionFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutSubscriptionFilterRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationAbortedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'StartQuery' => [ 'name' => 'StartQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartQueryRequest', ], 'output' => [ 'shape' => 'StartQueryResponse', ], 'errors' => [ [ 'shape' => 'MalformedQueryException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'StopQuery' => [ 'name' => 'StopQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopQueryRequest', ], 'output' => [ 'shape' => 'StopQueryResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'TagLogGroup' => [ 'name' => 'TagLogGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagLogGroupRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Please use the generic tagging API TagResource', ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'TestMetricFilter' => [ 'name' => 'TestMetricFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestMetricFilterRequest', ], 'output' => [ 'shape' => 'TestMetricFilterResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UntagLogGroup' => [ 'name' => 'UntagLogGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagLogGroupRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Please use the generic tagging API UntagResource', ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateAnomaly' => [ 'name' => 'UpdateAnomaly', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAnomalyRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'OperationAbortedException', ], ], ], 'UpdateLogAnomalyDetector' => [ 'name' => 'UpdateLogAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLogAnomalyDetectorRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'OperationAbortedException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AccessPolicy' => [ 'type' => 'string', 'min' => 1, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'AccountIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 20, 'min' => 0, ], 'AccountPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountPolicy', ], ], 'AccountPolicy' => [ 'type' => 'structure', 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyDocument' => [ 'shape' => 'AccountPolicyDocument', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'policyType' => [ 'shape' => 'PolicyType', ], 'scope' => [ 'shape' => 'Scope', ], 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'AccountPolicyDocument' => [ 'type' => 'string', ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => '[\\w+=/:,.@-]*', ], 'Anomalies' => [ 'type' => 'list', 'member' => [ 'shape' => 'Anomaly', ], ], 'Anomaly' => [ 'type' => 'structure', 'required' => [ 'anomalyId', 'patternId', 'anomalyDetectorArn', 'patternString', 'firstSeen', 'lastSeen', 'description', 'active', 'state', 'histogram', 'logSamples', 'patternTokens', 'logGroupArnList', ], 'members' => [ 'anomalyId' => [ 'shape' => 'AnomalyId', ], 'patternId' => [ 'shape' => 'PatternId', ], 'anomalyDetectorArn' => [ 'shape' => 'AnomalyDetectorArn', ], 'patternString' => [ 'shape' => 'PatternString', ], 'patternRegex' => [ 'shape' => 'PatternRegex', ], 'priority' => [ 'shape' => 'Priority', ], 'firstSeen' => [ 'shape' => 'EpochMillis', ], 'lastSeen' => [ 'shape' => 'EpochMillis', ], 'description' => [ 'shape' => 'Description', ], 'active' => [ 'shape' => 'Boolean', ], 'state' => [ 'shape' => 'State', ], 'histogram' => [ 'shape' => 'Histogram', ], 'logSamples' => [ 'shape' => 'LogSamples', ], 'patternTokens' => [ 'shape' => 'PatternTokens', ], 'logGroupArnList' => [ 'shape' => 'LogGroupArnList', ], 'suppressed' => [ 'shape' => 'Boolean', ], 'suppressedDate' => [ 'shape' => 'EpochMillis', ], 'suppressedUntil' => [ 'shape' => 'EpochMillis', ], 'isPatternLevelSuppression' => [ 'shape' => 'Boolean', ], ], ], 'AnomalyDetector' => [ 'type' => 'structure', 'members' => [ 'anomalyDetectorArn' => [ 'shape' => 'AnomalyDetectorArn', ], 'detectorName' => [ 'shape' => 'DetectorName', ], 'logGroupArnList' => [ 'shape' => 'LogGroupArnList', ], 'evaluationFrequency' => [ 'shape' => 'EvaluationFrequency', ], 'filterPattern' => [ 'shape' => 'FilterPattern', ], 'anomalyDetectorStatus' => [ 'shape' => 'AnomalyDetectorStatus', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'creationTimeStamp' => [ 'shape' => 'EpochMillis', ], 'lastModifiedTimeStamp' => [ 'shape' => 'EpochMillis', ], 'anomalyVisibilityTime' => [ 'shape' => 'AnomalyVisibilityTime', ], ], ], 'AnomalyDetectorArn' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\w#+=/:,.@-]*', ], 'AnomalyDetectorStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZING', 'TRAINING', 'ANALYZING', 'FAILED', 'DELETED', 'PAUSED', ], ], 'AnomalyDetectors' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnomalyDetector', ], ], 'AnomalyId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'AnomalyVisibilityTime' => [ 'type' => 'long', 'max' => 90, 'min' => 7, ], 'Arn' => [ 'type' => 'string', ], 'AssociateKmsKeyRequest' => [ 'type' => 'structure', 'required' => [ 'kmsKeyId', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'CancelExportTaskRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', ], 'members' => [ 'taskId' => [ 'shape' => 'ExportTaskId', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 128, 'min' => 36, 'pattern' => '\\S{36,128}', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Count' => [ 'type' => 'long', ], 'CreateDeliveryRequest' => [ 'type' => 'structure', 'required' => [ 'deliverySourceName', 'deliveryDestinationArn', ], 'members' => [ 'deliverySourceName' => [ 'shape' => 'DeliverySourceName', ], 'deliveryDestinationArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateDeliveryResponse' => [ 'type' => 'structure', 'members' => [ 'delivery' => [ 'shape' => 'Delivery', ], ], ], 'CreateExportTaskRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'from', 'to', 'destination', ], 'members' => [ 'taskName' => [ 'shape' => 'ExportTaskName', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'logStreamNamePrefix' => [ 'shape' => 'LogStreamName', ], 'from' => [ 'shape' => 'Timestamp', ], 'to' => [ 'shape' => 'Timestamp', ], 'destination' => [ 'shape' => 'ExportDestinationBucket', ], 'destinationPrefix' => [ 'shape' => 'ExportDestinationPrefix', ], ], ], 'CreateExportTaskResponse' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'ExportTaskId', ], ], ], 'CreateLogAnomalyDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupArnList', ], 'members' => [ 'logGroupArnList' => [ 'shape' => 'LogGroupArnList', ], 'detectorName' => [ 'shape' => 'DetectorName', ], 'evaluationFrequency' => [ 'shape' => 'EvaluationFrequency', ], 'filterPattern' => [ 'shape' => 'FilterPattern', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'anomalyVisibilityTime' => [ 'shape' => 'AnomalyVisibilityTime', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateLogAnomalyDetectorResponse' => [ 'type' => 'structure', 'members' => [ 'anomalyDetectorArn' => [ 'shape' => 'AnomalyDetectorArn', ], ], ], 'CreateLogGroupRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'tags' => [ 'shape' => 'Tags', ], 'logGroupClass' => [ 'shape' => 'LogGroupClass', ], ], ], 'CreateLogStreamRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'logStreamName', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'logStreamName' => [ 'shape' => 'LogStreamName', ], ], ], 'DataAlreadyAcceptedException' => [ 'type' => 'structure', 'members' => [ 'expectedSequenceToken' => [ 'shape' => 'SequenceToken', ], ], 'exception' => true, ], 'DataProtectionPolicyDocument' => [ 'type' => 'string', ], 'DataProtectionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVATED', 'DELETED', 'ARCHIVED', 'DISABLED', ], ], 'Days' => [ 'type' => 'integer', ], 'DefaultValue' => [ 'type' => 'double', ], 'DeleteAccountPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'policyType', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyType' => [ 'shape' => 'PolicyType', ], ], ], 'DeleteDataProtectionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupIdentifier', ], 'members' => [ 'logGroupIdentifier' => [ 'shape' => 'LogGroupIdentifier', ], ], ], 'DeleteDeliveryDestinationPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'deliveryDestinationName', ], 'members' => [ 'deliveryDestinationName' => [ 'shape' => 'DeliveryDestinationName', ], ], ], 'DeleteDeliveryDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'DeliveryDestinationName', ], ], ], 'DeleteDeliveryRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'DeliveryId', ], ], ], 'DeleteDeliverySourceRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'DeliverySourceName', ], ], ], 'DeleteDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'destinationName', ], 'members' => [ 'destinationName' => [ 'shape' => 'DestinationName', ], ], ], 'DeleteLogAnomalyDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'anomalyDetectorArn', ], 'members' => [ 'anomalyDetectorArn' => [ 'shape' => 'AnomalyDetectorArn', ], ], ], 'DeleteLogGroupRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], ], ], 'DeleteLogStreamRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'logStreamName', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'logStreamName' => [ 'shape' => 'LogStreamName', ], ], ], 'DeleteMetricFilterRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'filterName', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'filterName' => [ 'shape' => 'FilterName', ], ], ], 'DeleteQueryDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'queryDefinitionId', ], 'members' => [ 'queryDefinitionId' => [ 'shape' => 'QueryId', ], ], ], 'DeleteQueryDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'success' => [ 'shape' => 'Success', ], ], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], ], ], 'DeleteRetentionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], ], ], 'DeleteSubscriptionFilterRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'filterName', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'filterName' => [ 'shape' => 'FilterName', ], ], ], 'Deliveries' => [ 'type' => 'list', 'member' => [ 'shape' => 'Delivery', ], ], 'Delivery' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'DeliveryId', ], 'arn' => [ 'shape' => 'Arn', ], 'deliverySourceName' => [ 'shape' => 'DeliverySourceName', ], 'deliveryDestinationArn' => [ 'shape' => 'Arn', ], 'deliveryDestinationType' => [ 'shape' => 'DeliveryDestinationType', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'DeliveryDestination' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DeliveryDestinationName', ], 'arn' => [ 'shape' => 'Arn', ], 'deliveryDestinationType' => [ 'shape' => 'DeliveryDestinationType', ], 'outputFormat' => [ 'shape' => 'OutputFormat', ], 'deliveryDestinationConfiguration' => [ 'shape' => 'DeliveryDestinationConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'DeliveryDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'destinationResourceArn', ], 'members' => [ 'destinationResourceArn' => [ 'shape' => 'Arn', ], ], ], 'DeliveryDestinationName' => [ 'type' => 'string', 'max' => 60, 'min' => 1, 'pattern' => '[\\w-]*', ], 'DeliveryDestinationPolicy' => [ 'type' => 'string', 'max' => 51200, 'min' => 1, ], 'DeliveryDestinationType' => [ 'type' => 'string', 'enum' => [ 'S3', 'CWL', 'FH', ], ], 'DeliveryDestinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeliveryDestination', ], ], 'DeliveryId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[0-9A-Za-z]+$', ], 'DeliverySource' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DeliverySourceName', ], 'arn' => [ 'shape' => 'Arn', ], 'resourceArns' => [ 'shape' => 'ResourceArns', ], 'service' => [ 'shape' => 'Service', ], 'logType' => [ 'shape' => 'LogType', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'DeliverySourceName' => [ 'type' => 'string', 'max' => 60, 'min' => 1, 'pattern' => '[\\w-]*', ], 'DeliverySources' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeliverySource', ], ], 'Descending' => [ 'type' => 'boolean', ], 'DescribeAccountPoliciesRequest' => [ 'type' => 'structure', 'required' => [ 'policyType', ], 'members' => [ 'policyType' => [ 'shape' => 'PolicyType', ], 'policyName' => [ 'shape' => 'PolicyName', ], 'accountIdentifiers' => [ 'shape' => 'AccountIds', ], ], ], 'DescribeAccountPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'accountPolicies' => [ 'shape' => 'AccountPolicies', ], ], ], 'DescribeDeliveriesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'limit' => [ 'shape' => 'DescribeLimit', ], ], ], 'DescribeDeliveriesResponse' => [ 'type' => 'structure', 'members' => [ 'deliveries' => [ 'shape' => 'Deliveries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDeliveryDestinationsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'limit' => [ 'shape' => 'DescribeLimit', ], ], ], 'DescribeDeliveryDestinationsResponse' => [ 'type' => 'structure', 'members' => [ 'deliveryDestinations' => [ 'shape' => 'DeliveryDestinations', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDeliverySourcesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'limit' => [ 'shape' => 'DescribeLimit', ], ], ], 'DescribeDeliverySourcesResponse' => [ 'type' => 'structure', 'members' => [ 'deliverySources' => [ 'shape' => 'DeliverySources', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDestinationsRequest' => [ 'type' => 'structure', 'members' => [ 'DestinationNamePrefix' => [ 'shape' => 'DestinationName', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'limit' => [ 'shape' => 'DescribeLimit', ], ], ], 'DescribeDestinationsResponse' => [ 'type' => 'structure', 'members' => [ 'destinations' => [ 'shape' => 'Destinations', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeExportTasksRequest' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'ExportTaskId', ], 'statusCode' => [ 'shape' => 'ExportTaskStatusCode', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'limit' => [ 'shape' => 'DescribeLimit', ], ], ], 'DescribeExportTasksResponse' => [ 'type' => 'structure', 'members' => [ 'exportTasks' => [ 'shape' => 'ExportTasks', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeLimit' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'DescribeLogGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'accountIdentifiers' => [ 'shape' => 'AccountIds', ], 'logGroupNamePrefix' => [ 'shape' => 'LogGroupName', ], 'logGroupNamePattern' => [ 'shape' => 'LogGroupNamePattern', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'limit' => [ 'shape' => 'DescribeLimit', ], 'includeLinkedAccounts' => [ 'shape' => 'IncludeLinkedAccounts', ], 'logGroupClass' => [ 'shape' => 'LogGroupClass', ], ], ], 'DescribeLogGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'logGroups' => [ 'shape' => 'LogGroups', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeLogStreamsRequest' => [ 'type' => 'structure', 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'logGroupIdentifier' => [ 'shape' => 'LogGroupIdentifier', ], 'logStreamNamePrefix' => [ 'shape' => 'LogStreamName', ], 'orderBy' => [ 'shape' => 'OrderBy', ], 'descending' => [ 'shape' => 'Descending', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'limit' => [ 'shape' => 'DescribeLimit', ], ], ], 'DescribeLogStreamsResponse' => [ 'type' => 'structure', 'members' => [ 'logStreams' => [ 'shape' => 'LogStreams', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMetricFiltersRequest' => [ 'type' => 'structure', 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'filterNamePrefix' => [ 'shape' => 'FilterName', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'limit' => [ 'shape' => 'DescribeLimit', ], 'metricName' => [ 'shape' => 'MetricName', ], 'metricNamespace' => [ 'shape' => 'MetricNamespace', ], ], ], 'DescribeMetricFiltersResponse' => [ 'type' => 'structure', 'members' => [ 'metricFilters' => [ 'shape' => 'MetricFilters', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeQueriesMaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'DescribeQueriesRequest' => [ 'type' => 'structure', 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'status' => [ 'shape' => 'QueryStatus', ], 'maxResults' => [ 'shape' => 'DescribeQueriesMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeQueriesResponse' => [ 'type' => 'structure', 'members' => [ 'queries' => [ 'shape' => 'QueryInfoList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeQueryDefinitionsRequest' => [ 'type' => 'structure', 'members' => [ 'queryDefinitionNamePrefix' => [ 'shape' => 'QueryDefinitionName', ], 'maxResults' => [ 'shape' => 'QueryListMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeQueryDefinitionsResponse' => [ 'type' => 'structure', 'members' => [ 'queryDefinitions' => [ 'shape' => 'QueryDefinitionList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeResourcePoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'limit' => [ 'shape' => 'DescribeLimit', ], ], ], 'DescribeResourcePoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'resourcePolicies' => [ 'shape' => 'ResourcePolicies', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeSubscriptionFiltersRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'filterNamePrefix' => [ 'shape' => 'FilterName', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'limit' => [ 'shape' => 'DescribeLimit', ], ], ], 'DescribeSubscriptionFiltersResponse' => [ 'type' => 'structure', 'members' => [ 'subscriptionFilters' => [ 'shape' => 'SubscriptionFilters', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'Description' => [ 'type' => 'string', 'min' => 1, ], 'Destination' => [ 'type' => 'structure', 'members' => [ 'destinationName' => [ 'shape' => 'DestinationName', ], 'targetArn' => [ 'shape' => 'TargetArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'accessPolicy' => [ 'shape' => 'AccessPolicy', ], 'arn' => [ 'shape' => 'Arn', ], 'creationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DestinationArn' => [ 'type' => 'string', 'min' => 1, ], 'DestinationName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[^:*]*', ], 'Destinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Destination', ], ], 'DetectorName' => [ 'type' => 'string', 'min' => 1, ], 'Dimensions' => [ 'type' => 'map', 'key' => [ 'shape' => 'DimensionsKey', ], 'value' => [ 'shape' => 'DimensionsValue', ], ], 'DimensionsKey' => [ 'type' => 'string', 'max' => 255, ], 'DimensionsValue' => [ 'type' => 'string', 'max' => 255, ], 'DisassociateKmsKeyRequest' => [ 'type' => 'structure', 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], ], ], 'Distribution' => [ 'type' => 'string', 'enum' => [ 'Random', 'ByLogStream', ], ], 'DynamicTokenPosition' => [ 'type' => 'integer', ], 'EncryptionKey' => [ 'type' => 'string', 'max' => 256, ], 'Enumerations' => [ 'type' => 'map', 'key' => [ 'shape' => 'TokenString', ], 'value' => [ 'shape' => 'TokenValue', ], ], 'EpochMillis' => [ 'type' => 'long', 'min' => 0, ], 'EvaluationFrequency' => [ 'type' => 'string', 'enum' => [ 'ONE_MIN', 'FIVE_MIN', 'TEN_MIN', 'FIFTEEN_MIN', 'THIRTY_MIN', 'ONE_HOUR', ], ], 'EventId' => [ 'type' => 'string', ], 'EventMessage' => [ 'type' => 'string', 'min' => 1, ], 'EventNumber' => [ 'type' => 'long', ], 'EventsLimit' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'ExportDestinationBucket' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'ExportDestinationPrefix' => [ 'type' => 'string', ], 'ExportTask' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'ExportTaskId', ], 'taskName' => [ 'shape' => 'ExportTaskName', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'from' => [ 'shape' => 'Timestamp', ], 'to' => [ 'shape' => 'Timestamp', ], 'destination' => [ 'shape' => 'ExportDestinationBucket', ], 'destinationPrefix' => [ 'shape' => 'ExportDestinationPrefix', ], 'status' => [ 'shape' => 'ExportTaskStatus', ], 'executionInfo' => [ 'shape' => 'ExportTaskExecutionInfo', ], ], ], 'ExportTaskExecutionInfo' => [ 'type' => 'structure', 'members' => [ 'creationTime' => [ 'shape' => 'Timestamp', ], 'completionTime' => [ 'shape' => 'Timestamp', ], ], ], 'ExportTaskId' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'ExportTaskName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'ExportTaskStatus' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'ExportTaskStatusCode', ], 'message' => [ 'shape' => 'ExportTaskStatusMessage', ], ], ], 'ExportTaskStatusCode' => [ 'type' => 'string', 'enum' => [ 'CANCELLED', 'COMPLETED', 'FAILED', 'PENDING', 'PENDING_CANCEL', 'RUNNING', ], ], 'ExportTaskStatusMessage' => [ 'type' => 'string', ], 'ExportTasks' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportTask', ], ], 'ExtractedValues' => [ 'type' => 'map', 'key' => [ 'shape' => 'Token', ], 'value' => [ 'shape' => 'Value', ], ], 'Field' => [ 'type' => 'string', ], 'FilterCount' => [ 'type' => 'integer', ], 'FilterLogEventsRequest' => [ 'type' => 'structure', 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'logGroupIdentifier' => [ 'shape' => 'LogGroupIdentifier', ], 'logStreamNames' => [ 'shape' => 'InputLogStreamNames', ], 'logStreamNamePrefix' => [ 'shape' => 'LogStreamName', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'filterPattern' => [ 'shape' => 'FilterPattern', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'limit' => [ 'shape' => 'EventsLimit', ], 'interleaved' => [ 'shape' => 'Interleaved', 'deprecated' => true, 'deprecatedMessage' => 'Starting on June 17, 2019, this parameter will be ignored and the value will be assumed to be true. The response from this operation will always interleave events from multiple log streams within a log group.', ], 'unmask' => [ 'shape' => 'Unmask', ], ], ], 'FilterLogEventsResponse' => [ 'type' => 'structure', 'members' => [ 'events' => [ 'shape' => 'FilteredLogEvents', ], 'searchedLogStreams' => [ 'shape' => 'SearchedLogStreams', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'FilterName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[^:*]*', ], 'FilterPattern' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'FilteredLogEvent' => [ 'type' => 'structure', 'members' => [ 'logStreamName' => [ 'shape' => 'LogStreamName', ], 'timestamp' => [ 'shape' => 'Timestamp', ], 'message' => [ 'shape' => 'EventMessage', ], 'ingestionTime' => [ 'shape' => 'Timestamp', ], 'eventId' => [ 'shape' => 'EventId', ], ], ], 'FilteredLogEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilteredLogEvent', ], ], 'ForceUpdate' => [ 'type' => 'boolean', ], 'GetDataProtectionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupIdentifier', ], 'members' => [ 'logGroupIdentifier' => [ 'shape' => 'LogGroupIdentifier', ], ], ], 'GetDataProtectionPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'logGroupIdentifier' => [ 'shape' => 'LogGroupIdentifier', ], 'policyDocument' => [ 'shape' => 'DataProtectionPolicyDocument', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetDeliveryDestinationPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'deliveryDestinationName', ], 'members' => [ 'deliveryDestinationName' => [ 'shape' => 'DeliveryDestinationName', ], ], ], 'GetDeliveryDestinationPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'policy' => [ 'shape' => 'Policy', ], ], ], 'GetDeliveryDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'DeliveryDestinationName', ], ], ], 'GetDeliveryDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'deliveryDestination' => [ 'shape' => 'DeliveryDestination', ], ], ], 'GetDeliveryRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'DeliveryId', ], ], ], 'GetDeliveryResponse' => [ 'type' => 'structure', 'members' => [ 'delivery' => [ 'shape' => 'Delivery', ], ], ], 'GetDeliverySourceRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'DeliverySourceName', ], ], ], 'GetDeliverySourceResponse' => [ 'type' => 'structure', 'members' => [ 'deliverySource' => [ 'shape' => 'DeliverySource', ], ], ], 'GetLogAnomalyDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'anomalyDetectorArn', ], 'members' => [ 'anomalyDetectorArn' => [ 'shape' => 'AnomalyDetectorArn', ], ], ], 'GetLogAnomalyDetectorResponse' => [ 'type' => 'structure', 'members' => [ 'detectorName' => [ 'shape' => 'DetectorName', ], 'logGroupArnList' => [ 'shape' => 'LogGroupArnList', ], 'evaluationFrequency' => [ 'shape' => 'EvaluationFrequency', ], 'filterPattern' => [ 'shape' => 'FilterPattern', ], 'anomalyDetectorStatus' => [ 'shape' => 'AnomalyDetectorStatus', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'creationTimeStamp' => [ 'shape' => 'EpochMillis', ], 'lastModifiedTimeStamp' => [ 'shape' => 'EpochMillis', ], 'anomalyVisibilityTime' => [ 'shape' => 'AnomalyVisibilityTime', ], ], ], 'GetLogEventsRequest' => [ 'type' => 'structure', 'required' => [ 'logStreamName', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'logGroupIdentifier' => [ 'shape' => 'LogGroupIdentifier', ], 'logStreamName' => [ 'shape' => 'LogStreamName', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'limit' => [ 'shape' => 'EventsLimit', ], 'startFromHead' => [ 'shape' => 'StartFromHead', ], 'unmask' => [ 'shape' => 'Unmask', ], ], ], 'GetLogEventsResponse' => [ 'type' => 'structure', 'members' => [ 'events' => [ 'shape' => 'OutputLogEvents', ], 'nextForwardToken' => [ 'shape' => 'NextToken', ], 'nextBackwardToken' => [ 'shape' => 'NextToken', ], ], ], 'GetLogGroupFieldsRequest' => [ 'type' => 'structure', 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'time' => [ 'shape' => 'Timestamp', ], 'logGroupIdentifier' => [ 'shape' => 'LogGroupIdentifier', ], ], ], 'GetLogGroupFieldsResponse' => [ 'type' => 'structure', 'members' => [ 'logGroupFields' => [ 'shape' => 'LogGroupFieldList', ], ], ], 'GetLogRecordRequest' => [ 'type' => 'structure', 'required' => [ 'logRecordPointer', ], 'members' => [ 'logRecordPointer' => [ 'shape' => 'LogRecordPointer', ], 'unmask' => [ 'shape' => 'Unmask', ], ], ], 'GetLogRecordResponse' => [ 'type' => 'structure', 'members' => [ 'logRecord' => [ 'shape' => 'LogRecord', ], ], ], 'GetQueryResultsRequest' => [ 'type' => 'structure', 'required' => [ 'queryId', ], 'members' => [ 'queryId' => [ 'shape' => 'QueryId', ], ], ], 'GetQueryResultsResponse' => [ 'type' => 'structure', 'members' => [ 'results' => [ 'shape' => 'QueryResults', ], 'statistics' => [ 'shape' => 'QueryStatistics', ], 'status' => [ 'shape' => 'QueryStatus', ], 'encryptionKey' => [ 'shape' => 'EncryptionKey', ], ], ], 'Histogram' => [ 'type' => 'map', 'key' => [ 'shape' => 'Time', ], 'value' => [ 'shape' => 'Count', ], ], 'IncludeLinkedAccounts' => [ 'type' => 'boolean', ], 'InheritedProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'InheritedProperty', ], ], 'InheritedProperty' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_DATA_PROTECTION', ], ], 'InputLogEvent' => [ 'type' => 'structure', 'required' => [ 'timestamp', 'message', ], 'members' => [ 'timestamp' => [ 'shape' => 'Timestamp', ], 'message' => [ 'shape' => 'EventMessage', ], ], ], 'InputLogEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputLogEvent', ], 'max' => 10000, 'min' => 1, ], 'InputLogStreamNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogStreamName', ], 'max' => 100, 'min' => 1, ], 'Integer' => [ 'type' => 'integer', ], 'Interleaved' => [ 'type' => 'boolean', ], 'InvalidOperationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidSequenceTokenException' => [ 'type' => 'structure', 'members' => [ 'expectedSequenceToken' => [ 'shape' => 'SequenceToken', ], ], 'exception' => true, ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 256, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ListAnomaliesLimit' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'ListAnomaliesRequest' => [ 'type' => 'structure', 'members' => [ 'anomalyDetectorArn' => [ 'shape' => 'AnomalyDetectorArn', ], 'suppressionState' => [ 'shape' => 'SuppressionState', ], 'limit' => [ 'shape' => 'ListAnomaliesLimit', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAnomaliesResponse' => [ 'type' => 'structure', 'members' => [ 'anomalies' => [ 'shape' => 'Anomalies', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLogAnomalyDetectorsLimit' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'ListLogAnomalyDetectorsRequest' => [ 'type' => 'structure', 'members' => [ 'filterLogGroupArn' => [ 'shape' => 'LogGroupArn', ], 'limit' => [ 'shape' => 'ListLogAnomalyDetectorsLimit', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLogAnomalyDetectorsResponse' => [ 'type' => 'structure', 'members' => [ 'anomalyDetectors' => [ 'shape' => 'AnomalyDetectors', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'ListTagsLogGroupRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Please use the generic tagging API model ListTagsForResourceRequest and ListTagsForResourceResponse', ], 'ListTagsLogGroupResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Please use the generic tagging API model ListTagsForResourceRequest and ListTagsForResourceResponse', ], 'LogEvent' => [ 'type' => 'string', 'min' => 1, ], 'LogEventIndex' => [ 'type' => 'integer', ], 'LogGroup' => [ 'type' => 'structure', 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'retentionInDays' => [ 'shape' => 'Days', ], 'metricFilterCount' => [ 'shape' => 'FilterCount', ], 'arn' => [ 'shape' => 'Arn', ], 'storedBytes' => [ 'shape' => 'StoredBytes', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'dataProtectionStatus' => [ 'shape' => 'DataProtectionStatus', ], 'inheritedProperties' => [ 'shape' => 'InheritedProperties', ], 'logGroupClass' => [ 'shape' => 'LogGroupClass', ], ], ], 'LogGroupArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[\\w#+=/:,.@-]*', ], 'LogGroupArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogGroupArn', ], ], 'LogGroupClass' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'INFREQUENT_ACCESS', ], ], 'LogGroupField' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Field', ], 'percent' => [ 'shape' => 'Percentage', ], ], ], 'LogGroupFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogGroupField', ], ], 'LogGroupIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[\\w#+=/:,.@-]*', ], 'LogGroupIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogGroupIdentifier', ], ], 'LogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\.\\-_/#A-Za-z0-9]+', ], 'LogGroupNamePattern' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '[\\.\\-_/#A-Za-z0-9]*', ], 'LogGroupNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogGroupName', ], ], 'LogGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogGroup', ], ], 'LogRecord' => [ 'type' => 'map', 'key' => [ 'shape' => 'Field', ], 'value' => [ 'shape' => 'Value', ], ], 'LogRecordPointer' => [ 'type' => 'string', ], 'LogSamples' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogEvent', ], ], 'LogStream' => [ 'type' => 'structure', 'members' => [ 'logStreamName' => [ 'shape' => 'LogStreamName', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'firstEventTimestamp' => [ 'shape' => 'Timestamp', ], 'lastEventTimestamp' => [ 'shape' => 'Timestamp', ], 'lastIngestionTime' => [ 'shape' => 'Timestamp', ], 'uploadSequenceToken' => [ 'shape' => 'SequenceToken', ], 'arn' => [ 'shape' => 'Arn', ], 'storedBytes' => [ 'shape' => 'StoredBytes', 'deprecated' => true, 'deprecatedMessage' => 'Starting on June 17, 2019, this parameter will be deprecated for log streams, and will be reported as zero. This change applies only to log streams. The storedBytes parameter for log groups is not affected.', ], ], ], 'LogStreamName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[^:*]*', ], 'LogStreamSearchedCompletely' => [ 'type' => 'boolean', ], 'LogStreams' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogStream', ], ], 'LogType' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\w]*', ], 'MalformedQueryException' => [ 'type' => 'structure', 'members' => [ 'queryCompileError' => [ 'shape' => 'QueryCompileError', ], ], 'exception' => true, ], 'Message' => [ 'type' => 'string', ], 'MetricFilter' => [ 'type' => 'structure', 'members' => [ 'filterName' => [ 'shape' => 'FilterName', ], 'filterPattern' => [ 'shape' => 'FilterPattern', ], 'metricTransformations' => [ 'shape' => 'MetricTransformations', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], ], ], 'MetricFilterMatchRecord' => [ 'type' => 'structure', 'members' => [ 'eventNumber' => [ 'shape' => 'EventNumber', ], 'eventMessage' => [ 'shape' => 'EventMessage', ], 'extractedValues' => [ 'shape' => 'ExtractedValues', ], ], ], 'MetricFilterMatches' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricFilterMatchRecord', ], ], 'MetricFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricFilter', ], ], 'MetricName' => [ 'type' => 'string', 'max' => 255, 'pattern' => '[^:*$]*', ], 'MetricNamespace' => [ 'type' => 'string', 'max' => 255, 'pattern' => '[^:*$]*', ], 'MetricTransformation' => [ 'type' => 'structure', 'required' => [ 'metricName', 'metricNamespace', 'metricValue', ], 'members' => [ 'metricName' => [ 'shape' => 'MetricName', ], 'metricNamespace' => [ 'shape' => 'MetricNamespace', ], 'metricValue' => [ 'shape' => 'MetricValue', ], 'defaultValue' => [ 'shape' => 'DefaultValue', ], 'dimensions' => [ 'shape' => 'Dimensions', ], 'unit' => [ 'shape' => 'StandardUnit', ], ], ], 'MetricTransformations' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricTransformation', ], 'max' => 1, 'min' => 1, ], 'MetricValue' => [ 'type' => 'string', 'max' => 100, ], 'NextToken' => [ 'type' => 'string', 'min' => 1, ], 'OperationAbortedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'OrderBy' => [ 'type' => 'string', 'enum' => [ 'LogStreamName', 'LastEventTime', ], ], 'OutputFormat' => [ 'type' => 'string', 'enum' => [ 'json', 'plain', 'w3c', 'raw', 'parquet', ], ], 'OutputLogEvent' => [ 'type' => 'structure', 'members' => [ 'timestamp' => [ 'shape' => 'Timestamp', ], 'message' => [ 'shape' => 'EventMessage', ], 'ingestionTime' => [ 'shape' => 'Timestamp', ], ], ], 'OutputLogEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputLogEvent', ], ], 'PatternId' => [ 'type' => 'string', 'max' => 32, 'min' => 32, ], 'PatternRegex' => [ 'type' => 'string', 'min' => 1, ], 'PatternString' => [ 'type' => 'string', 'min' => 1, ], 'PatternToken' => [ 'type' => 'structure', 'members' => [ 'dynamicTokenPosition' => [ 'shape' => 'DynamicTokenPosition', ], 'isDynamic' => [ 'shape' => 'Boolean', ], 'tokenString' => [ 'shape' => 'TokenString', ], 'enumerations' => [ 'shape' => 'Enumerations', ], ], ], 'PatternTokens' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatternToken', ], ], 'Percentage' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'Policy' => [ 'type' => 'structure', 'members' => [ 'deliveryDestinationPolicy' => [ 'shape' => 'DeliveryDestinationPolicy', ], ], ], 'PolicyDocument' => [ 'type' => 'string', 'max' => 5120, 'min' => 1, ], 'PolicyName' => [ 'type' => 'string', ], 'PolicyType' => [ 'type' => 'string', 'enum' => [ 'DATA_PROTECTION_POLICY', ], ], 'Priority' => [ 'type' => 'string', 'min' => 1, ], 'PutAccountPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'policyDocument', 'policyType', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyDocument' => [ 'shape' => 'AccountPolicyDocument', ], 'policyType' => [ 'shape' => 'PolicyType', ], 'scope' => [ 'shape' => 'Scope', ], ], ], 'PutAccountPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'accountPolicy' => [ 'shape' => 'AccountPolicy', ], ], ], 'PutDataProtectionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupIdentifier', 'policyDocument', ], 'members' => [ 'logGroupIdentifier' => [ 'shape' => 'LogGroupIdentifier', ], 'policyDocument' => [ 'shape' => 'DataProtectionPolicyDocument', ], ], ], 'PutDataProtectionPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'logGroupIdentifier' => [ 'shape' => 'LogGroupIdentifier', ], 'policyDocument' => [ 'shape' => 'DataProtectionPolicyDocument', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'PutDeliveryDestinationPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'deliveryDestinationName', 'deliveryDestinationPolicy', ], 'members' => [ 'deliveryDestinationName' => [ 'shape' => 'DeliveryDestinationName', ], 'deliveryDestinationPolicy' => [ 'shape' => 'DeliveryDestinationPolicy', ], ], ], 'PutDeliveryDestinationPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'policy' => [ 'shape' => 'Policy', ], ], ], 'PutDeliveryDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'deliveryDestinationConfiguration', ], 'members' => [ 'name' => [ 'shape' => 'DeliveryDestinationName', ], 'outputFormat' => [ 'shape' => 'OutputFormat', ], 'deliveryDestinationConfiguration' => [ 'shape' => 'DeliveryDestinationConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'PutDeliveryDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'deliveryDestination' => [ 'shape' => 'DeliveryDestination', ], ], ], 'PutDeliverySourceRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'resourceArn', 'logType', ], 'members' => [ 'name' => [ 'shape' => 'DeliverySourceName', ], 'resourceArn' => [ 'shape' => 'Arn', ], 'logType' => [ 'shape' => 'LogType', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'PutDeliverySourceResponse' => [ 'type' => 'structure', 'members' => [ 'deliverySource' => [ 'shape' => 'DeliverySource', ], ], ], 'PutDestinationPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'destinationName', 'accessPolicy', ], 'members' => [ 'destinationName' => [ 'shape' => 'DestinationName', ], 'accessPolicy' => [ 'shape' => 'AccessPolicy', ], 'forceUpdate' => [ 'shape' => 'ForceUpdate', ], ], ], 'PutDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'destinationName', 'targetArn', 'roleArn', ], 'members' => [ 'destinationName' => [ 'shape' => 'DestinationName', ], 'targetArn' => [ 'shape' => 'TargetArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'PutDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'destination' => [ 'shape' => 'Destination', ], ], ], 'PutLogEventsRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'logStreamName', 'logEvents', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'logStreamName' => [ 'shape' => 'LogStreamName', ], 'logEvents' => [ 'shape' => 'InputLogEvents', ], 'sequenceToken' => [ 'shape' => 'SequenceToken', ], ], ], 'PutLogEventsResponse' => [ 'type' => 'structure', 'members' => [ 'nextSequenceToken' => [ 'shape' => 'SequenceToken', ], 'rejectedLogEventsInfo' => [ 'shape' => 'RejectedLogEventsInfo', ], ], ], 'PutMetricFilterRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'filterName', 'filterPattern', 'metricTransformations', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'filterName' => [ 'shape' => 'FilterName', ], 'filterPattern' => [ 'shape' => 'FilterPattern', ], 'metricTransformations' => [ 'shape' => 'MetricTransformations', ], ], ], 'PutQueryDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'queryString', ], 'members' => [ 'name' => [ 'shape' => 'QueryDefinitionName', ], 'queryDefinitionId' => [ 'shape' => 'QueryId', ], 'logGroupNames' => [ 'shape' => 'LogGroupNames', ], 'queryString' => [ 'shape' => 'QueryDefinitionString', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'PutQueryDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'queryDefinitionId' => [ 'shape' => 'QueryId', ], ], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'resourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'PutRetentionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'retentionInDays', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'retentionInDays' => [ 'shape' => 'Days', ], ], ], 'PutSubscriptionFilterRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'filterName', 'filterPattern', 'destinationArn', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'filterName' => [ 'shape' => 'FilterName', ], 'filterPattern' => [ 'shape' => 'FilterPattern', ], 'destinationArn' => [ 'shape' => 'DestinationArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'distribution' => [ 'shape' => 'Distribution', ], ], ], 'QueryCharOffset' => [ 'type' => 'integer', ], 'QueryCompileError' => [ 'type' => 'structure', 'members' => [ 'location' => [ 'shape' => 'QueryCompileErrorLocation', ], 'message' => [ 'shape' => 'Message', ], ], ], 'QueryCompileErrorLocation' => [ 'type' => 'structure', 'members' => [ 'startCharOffset' => [ 'shape' => 'QueryCharOffset', ], 'endCharOffset' => [ 'shape' => 'QueryCharOffset', ], ], ], 'QueryDefinition' => [ 'type' => 'structure', 'members' => [ 'queryDefinitionId' => [ 'shape' => 'QueryId', ], 'name' => [ 'shape' => 'QueryDefinitionName', ], 'queryString' => [ 'shape' => 'QueryDefinitionString', ], 'lastModified' => [ 'shape' => 'Timestamp', ], 'logGroupNames' => [ 'shape' => 'LogGroupNames', ], ], ], 'QueryDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryDefinition', ], ], 'QueryDefinitionName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'QueryDefinitionString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'QueryId' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'QueryInfo' => [ 'type' => 'structure', 'members' => [ 'queryId' => [ 'shape' => 'QueryId', ], 'queryString' => [ 'shape' => 'QueryString', ], 'status' => [ 'shape' => 'QueryStatus', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], ], ], 'QueryInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryInfo', ], ], 'QueryListMaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'QueryResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResultRows', ], ], 'QueryStatistics' => [ 'type' => 'structure', 'members' => [ 'recordsMatched' => [ 'shape' => 'StatsValue', ], 'recordsScanned' => [ 'shape' => 'StatsValue', ], 'bytesScanned' => [ 'shape' => 'StatsValue', ], ], ], 'QueryStatus' => [ 'type' => 'string', 'enum' => [ 'Scheduled', 'Running', 'Complete', 'Failed', 'Cancelled', 'Timeout', 'Unknown', ], ], 'QueryString' => [ 'type' => 'string', 'max' => 10000, 'min' => 0, ], 'RejectedLogEventsInfo' => [ 'type' => 'structure', 'members' => [ 'tooNewLogEventStartIndex' => [ 'shape' => 'LogEventIndex', ], 'tooOldLogEventEndIndex' => [ 'shape' => 'LogEventIndex', ], 'expiredLogEventEndIndex' => [ 'shape' => 'LogEventIndex', ], ], ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'ResourceIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[\\w+=/:,.@\\-\\*]*', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourcePolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcePolicy', ], ], 'ResourcePolicy' => [ 'type' => 'structure', 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ResultField' => [ 'type' => 'structure', 'members' => [ 'field' => [ 'shape' => 'Field', ], 'value' => [ 'shape' => 'Value', ], ], ], 'ResultRows' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResultField', ], ], 'RoleArn' => [ 'type' => 'string', 'min' => 1, ], 'Scope' => [ 'type' => 'string', 'enum' => [ 'ALL', ], ], 'SearchedLogStream' => [ 'type' => 'structure', 'members' => [ 'logStreamName' => [ 'shape' => 'LogStreamName', ], 'searchedCompletely' => [ 'shape' => 'LogStreamSearchedCompletely', ], ], ], 'SearchedLogStreams' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchedLogStream', ], ], 'SequenceToken' => [ 'type' => 'string', 'min' => 1, ], 'Service' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\w]*', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, 'fault' => true, ], 'StandardUnit' => [ 'type' => 'string', 'enum' => [ 'Seconds', 'Microseconds', 'Milliseconds', 'Bytes', 'Kilobytes', 'Megabytes', 'Gigabytes', 'Terabytes', 'Bits', 'Kilobits', 'Megabits', 'Gigabits', 'Terabits', 'Percent', 'Count', 'Bytes/Second', 'Kilobytes/Second', 'Megabytes/Second', 'Gigabytes/Second', 'Terabytes/Second', 'Bits/Second', 'Kilobits/Second', 'Megabits/Second', 'Gigabits/Second', 'Terabits/Second', 'Count/Second', 'None', ], ], 'StartFromHead' => [ 'type' => 'boolean', ], 'StartQueryRequest' => [ 'type' => 'structure', 'required' => [ 'startTime', 'endTime', 'queryString', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'logGroupNames' => [ 'shape' => 'LogGroupNames', ], 'logGroupIdentifiers' => [ 'shape' => 'LogGroupIdentifiers', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'queryString' => [ 'shape' => 'QueryString', ], 'limit' => [ 'shape' => 'EventsLimit', ], ], ], 'StartQueryResponse' => [ 'type' => 'structure', 'members' => [ 'queryId' => [ 'shape' => 'QueryId', ], ], ], 'State' => [ 'type' => 'string', 'enum' => [ 'Active', 'Suppressed', 'Baseline', ], ], 'StatsValue' => [ 'type' => 'double', ], 'StopQueryRequest' => [ 'type' => 'structure', 'required' => [ 'queryId', ], 'members' => [ 'queryId' => [ 'shape' => 'QueryId', ], ], ], 'StopQueryResponse' => [ 'type' => 'structure', 'members' => [ 'success' => [ 'shape' => 'Success', ], ], ], 'StoredBytes' => [ 'type' => 'long', 'min' => 0, ], 'SubscriptionFilter' => [ 'type' => 'structure', 'members' => [ 'filterName' => [ 'shape' => 'FilterName', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'filterPattern' => [ 'shape' => 'FilterPattern', ], 'destinationArn' => [ 'shape' => 'DestinationArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'distribution' => [ 'shape' => 'Distribution', ], 'creationTime' => [ 'shape' => 'Timestamp', ], ], ], 'SubscriptionFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscriptionFilter', ], ], 'Success' => [ 'type' => 'boolean', ], 'SuppressionPeriod' => [ 'type' => 'structure', 'members' => [ 'value' => [ 'shape' => 'Integer', ], 'suppressionUnit' => [ 'shape' => 'SuppressionUnit', ], ], ], 'SuppressionState' => [ 'type' => 'string', 'enum' => [ 'SUPPRESSED', 'UNSUPPRESSED', ], ], 'SuppressionType' => [ 'type' => 'string', 'enum' => [ 'LIMITED', 'INFINITE', ], ], 'SuppressionUnit' => [ 'type' => 'string', 'enum' => [ 'SECONDS', 'MINUTES', 'HOURS', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]+)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'min' => 1, ], 'TagLogGroupRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'tags', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'tags' => [ 'shape' => 'Tags', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Please use the generic tagging API model TagResourceRequest', ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TargetArn' => [ 'type' => 'string', 'min' => 1, ], 'TestEventMessages' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventMessage', ], 'max' => 50, 'min' => 1, ], 'TestMetricFilterRequest' => [ 'type' => 'structure', 'required' => [ 'filterPattern', 'logEventMessages', ], 'members' => [ 'filterPattern' => [ 'shape' => 'FilterPattern', ], 'logEventMessages' => [ 'shape' => 'TestEventMessages', ], ], ], 'TestMetricFilterResponse' => [ 'type' => 'structure', 'members' => [ 'matches' => [ 'shape' => 'MetricFilterMatches', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Time' => [ 'type' => 'string', 'min' => 1, ], 'Timestamp' => [ 'type' => 'long', 'min' => 0, ], 'Token' => [ 'type' => 'string', ], 'TokenString' => [ 'type' => 'string', 'min' => 1, ], 'TokenValue' => [ 'type' => 'long', ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], 'resourceName' => [ 'shape' => 'AmazonResourceName', ], ], 'exception' => true, ], 'Unmask' => [ 'type' => 'boolean', ], 'UnrecognizedClientException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'UntagLogGroupRequest' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'tags', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'tags' => [ 'shape' => 'TagList', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Please use the generic tagging API model UntagResourceRequest', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'tagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UpdateAnomalyRequest' => [ 'type' => 'structure', 'required' => [ 'anomalyDetectorArn', ], 'members' => [ 'anomalyId' => [ 'shape' => 'AnomalyId', ], 'patternId' => [ 'shape' => 'PatternId', ], 'anomalyDetectorArn' => [ 'shape' => 'AnomalyDetectorArn', ], 'suppressionType' => [ 'shape' => 'SuppressionType', ], 'suppressionPeriod' => [ 'shape' => 'SuppressionPeriod', ], ], ], 'UpdateLogAnomalyDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'anomalyDetectorArn', 'enabled', ], 'members' => [ 'anomalyDetectorArn' => [ 'shape' => 'AnomalyDetectorArn', ], 'evaluationFrequency' => [ 'shape' => 'EvaluationFrequency', ], 'filterPattern' => [ 'shape' => 'FilterPattern', ], 'anomalyVisibilityTime' => [ 'shape' => 'AnomalyVisibilityTime', ], 'enabled' => [ 'shape' => 'Boolean', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Value' => [ 'type' => 'string', ], ],];
