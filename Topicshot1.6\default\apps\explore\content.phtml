<div class="timeline-container" data-app="explore">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("explore"); ?>" data-spa="true">
						<?php echo cl_translate("Explore"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link('/'); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="d-block" id="vue-explore-app">
		<div class="timeline-searchbar">
			<form data-an="search-form" class="form timeline-searchbar__form" v-on:submit="search_entries($event)" autocomplete="off">
				<div class="keyword-input">
					<input v-on:input="search_entries" v-model="search_query" v-bind:disabled="(empty_list == '1')" type="text" v-bind:placeholder="input_place_holder" autofocus="true">
					
					<span class="keyword-input__right-spinner" v-if="searching">
						<?php echo cl_icon('spinner-icon'); ?>
					</span>
					<span class="keyword-input__left-icon" v-else>
						<?php echo cl_ficon('search'); ?>
					</span>
				</div>
			</form>
		</div>

		<div class="timeline-navbar">
			<div class="timeline-navbar__item">
				<a v-bind:href="search_htags_url" data-spa="true">
					<button class="timeline-navbar__item-btn <?php if($cl["page_tab"] == "htags") { echo("active");} ?>">
						<span class="btn-flex-inner">
							<?php echo cl_translate("Hashtags"); ?>
						</span>
					</button>
				</a>
			</div>
			<div class="timeline-navbar__item">
				<a v-bind:href="search_people_url" data-spa="true">
					<button class="timeline-navbar__item-btn <?php if($cl["page_tab"] == "people") { echo("active");} ?>">
						<span class="btn-flex-inner">
							<?php echo cl_translate("People"); ?>
						</span>
					</button>
				</a>
			</div>
			<div class="timeline-navbar__item">
				<a v-bind:href="search_posts_url" data-spa="true">
					<button class="timeline-navbar__item-btn <?php if($cl["page_tab"] == "posts") { echo("active");} ?>">
						<span class="btn-flex-inner">
							<?php echo cl_translate("Posts"); ?>
						</span>
					</button>
				</a>
			</div>
		</div>

		<?php if (empty($cl["query_result"]) && empty($cl["search_query"])): ?>
			<?php echo cl_template('explore/includes/empty_list'); ?>
		<?php else: ?>
			<?php echo cl_template(cl_strf('explore/includes/%s', $cl["page_tab"])); ?>
			<?php echo cl_template('explore/includes/empty_result'); ?>
		<?php endif; ?>

		<div class="timeline-data-loader" v-if="show_loader">
			<button v-if="loading_more" class="timeline-data-loader__btn" disabled="true">
				<?php echo cl_translate("Please wait"); ?>
			</button>
			<button v-else-if="load_more" class="timeline-data-loader__btn" v-on:click="load_entries($event)">
				<?php echo cl_translate("Show more"); ?>
			</button>
			<button v-else class="timeline-data-loader__btn" disabled="true">
				<?php echo cl_translate("That is all for now!"); ?>
			</button>
		</div>
	</div>

	<?php echo cl_template('explore/scripts/app_master_script'); ?>
</div>