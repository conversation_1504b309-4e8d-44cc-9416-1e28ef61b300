<?php
// This file was auto-generated from sdk-root/src/data/iot-roborunner/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'endpointPrefix' => 'iotroborunner', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS IoT RoboRunner', 'serviceId' => 'IoT RoboRunner', 'signatureVersion' => 'v4', 'signingName' => 'iotroborunner', 'uid' => 'iot-roborunner-2018-05-10', ], 'operations' => [ 'CreateDestination' => [ 'name' => 'CreateDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/createDestination', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDestinationRequest', ], 'output' => [ 'shape' => 'CreateDestinationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateSite' => [ 'name' => 'CreateSite', 'http' => [ 'method' => 'POST', 'requestUri' => '/createSite', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSiteRequest', ], 'output' => [ 'shape' => 'CreateSiteResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateWorker' => [ 'name' => 'CreateWorker', 'http' => [ 'method' => 'POST', 'requestUri' => '/createWorker', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateWorkerRequest', ], 'output' => [ 'shape' => 'CreateWorkerResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateWorkerFleet' => [ 'name' => 'CreateWorkerFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/createWorkerFleet', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateWorkerFleetRequest', ], 'output' => [ 'shape' => 'CreateWorkerFleetResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteDestination' => [ 'name' => 'DeleteDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteDestination', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDestinationRequest', ], 'output' => [ 'shape' => 'DeleteDestinationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteSite' => [ 'name' => 'DeleteSite', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteSite', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSiteRequest', ], 'output' => [ 'shape' => 'DeleteSiteResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteWorker' => [ 'name' => 'DeleteWorker', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteWorker', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteWorkerRequest', ], 'output' => [ 'shape' => 'DeleteWorkerResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteWorkerFleet' => [ 'name' => 'DeleteWorkerFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteWorkerFleet', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteWorkerFleetRequest', ], 'output' => [ 'shape' => 'DeleteWorkerFleetResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDestination' => [ 'name' => 'GetDestination', 'http' => [ 'method' => 'GET', 'requestUri' => '/getDestination', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDestinationRequest', ], 'output' => [ 'shape' => 'GetDestinationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetSite' => [ 'name' => 'GetSite', 'http' => [ 'method' => 'GET', 'requestUri' => '/getSite', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSiteRequest', ], 'output' => [ 'shape' => 'GetSiteResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetWorker' => [ 'name' => 'GetWorker', 'http' => [ 'method' => 'GET', 'requestUri' => '/getWorker', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkerRequest', ], 'output' => [ 'shape' => 'GetWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetWorkerFleet' => [ 'name' => 'GetWorkerFleet', 'http' => [ 'method' => 'GET', 'requestUri' => '/getWorkerFleet', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkerFleetRequest', ], 'output' => [ 'shape' => 'GetWorkerFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDestinations' => [ 'name' => 'ListDestinations', 'http' => [ 'method' => 'GET', 'requestUri' => '/listDestinations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDestinationsRequest', ], 'output' => [ 'shape' => 'ListDestinationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSites' => [ 'name' => 'ListSites', 'http' => [ 'method' => 'GET', 'requestUri' => '/listSites', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSitesRequest', ], 'output' => [ 'shape' => 'ListSitesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListWorkerFleets' => [ 'name' => 'ListWorkerFleets', 'http' => [ 'method' => 'GET', 'requestUri' => '/listWorkerFleets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWorkerFleetsRequest', ], 'output' => [ 'shape' => 'ListWorkerFleetsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListWorkers' => [ 'name' => 'ListWorkers', 'http' => [ 'method' => 'GET', 'requestUri' => '/listWorkers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWorkersRequest', ], 'output' => [ 'shape' => 'ListWorkersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateDestination' => [ 'name' => 'UpdateDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateDestination', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDestinationRequest', ], 'output' => [ 'shape' => 'UpdateDestinationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateSite' => [ 'name' => 'UpdateSite', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateSite', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSiteRequest', ], 'output' => [ 'shape' => 'UpdateSiteResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateWorker' => [ 'name' => 'UpdateWorker', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateWorker', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateWorkerRequest', ], 'output' => [ 'shape' => 'UpdateWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateWorkerFleet' => [ 'name' => 'UpdateWorkerFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateWorkerFleet', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateWorkerFleetRequest', ], 'output' => [ 'shape' => 'UpdateWorkerFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'CartesianCoordinates' => [ 'type' => 'structure', 'required' => [ 'x', 'y', ], 'members' => [ 'x' => [ 'shape' => 'Double', ], 'y' => [ 'shape' => 'Double', ], 'z' => [ 'shape' => 'Double', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'site', ], 'members' => [ 'clientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'name' => [ 'shape' => 'Name', ], 'site' => [ 'shape' => 'SiteGenericIdentifier', ], 'state' => [ 'shape' => 'DestinationState', ], 'additionalFixedProperties' => [ 'shape' => 'DestinationAdditionalFixedProperties', ], ], ], 'CreateDestinationResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'createdAt', 'updatedAt', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'DestinationArn', ], 'id' => [ 'shape' => 'DestinationId', ], 'createdAt' => [ 'shape' => 'CreatedAtTimestamp', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], 'state' => [ 'shape' => 'DestinationState', ], ], ], 'CreateSiteRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'countryCode', ], 'members' => [ 'clientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'name' => [ 'shape' => 'Name', ], 'countryCode' => [ 'shape' => 'SiteCountryCode', ], 'description' => [ 'shape' => 'SiteDescription', ], ], ], 'CreateSiteResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'createdAt', 'updatedAt', ], 'members' => [ 'arn' => [ 'shape' => 'SiteArn', ], 'id' => [ 'shape' => 'SiteId', ], 'createdAt' => [ 'shape' => 'CreatedAtTimestamp', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], ], ], 'CreateWorkerFleetRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'site', ], 'members' => [ 'clientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'name' => [ 'shape' => 'Name', ], 'site' => [ 'shape' => 'SiteGenericIdentifier', ], 'additionalFixedProperties' => [ 'shape' => 'WorkerFleetAdditionalFixedProperties', ], ], ], 'CreateWorkerFleetResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'createdAt', 'updatedAt', ], 'members' => [ 'arn' => [ 'shape' => 'WorkerFleetArn', ], 'id' => [ 'shape' => 'WorkerFleetId', ], 'createdAt' => [ 'shape' => 'CreatedAtTimestamp', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], ], ], 'CreateWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'fleet', ], 'members' => [ 'clientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'name' => [ 'shape' => 'Name', ], 'fleet' => [ 'shape' => 'WorkerFleetGenericIdentifier', ], 'additionalTransientProperties' => [ 'shape' => 'WorkerAdditionalTransientPropertiesJson', ], 'additionalFixedProperties' => [ 'shape' => 'WorkerAdditionalFixedPropertiesJson', ], 'vendorProperties' => [ 'shape' => 'VendorProperties', ], 'position' => [ 'shape' => 'PositionCoordinates', ], 'orientation' => [ 'shape' => 'Orientation', ], ], ], 'CreateWorkerResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'createdAt', 'updatedAt', 'site', ], 'members' => [ 'arn' => [ 'shape' => 'WorkerArn', ], 'id' => [ 'shape' => 'WorkerId', ], 'createdAt' => [ 'shape' => 'CreatedAtTimestamp', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], 'site' => [ 'shape' => 'SiteArn', ], ], ], 'CreatedAtTimestamp' => [ 'type' => 'timestamp', ], 'DeleteDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'DestinationGenericIdentifier', ], ], ], 'DeleteDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSiteRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'SiteGenericIdentifier', ], ], ], 'DeleteSiteResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkerFleetRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'WorkerFleetGenericIdentifier', ], ], ], 'DeleteWorkerFleetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'WorkerGenericIdentifier', ], ], ], 'DeleteWorkerResponse' => [ 'type' => 'structure', 'members' => [], ], 'Destination' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'name', 'site', 'createdAt', 'updatedAt', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'DestinationArn', ], 'id' => [ 'shape' => 'DestinationId', ], 'name' => [ 'shape' => 'Name', ], 'site' => [ 'shape' => 'SiteArn', ], 'createdAt' => [ 'shape' => 'CreatedAtTimestamp', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], 'state' => [ 'shape' => 'DestinationState', ], 'additionalFixedProperties' => [ 'shape' => 'DestinationAdditionalFixedProperties', ], ], ], 'DestinationAdditionalFixedProperties' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, ], 'DestinationArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:iotroborunner:[\\w-]+:\\w+:destination/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$|^arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/destination/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'DestinationGenericIdentifier' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:iotroborunner:[\\w-]+:\\w+:destination/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$|^arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/destination/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'DestinationId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DestinationState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', 'DECOMMISSIONED', ], ], 'Destinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Destination', ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'GetDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'DestinationGenericIdentifier', 'location' => 'querystring', 'locationName' => 'id', ], ], ], 'GetDestinationResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'name', 'site', 'createdAt', 'updatedAt', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'DestinationArn', ], 'id' => [ 'shape' => 'DestinationId', ], 'name' => [ 'shape' => 'Name', ], 'site' => [ 'shape' => 'SiteArn', ], 'createdAt' => [ 'shape' => 'CreatedAtTimestamp', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], 'state' => [ 'shape' => 'DestinationState', ], 'additionalFixedProperties' => [ 'shape' => 'DestinationAdditionalFixedProperties', ], ], ], 'GetSiteRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'SiteGenericIdentifier', 'location' => 'querystring', 'locationName' => 'id', ], ], ], 'GetSiteResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'name', 'countryCode', 'createdAt', 'updatedAt', ], 'members' => [ 'arn' => [ 'shape' => 'SiteArn', ], 'id' => [ 'shape' => 'SiteId', ], 'name' => [ 'shape' => 'Name', ], 'countryCode' => [ 'shape' => 'SiteCountryCode', ], 'description' => [ 'shape' => 'SiteDescription', ], 'createdAt' => [ 'shape' => 'CreatedAtTimestamp', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], ], ], 'GetWorkerFleetRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'WorkerFleetGenericIdentifier', 'location' => 'querystring', 'locationName' => 'id', ], ], ], 'GetWorkerFleetResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'site', 'createdAt', 'updatedAt', ], 'members' => [ 'id' => [ 'shape' => 'WorkerFleetId', ], 'arn' => [ 'shape' => 'WorkerFleetArn', ], 'name' => [ 'shape' => 'Name', ], 'site' => [ 'shape' => 'SiteArn', ], 'createdAt' => [ 'shape' => 'CreatedAtTimestamp', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], 'additionalFixedProperties' => [ 'shape' => 'WorkerFleetAdditionalFixedProperties', ], ], ], 'GetWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'WorkerGenericIdentifier', 'location' => 'querystring', 'locationName' => 'id', ], ], ], 'GetWorkerResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'fleet', 'site', 'createdAt', 'updatedAt', 'name', ], 'members' => [ 'arn' => [ 'shape' => 'WorkerArn', ], 'id' => [ 'shape' => 'WorkerId', ], 'fleet' => [ 'shape' => 'WorkerFleetArn', ], 'site' => [ 'shape' => 'SiteArn', ], 'createdAt' => [ 'shape' => 'CreatedAtTimestamp', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], 'name' => [ 'shape' => 'Name', ], 'additionalTransientProperties' => [ 'shape' => 'WorkerAdditionalTransientPropertiesJson', ], 'additionalFixedProperties' => [ 'shape' => 'WorkerAdditionalFixedPropertiesJson', ], 'vendorProperties' => [ 'shape' => 'VendorProperties', ], 'position' => [ 'shape' => 'PositionCoordinates', ], 'orientation' => [ 'shape' => 'Orientation', ], ], ], 'IdempotencyToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[!-~]*', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListDestinationsRequest' => [ 'type' => 'structure', 'required' => [ 'site', ], 'members' => [ 'site' => [ 'shape' => 'SiteGenericIdentifier', 'location' => 'querystring', 'locationName' => 'site', ], 'maxResults' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'state' => [ 'shape' => 'DestinationState', 'location' => 'querystring', 'locationName' => 'state', ], ], ], 'ListDestinationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'destinations' => [ 'shape' => 'Destinations', ], ], ], 'ListSitesPageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListSitesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListSitesPageSize', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSitesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'sites' => [ 'shape' => 'Sites', ], ], ], 'ListWorkerFleetsPageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListWorkerFleetsRequest' => [ 'type' => 'structure', 'required' => [ 'site', ], 'members' => [ 'site' => [ 'shape' => 'SiteGenericIdentifier', 'location' => 'querystring', 'locationName' => 'site', ], 'maxResults' => [ 'shape' => 'ListWorkerFleetsPageSize', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListWorkerFleetsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'workerFleets' => [ 'shape' => 'WorkerFleets', ], ], ], 'ListWorkersPageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListWorkersRequest' => [ 'type' => 'structure', 'required' => [ 'site', ], 'members' => [ 'site' => [ 'shape' => 'SiteGenericIdentifier', 'location' => 'querystring', 'locationName' => 'site', ], 'maxResults' => [ 'shape' => 'ListWorkersPageSize', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'fleet' => [ 'shape' => 'WorkerFleetGenericIdentifier', 'location' => 'querystring', 'locationName' => 'fleet', ], ], ], 'ListWorkersResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'workers' => [ 'shape' => 'Workers', ], ], ], 'Name' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'Orientation' => [ 'type' => 'structure', 'members' => [ 'degrees' => [ 'shape' => 'OrientationDegreesDouble', ], ], 'union' => true, ], 'OrientationDegreesDouble' => [ 'type' => 'double', 'box' => true, 'max' => 360, 'min' => 0, ], 'PageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '.*[a-zA-Z0-9_.-/+=]*.*', ], 'PositionCoordinates' => [ 'type' => 'structure', 'members' => [ 'cartesianCoordinates' => [ 'shape' => 'CartesianCoordinates', ], ], 'union' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'Site' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'countryCode', 'createdAt', ], 'members' => [ 'arn' => [ 'shape' => 'SiteArn', ], 'name' => [ 'shape' => 'Name', ], 'countryCode' => [ 'shape' => 'SiteCountryCode', ], 'createdAt' => [ 'shape' => 'CreatedAtTimestamp', ], ], ], 'SiteArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'SiteCountryCode' => [ 'type' => 'string', 'max' => 2, 'min' => 2, 'pattern' => '[a-zA-Z]{2}', ], 'SiteDescription' => [ 'type' => 'string', 'max' => 140, 'min' => 0, ], 'SiteGenericIdentifier' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'SiteId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'Sites' => [ 'type' => 'list', 'member' => [ 'shape' => 'Site', ], ], 'String' => [ 'type' => 'string', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'UpdateDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'DestinationGenericIdentifier', ], 'name' => [ 'shape' => 'Name', ], 'state' => [ 'shape' => 'DestinationState', ], 'additionalFixedProperties' => [ 'shape' => 'DestinationAdditionalFixedProperties', ], ], ], 'UpdateDestinationResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'name', 'updatedAt', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'DestinationArn', ], 'id' => [ 'shape' => 'DestinationId', ], 'name' => [ 'shape' => 'Name', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], 'state' => [ 'shape' => 'DestinationState', ], 'additionalFixedProperties' => [ 'shape' => 'DestinationAdditionalFixedProperties', ], ], ], 'UpdateSiteRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'SiteGenericIdentifier', ], 'name' => [ 'shape' => 'Name', ], 'countryCode' => [ 'shape' => 'SiteCountryCode', ], 'description' => [ 'shape' => 'SiteDescription', ], ], ], 'UpdateSiteResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'name', 'updatedAt', ], 'members' => [ 'arn' => [ 'shape' => 'SiteArn', ], 'id' => [ 'shape' => 'SiteId', ], 'name' => [ 'shape' => 'Name', ], 'countryCode' => [ 'shape' => 'SiteCountryCode', ], 'description' => [ 'shape' => 'SiteDescription', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], ], ], 'UpdateWorkerFleetRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'WorkerFleetGenericIdentifier', ], 'name' => [ 'shape' => 'Name', ], 'additionalFixedProperties' => [ 'shape' => 'WorkerFleetAdditionalFixedProperties', ], ], ], 'UpdateWorkerFleetResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'name', 'updatedAt', ], 'members' => [ 'arn' => [ 'shape' => 'WorkerFleetArn', ], 'id' => [ 'shape' => 'WorkerFleetId', ], 'name' => [ 'shape' => 'Name', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], 'additionalFixedProperties' => [ 'shape' => 'WorkerFleetAdditionalFixedProperties', ], ], ], 'UpdateWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'WorkerGenericIdentifier', ], 'name' => [ 'shape' => 'Name', ], 'additionalTransientProperties' => [ 'shape' => 'WorkerAdditionalTransientPropertiesJson', ], 'additionalFixedProperties' => [ 'shape' => 'WorkerAdditionalFixedPropertiesJson', ], 'vendorProperties' => [ 'shape' => 'VendorProperties', ], 'position' => [ 'shape' => 'PositionCoordinates', ], 'orientation' => [ 'shape' => 'Orientation', ], ], ], 'UpdateWorkerResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'fleet', 'updatedAt', 'name', ], 'members' => [ 'arn' => [ 'shape' => 'WorkerArn', ], 'id' => [ 'shape' => 'WorkerId', ], 'fleet' => [ 'shape' => 'WorkerFleetArn', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], 'name' => [ 'shape' => 'Name', ], 'additionalTransientProperties' => [ 'shape' => 'WorkerAdditionalTransientPropertiesJson', ], 'additionalFixedProperties' => [ 'shape' => 'WorkerAdditionalFixedPropertiesJson', ], 'orientation' => [ 'shape' => 'Orientation', ], 'vendorProperties' => [ 'shape' => 'VendorProperties', ], 'position' => [ 'shape' => 'PositionCoordinates', ], ], ], 'UpdatedAtTimestamp' => [ 'type' => 'timestamp', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VendorAdditionalFixedPropertiesJson' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, ], 'VendorAdditionalTransientPropertiesJson' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, ], 'VendorProperties' => [ 'type' => 'structure', 'required' => [ 'vendorWorkerId', ], 'members' => [ 'vendorWorkerId' => [ 'shape' => 'VendorWorkerId', ], 'vendorWorkerIpAddress' => [ 'shape' => 'VendorWorkerIpAddress', ], 'vendorAdditionalTransientProperties' => [ 'shape' => 'VendorAdditionalTransientPropertiesJson', ], 'vendorAdditionalFixedProperties' => [ 'shape' => 'VendorAdditionalFixedPropertiesJson', ], ], ], 'VendorWorkerId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'VendorWorkerIpAddress' => [ 'type' => 'string', 'max' => 45, 'min' => 1, ], 'Worker' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'fleet', 'createdAt', 'updatedAt', 'name', 'site', ], 'members' => [ 'arn' => [ 'shape' => 'WorkerArn', ], 'id' => [ 'shape' => 'WorkerId', ], 'fleet' => [ 'shape' => 'WorkerFleetArn', ], 'createdAt' => [ 'shape' => 'CreatedAtTimestamp', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], 'name' => [ 'shape' => 'Name', ], 'site' => [ 'shape' => 'SiteArn', ], 'additionalTransientProperties' => [ 'shape' => 'WorkerAdditionalTransientPropertiesJson', ], 'additionalFixedProperties' => [ 'shape' => 'WorkerAdditionalFixedPropertiesJson', ], 'vendorProperties' => [ 'shape' => 'VendorProperties', ], 'position' => [ 'shape' => 'PositionCoordinates', ], 'orientation' => [ 'shape' => 'Orientation', ], ], ], 'WorkerAdditionalFixedPropertiesJson' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, ], 'WorkerAdditionalTransientPropertiesJson' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, ], 'WorkerArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/worker-fleet/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/worker/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'WorkerFleet' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'name', 'site', 'createdAt', 'updatedAt', ], 'members' => [ 'arn' => [ 'shape' => 'WorkerFleetArn', ], 'id' => [ 'shape' => 'WorkerFleetId', ], 'name' => [ 'shape' => 'Name', ], 'site' => [ 'shape' => 'SiteArn', ], 'createdAt' => [ 'shape' => 'CreatedAtTimestamp', ], 'updatedAt' => [ 'shape' => 'UpdatedAtTimestamp', ], 'additionalFixedProperties' => [ 'shape' => 'WorkerFleetAdditionalFixedProperties', ], ], ], 'WorkerFleetAdditionalFixedProperties' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, ], 'WorkerFleetArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/worker-fleet/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'WorkerFleetGenericIdentifier' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/worker-fleet/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'WorkerFleetId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'WorkerFleets' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkerFleet', ], ], 'WorkerGenericIdentifier' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/worker-fleet/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/worker/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'WorkerId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'Workers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Worker', ], ], ],];
