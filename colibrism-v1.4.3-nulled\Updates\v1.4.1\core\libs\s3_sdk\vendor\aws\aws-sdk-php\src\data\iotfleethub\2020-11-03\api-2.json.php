<?php
// This file was auto-generated from sdk-root/src/data/iotfleethub/2020-11-03/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-11-03', 'endpointPrefix' => 'api.fleethub.iot', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS IoT Fleet Hub', 'serviceId' => 'IoTFleetHub', 'signatureVersion' => 'v4', 'signingName' => 'iotfleethub', 'uid' => 'iotfleethub-2020-11-03', ], 'operations' => [ 'CreateApplication' => [ 'name' => 'CreateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateApplicationRequest', ], 'output' => [ 'shape' => 'CreateApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DeleteApplication' => [ 'name' => 'DeleteApplication', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteApplicationRequest', ], 'output' => [ 'shape' => 'DeleteApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeApplication' => [ 'name' => 'DescribeApplication', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeApplicationRequest', ], 'output' => [ 'shape' => 'DescribeApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListApplications' => [ 'name' => 'ListApplications', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListApplicationsRequest', ], 'output' => [ 'shape' => 'ListApplicationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateApplication' => [ 'name' => 'UpdateApplication', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/applications/{applicationId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateApplicationRequest', ], 'output' => [ 'shape' => 'UpdateApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'ApplicationState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'DELETING', 'ACTIVE', 'CREATE_FAILED', 'DELETE_FAILED', ], ], 'ApplicationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationSummary', ], ], 'ApplicationSummary' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'applicationName', 'applicationUrl', ], 'members' => [ 'applicationId' => [ 'shape' => 'Id', ], 'applicationName' => [ 'shape' => 'Name', ], 'applicationDescription' => [ 'shape' => 'Description', ], 'applicationUrl' => [ 'shape' => 'Url', ], 'applicationCreationDate' => [ 'shape' => 'Timestamp', ], 'applicationLastUpdateDate' => [ 'shape' => 'Timestamp', ], 'applicationState' => [ 'shape' => 'ApplicationState', ], ], ], 'Arn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:[!-~]+$', ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_]+$', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CreateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationName', 'roleArn', ], 'members' => [ 'applicationName' => [ 'shape' => 'Name', ], 'applicationDescription' => [ 'shape' => 'Description', ], 'clientToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'roleArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateApplicationResponse' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'applicationArn', ], 'members' => [ 'applicationId' => [ 'shape' => 'Id', ], 'applicationArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'applicationId', ], 'clientToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'applicationId', ], ], ], 'DescribeApplicationResponse' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'applicationArn', 'applicationName', 'applicationUrl', 'applicationState', 'applicationCreationDate', 'applicationLastUpdateDate', 'roleArn', ], 'members' => [ 'applicationId' => [ 'shape' => 'Id', ], 'applicationArn' => [ 'shape' => 'Arn', ], 'applicationName' => [ 'shape' => 'Name', ], 'applicationDescription' => [ 'shape' => 'Description', ], 'applicationUrl' => [ 'shape' => 'Url', ], 'applicationState' => [ 'shape' => 'ApplicationState', ], 'applicationCreationDate' => [ 'shape' => 'Timestamp', ], 'applicationLastUpdateDate' => [ 'shape' => 'Timestamp', ], 'roleArn' => [ 'shape' => 'Arn', ], 'ssoClientId' => [ 'shape' => 'SsoClientId', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[ -~]*$', ], 'ErrorMessage' => [ 'type' => 'string', ], 'Id' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', ], 'InternalFailureException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], 'ListApplicationsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListApplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'applicationSummaries' => [ 'shape' => 'ApplicationSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'Name' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[ -~]*$', ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[A-Za-z0-9+/=]+$', ], 'ResourceArn' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'SsoClientId' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'long', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'applicationId', ], 'applicationName' => [ 'shape' => 'Name', ], 'applicationDescription' => [ 'shape' => 'Description', ], 'clientToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'UpdateApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'Url' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^https\\://\\S+$', ], 'errorMessage' => [ 'type' => 'string', ], ],];
