<?php
// This file was auto-generated from sdk-root/src/data/lookoutmetrics/2017-07-25/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-07-25', 'endpointPrefix' => 'lookoutmetrics', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'LookoutMetrics', 'serviceFullName' => 'Amazon Lookout for Metrics', 'serviceId' => 'LookoutMetrics', 'signatureVersion' => 'v4', 'signingName' => 'lookoutmetrics', 'uid' => 'lookoutmetrics-2017-07-25', ], 'operations' => [ 'ActivateAnomalyDetector' => [ 'name' => 'ActivateAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/ActivateAnomalyDetector', ], 'input' => [ 'shape' => 'ActivateAnomalyDetectorRequest', ], 'output' => [ 'shape' => 'ActivateAnomalyDetectorResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'BackTestAnomalyDetector' => [ 'name' => 'BackTestAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/BackTestAnomalyDetector', ], 'input' => [ 'shape' => 'BackTestAnomalyDetectorRequest', ], 'output' => [ 'shape' => 'BackTestAnomalyDetectorResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateAlert' => [ 'name' => 'CreateAlert', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateAlert', ], 'input' => [ 'shape' => 'CreateAlertRequest', ], 'output' => [ 'shape' => 'CreateAlertResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateAnomalyDetector' => [ 'name' => 'CreateAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateAnomalyDetector', ], 'input' => [ 'shape' => 'CreateAnomalyDetectorRequest', ], 'output' => [ 'shape' => 'CreateAnomalyDetectorResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateMetricSet' => [ 'name' => 'CreateMetricSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateMetricSet', ], 'input' => [ 'shape' => 'CreateMetricSetRequest', ], 'output' => [ 'shape' => 'CreateMetricSetResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeactivateAnomalyDetector' => [ 'name' => 'DeactivateAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeactivateAnomalyDetector', ], 'input' => [ 'shape' => 'DeactivateAnomalyDetectorRequest', ], 'output' => [ 'shape' => 'DeactivateAnomalyDetectorResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteAlert' => [ 'name' => 'DeleteAlert', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteAlert', ], 'input' => [ 'shape' => 'DeleteAlertRequest', ], 'output' => [ 'shape' => 'DeleteAlertResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteAnomalyDetector' => [ 'name' => 'DeleteAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteAnomalyDetector', ], 'input' => [ 'shape' => 'DeleteAnomalyDetectorRequest', ], 'output' => [ 'shape' => 'DeleteAnomalyDetectorResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeAlert' => [ 'name' => 'DescribeAlert', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeAlert', ], 'input' => [ 'shape' => 'DescribeAlertRequest', ], 'output' => [ 'shape' => 'DescribeAlertResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeAnomalyDetectionExecutions' => [ 'name' => 'DescribeAnomalyDetectionExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeAnomalyDetectionExecutions', ], 'input' => [ 'shape' => 'DescribeAnomalyDetectionExecutionsRequest', ], 'output' => [ 'shape' => 'DescribeAnomalyDetectionExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeAnomalyDetector' => [ 'name' => 'DescribeAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeAnomalyDetector', ], 'input' => [ 'shape' => 'DescribeAnomalyDetectorRequest', ], 'output' => [ 'shape' => 'DescribeAnomalyDetectorResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeMetricSet' => [ 'name' => 'DescribeMetricSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeMetricSet', ], 'input' => [ 'shape' => 'DescribeMetricSetRequest', ], 'output' => [ 'shape' => 'DescribeMetricSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DetectMetricSetConfig' => [ 'name' => 'DetectMetricSetConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/DetectMetricSetConfig', ], 'input' => [ 'shape' => 'DetectMetricSetConfigRequest', ], 'output' => [ 'shape' => 'DetectMetricSetConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetAnomalyGroup' => [ 'name' => 'GetAnomalyGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetAnomalyGroup', ], 'input' => [ 'shape' => 'GetAnomalyGroupRequest', ], 'output' => [ 'shape' => 'GetAnomalyGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetDataQualityMetrics' => [ 'name' => 'GetDataQualityMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetDataQualityMetrics', ], 'input' => [ 'shape' => 'GetDataQualityMetricsRequest', ], 'output' => [ 'shape' => 'GetDataQualityMetricsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetFeedback' => [ 'name' => 'GetFeedback', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetFeedback', ], 'input' => [ 'shape' => 'GetFeedbackRequest', ], 'output' => [ 'shape' => 'GetFeedbackResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetSampleData' => [ 'name' => 'GetSampleData', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetSampleData', ], 'input' => [ 'shape' => 'GetSampleDataRequest', ], 'output' => [ 'shape' => 'GetSampleDataResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAlerts' => [ 'name' => 'ListAlerts', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListAlerts', ], 'input' => [ 'shape' => 'ListAlertsRequest', ], 'output' => [ 'shape' => 'ListAlertsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListAnomalyDetectors' => [ 'name' => 'ListAnomalyDetectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListAnomalyDetectors', ], 'input' => [ 'shape' => 'ListAnomalyDetectorsRequest', ], 'output' => [ 'shape' => 'ListAnomalyDetectorsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAnomalyGroupRelatedMetrics' => [ 'name' => 'ListAnomalyGroupRelatedMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListAnomalyGroupRelatedMetrics', ], 'input' => [ 'shape' => 'ListAnomalyGroupRelatedMetricsRequest', ], 'output' => [ 'shape' => 'ListAnomalyGroupRelatedMetricsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListAnomalyGroupSummaries' => [ 'name' => 'ListAnomalyGroupSummaries', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListAnomalyGroupSummaries', ], 'input' => [ 'shape' => 'ListAnomalyGroupSummariesRequest', ], 'output' => [ 'shape' => 'ListAnomalyGroupSummariesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListAnomalyGroupTimeSeries' => [ 'name' => 'ListAnomalyGroupTimeSeries', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListAnomalyGroupTimeSeries', ], 'input' => [ 'shape' => 'ListAnomalyGroupTimeSeriesRequest', ], 'output' => [ 'shape' => 'ListAnomalyGroupTimeSeriesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListMetricSets' => [ 'name' => 'ListMetricSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListMetricSets', ], 'input' => [ 'shape' => 'ListMetricSetsRequest', ], 'output' => [ 'shape' => 'ListMetricSetsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'PutFeedback' => [ 'name' => 'PutFeedback', 'http' => [ 'method' => 'POST', 'requestUri' => '/PutFeedback', ], 'input' => [ 'shape' => 'PutFeedbackRequest', ], 'output' => [ 'shape' => 'PutFeedbackResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateAlert' => [ 'name' => 'UpdateAlert', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateAlert', ], 'input' => [ 'shape' => 'UpdateAlertRequest', ], 'output' => [ 'shape' => 'UpdateAlertResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateAnomalyDetector' => [ 'name' => 'UpdateAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateAnomalyDetector', ], 'input' => [ 'shape' => 'UpdateAnomalyDetectorRequest', ], 'output' => [ 'shape' => 'UpdateAnomalyDetectorResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateMetricSet' => [ 'name' => 'UpdateMetricSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateMetricSet', ], 'input' => [ 'shape' => 'UpdateMetricSetRequest', ], 'output' => [ 'shape' => 'UpdateMetricSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'Action' => [ 'type' => 'structure', 'members' => [ 'SNSConfiguration' => [ 'shape' => 'SNSConfiguration', ], 'LambdaConfiguration' => [ 'shape' => 'LambdaConfiguration', ], ], ], 'ActivateAnomalyDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], ], ], 'ActivateAnomalyDetectorResponse' => [ 'type' => 'structure', 'members' => [], ], 'AggregationFunction' => [ 'type' => 'string', 'enum' => [ 'AVG', 'SUM', ], ], 'Alert' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'AlertDescription' => [ 'shape' => 'AlertDescription', ], 'AlertArn' => [ 'shape' => 'Arn', ], 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'AlertName' => [ 'shape' => 'AlertName', ], 'AlertSensitivityThreshold' => [ 'shape' => 'SensitivityThreshold', ], 'AlertType' => [ 'shape' => 'AlertType', ], 'AlertStatus' => [ 'shape' => 'AlertStatus', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'AlertFilters' => [ 'shape' => 'AlertFilters', ], ], ], 'AlertDescription' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*\\S.*', ], 'AlertFilters' => [ 'type' => 'structure', 'members' => [ 'MetricList' => [ 'shape' => 'MetricNameList', ], 'DimensionFilterList' => [ 'shape' => 'DimensionFilterList', ], ], ], 'AlertName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9\\-_]*', ], 'AlertStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'AlertSummary' => [ 'type' => 'structure', 'members' => [ 'AlertArn' => [ 'shape' => 'Arn', ], 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'AlertName' => [ 'shape' => 'AlertName', ], 'AlertSensitivityThreshold' => [ 'shape' => 'SensitivityThreshold', ], 'AlertType' => [ 'shape' => 'AlertType', ], 'AlertStatus' => [ 'shape' => 'AlertStatus', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'AlertSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlertSummary', ], ], 'AlertType' => [ 'type' => 'string', 'enum' => [ 'SNS', 'LAMBDA', ], ], 'AnomalyDetectionTaskStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'FAILED_TO_SCHEDULE', ], ], 'AnomalyDetectionTaskStatusMessage' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*\\S.*', ], 'AnomalyDetectorConfig' => [ 'type' => 'structure', 'members' => [ 'AnomalyDetectorFrequency' => [ 'shape' => 'Frequency', ], ], ], 'AnomalyDetectorConfigSummary' => [ 'type' => 'structure', 'members' => [ 'AnomalyDetectorFrequency' => [ 'shape' => 'Frequency', ], ], ], 'AnomalyDetectorDataQualityMetric' => [ 'type' => 'structure', 'members' => [ 'StartTimestamp' => [ 'shape' => 'Timestamp', ], 'MetricSetDataQualityMetricList' => [ 'shape' => 'MetricSetDataQualityMetricList', ], ], ], 'AnomalyDetectorDataQualityMetricList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnomalyDetectorDataQualityMetric', ], ], 'AnomalyDetectorDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*\\S.*', ], 'AnomalyDetectorFailureType' => [ 'type' => 'string', 'enum' => [ 'ACTIVATION_FAILURE', 'BACK_TEST_ACTIVATION_FAILURE', 'DELETION_FAILURE', 'DEACTIVATION_FAILURE', ], ], 'AnomalyDetectorName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9\\-_]*', ], 'AnomalyDetectorStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ACTIVATING', 'DELETING', 'FAILED', 'INACTIVE', 'LEARNING', 'BACK_TEST_ACTIVATING', 'BACK_TEST_ACTIVE', 'BACK_TEST_COMPLETE', 'DEACTIVATED', 'DEACTIVATING', ], ], 'AnomalyDetectorSummary' => [ 'type' => 'structure', 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'AnomalyDetectorName' => [ 'shape' => 'AnomalyDetectorName', ], 'AnomalyDetectorDescription' => [ 'shape' => 'AnomalyDetectorDescription', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'AnomalyDetectorStatus', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'AnomalyDetectorSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnomalyDetectorSummary', ], ], 'AnomalyGroup' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'TimestampString', ], 'EndTime' => [ 'shape' => 'TimestampString', ], 'AnomalyGroupId' => [ 'shape' => 'UUID', ], 'AnomalyGroupScore' => [ 'shape' => 'Score', ], 'PrimaryMetricName' => [ 'shape' => 'MetricName', ], 'MetricLevelImpactList' => [ 'shape' => 'MetricLevelImpactList', ], ], ], 'AnomalyGroupStatistics' => [ 'type' => 'structure', 'members' => [ 'EvaluationStartDate' => [ 'shape' => 'TimestampString', ], 'TotalCount' => [ 'shape' => 'Integer', ], 'ItemizedMetricStatsList' => [ 'shape' => 'ItemizedMetricStatsList', ], ], ], 'AnomalyGroupSummary' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'TimestampString', ], 'EndTime' => [ 'shape' => 'TimestampString', ], 'AnomalyGroupId' => [ 'shape' => 'UUID', ], 'AnomalyGroupScore' => [ 'shape' => 'Score', ], 'PrimaryMetricName' => [ 'shape' => 'MetricName', ], ], ], 'AnomalyGroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnomalyGroupSummary', ], ], 'AnomalyGroupTimeSeries' => [ 'type' => 'structure', 'required' => [ 'AnomalyGroupId', ], 'members' => [ 'AnomalyGroupId' => [ 'shape' => 'UUID', ], 'TimeSeriesId' => [ 'shape' => 'TimeSeriesId', ], ], ], 'AnomalyGroupTimeSeriesFeedback' => [ 'type' => 'structure', 'required' => [ 'AnomalyGroupId', 'TimeSeriesId', 'IsAnomaly', ], 'members' => [ 'AnomalyGroupId' => [ 'shape' => 'UUID', ], 'TimeSeriesId' => [ 'shape' => 'TimeSeriesId', ], 'IsAnomaly' => [ 'shape' => 'Boolean', ], ], ], 'AppFlowConfig' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'Arn', ], 'FlowName' => [ 'shape' => 'FlowName', ], ], ], 'Arn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:([a-z\\d-]+):.*:.*:.*:.+', ], 'AthenaDataCatalog' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'AthenaDatabaseName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9_]+', ], 'AthenaS3ResultsPath' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^s3://[a-z0-9].+$', ], 'AthenaSourceConfig' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'Arn', ], 'DatabaseName' => [ 'shape' => 'AthenaDatabaseName', ], 'DataCatalog' => [ 'shape' => 'AthenaDataCatalog', ], 'TableName' => [ 'shape' => 'AthenaTableName', ], 'WorkGroupName' => [ 'shape' => 'AthenaWorkGroupName', ], 'S3ResultsPath' => [ 'shape' => 'AthenaS3ResultsPath', ], 'BackTestConfiguration' => [ 'shape' => 'BackTestConfiguration', ], ], ], 'AthenaTableName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_]+', ], 'AthenaWorkGroupName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9._-]{1,128}', ], 'AttributeValue' => [ 'type' => 'structure', 'members' => [ 'S' => [ 'shape' => 'StringAttributeValue', ], 'N' => [ 'shape' => 'NumberAttributeValue', ], 'B' => [ 'shape' => 'BinaryAttributeValue', ], 'SS' => [ 'shape' => 'StringListAttributeValue', ], 'NS' => [ 'shape' => 'NumberListAttributeValue', ], 'BS' => [ 'shape' => 'BinaryListAttributeValue', ], ], ], 'AutoDetectionMetricSource' => [ 'type' => 'structure', 'members' => [ 'S3SourceConfig' => [ 'shape' => 'AutoDetectionS3SourceConfig', ], ], ], 'AutoDetectionS3SourceConfig' => [ 'type' => 'structure', 'members' => [ 'TemplatedPathList' => [ 'shape' => 'TemplatedPathList', ], 'HistoricalDataPathList' => [ 'shape' => 'HistoricalDataPathList', ], ], ], 'BackTestAnomalyDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], ], ], 'BackTestAnomalyDetectorResponse' => [ 'type' => 'structure', 'members' => [], ], 'BackTestConfiguration' => [ 'type' => 'structure', 'required' => [ 'RunBackTestMode', ], 'members' => [ 'RunBackTestMode' => [ 'shape' => 'Boolean', ], ], ], 'BinaryAttributeValue' => [ 'type' => 'string', ], 'BinaryListAttributeValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'BinaryAttributeValue', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'CSVFileCompression' => [ 'type' => 'string', 'enum' => [ 'NONE', 'GZIP', ], ], 'Charset' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9\\-_]*', ], 'CloudWatchConfig' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'Arn', ], 'BackTestConfiguration' => [ 'shape' => 'BackTestConfiguration', ], ], ], 'ColumnName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9\\-_]*', ], 'Confidence' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'LOW', 'NONE', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ContributionMatrix' => [ 'type' => 'structure', 'members' => [ 'DimensionContributionList' => [ 'shape' => 'DimensionContributionList', ], ], ], 'CreateAlertRequest' => [ 'type' => 'structure', 'required' => [ 'AlertName', 'AnomalyDetectorArn', 'Action', ], 'members' => [ 'AlertName' => [ 'shape' => 'AlertName', ], 'AlertSensitivityThreshold' => [ 'shape' => 'SensitivityThreshold', ], 'AlertDescription' => [ 'shape' => 'AlertDescription', ], 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'Action' => [ 'shape' => 'Action', ], 'Tags' => [ 'shape' => 'TagMap', ], 'AlertFilters' => [ 'shape' => 'AlertFilters', ], ], ], 'CreateAlertResponse' => [ 'type' => 'structure', 'members' => [ 'AlertArn' => [ 'shape' => 'Arn', ], ], ], 'CreateAnomalyDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorName', 'AnomalyDetectorConfig', ], 'members' => [ 'AnomalyDetectorName' => [ 'shape' => 'AnomalyDetectorName', ], 'AnomalyDetectorDescription' => [ 'shape' => 'AnomalyDetectorDescription', ], 'AnomalyDetectorConfig' => [ 'shape' => 'AnomalyDetectorConfig', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAnomalyDetectorResponse' => [ 'type' => 'structure', 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], ], ], 'CreateMetricSetRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', 'MetricSetName', 'MetricList', 'MetricSource', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'MetricSetName' => [ 'shape' => 'MetricSetName', ], 'MetricSetDescription' => [ 'shape' => 'MetricSetDescription', ], 'MetricList' => [ 'shape' => 'MetricList', ], 'Offset' => [ 'shape' => 'Offset', 'box' => true, ], 'TimestampColumn' => [ 'shape' => 'TimestampColumn', ], 'DimensionList' => [ 'shape' => 'DimensionList', ], 'MetricSetFrequency' => [ 'shape' => 'Frequency', ], 'MetricSource' => [ 'shape' => 'MetricSource', ], 'Timezone' => [ 'shape' => 'Timezone', ], 'Tags' => [ 'shape' => 'TagMap', ], 'DimensionFilterList' => [ 'shape' => 'MetricSetDimensionFilterList', ], ], ], 'CreateMetricSetResponse' => [ 'type' => 'structure', 'members' => [ 'MetricSetArn' => [ 'shape' => 'Arn', ], ], ], 'CsvFormatDescriptor' => [ 'type' => 'structure', 'members' => [ 'FileCompression' => [ 'shape' => 'CSVFileCompression', ], 'Charset' => [ 'shape' => 'Charset', ], 'ContainsHeader' => [ 'shape' => 'Boolean', ], 'Delimiter' => [ 'shape' => 'Delimiter', ], 'HeaderList' => [ 'shape' => 'HeaderList', ], 'QuoteSymbol' => [ 'shape' => 'QuoteSymbol', ], ], ], 'DataItem' => [ 'type' => 'string', ], 'DataQualityMetric' => [ 'type' => 'structure', 'members' => [ 'MetricType' => [ 'shape' => 'DataQualityMetricType', ], 'MetricDescription' => [ 'shape' => 'DataQualityMetricDescription', ], 'RelatedColumnName' => [ 'shape' => 'RelatedColumnName', ], 'MetricValue' => [ 'shape' => 'Double', ], ], ], 'DataQualityMetricDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*\\S.*', ], 'DataQualityMetricList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataQualityMetric', ], ], 'DataQualityMetricType' => [ 'type' => 'string', 'enum' => [ 'COLUMN_COMPLETENESS', 'DIMENSION_UNIQUENESS', 'TIME_SERIES_COUNT', 'ROWS_PROCESSED', 'ROWS_PARTIAL_COMPLIANCE', 'INVALID_ROWS_COMPLIANCE', 'BACKTEST_TRAINING_DATA_START_TIME_STAMP', 'BACKTEST_TRAINING_DATA_END_TIME_STAMP', 'BACKTEST_INFERENCE_DATA_START_TIME_STAMP', 'BACKTEST_INFERENCE_DATA_END_TIME_STAMP', ], ], 'DatabaseHost' => [ 'type' => 'string', 'max' => 253, 'min' => 1, 'pattern' => '.*\\S.*', ], 'DatabasePort' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'DateTimeFormat' => [ 'type' => 'string', 'max' => 63, 'pattern' => '.*\\S.*', ], 'DeactivateAnomalyDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], ], ], 'DeactivateAnomalyDetectorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAlertRequest' => [ 'type' => 'structure', 'required' => [ 'AlertArn', ], 'members' => [ 'AlertArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteAlertResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAnomalyDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteAnomalyDetectorResponse' => [ 'type' => 'structure', 'members' => [], ], 'Delimiter' => [ 'type' => 'string', 'max' => 1, 'pattern' => '[^\\r\\n]', ], 'DescribeAlertRequest' => [ 'type' => 'structure', 'required' => [ 'AlertArn', ], 'members' => [ 'AlertArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAlertResponse' => [ 'type' => 'structure', 'members' => [ 'Alert' => [ 'shape' => 'Alert', ], ], ], 'DescribeAnomalyDetectionExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'Timestamp' => [ 'shape' => 'TimestampString', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAnomalyDetectionExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'ExecutionList' => [ 'shape' => 'ExecutionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAnomalyDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAnomalyDetectorResponse' => [ 'type' => 'structure', 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'AnomalyDetectorName' => [ 'shape' => 'AnomalyDetectorName', ], 'AnomalyDetectorDescription' => [ 'shape' => 'AnomalyDetectorDescription', ], 'AnomalyDetectorConfig' => [ 'shape' => 'AnomalyDetectorConfigSummary', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'AnomalyDetectorStatus', ], 'FailureReason' => [ 'shape' => 'ErrorMessage', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'FailureType' => [ 'shape' => 'AnomalyDetectorFailureType', ], ], ], 'DescribeMetricSetRequest' => [ 'type' => 'structure', 'required' => [ 'MetricSetArn', ], 'members' => [ 'MetricSetArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeMetricSetResponse' => [ 'type' => 'structure', 'members' => [ 'MetricSetArn' => [ 'shape' => 'Arn', ], 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'MetricSetName' => [ 'shape' => 'MetricSetName', ], 'MetricSetDescription' => [ 'shape' => 'MetricSetDescription', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], 'Offset' => [ 'shape' => 'Offset', 'box' => true, ], 'MetricList' => [ 'shape' => 'MetricList', ], 'TimestampColumn' => [ 'shape' => 'TimestampColumn', ], 'DimensionList' => [ 'shape' => 'DimensionList', ], 'MetricSetFrequency' => [ 'shape' => 'Frequency', ], 'Timezone' => [ 'shape' => 'Timezone', ], 'MetricSource' => [ 'shape' => 'MetricSource', ], 'DimensionFilterList' => [ 'shape' => 'MetricSetDimensionFilterList', ], ], ], 'DetectMetricSetConfigRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', 'AutoDetectionMetricSource', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'AutoDetectionMetricSource' => [ 'shape' => 'AutoDetectionMetricSource', ], ], ], 'DetectMetricSetConfigResponse' => [ 'type' => 'structure', 'members' => [ 'DetectedMetricSetConfig' => [ 'shape' => 'DetectedMetricSetConfig', ], ], ], 'DetectedCsvFormatDescriptor' => [ 'type' => 'structure', 'members' => [ 'FileCompression' => [ 'shape' => 'DetectedField', ], 'Charset' => [ 'shape' => 'DetectedField', ], 'ContainsHeader' => [ 'shape' => 'DetectedField', ], 'Delimiter' => [ 'shape' => 'DetectedField', ], 'HeaderList' => [ 'shape' => 'DetectedField', ], 'QuoteSymbol' => [ 'shape' => 'DetectedField', ], ], ], 'DetectedField' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'AttributeValue', ], 'Confidence' => [ 'shape' => 'Confidence', ], 'Message' => [ 'shape' => 'Message', ], ], ], 'DetectedFileFormatDescriptor' => [ 'type' => 'structure', 'members' => [ 'CsvFormatDescriptor' => [ 'shape' => 'DetectedCsvFormatDescriptor', ], 'JsonFormatDescriptor' => [ 'shape' => 'DetectedJsonFormatDescriptor', ], ], ], 'DetectedJsonFormatDescriptor' => [ 'type' => 'structure', 'members' => [ 'FileCompression' => [ 'shape' => 'DetectedField', ], 'Charset' => [ 'shape' => 'DetectedField', ], ], ], 'DetectedMetricSetConfig' => [ 'type' => 'structure', 'members' => [ 'Offset' => [ 'shape' => 'DetectedField', ], 'MetricSetFrequency' => [ 'shape' => 'DetectedField', ], 'MetricSource' => [ 'shape' => 'DetectedMetricSource', ], ], ], 'DetectedMetricSource' => [ 'type' => 'structure', 'members' => [ 'S3SourceConfig' => [ 'shape' => 'DetectedS3SourceConfig', ], ], ], 'DetectedS3SourceConfig' => [ 'type' => 'structure', 'members' => [ 'FileFormatDescriptor' => [ 'shape' => 'DetectedFileFormatDescriptor', ], ], ], 'DimensionContribution' => [ 'type' => 'structure', 'members' => [ 'DimensionName' => [ 'shape' => 'ColumnName', ], 'DimensionValueContributionList' => [ 'shape' => 'DimensionValueContributionList', ], ], ], 'DimensionContributionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionContribution', ], ], 'DimensionFilter' => [ 'type' => 'structure', 'members' => [ 'DimensionName' => [ 'shape' => 'ColumnName', ], 'DimensionValueList' => [ 'shape' => 'DimensionValueList', ], ], ], 'DimensionFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionFilter', ], 'max' => 5, 'min' => 1, ], 'DimensionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnName', ], 'min' => 1, ], 'DimensionNameValue' => [ 'type' => 'structure', 'required' => [ 'DimensionName', 'DimensionValue', ], 'members' => [ 'DimensionName' => [ 'shape' => 'ColumnName', ], 'DimensionValue' => [ 'shape' => 'DimensionValue', ], ], ], 'DimensionNameValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionNameValue', ], ], 'DimensionValue' => [ 'type' => 'string', ], 'DimensionValueContribution' => [ 'type' => 'structure', 'members' => [ 'DimensionValue' => [ 'shape' => 'DimensionValue', ], 'ContributionScore' => [ 'shape' => 'Score', ], ], ], 'DimensionValueContributionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionValueContribution', ], ], 'DimensionValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionValue', ], 'max' => 10, 'min' => 1, ], 'Double' => [ 'type' => 'double', ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 256, ], 'ExecutionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExecutionStatus', ], ], 'ExecutionStatus' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'TimestampString', ], 'Status' => [ 'shape' => 'AnomalyDetectionTaskStatus', ], 'FailureReason' => [ 'shape' => 'AnomalyDetectionTaskStatusMessage', ], ], ], 'FieldName' => [ 'type' => 'string', ], 'FileFormatDescriptor' => [ 'type' => 'structure', 'members' => [ 'CsvFormatDescriptor' => [ 'shape' => 'CsvFormatDescriptor', ], 'JsonFormatDescriptor' => [ 'shape' => 'JsonFormatDescriptor', ], ], ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'DimensionValue' => [ 'shape' => 'DimensionValue', ], 'FilterOperation' => [ 'shape' => 'FilterOperation', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'min' => 1, ], 'FilterOperation' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'FlowName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[a-zA-Z0-9][\\w!@#.-]+', ], 'Frequency' => [ 'type' => 'string', 'enum' => [ 'P1D', 'PT1H', 'PT10M', 'PT5M', ], ], 'GetAnomalyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyGroupId', 'AnomalyDetectorArn', ], 'members' => [ 'AnomalyGroupId' => [ 'shape' => 'UUID', ], 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], ], ], 'GetAnomalyGroupResponse' => [ 'type' => 'structure', 'members' => [ 'AnomalyGroup' => [ 'shape' => 'AnomalyGroup', ], ], ], 'GetDataQualityMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'MetricSetArn' => [ 'shape' => 'Arn', ], ], ], 'GetDataQualityMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'AnomalyDetectorDataQualityMetricList' => [ 'shape' => 'AnomalyDetectorDataQualityMetricList', ], ], ], 'GetFeedbackRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', 'AnomalyGroupTimeSeriesFeedback', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'AnomalyGroupTimeSeriesFeedback' => [ 'shape' => 'AnomalyGroupTimeSeries', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetFeedbackResponse' => [ 'type' => 'structure', 'members' => [ 'AnomalyGroupTimeSeriesFeedback' => [ 'shape' => 'TimeSeriesFeedbackList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetSampleDataRequest' => [ 'type' => 'structure', 'members' => [ 'S3SourceConfig' => [ 'shape' => 'SampleDataS3SourceConfig', ], ], ], 'GetSampleDataResponse' => [ 'type' => 'structure', 'members' => [ 'HeaderValues' => [ 'shape' => 'HeaderValueList', ], 'SampleRows' => [ 'shape' => 'SampleRows', ], ], ], 'HeaderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnName', ], ], 'HeaderValue' => [ 'type' => 'string', ], 'HeaderValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HeaderValue', ], ], 'HistoricalDataPath' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^s3://[a-z0-9].+$', ], 'HistoricalDataPathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HistoricalDataPath', ], 'max' => 1, 'min' => 1, ], 'Integer' => [ 'type' => 'integer', ], 'InterMetricImpactDetails' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'MetricName', ], 'AnomalyGroupId' => [ 'shape' => 'UUID', ], 'RelationshipType' => [ 'shape' => 'RelationshipType', ], 'ContributionPercentage' => [ 'shape' => 'MetricChangePercentage', ], ], ], 'InterMetricImpactList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InterMetricImpactDetails', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ItemizedMetricStats' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'ColumnName', ], 'OccurrenceCount' => [ 'shape' => 'Integer', ], ], ], 'ItemizedMetricStatsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ItemizedMetricStats', ], ], 'JsonFileCompression' => [ 'type' => 'string', 'enum' => [ 'NONE', 'GZIP', ], ], 'JsonFormatDescriptor' => [ 'type' => 'structure', 'members' => [ 'FileCompression' => [ 'shape' => 'JsonFileCompression', ], 'Charset' => [ 'shape' => 'Charset', ], ], ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws.*:kms:.*:[0-9]{12}:key/[a-z0-9]{8}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{12}', ], 'LambdaConfiguration' => [ 'type' => 'structure', 'required' => [ 'RoleArn', 'LambdaArn', ], 'members' => [ 'RoleArn' => [ 'shape' => 'Arn', ], 'LambdaArn' => [ 'shape' => 'Arn', ], ], ], 'ListAlertsRequest' => [ 'type' => 'structure', 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListAlertsResponse' => [ 'type' => 'structure', 'members' => [ 'AlertSummaryList' => [ 'shape' => 'AlertSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAnomalyDetectorsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAnomalyDetectorsResponse' => [ 'type' => 'structure', 'members' => [ 'AnomalyDetectorSummaryList' => [ 'shape' => 'AnomalyDetectorSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAnomalyGroupRelatedMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', 'AnomalyGroupId', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'AnomalyGroupId' => [ 'shape' => 'UUID', ], 'RelationshipTypeFilter' => [ 'shape' => 'RelationshipType', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAnomalyGroupRelatedMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'InterMetricImpactList' => [ 'shape' => 'InterMetricImpactList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAnomalyGroupSummariesRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', 'SensitivityThreshold', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'SensitivityThreshold' => [ 'shape' => 'SensitivityThreshold', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAnomalyGroupSummariesResponse' => [ 'type' => 'structure', 'members' => [ 'AnomalyGroupSummaryList' => [ 'shape' => 'AnomalyGroupSummaryList', ], 'AnomalyGroupStatistics' => [ 'shape' => 'AnomalyGroupStatistics', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAnomalyGroupTimeSeriesRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', 'AnomalyGroupId', 'MetricName', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'AnomalyGroupId' => [ 'shape' => 'UUID', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAnomalyGroupTimeSeriesResponse' => [ 'type' => 'structure', 'members' => [ 'AnomalyGroupId' => [ 'shape' => 'UUID', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'TimestampList' => [ 'shape' => 'TimestampList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TimeSeriesList' => [ 'shape' => 'TimeSeriesList', ], ], ], 'ListMetricSetsRequest' => [ 'type' => 'structure', 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMetricSetsResponse' => [ 'type' => 'structure', 'members' => [ 'MetricSetSummaryList' => [ 'shape' => 'MetricSetSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'Tags', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'Message' => [ 'type' => 'string', ], 'Metric' => [ 'type' => 'structure', 'required' => [ 'MetricName', 'AggregationFunction', ], 'members' => [ 'MetricName' => [ 'shape' => 'ColumnName', ], 'AggregationFunction' => [ 'shape' => 'AggregationFunction', ], 'Namespace' => [ 'shape' => 'Namespace', ], ], ], 'MetricChangePercentage' => [ 'type' => 'double', 'max' => 100.0, 'min' => 0.0, ], 'MetricLevelImpact' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'MetricName', ], 'NumTimeSeries' => [ 'shape' => 'Integer', ], 'ContributionMatrix' => [ 'shape' => 'ContributionMatrix', ], ], ], 'MetricLevelImpactList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricLevelImpact', ], ], 'MetricList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Metric', ], 'min' => 1, ], 'MetricName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9\\-_]*', ], 'MetricNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricName', ], 'max' => 5, 'min' => 1, ], 'MetricSetDataQualityMetric' => [ 'type' => 'structure', 'members' => [ 'MetricSetArn' => [ 'shape' => 'Arn', ], 'DataQualityMetricList' => [ 'shape' => 'DataQualityMetricList', ], ], ], 'MetricSetDataQualityMetricList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricSetDataQualityMetric', ], ], 'MetricSetDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*\\S.*', ], 'MetricSetDimensionFilter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ColumnName', ], 'FilterList' => [ 'shape' => 'FilterList', ], ], ], 'MetricSetDimensionFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricSetDimensionFilter', ], ], 'MetricSetName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9\\-_]*', ], 'MetricSetSummary' => [ 'type' => 'structure', 'members' => [ 'MetricSetArn' => [ 'shape' => 'Arn', ], 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'MetricSetDescription' => [ 'shape' => 'MetricSetDescription', ], 'MetricSetName' => [ 'shape' => 'MetricSetName', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'MetricSetSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricSetSummary', ], ], 'MetricSource' => [ 'type' => 'structure', 'members' => [ 'S3SourceConfig' => [ 'shape' => 'S3SourceConfig', ], 'AppFlowConfig' => [ 'shape' => 'AppFlowConfig', ], 'CloudWatchConfig' => [ 'shape' => 'CloudWatchConfig', ], 'RDSSourceConfig' => [ 'shape' => 'RDSSourceConfig', ], 'RedshiftSourceConfig' => [ 'shape' => 'RedshiftSourceConfig', ], 'AthenaSourceConfig' => [ 'shape' => 'AthenaSourceConfig', ], ], ], 'MetricValue' => [ 'type' => 'double', ], 'MetricValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricValue', ], ], 'Namespace' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[^:].*', ], 'NextToken' => [ 'type' => 'string', 'max' => 3000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'NumberAttributeValue' => [ 'type' => 'string', ], 'NumberListAttributeValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'NumberAttributeValue', ], ], 'Offset' => [ 'type' => 'integer', 'max' => 432000, 'min' => 0, ], 'PoirotSecretManagerArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:([a-z\\d-]+):.*:.*:secret:AmazonLookoutMetrics-.+', ], 'PutFeedbackRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', 'AnomalyGroupTimeSeriesFeedback', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'AnomalyGroupTimeSeriesFeedback' => [ 'shape' => 'AnomalyGroupTimeSeriesFeedback', ], ], ], 'PutFeedbackResponse' => [ 'type' => 'structure', 'members' => [], ], 'QuotaCode' => [ 'type' => 'string', ], 'QuoteSymbol' => [ 'type' => 'string', 'max' => 1, 'pattern' => '[^\\r\\n]|^$', ], 'RDSDatabaseIdentifier' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z](?!.*--)(?!.*-$)[0-9a-zA-Z\\-]*$', ], 'RDSDatabaseName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.]+', ], 'RDSSourceConfig' => [ 'type' => 'structure', 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'RDSDatabaseIdentifier', ], 'DatabaseHost' => [ 'shape' => 'DatabaseHost', ], 'DatabasePort' => [ 'shape' => 'DatabasePort', 'box' => true, ], 'SecretManagerArn' => [ 'shape' => 'PoirotSecretManagerArn', ], 'DatabaseName' => [ 'shape' => 'RDSDatabaseName', ], 'TableName' => [ 'shape' => 'TableName', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'VpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], ], ], 'RedshiftClusterIdentifier' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-z](?!.*--)(?!.*-$)[0-9a-z\\-]*$', ], 'RedshiftDatabaseName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.]+', ], 'RedshiftSourceConfig' => [ 'type' => 'structure', 'members' => [ 'ClusterIdentifier' => [ 'shape' => 'RedshiftClusterIdentifier', ], 'DatabaseHost' => [ 'shape' => 'DatabaseHost', ], 'DatabasePort' => [ 'shape' => 'DatabasePort', 'box' => true, ], 'SecretManagerArn' => [ 'shape' => 'PoirotSecretManagerArn', ], 'DatabaseName' => [ 'shape' => 'RedshiftDatabaseName', ], 'TableName' => [ 'shape' => 'TableName', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'VpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], ], ], 'RelatedColumnName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*\\S.*', ], 'RelationshipType' => [ 'type' => 'string', 'enum' => [ 'CAUSE_OF_INPUT_ANOMALY_GROUP', 'EFFECT_OF_INPUT_ANOMALY_GROUP', ], ], 'ResourceId' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', ], 'S3SourceConfig' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'Arn', ], 'TemplatedPathList' => [ 'shape' => 'TemplatedPathList', ], 'HistoricalDataPathList' => [ 'shape' => 'HistoricalDataPathList', ], 'FileFormatDescriptor' => [ 'shape' => 'FileFormatDescriptor', ], ], ], 'SNSConfiguration' => [ 'type' => 'structure', 'required' => [ 'RoleArn', 'SnsTopicArn', ], 'members' => [ 'RoleArn' => [ 'shape' => 'Arn', ], 'SnsTopicArn' => [ 'shape' => 'Arn', ], 'SnsFormat' => [ 'shape' => 'SnsFormat', ], ], ], 'SampleDataS3SourceConfig' => [ 'type' => 'structure', 'required' => [ 'RoleArn', 'FileFormatDescriptor', ], 'members' => [ 'RoleArn' => [ 'shape' => 'Arn', ], 'TemplatedPathList' => [ 'shape' => 'TemplatedPathList', ], 'HistoricalDataPathList' => [ 'shape' => 'HistoricalDataPathList', ], 'FileFormatDescriptor' => [ 'shape' => 'FileFormatDescriptor', ], ], ], 'SampleRow' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataItem', ], ], 'SampleRows' => [ 'type' => 'list', 'member' => [ 'shape' => 'SampleRow', ], ], 'Score' => [ 'type' => 'double', 'max' => 100.0, 'min' => 0.0, ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[-0-9a-zA-Z]+', ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], ], 'SensitivityThreshold' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'ServiceCode' => [ 'type' => 'string', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'QuotaCode' => [ 'shape' => 'QuotaCode', ], 'ServiceCode' => [ 'shape' => 'ServiceCode', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'SnsFormat' => [ 'type' => 'string', 'enum' => [ 'LONG_TEXT', 'SHORT_TEXT', 'JSON', ], ], 'StringAttributeValue' => [ 'type' => 'string', ], 'StringListAttributeValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringAttributeValue', ], ], 'SubnetId' => [ 'type' => 'string', 'max' => 255, 'pattern' => '[\\-0-9a-zA-Z]+', ], 'SubnetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], ], 'TableName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z][a-zA-Z0-9_.]*$', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'TemplatedPath' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^s3://[a-zA-Z0-9_\\-\\/ {}=]+$', ], 'TemplatedPathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplatedPath', ], 'max' => 1, 'min' => 1, ], 'TimeSeries' => [ 'type' => 'structure', 'required' => [ 'TimeSeriesId', 'DimensionList', 'MetricValueList', ], 'members' => [ 'TimeSeriesId' => [ 'shape' => 'TimeSeriesId', ], 'DimensionList' => [ 'shape' => 'DimensionNameValueList', ], 'MetricValueList' => [ 'shape' => 'MetricValueList', ], ], ], 'TimeSeriesFeedback' => [ 'type' => 'structure', 'members' => [ 'TimeSeriesId' => [ 'shape' => 'TimeSeriesId', ], 'IsAnomaly' => [ 'shape' => 'Boolean', ], ], ], 'TimeSeriesFeedbackList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimeSeriesFeedback', ], ], 'TimeSeriesId' => [ 'type' => 'string', 'max' => 520, 'pattern' => '.*\\S.*', ], 'TimeSeriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimeSeries', ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampColumn' => [ 'type' => 'structure', 'members' => [ 'ColumnName' => [ 'shape' => 'ColumnName', ], 'ColumnFormat' => [ 'shape' => 'DateTimeFormat', ], ], ], 'TimestampList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimestampString', ], ], 'TimestampString' => [ 'type' => 'string', 'max' => 60, 'pattern' => '^([12]\\d{3})-(1[0-2]|0[1-9])-(0[1-9]|[12]\\d|3[01])T([01]\\d|2[0-3]):([0-5]\\d):([0-5]\\d)(Z|(\\+|\\-)(0\\d|1[0-2]):([0-5]\\d)(\\[[[:alnum:]\\/\\_]+\\])?)$', ], 'Timezone' => [ 'type' => 'string', 'max' => 60, 'pattern' => '.*\\S.*', ], 'TooManyRequestsException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'UUID' => [ 'type' => 'string', 'max' => 63, 'pattern' => '[a-z0-9]{8}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{12}', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAlertRequest' => [ 'type' => 'structure', 'required' => [ 'AlertArn', ], 'members' => [ 'AlertArn' => [ 'shape' => 'Arn', ], 'AlertDescription' => [ 'shape' => 'AlertDescription', ], 'AlertSensitivityThreshold' => [ 'shape' => 'SensitivityThreshold', ], 'Action' => [ 'shape' => 'Action', ], 'AlertFilters' => [ 'shape' => 'AlertFilters', ], ], ], 'UpdateAlertResponse' => [ 'type' => 'structure', 'members' => [ 'AlertArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateAnomalyDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyDetectorArn', ], 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'AnomalyDetectorDescription' => [ 'shape' => 'AnomalyDetectorDescription', ], 'AnomalyDetectorConfig' => [ 'shape' => 'AnomalyDetectorConfig', ], ], ], 'UpdateAnomalyDetectorResponse' => [ 'type' => 'structure', 'members' => [ 'AnomalyDetectorArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateMetricSetRequest' => [ 'type' => 'structure', 'required' => [ 'MetricSetArn', ], 'members' => [ 'MetricSetArn' => [ 'shape' => 'Arn', ], 'MetricSetDescription' => [ 'shape' => 'MetricSetDescription', ], 'MetricList' => [ 'shape' => 'MetricList', ], 'Offset' => [ 'shape' => 'Offset', 'box' => true, ], 'TimestampColumn' => [ 'shape' => 'TimestampColumn', ], 'DimensionList' => [ 'shape' => 'DimensionList', ], 'MetricSetFrequency' => [ 'shape' => 'Frequency', ], 'MetricSource' => [ 'shape' => 'MetricSource', ], 'DimensionFilterList' => [ 'shape' => 'MetricSetDimensionFilterList', ], ], ], 'UpdateMetricSetResponse' => [ 'type' => 'structure', 'members' => [ 'MetricSetArn' => [ 'shape' => 'Arn', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', ], 'Fields' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Name', 'Message', ], 'members' => [ 'Name' => [ 'shape' => 'FieldName', ], 'Message' => [ 'shape' => 'Message', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'OTHER', ], ], 'VpcConfiguration' => [ 'type' => 'structure', 'required' => [ 'SubnetIdList', 'SecurityGroupIdList', ], 'members' => [ 'SubnetIdList' => [ 'shape' => 'SubnetIdList', ], 'SecurityGroupIdList' => [ 'shape' => 'SecurityGroupIdList', ], ], ], ],];
