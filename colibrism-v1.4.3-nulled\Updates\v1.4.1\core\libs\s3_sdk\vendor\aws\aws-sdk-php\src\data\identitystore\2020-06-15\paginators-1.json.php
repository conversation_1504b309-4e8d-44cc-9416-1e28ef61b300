<?php
// This file was auto-generated from sdk-root/src/data/identitystore/2020-06-15/paginators-1.json
return [ 'pagination' => [ 'ListGroupMemberships' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'GroupMemberships', ], 'ListGroupMembershipsForMember' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'GroupMemberships', ], 'ListGroups' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Groups', ], 'ListUsers' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Users', ], ],];
