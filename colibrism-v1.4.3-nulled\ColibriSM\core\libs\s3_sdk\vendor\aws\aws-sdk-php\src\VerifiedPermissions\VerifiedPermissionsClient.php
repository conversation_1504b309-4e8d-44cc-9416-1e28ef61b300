<?php
namespace Aws\VerifiedPermissions;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Verified Permissions** service.
 * @method \Aws\Result batchIsAuthorized(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchIsAuthorizedAsync(array $args = [])
 * @method \Aws\Result createIdentitySource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createIdentitySourceAsync(array $args = [])
 * @method \Aws\Result createPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPolicyAsync(array $args = [])
 * @method \Aws\Result createPolicyStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPolicyStoreAsync(array $args = [])
 * @method \Aws\Result createPolicyTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPolicyTemplateAsync(array $args = [])
 * @method \Aws\Result deleteIdentitySource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteIdentitySourceAsync(array $args = [])
 * @method \Aws\Result deletePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePolicyAsync(array $args = [])
 * @method \Aws\Result deletePolicyStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePolicyStoreAsync(array $args = [])
 * @method \Aws\Result deletePolicyTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePolicyTemplateAsync(array $args = [])
 * @method \Aws\Result getIdentitySource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getIdentitySourceAsync(array $args = [])
 * @method \Aws\Result getPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPolicyAsync(array $args = [])
 * @method \Aws\Result getPolicyStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPolicyStoreAsync(array $args = [])
 * @method \Aws\Result getPolicyTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPolicyTemplateAsync(array $args = [])
 * @method \Aws\Result getSchema(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSchemaAsync(array $args = [])
 * @method \Aws\Result isAuthorized(array $args = [])
 * @method \GuzzleHttp\Promise\Promise isAuthorizedAsync(array $args = [])
 * @method \Aws\Result isAuthorizedWithToken(array $args = [])
 * @method \GuzzleHttp\Promise\Promise isAuthorizedWithTokenAsync(array $args = [])
 * @method \Aws\Result listIdentitySources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listIdentitySourcesAsync(array $args = [])
 * @method \Aws\Result listPolicies(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPoliciesAsync(array $args = [])
 * @method \Aws\Result listPolicyStores(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPolicyStoresAsync(array $args = [])
 * @method \Aws\Result listPolicyTemplates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPolicyTemplatesAsync(array $args = [])
 * @method \Aws\Result putSchema(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putSchemaAsync(array $args = [])
 * @method \Aws\Result updateIdentitySource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateIdentitySourceAsync(array $args = [])
 * @method \Aws\Result updatePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updatePolicyAsync(array $args = [])
 * @method \Aws\Result updatePolicyStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updatePolicyStoreAsync(array $args = [])
 * @method \Aws\Result updatePolicyTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updatePolicyTemplateAsync(array $args = [])
 */
class VerifiedPermissionsClient extends AwsClient {}
