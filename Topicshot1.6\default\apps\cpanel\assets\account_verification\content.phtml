<div class="cp-app-container" data-app="account-verification">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Account verification
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card" id="vue-cpanel-account-verification-app">
                <div class="header">
                    <h2>
                        Account Verification requests - ({{total_requests}})
                    </h2>
                </div>
                <div class="body">
                    <div class="regular-table">
                        <table class="table table-hover no-mb">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>
                                        Username
                                    </th>
                                    <th>
                                        E-mail address
                                    </th>
                                    <th>
                                        Last seen
                                    </th>
                                    
                                    <th>
                                        Status
                                    </th>
                                    <th>
                                        Requested at
                                    </th>
                                    <th>
                                        Action
                                    </th>
                                </tr>
                            </thead>
                            <tbody data-an="requests-list">
                                <?php if (not_empty($cl['requests'])): ?>
                                    <?php foreach ($cl['requests'] as $cl['li']): ?>
                                        <?php echo cl_template('cpanel/assets/account_verification/includes/list_item'); ?>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7">
                                            <div class="empty-table">
                                                <h4><?php echo('No requests found'); ?></h4>
                                                <p>
                                                    <?php echo('It looks like there are no account verification requests in your database yet. All account verification requests will be displayed here.'); ?>
                                                </p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        <div class="table-pagination">
                            <a v-if="show_ctrls" v-bind:class="{'disabled': dis_prev_ctrl}" v-on:click="paginate('up')" href="javascript:void(0);" class="pagination-ctrls prev">
                                <?php echo cl_ficon("arrow_left"); ?>
                            </a>

                            <a v-if="show_ctrls" v-bind:class="{'disabled': dis_next_ctrl}" v-on:click="paginate('down')" href="javascript:void(0);" class="pagination-ctrls next">
                                <?php echo cl_ficon("arrow_right"); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/account_verification/scripts/app_master_script'); ?>