<?php
// This file was auto-generated from sdk-root/src/data/docdb/2014-10-31/paginators-1.json
return [ 'pagination' => [ 'DescribeCertificates' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'Certificates', ], 'DescribeDBClusterParameterGroups' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBClusterParameterGroups', ], 'DescribeDBClusterParameters' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'Parameters', ], 'DescribeDBClusterSnapshots' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBClusterSnapshots', ], 'DescribeDBClusters' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBClusters', ], 'DescribeDBEngineVersions' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBEngineVersions', ], 'DescribeDBInstances' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBInstances', ], 'DescribeDBSubnetGroups' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBSubnetGroups', ], 'DescribeEventSubscriptions' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'EventSubscriptionsList', ], 'DescribeEvents' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'Events', ], 'DescribeGlobalClusters' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'GlobalClusters', ], 'DescribeOrderableDBInstanceOptions' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'OrderableDBInstanceOptions', ], 'DescribePendingMaintenanceActions' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'PendingMaintenanceActions', ], 'ListTagsForResource' => [ 'result_key' => 'TagList', ], ],];
