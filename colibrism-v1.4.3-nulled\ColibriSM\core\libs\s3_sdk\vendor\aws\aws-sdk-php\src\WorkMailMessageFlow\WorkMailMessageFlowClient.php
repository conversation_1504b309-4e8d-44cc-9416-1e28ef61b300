<?php
namespace Aws\WorkMailMessageFlow;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon WorkMail Message Flow** service.
 * @method \Aws\Result getRawMessageContent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getRawMessageContentAsync(array $args = [])
 * @method \Aws\Result putRawMessageContent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putRawMessageContentAsync(array $args = [])
 */
class WorkMailMessageFlowClient extends AwsClient {}
