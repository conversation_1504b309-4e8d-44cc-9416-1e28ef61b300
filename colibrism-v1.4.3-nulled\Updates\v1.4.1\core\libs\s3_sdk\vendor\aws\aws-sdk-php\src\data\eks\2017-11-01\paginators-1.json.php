<?php
// This file was auto-generated from sdk-root/src/data/eks/2017-11-01/paginators-1.json
return [ 'pagination' => [ 'DescribeAddonVersions' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'addons', ], 'ListAddons' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'addons', ], 'ListClusters' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'clusters', ], 'ListEksAnywhereSubscriptions' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'subscriptions', ], 'ListFargateProfiles' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'fargateProfileNames', ], 'ListIdentityProviderConfigs' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'identityProviderConfigs', ], 'ListNodegroups' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'nodegroups', ], 'ListPodIdentityAssociations' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'associations', ], 'ListUpdates' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'updateIds', ], ],];
