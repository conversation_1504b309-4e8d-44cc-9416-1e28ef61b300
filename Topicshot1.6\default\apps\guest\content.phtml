<div class="welcome-page" data-app="guest">
	<div class="welcome-page__slider" id="guest-slider">
		<div v-for="(img, ind) in images" class="slide" v-bind:class="{'show': ind == curr_ind}" v-bind:style="{'background-image': 'url(' + img + ')'}"></div>
	</div>
	<div class="welcome-page__body">
		<div class="row justify-content-center">
			<div class="col-xxl-9 col-xl-10 col-12">
				<div class="row">
					<div class="col-xxl-6 col-xl-6 col-lg-6 col-md-12 ml-auto">
						<div class="welcome-page__rp">
							<div class="welcome-page__rp-inner">
								<div class="welcome-page-form">
									<div class="welcome-page-form__header">
										<a href="<?php echo cl_link('/'); ?>" class="logo">
											<img src="/themes/default/statics/img/logo_welcome.png" alt="Logo">
										</a>
										<h1>
											<?php echo  cl_translate("Welcome to {@site_name@}", array("site_name" => $cl["config"]["name"])); ?>
										</h1>
									</div>
									<div class="welcome-page-form__body">
										<div class="auth-form">
											<div class="auth-form-holder">
												<?php if ($cl["auth_type"] == "login"): ?>
													<?php echo cl_template('auth/login'); ?>
												<?php elseif($cl["auth_type"] == "forgot_pass"): ?>
													<?php echo cl_template('auth/reset_password'); ?>
												<?php elseif($cl["auth_type"] == "reset_pass" && not_empty($cl['em_code'])): ?>
													<?php echo cl_template('auth/new_password'); ?>
												<?php else: ?>
													<?php if ($cl['config']['user_signup'] == "on" || not_empty($cl["invite_code_status"])): ?>
														<?php echo cl_template('auth/register'); ?>
													<?php else: ?>
														<?php echo cl_template('auth/register_off'); ?>
													<?php endif; ?>
												<?php endif; ?>
											</div>	
										</div>
									</div>	
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="welcome-page__footer">
		<div class="row justify-content-center">
			<div class="col-xxl-9 col-xl-10 col-12">
				<div class="welcome-page__bp-wrapper">
					<div class="welcome-page__bp">
						<?php echo cl_template('main/footer'); ?>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	$(document).ready(function() {
		var _app = $("[data-app='guest']");

		new Vue({
			el: "#guest-slider",
			data: {
				curr_ind: 0,
				images: <?php echo cl_minify_js(json($cl["slider_imgs"], true)); ?>
			},
			created: function() {
				var _app_ = this;

				if (_app_.images.length >= 2) {
					setInterval(function() {

						if (_app_.curr_ind >= _app_.images.length - 1) {
							_app_.curr_ind = 0;
						}
						else{
							_app_.curr_ind += 1;
						}

					}, 3000);
				}
			}
		});

		
	});
</script>

<?php echo cl_template('auth/scripts/app_master_script'); ?>

