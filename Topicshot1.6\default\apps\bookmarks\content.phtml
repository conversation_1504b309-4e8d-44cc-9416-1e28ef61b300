<div class="timeline-container" data-app="bookmarks">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link('bookmarks'); ?>" data-spa="true">
						<?php echo cl_translate("Bookmarks"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link('/'); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>	
	<?php if (not_empty($cl["bookmarks"])): ?>
		<div class="timeline-posts-container">
			<div class="timeline-posts-ls" data-an="entry-list">
				<?php foreach ($cl["bookmarks"] as $cl['li']): ?>
					<?php echo cl_template('timeline/post'); ?>
				<?php endforeach; ?>
			</div>

			<?php if (count($cl["bookmarks"]) == 30): ?>
				<div class="timeline-data-loader">
					<button class="timeline-data-loader__btn" data-an="load-more">
						<?php echo cl_translate("Show more"); ?>
					</button>
				</div>
			<?php endif; ?>
		</div>
	<?php else: ?>
		<div class="timeline-placeholder">
			<div class="icon">
				<div class="icon__bg">
					<?php echo cl_ficon('bookmark'); ?>
				</div>
			</div>
			<div class="pl-message">
				<h4>
					<?php echo cl_translate("No bookmarks yet!"); ?>
				</h4>
				<p>
					<?php echo cl_translate("It looks like you don't have any bookmarks yet. To add a post to the list of your bookmarks, open the menu at the bottom of the publication and click on (Add to Bookmarks)"); ?>
				</p>
			</div>
		</div>
	<?php endif; ?>
	
	<?php echo cl_template('bookmarks/scripts/app_master_script'); ?>
</div>
