<?php
// This file was auto-generated from sdk-root/src/data/iot-roborunner/2018-05-10/paginators-1.json
return [ 'pagination' => [ 'ListDestinations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'destinations', ], 'ListSites' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'sites', ], 'ListWorkerFleets' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'workerFleets', ], 'ListWorkers' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'workers', ], ],];
