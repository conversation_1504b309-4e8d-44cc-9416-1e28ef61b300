<?php
// This file was auto-generated from sdk-root/src/data/fsx/2018-03-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-03-01', 'endpointPrefix' => 'fsx', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Amazon FSx', 'serviceId' => 'FSx', 'signatureVersion' => 'v4', 'signingName' => 'fsx', 'targetPrefix' => 'AWSSimbaAPIService_v20180301', 'uid' => 'fsx-2018-03-01', ], 'operations' => [ 'AssociateFileSystemAliases' => [ 'name' => 'AssociateFileSystemAliases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateFileSystemAliasesRequest', ], 'output' => [ 'shape' => 'AssociateFileSystemAliasesResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'InternalServerError', ], ], ], 'CancelDataRepositoryTask' => [ 'name' => 'CancelDataRepositoryTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelDataRepositoryTaskRequest', ], 'output' => [ 'shape' => 'CancelDataRepositoryTaskResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'DataRepositoryTaskNotFound', ], [ 'shape' => 'DataRepositoryTaskEnded', ], [ 'shape' => 'InternalServerError', ], ], 'idempotent' => true, ], 'CopyBackup' => [ 'name' => 'CopyBackup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopyBackupRequest', ], 'output' => [ 'shape' => 'CopyBackupResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'BackupNotFound', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidSourceKmsKey', ], [ 'shape' => 'InvalidDestinationKmsKey', ], [ 'shape' => 'InvalidRegion', ], [ 'shape' => 'SourceBackupUnavailable', ], [ 'shape' => 'IncompatibleRegionForMultiAZ', ], ], 'idempotent' => true, ], 'CopySnapshotAndUpdateVolume' => [ 'name' => 'CopySnapshotAndUpdateVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopySnapshotAndUpdateVolumeRequest', ], 'output' => [ 'shape' => 'CopySnapshotAndUpdateVolumeResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ServiceLimitExceeded', ], ], 'idempotent' => true, ], 'CreateBackup' => [ 'name' => 'CreateBackup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBackupRequest', ], 'output' => [ 'shape' => 'CreateBackupResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'VolumeNotFound', ], [ 'shape' => 'BackupInProgress', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'idempotent' => true, ], 'CreateDataRepositoryAssociation' => [ 'name' => 'CreateDataRepositoryAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDataRepositoryAssociationRequest', ], 'output' => [ 'shape' => 'CreateDataRepositoryAssociationResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'idempotent' => true, ], 'CreateDataRepositoryTask' => [ 'name' => 'CreateDataRepositoryTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDataRepositoryTaskRequest', ], 'output' => [ 'shape' => 'CreateDataRepositoryTaskResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'DataRepositoryTaskExecuting', ], ], 'idempotent' => true, ], 'CreateFileCache' => [ 'name' => 'CreateFileCache', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFileCacheRequest', ], 'output' => [ 'shape' => 'CreateFileCacheResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InvalidNetworkSettings', ], [ 'shape' => 'InvalidPerUnitStorageThroughput', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'MissingFileCacheConfiguration', ], ], 'idempotent' => true, ], 'CreateFileSystem' => [ 'name' => 'CreateFileSystem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFileSystemRequest', ], 'output' => [ 'shape' => 'CreateFileSystemResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'ActiveDirectoryError', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InvalidImportPath', ], [ 'shape' => 'InvalidExportPath', ], [ 'shape' => 'InvalidNetworkSettings', ], [ 'shape' => 'InvalidPerUnitStorageThroughput', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'MissingFileSystemConfiguration', ], ], ], 'CreateFileSystemFromBackup' => [ 'name' => 'CreateFileSystemFromBackup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFileSystemFromBackupRequest', ], 'output' => [ 'shape' => 'CreateFileSystemFromBackupResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'ActiveDirectoryError', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InvalidNetworkSettings', ], [ 'shape' => 'InvalidPerUnitStorageThroughput', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'BackupNotFound', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'MissingFileSystemConfiguration', ], ], ], 'CreateSnapshot' => [ 'name' => 'CreateSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSnapshotRequest', ], 'output' => [ 'shape' => 'CreateSnapshotResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'VolumeNotFound', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'idempotent' => true, ], 'CreateStorageVirtualMachine' => [ 'name' => 'CreateStorageVirtualMachine', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStorageVirtualMachineRequest', ], 'output' => [ 'shape' => 'CreateStorageVirtualMachineResponse', ], 'errors' => [ [ 'shape' => 'ActiveDirectoryError', ], [ 'shape' => 'BadRequest', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'CreateVolume' => [ 'name' => 'CreateVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateVolumeRequest', ], 'output' => [ 'shape' => 'CreateVolumeResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'MissingVolumeConfiguration', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'StorageVirtualMachineNotFound', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'CreateVolumeFromBackup' => [ 'name' => 'CreateVolumeFromBackup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateVolumeFromBackupRequest', ], 'output' => [ 'shape' => 'CreateVolumeFromBackupResponse', ], 'errors' => [ [ 'shape' => 'BackupNotFound', ], [ 'shape' => 'BadRequest', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'MissingVolumeConfiguration', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'StorageVirtualMachineNotFound', ], ], ], 'DeleteBackup' => [ 'name' => 'DeleteBackup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBackupRequest', ], 'output' => [ 'shape' => 'DeleteBackupResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'BackupInProgress', ], [ 'shape' => 'BackupNotFound', ], [ 'shape' => 'BackupRestoring', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'BackupBeingCopied', ], ], 'idempotent' => true, ], 'DeleteDataRepositoryAssociation' => [ 'name' => 'DeleteDataRepositoryAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDataRepositoryAssociationRequest', ], 'output' => [ 'shape' => 'DeleteDataRepositoryAssociationResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'DataRepositoryAssociationNotFound', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'idempotent' => true, ], 'DeleteFileCache' => [ 'name' => 'DeleteFileCache', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFileCacheRequest', ], 'output' => [ 'shape' => 'DeleteFileCacheResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'FileCacheNotFound', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'idempotent' => true, ], 'DeleteFileSystem' => [ 'name' => 'DeleteFileSystem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFileSystemRequest', ], 'output' => [ 'shape' => 'DeleteFileSystemResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'idempotent' => true, ], 'DeleteSnapshot' => [ 'name' => 'DeleteSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSnapshotRequest', ], 'output' => [ 'shape' => 'DeleteSnapshotResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'SnapshotNotFound', ], ], 'idempotent' => true, ], 'DeleteStorageVirtualMachine' => [ 'name' => 'DeleteStorageVirtualMachine', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStorageVirtualMachineRequest', ], 'output' => [ 'shape' => 'DeleteStorageVirtualMachineResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'StorageVirtualMachineNotFound', ], ], ], 'DeleteVolume' => [ 'name' => 'DeleteVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteVolumeRequest', ], 'output' => [ 'shape' => 'DeleteVolumeResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'VolumeNotFound', ], [ 'shape' => 'ServiceLimitExceeded', ], ], ], 'DescribeBackups' => [ 'name' => 'DescribeBackups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeBackupsRequest', ], 'output' => [ 'shape' => 'DescribeBackupsResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'VolumeNotFound', ], [ 'shape' => 'BackupNotFound', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeDataRepositoryAssociations' => [ 'name' => 'DescribeDataRepositoryAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDataRepositoryAssociationsRequest', ], 'output' => [ 'shape' => 'DescribeDataRepositoryAssociationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'DataRepositoryAssociationNotFound', ], [ 'shape' => 'InvalidDataRepositoryType', ], [ 'shape' => 'InternalServerError', ], ], 'idempotent' => true, ], 'DescribeDataRepositoryTasks' => [ 'name' => 'DescribeDataRepositoryTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDataRepositoryTasksRequest', ], 'output' => [ 'shape' => 'DescribeDataRepositoryTasksResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'DataRepositoryTaskNotFound', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeFileCaches' => [ 'name' => 'DescribeFileCaches', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFileCachesRequest', ], 'output' => [ 'shape' => 'DescribeFileCachesResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'FileCacheNotFound', ], [ 'shape' => 'InternalServerError', ], ], 'idempotent' => true, ], 'DescribeFileSystemAliases' => [ 'name' => 'DescribeFileSystemAliases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFileSystemAliasesRequest', ], 'output' => [ 'shape' => 'DescribeFileSystemAliasesResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeFileSystems' => [ 'name' => 'DescribeFileSystems', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFileSystemsRequest', ], 'output' => [ 'shape' => 'DescribeFileSystemsResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeSharedVpcConfiguration' => [ 'name' => 'DescribeSharedVpcConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSharedVpcConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeSharedVpcConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeSnapshots' => [ 'name' => 'DescribeSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSnapshotsRequest', ], 'output' => [ 'shape' => 'DescribeSnapshotsResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'SnapshotNotFound', ], ], ], 'DescribeStorageVirtualMachines' => [ 'name' => 'DescribeStorageVirtualMachines', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStorageVirtualMachinesRequest', ], 'output' => [ 'shape' => 'DescribeStorageVirtualMachinesResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'StorageVirtualMachineNotFound', ], ], ], 'DescribeVolumes' => [ 'name' => 'DescribeVolumes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeVolumesRequest', ], 'output' => [ 'shape' => 'DescribeVolumesResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'VolumeNotFound', ], ], ], 'DisassociateFileSystemAliases' => [ 'name' => 'DisassociateFileSystemAliases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateFileSystemAliasesRequest', ], 'output' => [ 'shape' => 'DisassociateFileSystemAliasesResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFound', ], [ 'shape' => 'NotServiceResourceError', ], [ 'shape' => 'ResourceDoesNotSupportTagging', ], ], ], 'ReleaseFileSystemNfsV3Locks' => [ 'name' => 'ReleaseFileSystemNfsV3Locks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ReleaseFileSystemNfsV3LocksRequest', ], 'output' => [ 'shape' => 'ReleaseFileSystemNfsV3LocksResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'idempotent' => true, ], 'RestoreVolumeFromSnapshot' => [ 'name' => 'RestoreVolumeFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreVolumeFromSnapshotRequest', ], 'output' => [ 'shape' => 'RestoreVolumeFromSnapshotResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'VolumeNotFound', ], ], 'idempotent' => true, ], 'StartMisconfiguredStateRecovery' => [ 'name' => 'StartMisconfiguredStateRecovery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMisconfiguredStateRecoveryRequest', ], 'output' => [ 'shape' => 'StartMisconfiguredStateRecoveryResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'InternalServerError', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFound', ], [ 'shape' => 'NotServiceResourceError', ], [ 'shape' => 'ResourceDoesNotSupportTagging', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFound', ], [ 'shape' => 'NotServiceResourceError', ], [ 'shape' => 'ResourceDoesNotSupportTagging', ], ], 'idempotent' => true, ], 'UpdateDataRepositoryAssociation' => [ 'name' => 'UpdateDataRepositoryAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDataRepositoryAssociationRequest', ], 'output' => [ 'shape' => 'UpdateDataRepositoryAssociationResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'DataRepositoryAssociationNotFound', ], [ 'shape' => 'ServiceLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'idempotent' => true, ], 'UpdateFileCache' => [ 'name' => 'UpdateFileCache', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFileCacheRequest', ], 'output' => [ 'shape' => 'UpdateFileCacheResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'FileCacheNotFound', ], [ 'shape' => 'MissingFileCacheConfiguration', ], [ 'shape' => 'ServiceLimitExceeded', ], ], 'idempotent' => true, ], 'UpdateFileSystem' => [ 'name' => 'UpdateFileSystem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFileSystemRequest', ], 'output' => [ 'shape' => 'UpdateFileSystemResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InvalidNetworkSettings', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'FileSystemNotFound', ], [ 'shape' => 'MissingFileSystemConfiguration', ], [ 'shape' => 'ServiceLimitExceeded', ], ], ], 'UpdateSharedVpcConfiguration' => [ 'name' => 'UpdateSharedVpcConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSharedVpcConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateSharedVpcConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateSnapshot' => [ 'name' => 'UpdateSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSnapshotRequest', ], 'output' => [ 'shape' => 'UpdateSnapshotResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'SnapshotNotFound', ], [ 'shape' => 'InternalServerError', ], ], 'idempotent' => true, ], 'UpdateStorageVirtualMachine' => [ 'name' => 'UpdateStorageVirtualMachine', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStorageVirtualMachineRequest', ], 'output' => [ 'shape' => 'UpdateStorageVirtualMachineResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'StorageVirtualMachineNotFound', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'UpdateVolume' => [ 'name' => 'UpdateVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateVolumeRequest', ], 'output' => [ 'shape' => 'UpdateVolumeResponse', ], 'errors' => [ [ 'shape' => 'BadRequest', ], [ 'shape' => 'IncompatibleParameterError', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'MissingVolumeConfiguration', ], [ 'shape' => 'VolumeNotFound', ], ], ], ], 'shapes' => [ 'AWSAccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'ActiveDirectoryBackupAttributes' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'ActiveDirectoryFullyQualifiedName', ], 'ActiveDirectoryId' => [ 'shape' => 'DirectoryId', ], 'ResourceARN' => [ 'shape' => 'ResourceARN', ], ], ], 'ActiveDirectoryError' => [ 'type' => 'structure', 'required' => [ 'ActiveDirectoryId', ], 'members' => [ 'ActiveDirectoryId' => [ 'shape' => 'DirectoryId', ], 'Type' => [ 'shape' => 'ActiveDirectoryErrorType', ], 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ActiveDirectoryErrorType' => [ 'type' => 'string', 'enum' => [ 'DOMAIN_NOT_FOUND', 'INCOMPATIBLE_DOMAIN_MODE', 'WRONG_VPC', 'INVALID_DOMAIN_STAGE', ], ], 'ActiveDirectoryFullyQualifiedName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,255}$', ], 'AdminPassword' => [ 'type' => 'string', 'max' => 50, 'min' => 8, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{8,50}$', 'sensitive' => true, ], 'AdministrativeAction' => [ 'type' => 'structure', 'members' => [ 'AdministrativeActionType' => [ 'shape' => 'AdministrativeActionType', ], 'ProgressPercent' => [ 'shape' => 'ProgressPercent', ], 'RequestTime' => [ 'shape' => 'RequestTime', ], 'Status' => [ 'shape' => 'Status', ], 'TargetFileSystemValues' => [ 'shape' => 'FileSystem', ], 'FailureDetails' => [ 'shape' => 'AdministrativeActionFailureDetails', ], 'TargetVolumeValues' => [ 'shape' => 'Volume', ], 'TargetSnapshotValues' => [ 'shape' => 'Snapshot', ], 'TotalTransferBytes' => [ 'shape' => 'TotalTransferBytes', ], 'RemainingTransferBytes' => [ 'shape' => 'RemainingTransferBytes', ], ], ], 'AdministrativeActionFailureDetails' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], ], 'AdministrativeActionType' => [ 'type' => 'string', 'enum' => [ 'FILE_SYSTEM_UPDATE', 'STORAGE_OPTIMIZATION', 'FILE_SYSTEM_ALIAS_ASSOCIATION', 'FILE_SYSTEM_ALIAS_DISASSOCIATION', 'VOLUME_UPDATE', 'SNAPSHOT_UPDATE', 'RELEASE_NFS_V3_LOCKS', 'VOLUME_RESTORE', 'THROUGHPUT_OPTIMIZATION', 'IOPS_OPTIMIZATION', 'STORAGE_TYPE_OPTIMIZATION', 'MISCONFIGURED_STATE_RECOVERY', 'VOLUME_UPDATE_WITH_SNAPSHOT', 'VOLUME_INITIALIZE_WITH_SNAPSHOT', ], ], 'AdministrativeActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdministrativeAction', ], 'max' => 50, ], 'Aggregate' => [ 'type' => 'string', 'max' => 6, 'min' => 5, 'pattern' => '^(aggr[0-9]{1,2})$', ], 'AggregateConfiguration' => [ 'type' => 'structure', 'members' => [ 'Aggregates' => [ 'shape' => 'Aggregates', ], 'TotalConstituents' => [ 'shape' => 'TotalConstituents', ], ], ], 'AggregateListMultiplier' => [ 'type' => 'integer', 'max' => 200, 'min' => 1, ], 'Aggregates' => [ 'type' => 'list', 'member' => [ 'shape' => 'Aggregate', ], 'max' => 6, ], 'Alias' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'AlternateDNSName', ], 'Lifecycle' => [ 'shape' => 'AliasLifecycle', ], ], ], 'AliasLifecycle' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'CREATING', 'DELETING', 'CREATE_FAILED', 'DELETE_FAILED', ], ], 'Aliases' => [ 'type' => 'list', 'member' => [ 'shape' => 'Alias', ], 'max' => 50, ], 'AlternateDNSName' => [ 'type' => 'string', 'max' => 253, 'min' => 4, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{4,253}$', ], 'AlternateDNSNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlternateDNSName', ], 'max' => 50, ], 'ArchivePath' => [ 'type' => 'string', 'max' => 4357, 'min' => 3, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{3,4357}$', ], 'AssociateFileSystemAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'FileSystemId', 'Aliases', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'Aliases' => [ 'shape' => 'AlternateDNSNames', ], ], ], 'AssociateFileSystemAliasesResponse' => [ 'type' => 'structure', 'members' => [ 'Aliases' => [ 'shape' => 'Aliases', ], ], ], 'AutoExportPolicy' => [ 'type' => 'structure', 'members' => [ 'Events' => [ 'shape' => 'EventTypes', ], ], ], 'AutoImportPolicy' => [ 'type' => 'structure', 'members' => [ 'Events' => [ 'shape' => 'EventTypes', ], ], ], 'AutoImportPolicyType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'NEW', 'NEW_CHANGED', 'NEW_CHANGED_DELETED', ], ], 'AutocommitPeriod' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'AutocommitPeriodType', ], 'Value' => [ 'shape' => 'AutocommitPeriodValue', ], ], ], 'AutocommitPeriodType' => [ 'type' => 'string', 'enum' => [ 'MINUTES', 'HOURS', 'DAYS', 'MONTHS', 'YEARS', 'NONE', ], ], 'AutocommitPeriodValue' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'AutomaticBackupRetentionDays' => [ 'type' => 'integer', 'max' => 90, 'min' => 0, ], 'Backup' => [ 'type' => 'structure', 'required' => [ 'BackupId', 'Lifecycle', 'Type', 'CreationTime', 'FileSystem', ], 'members' => [ 'BackupId' => [ 'shape' => 'BackupId', ], 'Lifecycle' => [ 'shape' => 'BackupLifecycle', ], 'FailureDetails' => [ 'shape' => 'BackupFailureDetails', ], 'Type' => [ 'shape' => 'BackupType', ], 'ProgressPercent' => [ 'shape' => 'ProgressPercent', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Tags' => [ 'shape' => 'Tags', ], 'FileSystem' => [ 'shape' => 'FileSystem', ], 'DirectoryInformation' => [ 'shape' => 'ActiveDirectoryBackupAttributes', ], 'OwnerId' => [ 'shape' => 'AWSAccountId', ], 'SourceBackupId' => [ 'shape' => 'BackupId', ], 'SourceBackupRegion' => [ 'shape' => 'Region', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Volume' => [ 'shape' => 'Volume', ], ], ], 'BackupBeingCopied' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'BackupId' => [ 'shape' => 'BackupId', ], ], 'exception' => true, ], 'BackupFailureDetails' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], ], 'BackupId' => [ 'type' => 'string', 'max' => 128, 'min' => 12, 'pattern' => '^(backup-[0-9a-f]{8,})$', ], 'BackupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackupId', ], 'max' => 50, ], 'BackupInProgress' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'BackupLifecycle' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'CREATING', 'TRANSFERRING', 'DELETED', 'FAILED', 'PENDING', 'COPYING', ], ], 'BackupNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'BackupRestoring' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'FileSystemId' => [ 'shape' => 'FileSystemId', ], ], 'exception' => true, ], 'BackupType' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC', 'USER_INITIATED', 'AWS_BACKUP', ], ], 'Backups' => [ 'type' => 'list', 'member' => [ 'shape' => 'Backup', ], 'max' => 50, ], 'BadRequest' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'BatchImportMetaDataOnCreate' => [ 'type' => 'boolean', ], 'CancelDataRepositoryTaskRequest' => [ 'type' => 'structure', 'required' => [ 'TaskId', ], 'members' => [ 'TaskId' => [ 'shape' => 'TaskId', ], ], ], 'CancelDataRepositoryTaskResponse' => [ 'type' => 'structure', 'members' => [ 'Lifecycle' => [ 'shape' => 'DataRepositoryTaskLifecycle', ], 'TaskId' => [ 'shape' => 'TaskId', ], ], ], 'CapacityToRelease' => [ 'type' => 'long', 'max' => 2147483647, 'min' => 1, ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[A-za-z0-9_.-]{0,63}$', ], 'CompletionReport' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Enabled' => [ 'shape' => 'Flag', ], 'Path' => [ 'shape' => 'ArchivePath', ], 'Format' => [ 'shape' => 'ReportFormat', ], 'Scope' => [ 'shape' => 'ReportScope', ], ], ], 'CoolingPeriod' => [ 'type' => 'integer', 'max' => 183, 'min' => 2, ], 'CopyBackupRequest' => [ 'type' => 'structure', 'required' => [ 'SourceBackupId', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'SourceBackupId' => [ 'shape' => 'SourceBackupId', ], 'SourceRegion' => [ 'shape' => 'Region', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'CopyTags' => [ 'shape' => 'Flag', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CopyBackupResponse' => [ 'type' => 'structure', 'members' => [ 'Backup' => [ 'shape' => 'Backup', ], ], ], 'CopySnapshotAndUpdateVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'VolumeId', 'SourceSnapshotARN', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'VolumeId' => [ 'shape' => 'VolumeId', ], 'SourceSnapshotARN' => [ 'shape' => 'ResourceARN', ], 'CopyStrategy' => [ 'shape' => 'OpenZFSCopyStrategy', ], 'Options' => [ 'shape' => 'UpdateOpenZFSVolumeOptions', ], ], ], 'CopySnapshotAndUpdateVolumeResponse' => [ 'type' => 'structure', 'members' => [ 'VolumeId' => [ 'shape' => 'VolumeId', ], 'Lifecycle' => [ 'shape' => 'VolumeLifecycle', ], 'AdministrativeActions' => [ 'shape' => 'AdministrativeActions', ], ], ], 'CopyTagsToDataRepositoryAssociations' => [ 'type' => 'boolean', ], 'CreateAggregateConfiguration' => [ 'type' => 'structure', 'members' => [ 'Aggregates' => [ 'shape' => 'Aggregates', ], 'ConstituentsPerAggregate' => [ 'shape' => 'AggregateListMultiplier', ], ], ], 'CreateBackupRequest' => [ 'type' => 'structure', 'members' => [ 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', ], 'VolumeId' => [ 'shape' => 'VolumeId', ], ], ], 'CreateBackupResponse' => [ 'type' => 'structure', 'members' => [ 'Backup' => [ 'shape' => 'Backup', ], ], ], 'CreateDataRepositoryAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'FileSystemId', 'DataRepositoryPath', ], 'members' => [ 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'FileSystemPath' => [ 'shape' => 'Namespace', ], 'DataRepositoryPath' => [ 'shape' => 'ArchivePath', ], 'BatchImportMetaDataOnCreate' => [ 'shape' => 'BatchImportMetaDataOnCreate', ], 'ImportedFileChunkSize' => [ 'shape' => 'Megabytes', ], 'S3' => [ 'shape' => 'S3DataRepositoryConfiguration', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateDataRepositoryAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'Association' => [ 'shape' => 'DataRepositoryAssociation', ], ], ], 'CreateDataRepositoryTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Type', 'FileSystemId', 'Report', ], 'members' => [ 'Type' => [ 'shape' => 'DataRepositoryTaskType', ], 'Paths' => [ 'shape' => 'DataRepositoryTaskPaths', ], 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'Report' => [ 'shape' => 'CompletionReport', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', ], 'CapacityToRelease' => [ 'shape' => 'CapacityToRelease', ], 'ReleaseConfiguration' => [ 'shape' => 'ReleaseConfiguration', ], ], ], 'CreateDataRepositoryTaskResponse' => [ 'type' => 'structure', 'members' => [ 'DataRepositoryTask' => [ 'shape' => 'DataRepositoryTask', ], ], ], 'CreateFileCacheDataRepositoryAssociations' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileCacheDataRepositoryAssociation', ], 'max' => 8, ], 'CreateFileCacheLustreConfiguration' => [ 'type' => 'structure', 'required' => [ 'PerUnitStorageThroughput', 'DeploymentType', 'MetadataConfiguration', ], 'members' => [ 'PerUnitStorageThroughput' => [ 'shape' => 'PerUnitStorageThroughput', ], 'DeploymentType' => [ 'shape' => 'FileCacheLustreDeploymentType', ], 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'MetadataConfiguration' => [ 'shape' => 'FileCacheLustreMetadataConfiguration', ], ], ], 'CreateFileCacheRequest' => [ 'type' => 'structure', 'required' => [ 'FileCacheType', 'FileCacheTypeVersion', 'StorageCapacity', 'SubnetIds', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'FileCacheType' => [ 'shape' => 'FileCacheType', ], 'FileCacheTypeVersion' => [ 'shape' => 'FileSystemTypeVersion', ], 'StorageCapacity' => [ 'shape' => 'StorageCapacity', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'Tags' => [ 'shape' => 'Tags', ], 'CopyTagsToDataRepositoryAssociations' => [ 'shape' => 'CopyTagsToDataRepositoryAssociations', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'LustreConfiguration' => [ 'shape' => 'CreateFileCacheLustreConfiguration', ], 'DataRepositoryAssociations' => [ 'shape' => 'CreateFileCacheDataRepositoryAssociations', ], ], ], 'CreateFileCacheResponse' => [ 'type' => 'structure', 'members' => [ 'FileCache' => [ 'shape' => 'FileCacheCreating', ], ], ], 'CreateFileSystemFromBackupRequest' => [ 'type' => 'structure', 'required' => [ 'BackupId', 'SubnetIds', ], 'members' => [ 'BackupId' => [ 'shape' => 'BackupId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'Tags' => [ 'shape' => 'Tags', ], 'WindowsConfiguration' => [ 'shape' => 'CreateFileSystemWindowsConfiguration', ], 'LustreConfiguration' => [ 'shape' => 'CreateFileSystemLustreConfiguration', ], 'StorageType' => [ 'shape' => 'StorageType', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'FileSystemTypeVersion' => [ 'shape' => 'FileSystemTypeVersion', ], 'OpenZFSConfiguration' => [ 'shape' => 'CreateFileSystemOpenZFSConfiguration', ], 'StorageCapacity' => [ 'shape' => 'StorageCapacity', ], ], ], 'CreateFileSystemFromBackupResponse' => [ 'type' => 'structure', 'members' => [ 'FileSystem' => [ 'shape' => 'FileSystem', ], ], ], 'CreateFileSystemLustreConfiguration' => [ 'type' => 'structure', 'members' => [ 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'ImportPath' => [ 'shape' => 'ArchivePath', ], 'ExportPath' => [ 'shape' => 'ArchivePath', ], 'ImportedFileChunkSize' => [ 'shape' => 'Megabytes', ], 'DeploymentType' => [ 'shape' => 'LustreDeploymentType', ], 'AutoImportPolicy' => [ 'shape' => 'AutoImportPolicyType', ], 'PerUnitStorageThroughput' => [ 'shape' => 'PerUnitStorageThroughput', ], 'DailyAutomaticBackupStartTime' => [ 'shape' => 'DailyTime', ], 'AutomaticBackupRetentionDays' => [ 'shape' => 'AutomaticBackupRetentionDays', ], 'CopyTagsToBackups' => [ 'shape' => 'Flag', ], 'DriveCacheType' => [ 'shape' => 'DriveCacheType', ], 'DataCompressionType' => [ 'shape' => 'DataCompressionType', ], 'LogConfiguration' => [ 'shape' => 'LustreLogCreateConfiguration', ], 'RootSquashConfiguration' => [ 'shape' => 'LustreRootSquashConfiguration', ], ], ], 'CreateFileSystemOntapConfiguration' => [ 'type' => 'structure', 'required' => [ 'DeploymentType', ], 'members' => [ 'AutomaticBackupRetentionDays' => [ 'shape' => 'AutomaticBackupRetentionDays', ], 'DailyAutomaticBackupStartTime' => [ 'shape' => 'DailyTime', ], 'DeploymentType' => [ 'shape' => 'OntapDeploymentType', ], 'EndpointIpAddressRange' => [ 'shape' => 'IpAddressRange', ], 'FsxAdminPassword' => [ 'shape' => 'AdminPassword', ], 'DiskIopsConfiguration' => [ 'shape' => 'DiskIopsConfiguration', ], 'PreferredSubnetId' => [ 'shape' => 'SubnetId', ], 'RouteTableIds' => [ 'shape' => 'RouteTableIds', ], 'ThroughputCapacity' => [ 'shape' => 'MegabytesPerSecond', ], 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'HAPairs' => [ 'shape' => 'HAPairs', ], 'ThroughputCapacityPerHAPair' => [ 'shape' => 'ThroughputCapacityPerHAPair', ], ], ], 'CreateFileSystemOpenZFSConfiguration' => [ 'type' => 'structure', 'required' => [ 'DeploymentType', 'ThroughputCapacity', ], 'members' => [ 'AutomaticBackupRetentionDays' => [ 'shape' => 'AutomaticBackupRetentionDays', ], 'CopyTagsToBackups' => [ 'shape' => 'Flag', ], 'CopyTagsToVolumes' => [ 'shape' => 'Flag', ], 'DailyAutomaticBackupStartTime' => [ 'shape' => 'DailyTime', ], 'DeploymentType' => [ 'shape' => 'OpenZFSDeploymentType', ], 'ThroughputCapacity' => [ 'shape' => 'MegabytesPerSecond', ], 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'DiskIopsConfiguration' => [ 'shape' => 'DiskIopsConfiguration', ], 'RootVolumeConfiguration' => [ 'shape' => 'OpenZFSCreateRootVolumeConfiguration', ], 'PreferredSubnetId' => [ 'shape' => 'SubnetId', ], 'EndpointIpAddressRange' => [ 'shape' => 'IpAddressRange', ], 'RouteTableIds' => [ 'shape' => 'RouteTableIds', ], ], ], 'CreateFileSystemRequest' => [ 'type' => 'structure', 'required' => [ 'FileSystemType', 'StorageCapacity', 'SubnetIds', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'FileSystemType' => [ 'shape' => 'FileSystemType', ], 'StorageCapacity' => [ 'shape' => 'StorageCapacity', ], 'StorageType' => [ 'shape' => 'StorageType', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'Tags' => [ 'shape' => 'Tags', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'WindowsConfiguration' => [ 'shape' => 'CreateFileSystemWindowsConfiguration', ], 'LustreConfiguration' => [ 'shape' => 'CreateFileSystemLustreConfiguration', ], 'OntapConfiguration' => [ 'shape' => 'CreateFileSystemOntapConfiguration', ], 'FileSystemTypeVersion' => [ 'shape' => 'FileSystemTypeVersion', ], 'OpenZFSConfiguration' => [ 'shape' => 'CreateFileSystemOpenZFSConfiguration', ], ], ], 'CreateFileSystemResponse' => [ 'type' => 'structure', 'members' => [ 'FileSystem' => [ 'shape' => 'FileSystem', ], ], ], 'CreateFileSystemWindowsConfiguration' => [ 'type' => 'structure', 'required' => [ 'ThroughputCapacity', ], 'members' => [ 'ActiveDirectoryId' => [ 'shape' => 'DirectoryId', ], 'SelfManagedActiveDirectoryConfiguration' => [ 'shape' => 'SelfManagedActiveDirectoryConfiguration', ], 'DeploymentType' => [ 'shape' => 'WindowsDeploymentType', ], 'PreferredSubnetId' => [ 'shape' => 'SubnetId', ], 'ThroughputCapacity' => [ 'shape' => 'MegabytesPerSecond', ], 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'DailyAutomaticBackupStartTime' => [ 'shape' => 'DailyTime', ], 'AutomaticBackupRetentionDays' => [ 'shape' => 'AutomaticBackupRetentionDays', ], 'CopyTagsToBackups' => [ 'shape' => 'Flag', ], 'Aliases' => [ 'shape' => 'AlternateDNSNames', ], 'AuditLogConfiguration' => [ 'shape' => 'WindowsAuditLogCreateConfiguration', ], 'DiskIopsConfiguration' => [ 'shape' => 'DiskIopsConfiguration', ], ], ], 'CreateOntapVolumeConfiguration' => [ 'type' => 'structure', 'required' => [ 'StorageVirtualMachineId', ], 'members' => [ 'JunctionPath' => [ 'shape' => 'JunctionPath', ], 'SecurityStyle' => [ 'shape' => 'SecurityStyle', ], 'SizeInMegabytes' => [ 'shape' => 'VolumeCapacity', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use SizeInBytes instead', ], 'StorageEfficiencyEnabled' => [ 'shape' => 'Flag', ], 'StorageVirtualMachineId' => [ 'shape' => 'StorageVirtualMachineId', ], 'TieringPolicy' => [ 'shape' => 'TieringPolicy', ], 'OntapVolumeType' => [ 'shape' => 'InputOntapVolumeType', ], 'SnapshotPolicy' => [ 'shape' => 'SnapshotPolicy', ], 'CopyTagsToBackups' => [ 'shape' => 'Flag', ], 'SnaplockConfiguration' => [ 'shape' => 'CreateSnaplockConfiguration', ], 'VolumeStyle' => [ 'shape' => 'VolumeStyle', ], 'AggregateConfiguration' => [ 'shape' => 'CreateAggregateConfiguration', ], 'SizeInBytes' => [ 'shape' => 'VolumeCapacityBytes', ], ], ], 'CreateOpenZFSOriginSnapshotConfiguration' => [ 'type' => 'structure', 'required' => [ 'SnapshotARN', 'CopyStrategy', ], 'members' => [ 'SnapshotARN' => [ 'shape' => 'ResourceARN', ], 'CopyStrategy' => [ 'shape' => 'OpenZFSCopyStrategy', ], ], ], 'CreateOpenZFSVolumeConfiguration' => [ 'type' => 'structure', 'required' => [ 'ParentVolumeId', ], 'members' => [ 'ParentVolumeId' => [ 'shape' => 'VolumeId', ], 'StorageCapacityReservationGiB' => [ 'shape' => 'IntegerNoMaxFromNegativeOne', ], 'StorageCapacityQuotaGiB' => [ 'shape' => 'IntegerNoMaxFromNegativeOne', ], 'RecordSizeKiB' => [ 'shape' => 'IntegerRecordSizeKiB', ], 'DataCompressionType' => [ 'shape' => 'OpenZFSDataCompressionType', ], 'CopyTagsToSnapshots' => [ 'shape' => 'Flag', ], 'OriginSnapshot' => [ 'shape' => 'CreateOpenZFSOriginSnapshotConfiguration', ], 'ReadOnly' => [ 'shape' => 'ReadOnly', ], 'NfsExports' => [ 'shape' => 'OpenZFSNfsExports', ], 'UserAndGroupQuotas' => [ 'shape' => 'OpenZFSUserAndGroupQuotas', ], ], ], 'CreateSnaplockConfiguration' => [ 'type' => 'structure', 'required' => [ 'SnaplockType', ], 'members' => [ 'AuditLogVolume' => [ 'shape' => 'Flag', ], 'AutocommitPeriod' => [ 'shape' => 'AutocommitPeriod', ], 'PrivilegedDelete' => [ 'shape' => 'PrivilegedDelete', ], 'RetentionPeriod' => [ 'shape' => 'SnaplockRetentionPeriod', ], 'SnaplockType' => [ 'shape' => 'SnaplockType', ], 'VolumeAppendModeEnabled' => [ 'shape' => 'Flag', ], ], ], 'CreateSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'VolumeId', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Name' => [ 'shape' => 'SnapshotName', ], 'VolumeId' => [ 'shape' => 'VolumeId', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'Snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'CreateStorageVirtualMachineRequest' => [ 'type' => 'structure', 'required' => [ 'FileSystemId', 'Name', ], 'members' => [ 'ActiveDirectoryConfiguration' => [ 'shape' => 'CreateSvmActiveDirectoryConfiguration', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'Name' => [ 'shape' => 'StorageVirtualMachineName', ], 'SvmAdminPassword' => [ 'shape' => 'AdminPassword', ], 'Tags' => [ 'shape' => 'Tags', ], 'RootVolumeSecurityStyle' => [ 'shape' => 'StorageVirtualMachineRootVolumeSecurityStyle', ], ], ], 'CreateStorageVirtualMachineResponse' => [ 'type' => 'structure', 'members' => [ 'StorageVirtualMachine' => [ 'shape' => 'StorageVirtualMachine', ], ], ], 'CreateSvmActiveDirectoryConfiguration' => [ 'type' => 'structure', 'required' => [ 'NetBiosName', ], 'members' => [ 'NetBiosName' => [ 'shape' => 'NetBiosAlias', ], 'SelfManagedActiveDirectoryConfiguration' => [ 'shape' => 'SelfManagedActiveDirectoryConfiguration', ], ], ], 'CreateVolumeFromBackupRequest' => [ 'type' => 'structure', 'required' => [ 'BackupId', 'Name', ], 'members' => [ 'BackupId' => [ 'shape' => 'BackupId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Name' => [ 'shape' => 'VolumeName', ], 'OntapConfiguration' => [ 'shape' => 'CreateOntapVolumeConfiguration', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateVolumeFromBackupResponse' => [ 'type' => 'structure', 'members' => [ 'Volume' => [ 'shape' => 'Volume', ], ], ], 'CreateVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'VolumeType', 'Name', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'VolumeType' => [ 'shape' => 'VolumeType', ], 'Name' => [ 'shape' => 'VolumeName', ], 'OntapConfiguration' => [ 'shape' => 'CreateOntapVolumeConfiguration', ], 'Tags' => [ 'shape' => 'Tags', ], 'OpenZFSConfiguration' => [ 'shape' => 'CreateOpenZFSVolumeConfiguration', ], ], ], 'CreateVolumeResponse' => [ 'type' => 'structure', 'members' => [ 'Volume' => [ 'shape' => 'Volume', ], ], ], 'CreationTime' => [ 'type' => 'timestamp', ], 'DNSName' => [ 'type' => 'string', 'max' => 275, 'min' => 16, 'pattern' => '^((fs|fc)i?-[0-9a-f]{8,}\\..{4,253})$', ], 'DailyTime' => [ 'type' => 'string', 'max' => 5, 'min' => 5, 'pattern' => '^([01]\\d|2[0-3]):?([0-5]\\d)$', ], 'DataCompressionType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'LZ4', ], ], 'DataRepositoryAssociation' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'DataRepositoryAssociationId', ], 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'Lifecycle' => [ 'shape' => 'DataRepositoryLifecycle', ], 'FailureDetails' => [ 'shape' => 'DataRepositoryFailureDetails', ], 'FileSystemPath' => [ 'shape' => 'Namespace', ], 'DataRepositoryPath' => [ 'shape' => 'ArchivePath', ], 'BatchImportMetaDataOnCreate' => [ 'shape' => 'BatchImportMetaDataOnCreate', ], 'ImportedFileChunkSize' => [ 'shape' => 'Megabytes', ], 'S3' => [ 'shape' => 'S3DataRepositoryConfiguration', ], 'Tags' => [ 'shape' => 'Tags', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'FileCacheId' => [ 'shape' => 'FileCacheId', ], 'FileCachePath' => [ 'shape' => 'Namespace', ], 'DataRepositorySubdirectories' => [ 'shape' => 'SubDirectoriesPaths', ], 'NFS' => [ 'shape' => 'NFSDataRepositoryConfiguration', ], ], ], 'DataRepositoryAssociationId' => [ 'type' => 'string', 'max' => 23, 'min' => 13, 'pattern' => '^(dra-[0-9a-f]{8,})$', ], 'DataRepositoryAssociationIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataRepositoryAssociationId', ], 'max' => 50, ], 'DataRepositoryAssociationNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'DataRepositoryAssociations' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataRepositoryAssociation', ], 'max' => 100, ], 'DataRepositoryConfiguration' => [ 'type' => 'structure', 'members' => [ 'Lifecycle' => [ 'shape' => 'DataRepositoryLifecycle', ], 'ImportPath' => [ 'shape' => 'ArchivePath', ], 'ExportPath' => [ 'shape' => 'ArchivePath', ], 'ImportedFileChunkSize' => [ 'shape' => 'Megabytes', ], 'AutoImportPolicy' => [ 'shape' => 'AutoImportPolicyType', ], 'FailureDetails' => [ 'shape' => 'DataRepositoryFailureDetails', ], ], ], 'DataRepositoryFailureDetails' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], ], 'DataRepositoryLifecycle' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'AVAILABLE', 'MISCONFIGURED', 'UPDATING', 'DELETING', 'FAILED', ], ], 'DataRepositoryTask' => [ 'type' => 'structure', 'required' => [ 'TaskId', 'Lifecycle', 'Type', 'CreationTime', ], 'members' => [ 'TaskId' => [ 'shape' => 'TaskId', ], 'Lifecycle' => [ 'shape' => 'DataRepositoryTaskLifecycle', ], 'Type' => [ 'shape' => 'DataRepositoryTaskType', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'StartTime' => [ 'shape' => 'StartTime', ], 'EndTime' => [ 'shape' => 'EndTime', ], 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Tags' => [ 'shape' => 'Tags', ], 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'Paths' => [ 'shape' => 'DataRepositoryTaskPaths', ], 'FailureDetails' => [ 'shape' => 'DataRepositoryTaskFailureDetails', ], 'Status' => [ 'shape' => 'DataRepositoryTaskStatus', ], 'Report' => [ 'shape' => 'CompletionReport', ], 'CapacityToRelease' => [ 'shape' => 'CapacityToRelease', ], 'FileCacheId' => [ 'shape' => 'FileCacheId', ], 'ReleaseConfiguration' => [ 'shape' => 'ReleaseConfiguration', ], ], ], 'DataRepositoryTaskEnded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'DataRepositoryTaskExecuting' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'DataRepositoryTaskFailureDetails' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], ], 'DataRepositoryTaskFilter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DataRepositoryTaskFilterName', ], 'Values' => [ 'shape' => 'DataRepositoryTaskFilterValues', ], ], ], 'DataRepositoryTaskFilterName' => [ 'type' => 'string', 'enum' => [ 'file-system-id', 'task-lifecycle', 'data-repository-association-id', 'file-cache-id', ], ], 'DataRepositoryTaskFilterValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[0-9a-zA-Z\\*\\.\\\\/\\?\\-\\_]*$', ], 'DataRepositoryTaskFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataRepositoryTaskFilterValue', ], 'max' => 20, ], 'DataRepositoryTaskFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataRepositoryTaskFilter', ], 'max' => 3, ], 'DataRepositoryTaskLifecycle' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'EXECUTING', 'FAILED', 'SUCCEEDED', 'CANCELED', 'CANCELING', ], ], 'DataRepositoryTaskNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'DataRepositoryTaskPath' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{0,4096}$', ], 'DataRepositoryTaskPaths' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataRepositoryTaskPath', ], 'max' => 100, ], 'DataRepositoryTaskStatus' => [ 'type' => 'structure', 'members' => [ 'TotalCount' => [ 'shape' => 'TotalCount', ], 'SucceededCount' => [ 'shape' => 'SucceededCount', ], 'FailedCount' => [ 'shape' => 'FailedCount', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'ReleasedCapacity' => [ 'shape' => 'ReleasedCapacity', ], ], ], 'DataRepositoryTaskType' => [ 'type' => 'string', 'enum' => [ 'EXPORT_TO_REPOSITORY', 'IMPORT_METADATA_FROM_REPOSITORY', 'RELEASE_DATA_FROM_FILESYSTEM', 'AUTO_RELEASE_DATA', ], ], 'DataRepositoryTasks' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataRepositoryTask', ], 'max' => 50, ], 'DeleteBackupRequest' => [ 'type' => 'structure', 'required' => [ 'BackupId', ], 'members' => [ 'BackupId' => [ 'shape' => 'BackupId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'DeleteBackupResponse' => [ 'type' => 'structure', 'members' => [ 'BackupId' => [ 'shape' => 'BackupId', ], 'Lifecycle' => [ 'shape' => 'BackupLifecycle', ], ], ], 'DeleteDataInFileSystem' => [ 'type' => 'boolean', ], 'DeleteDataRepositoryAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'AssociationId', ], 'members' => [ 'AssociationId' => [ 'shape' => 'DataRepositoryAssociationId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'DeleteDataInFileSystem' => [ 'shape' => 'DeleteDataInFileSystem', ], ], ], 'DeleteDataRepositoryAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'DataRepositoryAssociationId', ], 'Lifecycle' => [ 'shape' => 'DataRepositoryLifecycle', ], 'DeleteDataInFileSystem' => [ 'shape' => 'DeleteDataInFileSystem', ], ], ], 'DeleteFileCacheRequest' => [ 'type' => 'structure', 'required' => [ 'FileCacheId', ], 'members' => [ 'FileCacheId' => [ 'shape' => 'FileCacheId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'DeleteFileCacheResponse' => [ 'type' => 'structure', 'members' => [ 'FileCacheId' => [ 'shape' => 'FileCacheId', ], 'Lifecycle' => [ 'shape' => 'FileCacheLifecycle', ], ], ], 'DeleteFileSystemLustreConfiguration' => [ 'type' => 'structure', 'members' => [ 'SkipFinalBackup' => [ 'shape' => 'Flag', ], 'FinalBackupTags' => [ 'shape' => 'Tags', ], ], ], 'DeleteFileSystemLustreResponse' => [ 'type' => 'structure', 'members' => [ 'FinalBackupId' => [ 'shape' => 'BackupId', ], 'FinalBackupTags' => [ 'shape' => 'Tags', ], ], ], 'DeleteFileSystemOpenZFSConfiguration' => [ 'type' => 'structure', 'members' => [ 'SkipFinalBackup' => [ 'shape' => 'Flag', ], 'FinalBackupTags' => [ 'shape' => 'Tags', ], 'Options' => [ 'shape' => 'DeleteFileSystemOpenZFSOptions', ], ], ], 'DeleteFileSystemOpenZFSOption' => [ 'type' => 'string', 'enum' => [ 'DELETE_CHILD_VOLUMES_AND_SNAPSHOTS', ], ], 'DeleteFileSystemOpenZFSOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeleteFileSystemOpenZFSOption', ], 'max' => 1, ], 'DeleteFileSystemOpenZFSResponse' => [ 'type' => 'structure', 'members' => [ 'FinalBackupId' => [ 'shape' => 'BackupId', ], 'FinalBackupTags' => [ 'shape' => 'Tags', ], ], ], 'DeleteFileSystemRequest' => [ 'type' => 'structure', 'required' => [ 'FileSystemId', ], 'members' => [ 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'WindowsConfiguration' => [ 'shape' => 'DeleteFileSystemWindowsConfiguration', ], 'LustreConfiguration' => [ 'shape' => 'DeleteFileSystemLustreConfiguration', ], 'OpenZFSConfiguration' => [ 'shape' => 'DeleteFileSystemOpenZFSConfiguration', ], ], ], 'DeleteFileSystemResponse' => [ 'type' => 'structure', 'members' => [ 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'Lifecycle' => [ 'shape' => 'FileSystemLifecycle', ], 'WindowsResponse' => [ 'shape' => 'DeleteFileSystemWindowsResponse', ], 'LustreResponse' => [ 'shape' => 'DeleteFileSystemLustreResponse', ], 'OpenZFSResponse' => [ 'shape' => 'DeleteFileSystemOpenZFSResponse', ], ], ], 'DeleteFileSystemWindowsConfiguration' => [ 'type' => 'structure', 'members' => [ 'SkipFinalBackup' => [ 'shape' => 'Flag', ], 'FinalBackupTags' => [ 'shape' => 'Tags', ], ], ], 'DeleteFileSystemWindowsResponse' => [ 'type' => 'structure', 'members' => [ 'FinalBackupId' => [ 'shape' => 'BackupId', ], 'FinalBackupTags' => [ 'shape' => 'Tags', ], ], ], 'DeleteOpenZFSVolumeOption' => [ 'type' => 'string', 'enum' => [ 'DELETE_CHILD_VOLUMES_AND_SNAPSHOTS', ], ], 'DeleteOpenZFSVolumeOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeleteOpenZFSVolumeOption', ], 'max' => 1, ], 'DeleteSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'SnapshotId', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'DeleteSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'Lifecycle' => [ 'shape' => 'SnapshotLifecycle', ], ], ], 'DeleteStorageVirtualMachineRequest' => [ 'type' => 'structure', 'required' => [ 'StorageVirtualMachineId', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'StorageVirtualMachineId' => [ 'shape' => 'StorageVirtualMachineId', ], ], ], 'DeleteStorageVirtualMachineResponse' => [ 'type' => 'structure', 'members' => [ 'StorageVirtualMachineId' => [ 'shape' => 'StorageVirtualMachineId', ], 'Lifecycle' => [ 'shape' => 'StorageVirtualMachineLifecycle', ], ], ], 'DeleteVolumeOntapConfiguration' => [ 'type' => 'structure', 'members' => [ 'SkipFinalBackup' => [ 'shape' => 'Flag', ], 'FinalBackupTags' => [ 'shape' => 'Tags', ], 'BypassSnaplockEnterpriseRetention' => [ 'shape' => 'Flag', ], ], ], 'DeleteVolumeOntapResponse' => [ 'type' => 'structure', 'members' => [ 'FinalBackupId' => [ 'shape' => 'BackupId', ], 'FinalBackupTags' => [ 'shape' => 'Tags', ], ], ], 'DeleteVolumeOpenZFSConfiguration' => [ 'type' => 'structure', 'members' => [ 'Options' => [ 'shape' => 'DeleteOpenZFSVolumeOptions', ], ], ], 'DeleteVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'VolumeId', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'VolumeId' => [ 'shape' => 'VolumeId', ], 'OntapConfiguration' => [ 'shape' => 'DeleteVolumeOntapConfiguration', ], 'OpenZFSConfiguration' => [ 'shape' => 'DeleteVolumeOpenZFSConfiguration', ], ], ], 'DeleteVolumeResponse' => [ 'type' => 'structure', 'members' => [ 'VolumeId' => [ 'shape' => 'VolumeId', ], 'Lifecycle' => [ 'shape' => 'VolumeLifecycle', ], 'OntapResponse' => [ 'shape' => 'DeleteVolumeOntapResponse', ], ], ], 'DescribeBackupsRequest' => [ 'type' => 'structure', 'members' => [ 'BackupIds' => [ 'shape' => 'BackupIds', ], 'Filters' => [ 'shape' => 'Filters', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeBackupsResponse' => [ 'type' => 'structure', 'members' => [ 'Backups' => [ 'shape' => 'Backups', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDataRepositoryAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'AssociationIds' => [ 'shape' => 'DataRepositoryAssociationIds', ], 'Filters' => [ 'shape' => 'Filters', ], 'MaxResults' => [ 'shape' => 'LimitedMaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDataRepositoryAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'Associations' => [ 'shape' => 'DataRepositoryAssociations', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDataRepositoryTasksRequest' => [ 'type' => 'structure', 'members' => [ 'TaskIds' => [ 'shape' => 'TaskIds', ], 'Filters' => [ 'shape' => 'DataRepositoryTaskFilters', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDataRepositoryTasksResponse' => [ 'type' => 'structure', 'members' => [ 'DataRepositoryTasks' => [ 'shape' => 'DataRepositoryTasks', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeFileCachesRequest' => [ 'type' => 'structure', 'members' => [ 'FileCacheIds' => [ 'shape' => 'FileCacheIds', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeFileCachesResponse' => [ 'type' => 'structure', 'members' => [ 'FileCaches' => [ 'shape' => 'FileCaches', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeFileSystemAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'FileSystemId', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeFileSystemAliasesResponse' => [ 'type' => 'structure', 'members' => [ 'Aliases' => [ 'shape' => 'Aliases', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeFileSystemsRequest' => [ 'type' => 'structure', 'members' => [ 'FileSystemIds' => [ 'shape' => 'FileSystemIds', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeFileSystemsResponse' => [ 'type' => 'structure', 'members' => [ 'FileSystems' => [ 'shape' => 'FileSystems', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeSharedVpcConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeSharedVpcConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'EnableFsxRouteTableUpdatesFromParticipantAccounts' => [ 'shape' => 'VerboseFlag', ], ], ], 'DescribeSnapshotsRequest' => [ 'type' => 'structure', 'members' => [ 'SnapshotIds' => [ 'shape' => 'SnapshotIds', ], 'Filters' => [ 'shape' => 'SnapshotFilters', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeSnapshotsResponse' => [ 'type' => 'structure', 'members' => [ 'Snapshots' => [ 'shape' => 'Snapshots', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStorageVirtualMachinesRequest' => [ 'type' => 'structure', 'members' => [ 'StorageVirtualMachineIds' => [ 'shape' => 'StorageVirtualMachineIds', ], 'Filters' => [ 'shape' => 'StorageVirtualMachineFilters', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStorageVirtualMachinesResponse' => [ 'type' => 'structure', 'members' => [ 'StorageVirtualMachines' => [ 'shape' => 'StorageVirtualMachines', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeVolumesRequest' => [ 'type' => 'structure', 'members' => [ 'VolumeIds' => [ 'shape' => 'VolumeIds', ], 'Filters' => [ 'shape' => 'VolumeFilters', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeVolumesResponse' => [ 'type' => 'structure', 'members' => [ 'Volumes' => [ 'shape' => 'Volumes', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DirectoryId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^d-[0-9a-f]{10}$', ], 'DirectoryPassword' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^.{1,256}$', 'sensitive' => true, ], 'DirectoryUserName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,256}$', ], 'DisassociateFileSystemAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'FileSystemId', 'Aliases', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'Aliases' => [ 'shape' => 'AlternateDNSNames', ], ], ], 'DisassociateFileSystemAliasesResponse' => [ 'type' => 'structure', 'members' => [ 'Aliases' => [ 'shape' => 'Aliases', ], ], ], 'DiskIopsConfiguration' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'DiskIopsConfigurationMode', ], 'Iops' => [ 'shape' => 'Iops', ], ], ], 'DiskIopsConfigurationMode' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC', 'USER_PROVISIONED', ], ], 'DnsIps' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddress', ], 'max' => 3, 'min' => 1, ], 'DriveCacheType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'READ', ], ], 'DurationSinceLastAccess' => [ 'type' => 'structure', 'members' => [ 'Unit' => [ 'shape' => 'Unit', ], 'Value' => [ 'shape' => 'Value', ], ], ], 'EndTime' => [ 'type' => 'timestamp', ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'EventType' => [ 'type' => 'string', 'enum' => [ 'NEW', 'CHANGED', 'DELETED', ], ], 'EventTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventType', ], 'max' => 3, ], 'FailedCount' => [ 'type' => 'long', ], 'FileCache' => [ 'type' => 'structure', 'members' => [ 'OwnerId' => [ 'shape' => 'AWSAccountId', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'FileCacheId' => [ 'shape' => 'FileCacheId', ], 'FileCacheType' => [ 'shape' => 'FileCacheType', ], 'FileCacheTypeVersion' => [ 'shape' => 'FileSystemTypeVersion', ], 'Lifecycle' => [ 'shape' => 'FileCacheLifecycle', ], 'FailureDetails' => [ 'shape' => 'FileCacheFailureDetails', ], 'StorageCapacity' => [ 'shape' => 'StorageCapacity', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'NetworkInterfaceIds' => [ 'shape' => 'NetworkInterfaceIds', ], 'DNSName' => [ 'shape' => 'DNSName', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'LustreConfiguration' => [ 'shape' => 'FileCacheLustreConfiguration', ], 'DataRepositoryAssociationIds' => [ 'shape' => 'DataRepositoryAssociationIds', ], ], ], 'FileCacheCreating' => [ 'type' => 'structure', 'members' => [ 'OwnerId' => [ 'shape' => 'AWSAccountId', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'FileCacheId' => [ 'shape' => 'FileCacheId', ], 'FileCacheType' => [ 'shape' => 'FileCacheType', ], 'FileCacheTypeVersion' => [ 'shape' => 'FileSystemTypeVersion', ], 'Lifecycle' => [ 'shape' => 'FileCacheLifecycle', ], 'FailureDetails' => [ 'shape' => 'FileCacheFailureDetails', ], 'StorageCapacity' => [ 'shape' => 'StorageCapacity', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'NetworkInterfaceIds' => [ 'shape' => 'NetworkInterfaceIds', ], 'DNSName' => [ 'shape' => 'DNSName', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Tags' => [ 'shape' => 'Tags', ], 'CopyTagsToDataRepositoryAssociations' => [ 'shape' => 'CopyTagsToDataRepositoryAssociations', ], 'LustreConfiguration' => [ 'shape' => 'FileCacheLustreConfiguration', ], 'DataRepositoryAssociationIds' => [ 'shape' => 'DataRepositoryAssociationIds', ], ], ], 'FileCacheDataRepositoryAssociation' => [ 'type' => 'structure', 'required' => [ 'FileCachePath', 'DataRepositoryPath', ], 'members' => [ 'FileCachePath' => [ 'shape' => 'Namespace', ], 'DataRepositoryPath' => [ 'shape' => 'ArchivePath', ], 'DataRepositorySubdirectories' => [ 'shape' => 'SubDirectoriesPaths', ], 'NFS' => [ 'shape' => 'FileCacheNFSConfiguration', ], ], ], 'FileCacheFailureDetails' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], ], 'FileCacheId' => [ 'type' => 'string', 'max' => 21, 'min' => 11, 'pattern' => '^(fc-[0-9a-f]{8,})$', ], 'FileCacheIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileCacheId', ], 'max' => 50, ], 'FileCacheLifecycle' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'CREATING', 'DELETING', 'UPDATING', 'FAILED', ], ], 'FileCacheLustreConfiguration' => [ 'type' => 'structure', 'members' => [ 'PerUnitStorageThroughput' => [ 'shape' => 'PerUnitStorageThroughput', ], 'DeploymentType' => [ 'shape' => 'FileCacheLustreDeploymentType', ], 'MountName' => [ 'shape' => 'LustreFileSystemMountName', ], 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'MetadataConfiguration' => [ 'shape' => 'FileCacheLustreMetadataConfiguration', ], 'LogConfiguration' => [ 'shape' => 'LustreLogConfiguration', ], ], ], 'FileCacheLustreDeploymentType' => [ 'type' => 'string', 'enum' => [ 'CACHE_1', ], ], 'FileCacheLustreMetadataConfiguration' => [ 'type' => 'structure', 'required' => [ 'StorageCapacity', ], 'members' => [ 'StorageCapacity' => [ 'shape' => 'MetadataStorageCapacity', ], ], ], 'FileCacheNFSConfiguration' => [ 'type' => 'structure', 'required' => [ 'Version', ], 'members' => [ 'Version' => [ 'shape' => 'NfsVersion', ], 'DnsIps' => [ 'shape' => 'RepositoryDnsIps', ], ], ], 'FileCacheNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'FileCacheType' => [ 'type' => 'string', 'enum' => [ 'LUSTRE', ], ], 'FileCaches' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileCache', ], 'max' => 50, ], 'FileSystem' => [ 'type' => 'structure', 'members' => [ 'OwnerId' => [ 'shape' => 'AWSAccountId', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'FileSystemType' => [ 'shape' => 'FileSystemType', ], 'Lifecycle' => [ 'shape' => 'FileSystemLifecycle', ], 'FailureDetails' => [ 'shape' => 'FileSystemFailureDetails', ], 'StorageCapacity' => [ 'shape' => 'StorageCapacity', ], 'StorageType' => [ 'shape' => 'StorageType', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'NetworkInterfaceIds' => [ 'shape' => 'NetworkInterfaceIds', ], 'DNSName' => [ 'shape' => 'DNSName', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Tags' => [ 'shape' => 'Tags', ], 'WindowsConfiguration' => [ 'shape' => 'WindowsFileSystemConfiguration', ], 'LustreConfiguration' => [ 'shape' => 'LustreFileSystemConfiguration', ], 'AdministrativeActions' => [ 'shape' => 'AdministrativeActions', ], 'OntapConfiguration' => [ 'shape' => 'OntapFileSystemConfiguration', ], 'FileSystemTypeVersion' => [ 'shape' => 'FileSystemTypeVersion', ], 'OpenZFSConfiguration' => [ 'shape' => 'OpenZFSFileSystemConfiguration', ], ], ], 'FileSystemAdministratorsGroupName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,256}$', ], 'FileSystemEndpoint' => [ 'type' => 'structure', 'members' => [ 'DNSName' => [ 'shape' => 'DNSName', ], 'IpAddresses' => [ 'shape' => 'OntapEndpointIpAddresses', ], ], ], 'FileSystemEndpoints' => [ 'type' => 'structure', 'members' => [ 'Intercluster' => [ 'shape' => 'FileSystemEndpoint', ], 'Management' => [ 'shape' => 'FileSystemEndpoint', ], ], ], 'FileSystemFailureDetails' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], ], 'FileSystemId' => [ 'type' => 'string', 'max' => 21, 'min' => 11, 'pattern' => '^(fs-[0-9a-f]{8,})$', ], 'FileSystemIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileSystemId', ], 'max' => 50, ], 'FileSystemLifecycle' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'CREATING', 'FAILED', 'DELETING', 'MISCONFIGURED', 'UPDATING', 'MISCONFIGURED_UNAVAILABLE', ], ], 'FileSystemMaintenanceOperation' => [ 'type' => 'string', 'enum' => [ 'PATCHING', 'BACKING_UP', ], ], 'FileSystemMaintenanceOperations' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileSystemMaintenanceOperation', ], 'max' => 20, ], 'FileSystemNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'FileSystemType' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', 'LUSTRE', 'ONTAP', 'OPENZFS', ], ], 'FileSystemTypeVersion' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[0-9](.[0-9]*)*$', ], 'FileSystems' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileSystem', ], 'max' => 50, ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'FilterName', ], 'Values' => [ 'shape' => 'FilterValues', ], ], ], 'FilterName' => [ 'type' => 'string', 'enum' => [ 'file-system-id', 'backup-type', 'file-system-type', 'volume-id', 'data-repository-type', 'file-cache-id', 'file-cache-type', ], ], 'FilterValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[0-9a-zA-Z\\*\\.\\\\/\\?\\-\\_]*$', ], 'FilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], 'max' => 20, ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 10, ], 'Flag' => [ 'type' => 'boolean', ], 'FlexCacheEndpointType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'ORIGIN', 'CACHE', ], ], 'GeneralARN' => [ 'type' => 'string', 'max' => 1024, 'min' => 8, 'pattern' => '^arn:[^:]{1,63}:[^:]{0,63}:[^:]{0,63}:(?:|\\d{12}):[^/].{0,1023}$', ], 'HAPairs' => [ 'type' => 'integer', 'max' => 6, 'min' => 1, ], 'IncompatibleParameterError' => [ 'type' => 'structure', 'required' => [ 'Parameter', ], 'members' => [ 'Parameter' => [ 'shape' => 'Parameter', ], 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'IncompatibleRegionForMultiAZ' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InputOntapVolumeType' => [ 'type' => 'string', 'enum' => [ 'RW', 'DP', ], ], 'IntegerNoMax' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => 0, ], 'IntegerNoMaxFromNegativeOne' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => -1, ], 'IntegerRecordSizeKiB' => [ 'type' => 'integer', 'max' => 1024, 'min' => 4, ], 'InternalServerError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'InvalidDataRepositoryType' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidDestinationKmsKey' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidExportPath' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidImportPath' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidNetworkSettings' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'InvalidSubnetId' => [ 'shape' => 'SubnetId', ], 'InvalidSecurityGroupId' => [ 'shape' => 'SecurityGroupId', ], 'InvalidRouteTableId' => [ 'shape' => 'RouteTableId', ], ], 'exception' => true, ], 'InvalidPerUnitStorageThroughput' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidRegion' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidSourceKmsKey' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Iops' => [ 'type' => 'long', 'max' => 2400000, 'min' => 0, ], 'IpAddress' => [ 'type' => 'string', 'max' => 15, 'min' => 7, 'pattern' => '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$', ], 'IpAddressRange' => [ 'type' => 'string', 'max' => 17, 'min' => 9, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{9,17}$', ], 'JunctionPath' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,255}$', ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^.{1,2048}$', ], 'LastUpdatedTime' => [ 'type' => 'timestamp', ], 'LifecycleTransitionReason' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], ], 'LimitedMaxResults' => [ 'type' => 'integer', 'max' => 25, 'min' => 1, ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'LustreAccessAuditLogLevel' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'WARN_ONLY', 'ERROR_ONLY', 'WARN_ERROR', ], ], 'LustreDeploymentType' => [ 'type' => 'string', 'enum' => [ 'SCRATCH_1', 'SCRATCH_2', 'PERSISTENT_1', 'PERSISTENT_2', ], ], 'LustreFileSystemConfiguration' => [ 'type' => 'structure', 'members' => [ 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'DataRepositoryConfiguration' => [ 'shape' => 'DataRepositoryConfiguration', ], 'DeploymentType' => [ 'shape' => 'LustreDeploymentType', ], 'PerUnitStorageThroughput' => [ 'shape' => 'PerUnitStorageThroughput', ], 'MountName' => [ 'shape' => 'LustreFileSystemMountName', ], 'DailyAutomaticBackupStartTime' => [ 'shape' => 'DailyTime', ], 'AutomaticBackupRetentionDays' => [ 'shape' => 'AutomaticBackupRetentionDays', ], 'CopyTagsToBackups' => [ 'shape' => 'Flag', ], 'DriveCacheType' => [ 'shape' => 'DriveCacheType', ], 'DataCompressionType' => [ 'shape' => 'DataCompressionType', ], 'LogConfiguration' => [ 'shape' => 'LustreLogConfiguration', ], 'RootSquashConfiguration' => [ 'shape' => 'LustreRootSquashConfiguration', ], ], ], 'LustreFileSystemMountName' => [ 'type' => 'string', 'max' => 8, 'min' => 1, 'pattern' => '^([A-Za-z0-9_-]{1,8})$', ], 'LustreLogConfiguration' => [ 'type' => 'structure', 'required' => [ 'Level', ], 'members' => [ 'Level' => [ 'shape' => 'LustreAccessAuditLogLevel', ], 'Destination' => [ 'shape' => 'GeneralARN', ], ], ], 'LustreLogCreateConfiguration' => [ 'type' => 'structure', 'required' => [ 'Level', ], 'members' => [ 'Level' => [ 'shape' => 'LustreAccessAuditLogLevel', ], 'Destination' => [ 'shape' => 'GeneralARN', ], ], ], 'LustreNoSquashNid' => [ 'type' => 'string', 'max' => 43, 'min' => 11, 'pattern' => '^([0-9\\[\\]\\-]*\\.){3}([0-9\\[\\]\\-]*)@tcp$', ], 'LustreNoSquashNids' => [ 'type' => 'list', 'member' => [ 'shape' => 'LustreNoSquashNid', ], 'max' => 64, ], 'LustreRootSquash' => [ 'type' => 'string', 'max' => 21, 'min' => 3, 'pattern' => '^([0-9]{1,10}):([0-9]{1,10})$', ], 'LustreRootSquashConfiguration' => [ 'type' => 'structure', 'members' => [ 'RootSquash' => [ 'shape' => 'LustreRootSquash', ], 'NoSquashNids' => [ 'shape' => 'LustreNoSquashNids', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => 1, ], 'Megabytes' => [ 'type' => 'integer', 'max' => 512000, 'min' => 1, ], 'MegabytesPerSecond' => [ 'type' => 'integer', 'max' => 100000, 'min' => 8, ], 'MetadataStorageCapacity' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => 0, ], 'MissingFileCacheConfiguration' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'MissingFileSystemConfiguration' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'MissingVolumeConfiguration' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'NFSDataRepositoryConfiguration' => [ 'type' => 'structure', 'required' => [ 'Version', ], 'members' => [ 'Version' => [ 'shape' => 'NfsVersion', ], 'DnsIps' => [ 'shape' => 'RepositoryDnsIps', ], 'AutoExportPolicy' => [ 'shape' => 'AutoExportPolicy', ], ], ], 'Namespace' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,4096}$', ], 'NetBiosAlias' => [ 'type' => 'string', 'max' => 15, 'min' => 1, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,255}$', ], 'NetworkInterfaceId' => [ 'type' => 'string', 'max' => 21, 'min' => 12, 'pattern' => '^(eni-[0-9a-f]{8,})$', ], 'NetworkInterfaceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkInterfaceId', ], 'max' => 50, ], 'NextToken' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(?:[A-Za-z0-9+\\/]{4})*(?:[A-Za-z0-9+\\/]{2}==|[A-Za-z0-9+\\/]{3}=)?$', ], 'NfsVersion' => [ 'type' => 'string', 'enum' => [ 'NFS3', ], ], 'NotServiceResourceError' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'OntapDeploymentType' => [ 'type' => 'string', 'enum' => [ 'MULTI_AZ_1', 'SINGLE_AZ_1', 'SINGLE_AZ_2', ], ], 'OntapEndpointIpAddresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddress', ], 'max' => 24, 'min' => 1, ], 'OntapFileSystemConfiguration' => [ 'type' => 'structure', 'members' => [ 'AutomaticBackupRetentionDays' => [ 'shape' => 'AutomaticBackupRetentionDays', ], 'DailyAutomaticBackupStartTime' => [ 'shape' => 'DailyTime', ], 'DeploymentType' => [ 'shape' => 'OntapDeploymentType', ], 'EndpointIpAddressRange' => [ 'shape' => 'IpAddressRange', ], 'Endpoints' => [ 'shape' => 'FileSystemEndpoints', ], 'DiskIopsConfiguration' => [ 'shape' => 'DiskIopsConfiguration', ], 'PreferredSubnetId' => [ 'shape' => 'SubnetId', ], 'RouteTableIds' => [ 'shape' => 'RouteTableIds', ], 'ThroughputCapacity' => [ 'shape' => 'MegabytesPerSecond', ], 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'FsxAdminPassword' => [ 'shape' => 'AdminPassword', ], 'HAPairs' => [ 'shape' => 'HAPairs', ], 'ThroughputCapacityPerHAPair' => [ 'shape' => 'ThroughputCapacityPerHAPair', ], ], ], 'OntapVolumeConfiguration' => [ 'type' => 'structure', 'members' => [ 'FlexCacheEndpointType' => [ 'shape' => 'FlexCacheEndpointType', ], 'JunctionPath' => [ 'shape' => 'JunctionPath', ], 'SecurityStyle' => [ 'shape' => 'SecurityStyle', ], 'SizeInMegabytes' => [ 'shape' => 'VolumeCapacity', ], 'StorageEfficiencyEnabled' => [ 'shape' => 'Flag', ], 'StorageVirtualMachineId' => [ 'shape' => 'StorageVirtualMachineId', ], 'StorageVirtualMachineRoot' => [ 'shape' => 'Flag', ], 'TieringPolicy' => [ 'shape' => 'TieringPolicy', ], 'UUID' => [ 'shape' => 'UUID', ], 'OntapVolumeType' => [ 'shape' => 'OntapVolumeType', ], 'SnapshotPolicy' => [ 'shape' => 'SnapshotPolicy', ], 'CopyTagsToBackups' => [ 'shape' => 'Flag', ], 'SnaplockConfiguration' => [ 'shape' => 'SnaplockConfiguration', ], 'VolumeStyle' => [ 'shape' => 'VolumeStyle', ], 'AggregateConfiguration' => [ 'shape' => 'AggregateConfiguration', ], 'SizeInBytes' => [ 'shape' => 'VolumeCapacityBytes', ], ], ], 'OntapVolumeType' => [ 'type' => 'string', 'enum' => [ 'RW', 'DP', 'LS', ], ], 'OpenZFSClientConfiguration' => [ 'type' => 'structure', 'required' => [ 'Clients', 'Options', ], 'members' => [ 'Clients' => [ 'shape' => 'OpenZFSClients', ], 'Options' => [ 'shape' => 'OpenZFSNfsExportOptions', ], ], ], 'OpenZFSClientConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpenZFSClientConfiguration', ], 'max' => 25, ], 'OpenZFSClients' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[ -~]{1,128}$', ], 'OpenZFSCopyStrategy' => [ 'type' => 'string', 'enum' => [ 'CLONE', 'FULL_COPY', 'INCREMENTAL_COPY', ], ], 'OpenZFSCreateRootVolumeConfiguration' => [ 'type' => 'structure', 'members' => [ 'RecordSizeKiB' => [ 'shape' => 'IntegerRecordSizeKiB', ], 'DataCompressionType' => [ 'shape' => 'OpenZFSDataCompressionType', ], 'NfsExports' => [ 'shape' => 'OpenZFSNfsExports', ], 'UserAndGroupQuotas' => [ 'shape' => 'OpenZFSUserAndGroupQuotas', ], 'CopyTagsToSnapshots' => [ 'shape' => 'Flag', ], 'ReadOnly' => [ 'shape' => 'ReadOnly', ], ], ], 'OpenZFSDataCompressionType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'ZSTD', 'LZ4', ], ], 'OpenZFSDeploymentType' => [ 'type' => 'string', 'enum' => [ 'SINGLE_AZ_1', 'SINGLE_AZ_2', 'MULTI_AZ_1', ], ], 'OpenZFSFileSystemConfiguration' => [ 'type' => 'structure', 'members' => [ 'AutomaticBackupRetentionDays' => [ 'shape' => 'AutomaticBackupRetentionDays', ], 'CopyTagsToBackups' => [ 'shape' => 'Flag', ], 'CopyTagsToVolumes' => [ 'shape' => 'Flag', ], 'DailyAutomaticBackupStartTime' => [ 'shape' => 'DailyTime', ], 'DeploymentType' => [ 'shape' => 'OpenZFSDeploymentType', ], 'ThroughputCapacity' => [ 'shape' => 'MegabytesPerSecond', ], 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'DiskIopsConfiguration' => [ 'shape' => 'DiskIopsConfiguration', ], 'RootVolumeId' => [ 'shape' => 'VolumeId', ], 'PreferredSubnetId' => [ 'shape' => 'SubnetId', ], 'EndpointIpAddressRange' => [ 'shape' => 'IpAddressRange', ], 'RouteTableIds' => [ 'shape' => 'RouteTableIds', ], 'EndpointIpAddress' => [ 'shape' => 'IpAddress', ], ], ], 'OpenZFSNfsExport' => [ 'type' => 'structure', 'required' => [ 'ClientConfigurations', ], 'members' => [ 'ClientConfigurations' => [ 'shape' => 'OpenZFSClientConfigurations', ], ], ], 'OpenZFSNfsExportOption' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[ -~]{1,128}$', ], 'OpenZFSNfsExportOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpenZFSNfsExportOption', ], 'max' => 20, 'min' => 1, ], 'OpenZFSNfsExports' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpenZFSNfsExport', ], 'max' => 1, ], 'OpenZFSOriginSnapshotConfiguration' => [ 'type' => 'structure', 'members' => [ 'SnapshotARN' => [ 'shape' => 'ResourceARN', ], 'CopyStrategy' => [ 'shape' => 'OpenZFSCopyStrategy', ], ], ], 'OpenZFSQuotaType' => [ 'type' => 'string', 'enum' => [ 'USER', 'GROUP', ], ], 'OpenZFSUserAndGroupQuotas' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpenZFSUserOrGroupQuota', ], 'max' => 500, ], 'OpenZFSUserOrGroupQuota' => [ 'type' => 'structure', 'required' => [ 'Type', 'Id', 'StorageCapacityQuotaGiB', ], 'members' => [ 'Type' => [ 'shape' => 'OpenZFSQuotaType', ], 'Id' => [ 'shape' => 'IntegerNoMax', ], 'StorageCapacityQuotaGiB' => [ 'shape' => 'IntegerNoMax', ], ], ], 'OpenZFSVolumeConfiguration' => [ 'type' => 'structure', 'members' => [ 'ParentVolumeId' => [ 'shape' => 'VolumeId', ], 'VolumePath' => [ 'shape' => 'VolumePath', ], 'StorageCapacityReservationGiB' => [ 'shape' => 'IntegerNoMax', ], 'StorageCapacityQuotaGiB' => [ 'shape' => 'IntegerNoMax', ], 'RecordSizeKiB' => [ 'shape' => 'IntegerRecordSizeKiB', ], 'DataCompressionType' => [ 'shape' => 'OpenZFSDataCompressionType', ], 'CopyTagsToSnapshots' => [ 'shape' => 'Flag', ], 'OriginSnapshot' => [ 'shape' => 'OpenZFSOriginSnapshotConfiguration', ], 'ReadOnly' => [ 'shape' => 'ReadOnly', ], 'NfsExports' => [ 'shape' => 'OpenZFSNfsExports', ], 'UserAndGroupQuotas' => [ 'shape' => 'OpenZFSUserAndGroupQuotas', ], 'RestoreToSnapshot' => [ 'shape' => 'SnapshotId', ], 'DeleteIntermediateSnaphots' => [ 'shape' => 'Flag', ], 'DeleteClonedVolumes' => [ 'shape' => 'Flag', ], 'DeleteIntermediateData' => [ 'shape' => 'Flag', ], 'SourceSnapshotARN' => [ 'shape' => 'ResourceARN', ], 'DestinationSnapshot' => [ 'shape' => 'SnapshotId', ], ], ], 'OrganizationalUnitDistinguishedName' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,2000}$', ], 'Parameter' => [ 'type' => 'string', 'min' => 1, ], 'PerUnitStorageThroughput' => [ 'type' => 'integer', 'max' => 1000, 'min' => 12, ], 'PrivilegedDelete' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', 'PERMANENTLY_DISABLED', ], ], 'ProgressPercent' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'ReadOnly' => [ 'type' => 'boolean', ], 'Region' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[a-z0-9-]{1,20}$', ], 'ReleaseConfiguration' => [ 'type' => 'structure', 'members' => [ 'DurationSinceLastAccess' => [ 'shape' => 'DurationSinceLastAccess', ], ], ], 'ReleaseFileSystemNfsV3LocksRequest' => [ 'type' => 'structure', 'required' => [ 'FileSystemId', ], 'members' => [ 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'ReleaseFileSystemNfsV3LocksResponse' => [ 'type' => 'structure', 'members' => [ 'FileSystem' => [ 'shape' => 'FileSystem', ], ], ], 'ReleasedCapacity' => [ 'type' => 'long', ], 'RemainingTransferBytes' => [ 'type' => 'long', 'min' => 0, ], 'ReportFormat' => [ 'type' => 'string', 'enum' => [ 'REPORT_CSV_20191124', ], ], 'ReportScope' => [ 'type' => 'string', 'enum' => [ 'FAILED_FILES_ONLY', ], ], 'RepositoryDnsIps' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddress', ], 'max' => 10, ], 'RequestTime' => [ 'type' => 'timestamp', ], 'ResourceARN' => [ 'type' => 'string', 'max' => 512, 'min' => 8, 'pattern' => '^arn:(?=[^:]+:fsx:[^:]+:\\d{12}:)((|(?=[a-z0-9-.]{1,63})(?!\\d{1,3}(\\.\\d{1,3}){3})(?![^:]*-{2})(?![^:]*-\\.)(?![^:]*\\.-)[a-z0-9].*(?<!-)):){4}(?!/).{0,1024}$', ], 'ResourceDoesNotSupportTagging' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceNotFound' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'FILE_SYSTEM', 'VOLUME', ], ], 'RestoreOpenZFSVolumeOption' => [ 'type' => 'string', 'enum' => [ 'DELETE_INTERMEDIATE_SNAPSHOTS', 'DELETE_CLONED_VOLUMES', ], ], 'RestoreOpenZFSVolumeOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RestoreOpenZFSVolumeOption', ], 'max' => 2, ], 'RestoreVolumeFromSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'VolumeId', 'SnapshotId', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'VolumeId' => [ 'shape' => 'VolumeId', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'Options' => [ 'shape' => 'RestoreOpenZFSVolumeOptions', ], ], ], 'RestoreVolumeFromSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'VolumeId' => [ 'shape' => 'VolumeId', ], 'Lifecycle' => [ 'shape' => 'VolumeLifecycle', ], 'AdministrativeActions' => [ 'shape' => 'AdministrativeActions', ], ], ], 'RetentionPeriod' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'RetentionPeriodType', ], 'Value' => [ 'shape' => 'RetentionPeriodValue', ], ], ], 'RetentionPeriodType' => [ 'type' => 'string', 'enum' => [ 'SECONDS', 'MINUTES', 'HOURS', 'DAYS', 'MONTHS', 'YEARS', 'INFINITE', 'UNSPECIFIED', ], ], 'RetentionPeriodValue' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'RouteTableId' => [ 'type' => 'string', 'max' => 21, 'min' => 12, 'pattern' => '^(rtb-[0-9a-f]{8,})$', ], 'RouteTableIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteTableId', ], 'max' => 50, ], 'S3DataRepositoryConfiguration' => [ 'type' => 'structure', 'members' => [ 'AutoImportPolicy' => [ 'shape' => 'AutoImportPolicy', ], 'AutoExportPolicy' => [ 'shape' => 'AutoExportPolicy', ], ], ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 20, 'min' => 11, 'pattern' => '^(sg-[0-9a-f]{8,})$', ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 50, ], 'SecurityStyle' => [ 'type' => 'string', 'enum' => [ 'UNIX', 'NTFS', 'MIXED', ], ], 'SelfManagedActiveDirectoryAttributes' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'ActiveDirectoryFullyQualifiedName', ], 'OrganizationalUnitDistinguishedName' => [ 'shape' => 'OrganizationalUnitDistinguishedName', ], 'FileSystemAdministratorsGroup' => [ 'shape' => 'FileSystemAdministratorsGroupName', ], 'UserName' => [ 'shape' => 'DirectoryUserName', ], 'DnsIps' => [ 'shape' => 'DnsIps', ], ], ], 'SelfManagedActiveDirectoryConfiguration' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'UserName', 'Password', 'DnsIps', ], 'members' => [ 'DomainName' => [ 'shape' => 'ActiveDirectoryFullyQualifiedName', ], 'OrganizationalUnitDistinguishedName' => [ 'shape' => 'OrganizationalUnitDistinguishedName', ], 'FileSystemAdministratorsGroup' => [ 'shape' => 'FileSystemAdministratorsGroupName', ], 'UserName' => [ 'shape' => 'DirectoryUserName', ], 'Password' => [ 'shape' => 'DirectoryPassword', ], 'DnsIps' => [ 'shape' => 'DnsIps', ], ], ], 'SelfManagedActiveDirectoryConfigurationUpdates' => [ 'type' => 'structure', 'members' => [ 'UserName' => [ 'shape' => 'DirectoryUserName', ], 'Password' => [ 'shape' => 'DirectoryPassword', ], 'DnsIps' => [ 'shape' => 'DnsIps', ], 'DomainName' => [ 'shape' => 'ActiveDirectoryFullyQualifiedName', ], 'OrganizationalUnitDistinguishedName' => [ 'shape' => 'OrganizationalUnitDistinguishedName', ], 'FileSystemAdministratorsGroup' => [ 'shape' => 'FileSystemAdministratorsGroupName', ], ], ], 'ServiceLimit' => [ 'type' => 'string', 'enum' => [ 'FILE_SYSTEM_COUNT', 'TOTAL_THROUGHPUT_CAPACITY', 'TOTAL_STORAGE', 'TOTAL_USER_INITIATED_BACKUPS', 'TOTAL_USER_TAGS', 'TOTAL_IN_PROGRESS_COPY_BACKUPS', 'STORAGE_VIRTUAL_MACHINES_PER_FILE_SYSTEM', 'VOLUMES_PER_FILE_SYSTEM', 'TOTAL_SSD_IOPS', 'FILE_CACHE_COUNT', ], ], 'ServiceLimitExceeded' => [ 'type' => 'structure', 'required' => [ 'Limit', ], 'members' => [ 'Limit' => [ 'shape' => 'ServiceLimit', ], 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'SnaplockConfiguration' => [ 'type' => 'structure', 'members' => [ 'AuditLogVolume' => [ 'shape' => 'Flag', ], 'AutocommitPeriod' => [ 'shape' => 'AutocommitPeriod', ], 'PrivilegedDelete' => [ 'shape' => 'PrivilegedDelete', ], 'RetentionPeriod' => [ 'shape' => 'SnaplockRetentionPeriod', ], 'SnaplockType' => [ 'shape' => 'SnaplockType', ], 'VolumeAppendModeEnabled' => [ 'shape' => 'Flag', ], ], ], 'SnaplockRetentionPeriod' => [ 'type' => 'structure', 'required' => [ 'DefaultRetention', 'MinimumRetention', 'MaximumRetention', ], 'members' => [ 'DefaultRetention' => [ 'shape' => 'RetentionPeriod', ], 'MinimumRetention' => [ 'shape' => 'RetentionPeriod', ], 'MaximumRetention' => [ 'shape' => 'RetentionPeriod', ], ], ], 'SnaplockType' => [ 'type' => 'string', 'enum' => [ 'COMPLIANCE', 'ENTERPRISE', ], ], 'Snapshot' => [ 'type' => 'structure', 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'Name' => [ 'shape' => 'SnapshotName', ], 'VolumeId' => [ 'shape' => 'VolumeId', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'Lifecycle' => [ 'shape' => 'SnapshotLifecycle', ], 'LifecycleTransitionReason' => [ 'shape' => 'LifecycleTransitionReason', ], 'Tags' => [ 'shape' => 'Tags', ], 'AdministrativeActions' => [ 'shape' => 'AdministrativeActions', ], ], ], 'SnapshotFilter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'SnapshotFilterName', ], 'Values' => [ 'shape' => 'SnapshotFilterValues', ], ], ], 'SnapshotFilterName' => [ 'type' => 'string', 'enum' => [ 'file-system-id', 'volume-id', ], ], 'SnapshotFilterValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[0-9a-zA-Z\\*\\.\\\\/\\?\\-\\_]*$', ], 'SnapshotFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'SnapshotFilterValue', ], 'max' => 20, ], 'SnapshotFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'SnapshotFilter', ], 'max' => 2, ], 'SnapshotId' => [ 'type' => 'string', 'max' => 28, 'min' => 11, 'pattern' => '^((fs)?volsnap-[0-9a-f]{8,})$', ], 'SnapshotIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SnapshotId', ], 'max' => 50, ], 'SnapshotLifecycle' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'CREATING', 'DELETING', 'AVAILABLE', ], ], 'SnapshotName' => [ 'type' => 'string', 'max' => 203, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_:.-]{1,203}$', ], 'SnapshotNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'SnapshotPolicy' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'Snapshots' => [ 'type' => 'list', 'member' => [ 'shape' => 'Snapshot', ], 'max' => 50, ], 'SourceBackupId' => [ 'type' => 'string', 'max' => 128, 'min' => 12, 'pattern' => '^(backup-[0-9a-f]{8,})$', ], 'SourceBackupUnavailable' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'BackupId' => [ 'shape' => 'BackupId', ], ], 'exception' => true, ], 'StartMisconfiguredStateRecoveryRequest' => [ 'type' => 'structure', 'required' => [ 'FileSystemId', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'FileSystemId' => [ 'shape' => 'FileSystemId', ], ], ], 'StartMisconfiguredStateRecoveryResponse' => [ 'type' => 'structure', 'members' => [ 'FileSystem' => [ 'shape' => 'FileSystem', ], ], ], 'StartTime' => [ 'type' => 'timestamp', ], 'Status' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'IN_PROGRESS', 'PENDING', 'COMPLETED', 'UPDATED_OPTIMIZING', ], ], 'StorageCapacity' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => 0, ], 'StorageType' => [ 'type' => 'string', 'enum' => [ 'SSD', 'HDD', ], ], 'StorageVirtualMachine' => [ 'type' => 'structure', 'members' => [ 'ActiveDirectoryConfiguration' => [ 'shape' => 'SvmActiveDirectoryConfiguration', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'Endpoints' => [ 'shape' => 'SvmEndpoints', ], 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'Lifecycle' => [ 'shape' => 'StorageVirtualMachineLifecycle', ], 'Name' => [ 'shape' => 'StorageVirtualMachineName', ], 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'StorageVirtualMachineId' => [ 'shape' => 'StorageVirtualMachineId', ], 'Subtype' => [ 'shape' => 'StorageVirtualMachineSubtype', ], 'UUID' => [ 'shape' => 'UUID', ], 'Tags' => [ 'shape' => 'Tags', ], 'LifecycleTransitionReason' => [ 'shape' => 'LifecycleTransitionReason', ], 'RootVolumeSecurityStyle' => [ 'shape' => 'StorageVirtualMachineRootVolumeSecurityStyle', ], ], ], 'StorageVirtualMachineFilter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'StorageVirtualMachineFilterName', ], 'Values' => [ 'shape' => 'StorageVirtualMachineFilterValues', ], ], ], 'StorageVirtualMachineFilterName' => [ 'type' => 'string', 'enum' => [ 'file-system-id', ], ], 'StorageVirtualMachineFilterValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[0-9a-zA-Z\\*\\.\\\\/\\?\\-\\_]*$', ], 'StorageVirtualMachineFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageVirtualMachineFilterValue', ], 'max' => 20, ], 'StorageVirtualMachineFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageVirtualMachineFilter', ], 'max' => 1, ], 'StorageVirtualMachineId' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^(svm-[0-9a-f]{17,})$', ], 'StorageVirtualMachineIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageVirtualMachineId', ], 'max' => 50, ], 'StorageVirtualMachineLifecycle' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'CREATING', 'DELETING', 'FAILED', 'MISCONFIGURED', 'PENDING', ], ], 'StorageVirtualMachineName' => [ 'type' => 'string', 'max' => 47, 'min' => 1, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,47}$', ], 'StorageVirtualMachineNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'StorageVirtualMachineRootVolumeSecurityStyle' => [ 'type' => 'string', 'enum' => [ 'UNIX', 'NTFS', 'MIXED', ], ], 'StorageVirtualMachineSubtype' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'DP_DESTINATION', 'SYNC_DESTINATION', 'SYNC_SOURCE', ], ], 'StorageVirtualMachines' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageVirtualMachine', ], 'max' => 50, ], 'SubDirectoriesPaths' => [ 'type' => 'list', 'member' => [ 'shape' => 'Namespace', ], 'max' => 500, ], 'SubnetId' => [ 'type' => 'string', 'max' => 24, 'min' => 15, 'pattern' => '^(subnet-[0-9a-f]{8,})$', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 50, ], 'SucceededCount' => [ 'type' => 'long', ], 'SvmActiveDirectoryConfiguration' => [ 'type' => 'structure', 'members' => [ 'NetBiosName' => [ 'shape' => 'NetBiosAlias', ], 'SelfManagedActiveDirectoryConfiguration' => [ 'shape' => 'SelfManagedActiveDirectoryAttributes', ], ], ], 'SvmEndpoint' => [ 'type' => 'structure', 'members' => [ 'DNSName' => [ 'shape' => 'DNSName', ], 'IpAddresses' => [ 'shape' => 'OntapEndpointIpAddresses', ], ], ], 'SvmEndpoints' => [ 'type' => 'structure', 'members' => [ 'Iscsi' => [ 'shape' => 'SvmEndpoint', ], 'Management' => [ 'shape' => 'SvmEndpoint', ], 'Nfs' => [ 'shape' => 'SvmEndpoint', ], 'Smb' => [ 'shape' => 'SvmEndpoint', ], ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 1, ], 'TaskId' => [ 'type' => 'string', 'max' => 128, 'min' => 12, 'pattern' => '^(task-[0-9a-f]{17,})$', ], 'TaskIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskId', ], 'max' => 50, ], 'ThroughputCapacityPerHAPair' => [ 'type' => 'integer', 'max' => 6144, 'min' => 128, ], 'TieringPolicy' => [ 'type' => 'structure', 'members' => [ 'CoolingPeriod' => [ 'shape' => 'CoolingPeriod', ], 'Name' => [ 'shape' => 'TieringPolicyName', ], ], ], 'TieringPolicyName' => [ 'type' => 'string', 'enum' => [ 'SNAPSHOT_ONLY', 'AUTO', 'ALL', 'NONE', ], ], 'TotalConstituents' => [ 'type' => 'integer', 'max' => 200, 'min' => 1, ], 'TotalCount' => [ 'type' => 'long', ], 'TotalTransferBytes' => [ 'type' => 'long', 'min' => 0, ], 'UUID' => [ 'type' => 'string', 'max' => 36, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,36}$', ], 'Unit' => [ 'type' => 'string', 'enum' => [ 'DAYS', ], ], 'UnsupportedOperation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'TagKeys' => [ 'shape' => 'TagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDataRepositoryAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'AssociationId', ], 'members' => [ 'AssociationId' => [ 'shape' => 'DataRepositoryAssociationId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'ImportedFileChunkSize' => [ 'shape' => 'Megabytes', ], 'S3' => [ 'shape' => 'S3DataRepositoryConfiguration', ], ], ], 'UpdateDataRepositoryAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'Association' => [ 'shape' => 'DataRepositoryAssociation', ], ], ], 'UpdateFileCacheLustreConfiguration' => [ 'type' => 'structure', 'members' => [ 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], ], ], 'UpdateFileCacheRequest' => [ 'type' => 'structure', 'required' => [ 'FileCacheId', ], 'members' => [ 'FileCacheId' => [ 'shape' => 'FileCacheId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'LustreConfiguration' => [ 'shape' => 'UpdateFileCacheLustreConfiguration', ], ], ], 'UpdateFileCacheResponse' => [ 'type' => 'structure', 'members' => [ 'FileCache' => [ 'shape' => 'FileCache', ], ], ], 'UpdateFileSystemLustreConfiguration' => [ 'type' => 'structure', 'members' => [ 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'DailyAutomaticBackupStartTime' => [ 'shape' => 'DailyTime', ], 'AutomaticBackupRetentionDays' => [ 'shape' => 'AutomaticBackupRetentionDays', ], 'AutoImportPolicy' => [ 'shape' => 'AutoImportPolicyType', ], 'DataCompressionType' => [ 'shape' => 'DataCompressionType', ], 'LogConfiguration' => [ 'shape' => 'LustreLogCreateConfiguration', ], 'RootSquashConfiguration' => [ 'shape' => 'LustreRootSquashConfiguration', ], 'PerUnitStorageThroughput' => [ 'shape' => 'PerUnitStorageThroughput', ], ], ], 'UpdateFileSystemOntapConfiguration' => [ 'type' => 'structure', 'members' => [ 'AutomaticBackupRetentionDays' => [ 'shape' => 'AutomaticBackupRetentionDays', ], 'DailyAutomaticBackupStartTime' => [ 'shape' => 'DailyTime', ], 'FsxAdminPassword' => [ 'shape' => 'AdminPassword', ], 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'DiskIopsConfiguration' => [ 'shape' => 'DiskIopsConfiguration', ], 'ThroughputCapacity' => [ 'shape' => 'MegabytesPerSecond', ], 'AddRouteTableIds' => [ 'shape' => 'RouteTableIds', ], 'RemoveRouteTableIds' => [ 'shape' => 'RouteTableIds', ], 'ThroughputCapacityPerHAPair' => [ 'shape' => 'ThroughputCapacityPerHAPair', ], ], ], 'UpdateFileSystemOpenZFSConfiguration' => [ 'type' => 'structure', 'members' => [ 'AutomaticBackupRetentionDays' => [ 'shape' => 'AutomaticBackupRetentionDays', ], 'CopyTagsToBackups' => [ 'shape' => 'Flag', ], 'CopyTagsToVolumes' => [ 'shape' => 'Flag', ], 'DailyAutomaticBackupStartTime' => [ 'shape' => 'DailyTime', ], 'ThroughputCapacity' => [ 'shape' => 'MegabytesPerSecond', ], 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'DiskIopsConfiguration' => [ 'shape' => 'DiskIopsConfiguration', ], 'AddRouteTableIds' => [ 'shape' => 'RouteTableIds', ], 'RemoveRouteTableIds' => [ 'shape' => 'RouteTableIds', ], ], ], 'UpdateFileSystemRequest' => [ 'type' => 'structure', 'required' => [ 'FileSystemId', ], 'members' => [ 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'StorageCapacity' => [ 'shape' => 'StorageCapacity', ], 'WindowsConfiguration' => [ 'shape' => 'UpdateFileSystemWindowsConfiguration', ], 'LustreConfiguration' => [ 'shape' => 'UpdateFileSystemLustreConfiguration', ], 'OntapConfiguration' => [ 'shape' => 'UpdateFileSystemOntapConfiguration', ], 'OpenZFSConfiguration' => [ 'shape' => 'UpdateFileSystemOpenZFSConfiguration', ], 'StorageType' => [ 'shape' => 'StorageType', ], ], ], 'UpdateFileSystemResponse' => [ 'type' => 'structure', 'members' => [ 'FileSystem' => [ 'shape' => 'FileSystem', ], ], ], 'UpdateFileSystemWindowsConfiguration' => [ 'type' => 'structure', 'members' => [ 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'DailyAutomaticBackupStartTime' => [ 'shape' => 'DailyTime', ], 'AutomaticBackupRetentionDays' => [ 'shape' => 'AutomaticBackupRetentionDays', ], 'ThroughputCapacity' => [ 'shape' => 'MegabytesPerSecond', ], 'SelfManagedActiveDirectoryConfiguration' => [ 'shape' => 'SelfManagedActiveDirectoryConfigurationUpdates', ], 'AuditLogConfiguration' => [ 'shape' => 'WindowsAuditLogCreateConfiguration', ], 'DiskIopsConfiguration' => [ 'shape' => 'DiskIopsConfiguration', ], ], ], 'UpdateOntapVolumeConfiguration' => [ 'type' => 'structure', 'members' => [ 'JunctionPath' => [ 'shape' => 'JunctionPath', ], 'SecurityStyle' => [ 'shape' => 'SecurityStyle', ], 'SizeInMegabytes' => [ 'shape' => 'VolumeCapacity', ], 'StorageEfficiencyEnabled' => [ 'shape' => 'Flag', ], 'TieringPolicy' => [ 'shape' => 'TieringPolicy', ], 'SnapshotPolicy' => [ 'shape' => 'SnapshotPolicy', ], 'CopyTagsToBackups' => [ 'shape' => 'Flag', ], 'SnaplockConfiguration' => [ 'shape' => 'UpdateSnaplockConfiguration', ], 'SizeInBytes' => [ 'shape' => 'VolumeCapacityBytes', ], ], ], 'UpdateOpenZFSVolumeConfiguration' => [ 'type' => 'structure', 'members' => [ 'StorageCapacityReservationGiB' => [ 'shape' => 'IntegerNoMaxFromNegativeOne', ], 'StorageCapacityQuotaGiB' => [ 'shape' => 'IntegerNoMaxFromNegativeOne', ], 'RecordSizeKiB' => [ 'shape' => 'IntegerRecordSizeKiB', ], 'DataCompressionType' => [ 'shape' => 'OpenZFSDataCompressionType', ], 'NfsExports' => [ 'shape' => 'OpenZFSNfsExports', ], 'UserAndGroupQuotas' => [ 'shape' => 'OpenZFSUserAndGroupQuotas', ], 'ReadOnly' => [ 'shape' => 'ReadOnly', ], ], ], 'UpdateOpenZFSVolumeOption' => [ 'type' => 'string', 'enum' => [ 'DELETE_INTERMEDIATE_SNAPSHOTS', 'DELETE_CLONED_VOLUMES', 'DELETE_INTERMEDIATE_DATA', ], ], 'UpdateOpenZFSVolumeOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateOpenZFSVolumeOption', ], ], 'UpdateSharedVpcConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'EnableFsxRouteTableUpdatesFromParticipantAccounts' => [ 'shape' => 'VerboseFlag', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'UpdateSharedVpcConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'EnableFsxRouteTableUpdatesFromParticipantAccounts' => [ 'shape' => 'VerboseFlag', ], ], ], 'UpdateSnaplockConfiguration' => [ 'type' => 'structure', 'members' => [ 'AuditLogVolume' => [ 'shape' => 'Flag', ], 'AutocommitPeriod' => [ 'shape' => 'AutocommitPeriod', ], 'PrivilegedDelete' => [ 'shape' => 'PrivilegedDelete', ], 'RetentionPeriod' => [ 'shape' => 'SnaplockRetentionPeriod', ], 'VolumeAppendModeEnabled' => [ 'shape' => 'Flag', ], ], ], 'UpdateSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'SnapshotId', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Name' => [ 'shape' => 'SnapshotName', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'UpdateSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'Snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'UpdateStorageVirtualMachineRequest' => [ 'type' => 'structure', 'required' => [ 'StorageVirtualMachineId', ], 'members' => [ 'ActiveDirectoryConfiguration' => [ 'shape' => 'UpdateSvmActiveDirectoryConfiguration', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'StorageVirtualMachineId' => [ 'shape' => 'StorageVirtualMachineId', ], 'SvmAdminPassword' => [ 'shape' => 'AdminPassword', ], ], ], 'UpdateStorageVirtualMachineResponse' => [ 'type' => 'structure', 'members' => [ 'StorageVirtualMachine' => [ 'shape' => 'StorageVirtualMachine', ], ], ], 'UpdateSvmActiveDirectoryConfiguration' => [ 'type' => 'structure', 'members' => [ 'SelfManagedActiveDirectoryConfiguration' => [ 'shape' => 'SelfManagedActiveDirectoryConfigurationUpdates', ], 'NetBiosName' => [ 'shape' => 'NetBiosAlias', ], ], ], 'UpdateVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'VolumeId', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'VolumeId' => [ 'shape' => 'VolumeId', ], 'OntapConfiguration' => [ 'shape' => 'UpdateOntapVolumeConfiguration', ], 'Name' => [ 'shape' => 'VolumeName', ], 'OpenZFSConfiguration' => [ 'shape' => 'UpdateOpenZFSVolumeConfiguration', ], ], ], 'UpdateVolumeResponse' => [ 'type' => 'structure', 'members' => [ 'Volume' => [ 'shape' => 'Volume', ], ], ], 'Value' => [ 'type' => 'long', 'min' => 0, ], 'VerboseFlag' => [ 'type' => 'string', 'max' => 5, 'min' => 4, 'pattern' => '^(?i)(true|false)$', ], 'Volume' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => 'CreationTime', ], 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'Lifecycle' => [ 'shape' => 'VolumeLifecycle', ], 'Name' => [ 'shape' => 'VolumeName', ], 'OntapConfiguration' => [ 'shape' => 'OntapVolumeConfiguration', ], 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Tags' => [ 'shape' => 'Tags', ], 'VolumeId' => [ 'shape' => 'VolumeId', ], 'VolumeType' => [ 'shape' => 'VolumeType', ], 'LifecycleTransitionReason' => [ 'shape' => 'LifecycleTransitionReason', ], 'AdministrativeActions' => [ 'shape' => 'AdministrativeActions', ], 'OpenZFSConfiguration' => [ 'shape' => 'OpenZFSVolumeConfiguration', ], ], ], 'VolumeCapacity' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => 0, ], 'VolumeCapacityBytes' => [ 'type' => 'long', 'max' => 22517998000000000, 'min' => 0, ], 'VolumeFilter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'VolumeFilterName', ], 'Values' => [ 'shape' => 'VolumeFilterValues', ], ], ], 'VolumeFilterName' => [ 'type' => 'string', 'enum' => [ 'file-system-id', 'storage-virtual-machine-id', ], ], 'VolumeFilterValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[0-9a-zA-Z\\*\\.\\\\/\\?\\-\\_]*$', ], 'VolumeFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeFilterValue', ], 'max' => 20, ], 'VolumeFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeFilter', ], 'max' => 2, ], 'VolumeId' => [ 'type' => 'string', 'max' => 23, 'min' => 23, 'pattern' => '^(fsvol-[0-9a-f]{17,})$', ], 'VolumeIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeId', ], 'max' => 50, ], 'VolumeLifecycle' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATED', 'DELETING', 'FAILED', 'MISCONFIGURED', 'PENDING', 'AVAILABLE', ], ], 'VolumeName' => [ 'type' => 'string', 'max' => 203, 'min' => 1, 'pattern' => '^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,203}$', ], 'VolumeNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'VolumePath' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[A-za-z0-9\\_\\.\\:\\-\\/]*$', ], 'VolumeStyle' => [ 'type' => 'string', 'enum' => [ 'FLEXVOL', 'FLEXGROUP', ], ], 'VolumeType' => [ 'type' => 'string', 'enum' => [ 'ONTAP', 'OPENZFS', ], ], 'Volumes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Volume', ], 'max' => 50, ], 'VpcId' => [ 'type' => 'string', 'max' => 21, 'min' => 12, 'pattern' => '^(vpc-[0-9a-f]{8,})$', ], 'WeeklyTime' => [ 'type' => 'string', 'max' => 7, 'min' => 7, 'pattern' => '^[1-7]:([01]\\d|2[0-3]):?([0-5]\\d)$', ], 'WindowsAccessAuditLogLevel' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'SUCCESS_ONLY', 'FAILURE_ONLY', 'SUCCESS_AND_FAILURE', ], ], 'WindowsAuditLogConfiguration' => [ 'type' => 'structure', 'required' => [ 'FileAccessAuditLogLevel', 'FileShareAccessAuditLogLevel', ], 'members' => [ 'FileAccessAuditLogLevel' => [ 'shape' => 'WindowsAccessAuditLogLevel', ], 'FileShareAccessAuditLogLevel' => [ 'shape' => 'WindowsAccessAuditLogLevel', ], 'AuditLogDestination' => [ 'shape' => 'GeneralARN', ], ], ], 'WindowsAuditLogCreateConfiguration' => [ 'type' => 'structure', 'required' => [ 'FileAccessAuditLogLevel', 'FileShareAccessAuditLogLevel', ], 'members' => [ 'FileAccessAuditLogLevel' => [ 'shape' => 'WindowsAccessAuditLogLevel', ], 'FileShareAccessAuditLogLevel' => [ 'shape' => 'WindowsAccessAuditLogLevel', ], 'AuditLogDestination' => [ 'shape' => 'GeneralARN', ], ], ], 'WindowsDeploymentType' => [ 'type' => 'string', 'enum' => [ 'MULTI_AZ_1', 'SINGLE_AZ_1', 'SINGLE_AZ_2', ], ], 'WindowsFileSystemConfiguration' => [ 'type' => 'structure', 'members' => [ 'ActiveDirectoryId' => [ 'shape' => 'DirectoryId', ], 'SelfManagedActiveDirectoryConfiguration' => [ 'shape' => 'SelfManagedActiveDirectoryAttributes', ], 'DeploymentType' => [ 'shape' => 'WindowsDeploymentType', ], 'RemoteAdministrationEndpoint' => [ 'shape' => 'DNSName', ], 'PreferredSubnetId' => [ 'shape' => 'SubnetId', ], 'PreferredFileServerIp' => [ 'shape' => 'IpAddress', ], 'ThroughputCapacity' => [ 'shape' => 'MegabytesPerSecond', ], 'MaintenanceOperationsInProgress' => [ 'shape' => 'FileSystemMaintenanceOperations', ], 'WeeklyMaintenanceStartTime' => [ 'shape' => 'WeeklyTime', ], 'DailyAutomaticBackupStartTime' => [ 'shape' => 'DailyTime', ], 'AutomaticBackupRetentionDays' => [ 'shape' => 'AutomaticBackupRetentionDays', ], 'CopyTagsToBackups' => [ 'shape' => 'Flag', ], 'Aliases' => [ 'shape' => 'Aliases', ], 'AuditLogConfiguration' => [ 'shape' => 'WindowsAuditLogConfiguration', ], 'DiskIopsConfiguration' => [ 'shape' => 'DiskIopsConfiguration', ], ], ], ],];
