<?php
// This file was auto-generated from sdk-root/src/data/databrew/2017-07-25/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-07-25', 'endpointPrefix' => 'databrew', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Glue DataBrew', 'serviceId' => 'DataBrew', 'signatureVersion' => 'v4', 'signingName' => 'databrew', 'uid' => 'databrew-2017-07-25', ], 'operations' => [ 'BatchDeleteRecipeVersion' => [ 'name' => 'BatchDeleteRecipeVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/recipes/{name}/batchDeleteRecipeVersion', ], 'input' => [ 'shape' => 'BatchDeleteRecipeVersionRequest', ], 'output' => [ 'shape' => 'BatchDeleteRecipeVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateDataset' => [ 'name' => 'CreateDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/datasets', ], 'input' => [ 'shape' => 'CreateDatasetRequest', ], 'output' => [ 'shape' => 'CreateDatasetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateProfileJob' => [ 'name' => 'CreateProfileJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/profileJobs', ], 'input' => [ 'shape' => 'CreateProfileJobRequest', ], 'output' => [ 'shape' => 'CreateProfileJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateProject' => [ 'name' => 'CreateProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects', ], 'input' => [ 'shape' => 'CreateProjectRequest', ], 'output' => [ 'shape' => 'CreateProjectResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateRecipe' => [ 'name' => 'CreateRecipe', 'http' => [ 'method' => 'POST', 'requestUri' => '/recipes', ], 'input' => [ 'shape' => 'CreateRecipeRequest', ], 'output' => [ 'shape' => 'CreateRecipeResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateRecipeJob' => [ 'name' => 'CreateRecipeJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/recipeJobs', ], 'input' => [ 'shape' => 'CreateRecipeJobRequest', ], 'output' => [ 'shape' => 'CreateRecipeJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateRuleset' => [ 'name' => 'CreateRuleset', 'http' => [ 'method' => 'POST', 'requestUri' => '/rulesets', ], 'input' => [ 'shape' => 'CreateRulesetRequest', ], 'output' => [ 'shape' => 'CreateRulesetResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateSchedule' => [ 'name' => 'CreateSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/schedules', ], 'input' => [ 'shape' => 'CreateScheduleRequest', ], 'output' => [ 'shape' => 'CreateScheduleResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteDataset' => [ 'name' => 'DeleteDataset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/datasets/{name}', ], 'input' => [ 'shape' => 'DeleteDatasetRequest', ], 'output' => [ 'shape' => 'DeleteDatasetResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteJob' => [ 'name' => 'DeleteJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/jobs/{name}', ], 'input' => [ 'shape' => 'DeleteJobRequest', ], 'output' => [ 'shape' => 'DeleteJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteProject' => [ 'name' => 'DeleteProject', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/projects/{name}', ], 'input' => [ 'shape' => 'DeleteProjectRequest', ], 'output' => [ 'shape' => 'DeleteProjectResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteRecipeVersion' => [ 'name' => 'DeleteRecipeVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/recipes/{name}/recipeVersion/{recipeVersion}', ], 'input' => [ 'shape' => 'DeleteRecipeVersionRequest', ], 'output' => [ 'shape' => 'DeleteRecipeVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteRuleset' => [ 'name' => 'DeleteRuleset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/rulesets/{name}', ], 'input' => [ 'shape' => 'DeleteRulesetRequest', ], 'output' => [ 'shape' => 'DeleteRulesetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteSchedule' => [ 'name' => 'DeleteSchedule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/schedules/{name}', ], 'input' => [ 'shape' => 'DeleteScheduleRequest', ], 'output' => [ 'shape' => 'DeleteScheduleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeDataset' => [ 'name' => 'DescribeDataset', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasets/{name}', ], 'input' => [ 'shape' => 'DescribeDatasetRequest', ], 'output' => [ 'shape' => 'DescribeDatasetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeJob' => [ 'name' => 'DescribeJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs/{name}', ], 'input' => [ 'shape' => 'DescribeJobRequest', ], 'output' => [ 'shape' => 'DescribeJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeJobRun' => [ 'name' => 'DescribeJobRun', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs/{name}/jobRun/{runId}', ], 'input' => [ 'shape' => 'DescribeJobRunRequest', ], 'output' => [ 'shape' => 'DescribeJobRunResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeProject' => [ 'name' => 'DescribeProject', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects/{name}', ], 'input' => [ 'shape' => 'DescribeProjectRequest', ], 'output' => [ 'shape' => 'DescribeProjectResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeRecipe' => [ 'name' => 'DescribeRecipe', 'http' => [ 'method' => 'GET', 'requestUri' => '/recipes/{name}', ], 'input' => [ 'shape' => 'DescribeRecipeRequest', ], 'output' => [ 'shape' => 'DescribeRecipeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeRuleset' => [ 'name' => 'DescribeRuleset', 'http' => [ 'method' => 'GET', 'requestUri' => '/rulesets/{name}', ], 'input' => [ 'shape' => 'DescribeRulesetRequest', ], 'output' => [ 'shape' => 'DescribeRulesetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeSchedule' => [ 'name' => 'DescribeSchedule', 'http' => [ 'method' => 'GET', 'requestUri' => '/schedules/{name}', ], 'input' => [ 'shape' => 'DescribeScheduleRequest', ], 'output' => [ 'shape' => 'DescribeScheduleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListDatasets' => [ 'name' => 'ListDatasets', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasets', ], 'input' => [ 'shape' => 'ListDatasetsRequest', ], 'output' => [ 'shape' => 'ListDatasetsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListJobRuns' => [ 'name' => 'ListJobRuns', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs/{name}/jobRuns', ], 'input' => [ 'shape' => 'ListJobRunsRequest', ], 'output' => [ 'shape' => 'ListJobRunsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListJobs' => [ 'name' => 'ListJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs', ], 'input' => [ 'shape' => 'ListJobsRequest', ], 'output' => [ 'shape' => 'ListJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListProjects' => [ 'name' => 'ListProjects', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects', ], 'input' => [ 'shape' => 'ListProjectsRequest', ], 'output' => [ 'shape' => 'ListProjectsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListRecipeVersions' => [ 'name' => 'ListRecipeVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/recipeVersions', ], 'input' => [ 'shape' => 'ListRecipeVersionsRequest', ], 'output' => [ 'shape' => 'ListRecipeVersionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListRecipes' => [ 'name' => 'ListRecipes', 'http' => [ 'method' => 'GET', 'requestUri' => '/recipes', ], 'input' => [ 'shape' => 'ListRecipesRequest', ], 'output' => [ 'shape' => 'ListRecipesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListRulesets' => [ 'name' => 'ListRulesets', 'http' => [ 'method' => 'GET', 'requestUri' => '/rulesets', ], 'input' => [ 'shape' => 'ListRulesetsRequest', ], 'output' => [ 'shape' => 'ListRulesetsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListSchedules' => [ 'name' => 'ListSchedules', 'http' => [ 'method' => 'GET', 'requestUri' => '/schedules', ], 'input' => [ 'shape' => 'ListSchedulesRequest', ], 'output' => [ 'shape' => 'ListSchedulesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'PublishRecipe' => [ 'name' => 'PublishRecipe', 'http' => [ 'method' => 'POST', 'requestUri' => '/recipes/{name}/publishRecipe', ], 'input' => [ 'shape' => 'PublishRecipeRequest', ], 'output' => [ 'shape' => 'PublishRecipeResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'SendProjectSessionAction' => [ 'name' => 'SendProjectSessionAction', 'http' => [ 'method' => 'PUT', 'requestUri' => '/projects/{name}/sendProjectSessionAction', ], 'input' => [ 'shape' => 'SendProjectSessionActionRequest', ], 'output' => [ 'shape' => 'SendProjectSessionActionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartJobRun' => [ 'name' => 'StartJobRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/jobs/{name}/startJobRun', ], 'input' => [ 'shape' => 'StartJobRunRequest', ], 'output' => [ 'shape' => 'StartJobRunResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartProjectSession' => [ 'name' => 'StartProjectSession', 'http' => [ 'method' => 'PUT', 'requestUri' => '/projects/{name}/startProjectSession', ], 'input' => [ 'shape' => 'StartProjectSessionRequest', ], 'output' => [ 'shape' => 'StartProjectSessionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'StopJobRun' => [ 'name' => 'StopJobRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/jobs/{name}/jobRun/{runId}/stopJobRun', ], 'input' => [ 'shape' => 'StopJobRunRequest', ], 'output' => [ 'shape' => 'StopJobRunResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateDataset' => [ 'name' => 'UpdateDataset', 'http' => [ 'method' => 'PUT', 'requestUri' => '/datasets/{name}', ], 'input' => [ 'shape' => 'UpdateDatasetRequest', ], 'output' => [ 'shape' => 'UpdateDatasetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateProfileJob' => [ 'name' => 'UpdateProfileJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/profileJobs/{name}', ], 'input' => [ 'shape' => 'UpdateProfileJobRequest', ], 'output' => [ 'shape' => 'UpdateProfileJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateProject' => [ 'name' => 'UpdateProject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/projects/{name}', ], 'input' => [ 'shape' => 'UpdateProjectRequest', ], 'output' => [ 'shape' => 'UpdateProjectResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateRecipe' => [ 'name' => 'UpdateRecipe', 'http' => [ 'method' => 'PUT', 'requestUri' => '/recipes/{name}', ], 'input' => [ 'shape' => 'UpdateRecipeRequest', ], 'output' => [ 'shape' => 'UpdateRecipeResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateRecipeJob' => [ 'name' => 'UpdateRecipeJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/recipeJobs/{name}', ], 'input' => [ 'shape' => 'UpdateRecipeJobRequest', ], 'output' => [ 'shape' => 'UpdateRecipeJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateRuleset' => [ 'name' => 'UpdateRuleset', 'http' => [ 'method' => 'PUT', 'requestUri' => '/rulesets/{name}', ], 'input' => [ 'shape' => 'UpdateRulesetRequest', ], 'output' => [ 'shape' => 'UpdateRulesetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateSchedule' => [ 'name' => 'UpdateSchedule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/schedules/{name}', ], 'input' => [ 'shape' => 'UpdateScheduleRequest', ], 'output' => [ 'shape' => 'UpdateScheduleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 255, ], 'ActionId' => [ 'type' => 'integer', ], 'AllowedStatisticList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AllowedStatistics', ], 'min' => 1, ], 'AllowedStatistics' => [ 'type' => 'structure', 'required' => [ 'Statistics', ], 'members' => [ 'Statistics' => [ 'shape' => 'StatisticList', ], ], ], 'AnalyticsMode' => [ 'type' => 'string', 'enum' => [ 'ENABLE', 'DISABLE', ], ], 'Arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'AssumeControl' => [ 'type' => 'boolean', ], 'Attempt' => [ 'type' => 'integer', ], 'BatchDeleteRecipeVersionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RecipeVersions', ], 'members' => [ 'Name' => [ 'shape' => 'RecipeName', 'location' => 'uri', 'locationName' => 'name', ], 'RecipeVersions' => [ 'shape' => 'RecipeVersionList', ], ], ], 'BatchDeleteRecipeVersionResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RecipeName', ], 'Errors' => [ 'shape' => 'RecipeErrorList', ], ], ], 'Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 3, ], 'BucketOwner' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^[0-9]{12}$', ], 'CatalogId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ClientSessionId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]*$', 'sensitive' => true, ], 'ColumnName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ColumnNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnName', ], 'max' => 200, ], 'ColumnRange' => [ 'type' => 'integer', 'max' => 20, 'min' => 0, ], 'ColumnSelector' => [ 'type' => 'structure', 'members' => [ 'Regex' => [ 'shape' => 'ColumnName', ], 'Name' => [ 'shape' => 'ColumnName', ], ], ], 'ColumnSelectorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnSelector', ], 'min' => 1, ], 'ColumnStatisticsConfiguration' => [ 'type' => 'structure', 'required' => [ 'Statistics', ], 'members' => [ 'Selectors' => [ 'shape' => 'ColumnSelectorList', ], 'Statistics' => [ 'shape' => 'StatisticsConfiguration', ], ], ], 'ColumnStatisticsConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnStatisticsConfiguration', ], 'min' => 1, ], 'CompressionFormat' => [ 'type' => 'string', 'enum' => [ 'GZIP', 'LZ4', 'SNAPPY', 'BZIP2', 'DEFLATE', 'LZO', 'BROTLI', 'ZSTD', 'ZLIB', ], ], 'Condition' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Z\\_]+$', ], 'ConditionExpression' => [ 'type' => 'structure', 'required' => [ 'Condition', 'TargetColumn', ], 'members' => [ 'Condition' => [ 'shape' => 'Condition', ], 'Value' => [ 'shape' => 'ConditionValue', ], 'TargetColumn' => [ 'shape' => 'TargetColumn', ], ], ], 'ConditionExpressionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConditionExpression', ], ], 'ConditionValue' => [ 'type' => 'string', 'max' => 1024, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CreateColumn' => [ 'type' => 'boolean', ], 'CreateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Input', ], 'members' => [ 'Name' => [ 'shape' => 'DatasetName', ], 'Format' => [ 'shape' => 'InputFormat', ], 'FormatOptions' => [ 'shape' => 'FormatOptions', ], 'Input' => [ 'shape' => 'Input', ], 'PathOptions' => [ 'shape' => 'PathOptions', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DatasetName', ], ], ], 'CreateProfileJobRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetName', 'Name', 'OutputLocation', 'RoleArn', ], 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetName', ], 'EncryptionKeyArn' => [ 'shape' => 'EncryptionKeyArn', ], 'EncryptionMode' => [ 'shape' => 'EncryptionMode', ], 'Name' => [ 'shape' => 'JobName', ], 'LogSubscription' => [ 'shape' => 'LogSubscription', ], 'MaxCapacity' => [ 'shape' => 'MaxCapacity', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'OutputLocation' => [ 'shape' => 'S3Location', ], 'Configuration' => [ 'shape' => 'ProfileConfiguration', ], 'ValidationConfigurations' => [ 'shape' => 'ValidationConfigurationList', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagMap', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'JobSample' => [ 'shape' => 'JobSample', ], ], ], 'CreateProfileJobResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'JobName', ], ], ], 'CreateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetName', 'Name', 'RecipeName', 'RoleArn', ], 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetName', ], 'Name' => [ 'shape' => 'ProjectName', ], 'RecipeName' => [ 'shape' => 'RecipeName', ], 'Sample' => [ 'shape' => 'Sample', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateProjectResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ProjectName', ], ], ], 'CreateRecipeJobRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RoleArn', ], 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetName', ], 'EncryptionKeyArn' => [ 'shape' => 'EncryptionKeyArn', ], 'EncryptionMode' => [ 'shape' => 'EncryptionMode', ], 'Name' => [ 'shape' => 'JobName', ], 'LogSubscription' => [ 'shape' => 'LogSubscription', ], 'MaxCapacity' => [ 'shape' => 'MaxCapacity', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'Outputs' => [ 'shape' => 'OutputList', ], 'DataCatalogOutputs' => [ 'shape' => 'DataCatalogOutputList', ], 'DatabaseOutputs' => [ 'shape' => 'DatabaseOutputList', ], 'ProjectName' => [ 'shape' => 'ProjectName', ], 'RecipeReference' => [ 'shape' => 'RecipeReference', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagMap', ], 'Timeout' => [ 'shape' => 'Timeout', ], ], ], 'CreateRecipeJobResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'JobName', ], ], ], 'CreateRecipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Steps', ], 'members' => [ 'Description' => [ 'shape' => 'RecipeDescription', ], 'Name' => [ 'shape' => 'RecipeName', ], 'Steps' => [ 'shape' => 'RecipeStepList', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateRecipeResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RecipeName', ], ], ], 'CreateRulesetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'TargetArn', 'Rules', ], 'members' => [ 'Name' => [ 'shape' => 'RulesetName', ], 'Description' => [ 'shape' => 'RulesetDescription', ], 'TargetArn' => [ 'shape' => 'Arn', ], 'Rules' => [ 'shape' => 'RuleList', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateRulesetResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RulesetName', ], ], ], 'CreateScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'CronExpression', 'Name', ], 'members' => [ 'JobNames' => [ 'shape' => 'JobNameList', ], 'CronExpression' => [ 'shape' => 'CronExpression', ], 'Tags' => [ 'shape' => 'TagMap', ], 'Name' => [ 'shape' => 'ScheduleName', ], ], ], 'CreateScheduleResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ScheduleName', ], ], ], 'CreatedBy' => [ 'type' => 'string', ], 'CronExpression' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'CsvOptions' => [ 'type' => 'structure', 'members' => [ 'Delimiter' => [ 'shape' => 'Delimiter', ], 'HeaderRow' => [ 'shape' => 'HeaderRow', ], ], ], 'CsvOutputOptions' => [ 'type' => 'structure', 'members' => [ 'Delimiter' => [ 'shape' => 'Delimiter', ], ], ], 'DataCatalogInputDefinition' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogId', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'TableName' => [ 'shape' => 'TableName', ], 'TempDirectory' => [ 'shape' => 'S3Location', ], ], ], 'DataCatalogOutput' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogId', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'TableName' => [ 'shape' => 'TableName', ], 'S3Options' => [ 'shape' => 'S3TableOutputOptions', ], 'DatabaseOptions' => [ 'shape' => 'DatabaseTableOutputOptions', ], 'Overwrite' => [ 'shape' => 'OverwriteOutput', ], ], ], 'DataCatalogOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataCatalogOutput', ], 'min' => 1, ], 'DatabaseInputDefinition' => [ 'type' => 'structure', 'required' => [ 'GlueConnectionName', ], 'members' => [ 'GlueConnectionName' => [ 'shape' => 'GlueConnectionName', ], 'DatabaseTableName' => [ 'shape' => 'DatabaseTableName', ], 'TempDirectory' => [ 'shape' => 'S3Location', ], 'QueryString' => [ 'shape' => 'QueryString', ], ], ], 'DatabaseName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DatabaseOutput' => [ 'type' => 'structure', 'required' => [ 'GlueConnectionName', 'DatabaseOptions', ], 'members' => [ 'GlueConnectionName' => [ 'shape' => 'GlueConnectionName', ], 'DatabaseOptions' => [ 'shape' => 'DatabaseTableOutputOptions', ], 'DatabaseOutputMode' => [ 'shape' => 'DatabaseOutputMode', ], ], ], 'DatabaseOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatabaseOutput', ], 'min' => 1, ], 'DatabaseOutputMode' => [ 'type' => 'string', 'enum' => [ 'NEW_TABLE', ], ], 'DatabaseTableName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DatabaseTableOutputOptions' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'TempDirectory' => [ 'shape' => 'S3Location', ], 'TableName' => [ 'shape' => 'DatabaseTableName', ], ], ], 'Dataset' => [ 'type' => 'structure', 'required' => [ 'Name', 'Input', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'CreatedBy' => [ 'shape' => 'CreatedBy', ], 'CreateDate' => [ 'shape' => 'Date', ], 'Name' => [ 'shape' => 'DatasetName', ], 'Format' => [ 'shape' => 'InputFormat', ], 'FormatOptions' => [ 'shape' => 'FormatOptions', ], 'Input' => [ 'shape' => 'Input', ], 'LastModifiedDate' => [ 'shape' => 'Date', ], 'LastModifiedBy' => [ 'shape' => 'LastModifiedBy', ], 'Source' => [ 'shape' => 'Source', ], 'PathOptions' => [ 'shape' => 'PathOptions', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ResourceArn' => [ 'shape' => 'Arn', ], ], ], 'DatasetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dataset', ], ], 'DatasetName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DatasetParameter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', ], 'members' => [ 'Name' => [ 'shape' => 'PathParameterName', ], 'Type' => [ 'shape' => 'ParameterType', ], 'DatetimeOptions' => [ 'shape' => 'DatetimeOptions', ], 'CreateColumn' => [ 'shape' => 'CreateColumn', ], 'Filter' => [ 'shape' => 'FilterExpression', ], ], ], 'Date' => [ 'type' => 'timestamp', ], 'DatetimeFormat' => [ 'type' => 'string', 'max' => 100, 'min' => 2, ], 'DatetimeOptions' => [ 'type' => 'structure', 'required' => [ 'Format', ], 'members' => [ 'Format' => [ 'shape' => 'DatetimeFormat', ], 'TimezoneOffset' => [ 'shape' => 'TimezoneOffset', ], 'LocaleCode' => [ 'shape' => 'LocaleCode', ], ], ], 'DeleteDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DatasetName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DatasetName', ], ], ], 'DeleteJobRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'JobName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteJobResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'JobName', ], ], ], 'DeleteProjectRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ProjectName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteProjectResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ProjectName', ], ], ], 'DeleteRecipeVersionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RecipeVersion', ], 'members' => [ 'Name' => [ 'shape' => 'RecipeName', 'location' => 'uri', 'locationName' => 'name', ], 'RecipeVersion' => [ 'shape' => 'RecipeVersion', 'location' => 'uri', 'locationName' => 'recipeVersion', ], ], ], 'DeleteRecipeVersionResponse' => [ 'type' => 'structure', 'required' => [ 'Name', 'RecipeVersion', ], 'members' => [ 'Name' => [ 'shape' => 'RecipeName', ], 'RecipeVersion' => [ 'shape' => 'RecipeVersion', ], ], ], 'DeleteRulesetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RulesetName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteRulesetResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RulesetName', ], ], ], 'DeleteScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ScheduleName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteScheduleResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ScheduleName', ], ], ], 'Delimiter' => [ 'type' => 'string', 'max' => 1, 'min' => 1, ], 'DescribeDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DatasetName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DescribeDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'Name', 'Input', ], 'members' => [ 'CreatedBy' => [ 'shape' => 'CreatedBy', ], 'CreateDate' => [ 'shape' => 'Date', ], 'Name' => [ 'shape' => 'DatasetName', ], 'Format' => [ 'shape' => 'InputFormat', ], 'FormatOptions' => [ 'shape' => 'FormatOptions', ], 'Input' => [ 'shape' => 'Input', ], 'LastModifiedDate' => [ 'shape' => 'Date', ], 'LastModifiedBy' => [ 'shape' => 'LastModifiedBy', ], 'Source' => [ 'shape' => 'Source', ], 'PathOptions' => [ 'shape' => 'PathOptions', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ResourceArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeJobRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'JobName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DescribeJobResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CreateDate' => [ 'shape' => 'Date', ], 'CreatedBy' => [ 'shape' => 'CreatedBy', ], 'DatasetName' => [ 'shape' => 'DatasetName', ], 'EncryptionKeyArn' => [ 'shape' => 'EncryptionKeyArn', ], 'EncryptionMode' => [ 'shape' => 'EncryptionMode', ], 'Name' => [ 'shape' => 'JobName', ], 'Type' => [ 'shape' => 'JobType', ], 'LastModifiedBy' => [ 'shape' => 'LastModifiedBy', ], 'LastModifiedDate' => [ 'shape' => 'Date', ], 'LogSubscription' => [ 'shape' => 'LogSubscription', ], 'MaxCapacity' => [ 'shape' => 'MaxCapacity', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'Outputs' => [ 'shape' => 'OutputList', ], 'DataCatalogOutputs' => [ 'shape' => 'DataCatalogOutputList', ], 'DatabaseOutputs' => [ 'shape' => 'DatabaseOutputList', ], 'ProjectName' => [ 'shape' => 'ProjectName', ], 'ProfileConfiguration' => [ 'shape' => 'ProfileConfiguration', ], 'ValidationConfigurations' => [ 'shape' => 'ValidationConfigurationList', ], 'RecipeReference' => [ 'shape' => 'RecipeReference', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagMap', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'JobSample' => [ 'shape' => 'JobSample', ], ], ], 'DescribeJobRunRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RunId', ], 'members' => [ 'Name' => [ 'shape' => 'JobName', 'location' => 'uri', 'locationName' => 'name', ], 'RunId' => [ 'shape' => 'JobRunId', 'location' => 'uri', 'locationName' => 'runId', ], ], ], 'DescribeJobRunResponse' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'Attempt' => [ 'shape' => 'Attempt', ], 'CompletedOn' => [ 'shape' => 'Date', ], 'DatasetName' => [ 'shape' => 'DatasetName', ], 'ErrorMessage' => [ 'shape' => 'JobRunErrorMessage', ], 'ExecutionTime' => [ 'shape' => 'ExecutionTime', ], 'JobName' => [ 'shape' => 'JobName', ], 'ProfileConfiguration' => [ 'shape' => 'ProfileConfiguration', ], 'ValidationConfigurations' => [ 'shape' => 'ValidationConfigurationList', ], 'RunId' => [ 'shape' => 'JobRunId', ], 'State' => [ 'shape' => 'JobRunState', ], 'LogSubscription' => [ 'shape' => 'LogSubscription', ], 'LogGroupName' => [ 'shape' => 'LogGroupName', ], 'Outputs' => [ 'shape' => 'OutputList', ], 'DataCatalogOutputs' => [ 'shape' => 'DataCatalogOutputList', ], 'DatabaseOutputs' => [ 'shape' => 'DatabaseOutputList', ], 'RecipeReference' => [ 'shape' => 'RecipeReference', ], 'StartedBy' => [ 'shape' => 'StartedBy', ], 'StartedOn' => [ 'shape' => 'Date', ], 'JobSample' => [ 'shape' => 'JobSample', ], ], ], 'DescribeProjectRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ProjectName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DescribeProjectResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CreateDate' => [ 'shape' => 'Date', ], 'CreatedBy' => [ 'shape' => 'CreatedBy', ], 'DatasetName' => [ 'shape' => 'DatasetName', ], 'LastModifiedDate' => [ 'shape' => 'Date', ], 'LastModifiedBy' => [ 'shape' => 'LastModifiedBy', ], 'Name' => [ 'shape' => 'ProjectName', ], 'RecipeName' => [ 'shape' => 'RecipeName', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'Sample' => [ 'shape' => 'Sample', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagMap', ], 'SessionStatus' => [ 'shape' => 'SessionStatus', ], 'OpenedBy' => [ 'shape' => 'OpenedBy', ], 'OpenDate' => [ 'shape' => 'Date', ], ], ], 'DescribeRecipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RecipeName', 'location' => 'uri', 'locationName' => 'name', ], 'RecipeVersion' => [ 'shape' => 'RecipeVersion', 'location' => 'querystring', 'locationName' => 'recipeVersion', ], ], ], 'DescribeRecipeResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CreatedBy' => [ 'shape' => 'CreatedBy', ], 'CreateDate' => [ 'shape' => 'Date', ], 'LastModifiedBy' => [ 'shape' => 'LastModifiedBy', ], 'LastModifiedDate' => [ 'shape' => 'Date', ], 'ProjectName' => [ 'shape' => 'ProjectName', ], 'PublishedBy' => [ 'shape' => 'PublishedBy', ], 'PublishedDate' => [ 'shape' => 'Date', ], 'Description' => [ 'shape' => 'RecipeDescription', ], 'Name' => [ 'shape' => 'RecipeName', ], 'Steps' => [ 'shape' => 'RecipeStepList', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'RecipeVersion' => [ 'shape' => 'RecipeVersion', ], ], ], 'DescribeRulesetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RulesetName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DescribeRulesetResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RulesetName', ], 'Description' => [ 'shape' => 'RulesetDescription', ], 'TargetArn' => [ 'shape' => 'Arn', ], 'Rules' => [ 'shape' => 'RuleList', ], 'CreateDate' => [ 'shape' => 'Date', ], 'CreatedBy' => [ 'shape' => 'CreatedBy', ], 'LastModifiedBy' => [ 'shape' => 'LastModifiedBy', ], 'LastModifiedDate' => [ 'shape' => 'Date', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ScheduleName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DescribeScheduleResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CreateDate' => [ 'shape' => 'Date', ], 'CreatedBy' => [ 'shape' => 'CreatedBy', ], 'JobNames' => [ 'shape' => 'JobNameList', ], 'LastModifiedBy' => [ 'shape' => 'LastModifiedBy', ], 'LastModifiedDate' => [ 'shape' => 'Date', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'CronExpression' => [ 'shape' => 'CronExpression', ], 'Tags' => [ 'shape' => 'TagMap', ], 'Name' => [ 'shape' => 'ScheduleName', ], ], ], 'Disabled' => [ 'type' => 'boolean', ], 'EncryptionKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'EncryptionMode' => [ 'type' => 'string', 'enum' => [ 'SSE-KMS', 'SSE-S3', ], ], 'EntityDetectorConfiguration' => [ 'type' => 'structure', 'required' => [ 'EntityTypes', ], 'members' => [ 'EntityTypes' => [ 'shape' => 'EntityTypeList', ], 'AllowedStatistics' => [ 'shape' => 'AllowedStatisticList', ], ], ], 'EntityType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Z_][A-Z\\\\d_]*$', ], 'EntityTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityType', ], 'min' => 1, ], 'ErrorCode' => [ 'type' => 'string', 'pattern' => '^[1-5][0-9][0-9]$', ], 'ExcelOptions' => [ 'type' => 'structure', 'members' => [ 'SheetNames' => [ 'shape' => 'SheetNameList', ], 'SheetIndexes' => [ 'shape' => 'SheetIndexList', ], 'HeaderRow' => [ 'shape' => 'HeaderRow', ], ], ], 'ExecutionTime' => [ 'type' => 'integer', ], 'Expression' => [ 'type' => 'string', 'max' => 1024, 'min' => 4, 'pattern' => '^[<>0-9A-Za-z_.,:)(!= ]+$', ], 'FilesLimit' => [ 'type' => 'structure', 'required' => [ 'MaxFiles', ], 'members' => [ 'MaxFiles' => [ 'shape' => 'MaxFiles', ], 'OrderedBy' => [ 'shape' => 'OrderedBy', ], 'Order' => [ 'shape' => 'Order', ], ], ], 'FilterExpression' => [ 'type' => 'structure', 'required' => [ 'Expression', 'ValuesMap', ], 'members' => [ 'Expression' => [ 'shape' => 'Expression', ], 'ValuesMap' => [ 'shape' => 'ValuesMap', ], ], ], 'FormatOptions' => [ 'type' => 'structure', 'members' => [ 'Json' => [ 'shape' => 'JsonOptions', ], 'Excel' => [ 'shape' => 'ExcelOptions', ], 'Csv' => [ 'shape' => 'CsvOptions', ], ], ], 'GlueConnectionName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'HeaderRow' => [ 'type' => 'boolean', ], 'HiddenColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnName', ], ], 'Input' => [ 'type' => 'structure', 'members' => [ 'S3InputDefinition' => [ 'shape' => 'S3Location', ], 'DataCatalogInputDefinition' => [ 'shape' => 'DataCatalogInputDefinition', ], 'DatabaseInputDefinition' => [ 'shape' => 'DatabaseInputDefinition', ], 'Metadata' => [ 'shape' => 'Metadata', ], ], ], 'InputFormat' => [ 'type' => 'string', 'enum' => [ 'CSV', 'JSON', 'PARQUET', 'EXCEL', 'ORC', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'Job' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'CreatedBy' => [ 'shape' => 'CreatedBy', ], 'CreateDate' => [ 'shape' => 'Date', ], 'DatasetName' => [ 'shape' => 'DatasetName', ], 'EncryptionKeyArn' => [ 'shape' => 'EncryptionKeyArn', ], 'EncryptionMode' => [ 'shape' => 'EncryptionMode', ], 'Name' => [ 'shape' => 'JobName', ], 'Type' => [ 'shape' => 'JobType', ], 'LastModifiedBy' => [ 'shape' => 'LastModifiedBy', ], 'LastModifiedDate' => [ 'shape' => 'Date', ], 'LogSubscription' => [ 'shape' => 'LogSubscription', ], 'MaxCapacity' => [ 'shape' => 'MaxCapacity', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'Outputs' => [ 'shape' => 'OutputList', ], 'DataCatalogOutputs' => [ 'shape' => 'DataCatalogOutputList', ], 'DatabaseOutputs' => [ 'shape' => 'DatabaseOutputList', ], 'ProjectName' => [ 'shape' => 'ProjectName', ], 'RecipeReference' => [ 'shape' => 'RecipeReference', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'Tags' => [ 'shape' => 'TagMap', ], 'JobSample' => [ 'shape' => 'JobSample', ], 'ValidationConfigurations' => [ 'shape' => 'ValidationConfigurationList', ], ], ], 'JobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Job', ], ], 'JobName' => [ 'type' => 'string', 'max' => 240, 'min' => 1, ], 'JobNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobName', ], 'max' => 50, ], 'JobRun' => [ 'type' => 'structure', 'members' => [ 'Attempt' => [ 'shape' => 'Attempt', ], 'CompletedOn' => [ 'shape' => 'Date', ], 'DatasetName' => [ 'shape' => 'DatasetName', ], 'ErrorMessage' => [ 'shape' => 'JobRunErrorMessage', ], 'ExecutionTime' => [ 'shape' => 'ExecutionTime', ], 'JobName' => [ 'shape' => 'JobName', ], 'RunId' => [ 'shape' => 'JobRunId', ], 'State' => [ 'shape' => 'JobRunState', ], 'LogSubscription' => [ 'shape' => 'LogSubscription', ], 'LogGroupName' => [ 'shape' => 'LogGroupName', ], 'Outputs' => [ 'shape' => 'OutputList', ], 'DataCatalogOutputs' => [ 'shape' => 'DataCatalogOutputList', ], 'DatabaseOutputs' => [ 'shape' => 'DatabaseOutputList', ], 'RecipeReference' => [ 'shape' => 'RecipeReference', ], 'StartedBy' => [ 'shape' => 'StartedBy', ], 'StartedOn' => [ 'shape' => 'Date', ], 'JobSample' => [ 'shape' => 'JobSample', ], 'ValidationConfigurations' => [ 'shape' => 'ValidationConfigurationList', ], ], ], 'JobRunErrorMessage' => [ 'type' => 'string', ], 'JobRunId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'JobRunList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobRun', ], ], 'JobRunState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'RUNNING', 'STOPPING', 'STOPPED', 'SUCCEEDED', 'FAILED', 'TIMEOUT', ], ], 'JobSample' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'SampleMode', ], 'Size' => [ 'shape' => 'JobSize', ], ], ], 'JobSize' => [ 'type' => 'long', ], 'JobType' => [ 'type' => 'string', 'enum' => [ 'PROFILE', 'RECIPE', ], ], 'JsonOptions' => [ 'type' => 'structure', 'members' => [ 'MultiLine' => [ 'shape' => 'MultiLine', ], ], ], 'Key' => [ 'type' => 'string', 'max' => 1280, 'min' => 1, ], 'LastModifiedBy' => [ 'type' => 'string', ], 'ListDatasetsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults100', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDatasetsResponse' => [ 'type' => 'structure', 'required' => [ 'Datasets', ], 'members' => [ 'Datasets' => [ 'shape' => 'DatasetList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListJobRunsRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'JobName', 'location' => 'uri', 'locationName' => 'name', ], 'MaxResults' => [ 'shape' => 'MaxResults100', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListJobRunsResponse' => [ 'type' => 'structure', 'required' => [ 'JobRuns', ], 'members' => [ 'JobRuns' => [ 'shape' => 'JobRunList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListJobsRequest' => [ 'type' => 'structure', 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetName', 'location' => 'querystring', 'locationName' => 'datasetName', ], 'MaxResults' => [ 'shape' => 'MaxResults100', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'ProjectName' => [ 'shape' => 'ProjectName', 'location' => 'querystring', 'locationName' => 'projectName', ], ], ], 'ListJobsResponse' => [ 'type' => 'structure', 'required' => [ 'Jobs', ], 'members' => [ 'Jobs' => [ 'shape' => 'JobList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListProjectsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults100', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListProjectsResponse' => [ 'type' => 'structure', 'required' => [ 'Projects', ], 'members' => [ 'Projects' => [ 'shape' => 'ProjectList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRecipeVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults100', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'Name' => [ 'shape' => 'RecipeName', 'location' => 'querystring', 'locationName' => 'name', ], ], ], 'ListRecipeVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'Recipes', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Recipes' => [ 'shape' => 'RecipeList', ], ], ], 'ListRecipesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults100', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'RecipeVersion' => [ 'shape' => 'RecipeVersion', 'location' => 'querystring', 'locationName' => 'recipeVersion', ], ], ], 'ListRecipesResponse' => [ 'type' => 'structure', 'required' => [ 'Recipes', ], 'members' => [ 'Recipes' => [ 'shape' => 'RecipeList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRulesetsRequest' => [ 'type' => 'structure', 'members' => [ 'TargetArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'targetArn', ], 'MaxResults' => [ 'shape' => 'MaxResults100', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListRulesetsResponse' => [ 'type' => 'structure', 'required' => [ 'Rulesets', ], 'members' => [ 'Rulesets' => [ 'shape' => 'RulesetItemList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSchedulesRequest' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', 'location' => 'querystring', 'locationName' => 'jobName', ], 'MaxResults' => [ 'shape' => 'MaxResults100', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSchedulesResponse' => [ 'type' => 'structure', 'required' => [ 'Schedules', ], 'members' => [ 'Schedules' => [ 'shape' => 'ScheduleList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'LocaleCode' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '^[A-Za-z0-9_\\.#@\\-]+$', ], 'LogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'LogSubscription' => [ 'type' => 'string', 'enum' => [ 'ENABLE', 'DISABLE', ], ], 'MaxCapacity' => [ 'type' => 'integer', ], 'MaxFiles' => [ 'type' => 'integer', 'min' => 1, ], 'MaxOutputFiles' => [ 'type' => 'integer', 'max' => 999, 'min' => 1, ], 'MaxResults100' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxRetries' => [ 'type' => 'integer', 'min' => 0, ], 'Message' => [ 'type' => 'string', ], 'Metadata' => [ 'type' => 'structure', 'members' => [ 'SourceArn' => [ 'shape' => 'Arn', ], ], ], 'MultiLine' => [ 'type' => 'boolean', ], 'NextToken' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'OpenedBy' => [ 'type' => 'string', ], 'Operation' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Z\\_]+$', ], 'Order' => [ 'type' => 'string', 'enum' => [ 'DESCENDING', 'ASCENDING', ], ], 'OrderedBy' => [ 'type' => 'string', 'enum' => [ 'LAST_MODIFIED_DATE', ], ], 'Output' => [ 'type' => 'structure', 'required' => [ 'Location', ], 'members' => [ 'CompressionFormat' => [ 'shape' => 'CompressionFormat', ], 'Format' => [ 'shape' => 'OutputFormat', ], 'PartitionColumns' => [ 'shape' => 'ColumnNameList', ], 'Location' => [ 'shape' => 'S3Location', ], 'Overwrite' => [ 'shape' => 'OverwriteOutput', ], 'FormatOptions' => [ 'shape' => 'OutputFormatOptions', ], 'MaxOutputFiles' => [ 'shape' => 'MaxOutputFiles', ], ], ], 'OutputFormat' => [ 'type' => 'string', 'enum' => [ 'CSV', 'JSON', 'PARQUET', 'GLUEPARQUET', 'AVRO', 'ORC', 'XML', 'TABLEAUHYPER', ], ], 'OutputFormatOptions' => [ 'type' => 'structure', 'members' => [ 'Csv' => [ 'shape' => 'CsvOutputOptions', ], ], ], 'OutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Output', ], 'min' => 1, ], 'OverwriteOutput' => [ 'type' => 'boolean', ], 'ParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValue', ], ], 'ParameterName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Za-z0-9]+$', ], 'ParameterType' => [ 'type' => 'string', 'enum' => [ 'Datetime', 'Number', 'String', ], ], 'ParameterValue' => [ 'type' => 'string', 'max' => 32768, 'min' => 1, ], 'PathOptions' => [ 'type' => 'structure', 'members' => [ 'LastModifiedDateCondition' => [ 'shape' => 'FilterExpression', ], 'FilesLimit' => [ 'shape' => 'FilesLimit', ], 'Parameters' => [ 'shape' => 'PathParametersMap', ], ], ], 'PathParameterName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'PathParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PathParameterName', ], 'value' => [ 'shape' => 'DatasetParameter', ], 'max' => 10, 'min' => 1, ], 'Preview' => [ 'type' => 'boolean', ], 'ProfileConfiguration' => [ 'type' => 'structure', 'members' => [ 'DatasetStatisticsConfiguration' => [ 'shape' => 'StatisticsConfiguration', ], 'ProfileColumns' => [ 'shape' => 'ColumnSelectorList', ], 'ColumnStatisticsConfigurations' => [ 'shape' => 'ColumnStatisticsConfigurationList', ], 'EntityDetectorConfiguration' => [ 'shape' => 'EntityDetectorConfiguration', ], ], ], 'Project' => [ 'type' => 'structure', 'required' => [ 'Name', 'RecipeName', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'CreateDate' => [ 'shape' => 'Date', ], 'CreatedBy' => [ 'shape' => 'CreatedBy', ], 'DatasetName' => [ 'shape' => 'DatasetName', ], 'LastModifiedDate' => [ 'shape' => 'Date', ], 'LastModifiedBy' => [ 'shape' => 'LastModifiedBy', ], 'Name' => [ 'shape' => 'ProjectName', ], 'RecipeName' => [ 'shape' => 'RecipeName', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'Sample' => [ 'shape' => 'Sample', ], 'Tags' => [ 'shape' => 'TagMap', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'OpenedBy' => [ 'shape' => 'OpenedBy', ], 'OpenDate' => [ 'shape' => 'Date', ], ], ], 'ProjectList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Project', ], ], 'ProjectName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'PublishRecipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Description' => [ 'shape' => 'RecipeDescription', ], 'Name' => [ 'shape' => 'RecipeName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'PublishRecipeResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RecipeName', ], ], ], 'PublishedBy' => [ 'type' => 'string', ], 'QueryString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'Recipe' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CreatedBy' => [ 'shape' => 'CreatedBy', ], 'CreateDate' => [ 'shape' => 'Date', ], 'LastModifiedBy' => [ 'shape' => 'LastModifiedBy', ], 'LastModifiedDate' => [ 'shape' => 'Date', ], 'ProjectName' => [ 'shape' => 'ProjectName', ], 'PublishedBy' => [ 'shape' => 'PublishedBy', ], 'PublishedDate' => [ 'shape' => 'Date', ], 'Description' => [ 'shape' => 'RecipeDescription', ], 'Name' => [ 'shape' => 'RecipeName', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'Steps' => [ 'shape' => 'RecipeStepList', ], 'Tags' => [ 'shape' => 'TagMap', ], 'RecipeVersion' => [ 'shape' => 'RecipeVersion', ], ], ], 'RecipeAction' => [ 'type' => 'structure', 'required' => [ 'Operation', ], 'members' => [ 'Operation' => [ 'shape' => 'Operation', ], 'Parameters' => [ 'shape' => 'ParameterMap', ], ], ], 'RecipeDescription' => [ 'type' => 'string', 'max' => 1024, ], 'RecipeErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecipeVersionErrorDetail', ], ], 'RecipeErrorMessage' => [ 'type' => 'string', ], 'RecipeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Recipe', ], ], 'RecipeName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'RecipeReference' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RecipeName', ], 'RecipeVersion' => [ 'shape' => 'RecipeVersion', ], ], ], 'RecipeStep' => [ 'type' => 'structure', 'required' => [ 'Action', ], 'members' => [ 'Action' => [ 'shape' => 'RecipeAction', ], 'ConditionExpressions' => [ 'shape' => 'ConditionExpressionList', ], ], ], 'RecipeStepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecipeStep', ], ], 'RecipeVersion' => [ 'type' => 'string', 'max' => 16, 'min' => 1, ], 'RecipeVersionErrorDetail' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'RecipeErrorMessage', ], 'RecipeVersion' => [ 'shape' => 'RecipeVersion', ], ], ], 'RecipeVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecipeVersion', ], 'max' => 50, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'Result' => [ 'type' => 'string', ], 'RowRange' => [ 'type' => 'integer', ], 'Rule' => [ 'type' => 'structure', 'required' => [ 'Name', 'CheckExpression', ], 'members' => [ 'Name' => [ 'shape' => 'RuleName', ], 'Disabled' => [ 'shape' => 'Disabled', ], 'CheckExpression' => [ 'shape' => 'Expression', ], 'SubstitutionMap' => [ 'shape' => 'ValuesMap', ], 'Threshold' => [ 'shape' => 'Threshold', ], 'ColumnSelectors' => [ 'shape' => 'ColumnSelectorList', ], ], ], 'RuleCount' => [ 'type' => 'integer', 'min' => 0, ], 'RuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], 'min' => 1, ], 'RuleName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'RulesetDescription' => [ 'type' => 'string', 'max' => 1024, ], 'RulesetItem' => [ 'type' => 'structure', 'required' => [ 'Name', 'TargetArn', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'CreatedBy' => [ 'shape' => 'CreatedBy', ], 'CreateDate' => [ 'shape' => 'Date', ], 'Description' => [ 'shape' => 'RulesetDescription', ], 'LastModifiedBy' => [ 'shape' => 'LastModifiedBy', ], 'LastModifiedDate' => [ 'shape' => 'Date', ], 'Name' => [ 'shape' => 'RulesetName', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'RuleCount' => [ 'shape' => 'RuleCount', ], 'Tags' => [ 'shape' => 'TagMap', ], 'TargetArn' => [ 'shape' => 'Arn', ], ], ], 'RulesetItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RulesetItem', ], ], 'RulesetName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'S3Location' => [ 'type' => 'structure', 'required' => [ 'Bucket', ], 'members' => [ 'Bucket' => [ 'shape' => 'Bucket', ], 'Key' => [ 'shape' => 'Key', ], 'BucketOwner' => [ 'shape' => 'BucketOwner', ], ], ], 'S3TableOutputOptions' => [ 'type' => 'structure', 'required' => [ 'Location', ], 'members' => [ 'Location' => [ 'shape' => 'S3Location', ], ], ], 'Sample' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Size' => [ 'shape' => 'SampleSize', ], 'Type' => [ 'shape' => 'SampleType', ], ], ], 'SampleMode' => [ 'type' => 'string', 'enum' => [ 'FULL_DATASET', 'CUSTOM_ROWS', ], ], 'SampleSize' => [ 'type' => 'integer', 'max' => 5000, 'min' => 1, ], 'SampleType' => [ 'type' => 'string', 'enum' => [ 'FIRST_N', 'LAST_N', 'RANDOM', ], ], 'Schedule' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'CreatedBy' => [ 'shape' => 'CreatedBy', ], 'CreateDate' => [ 'shape' => 'Date', ], 'JobNames' => [ 'shape' => 'JobNameList', ], 'LastModifiedBy' => [ 'shape' => 'LastModifiedBy', ], 'LastModifiedDate' => [ 'shape' => 'Date', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'CronExpression' => [ 'shape' => 'CronExpression', ], 'Tags' => [ 'shape' => 'TagMap', ], 'Name' => [ 'shape' => 'ScheduleName', ], ], ], 'ScheduleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Schedule', ], ], 'ScheduleName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'SendProjectSessionActionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Preview' => [ 'shape' => 'Preview', ], 'Name' => [ 'shape' => 'ProjectName', 'location' => 'uri', 'locationName' => 'name', ], 'RecipeStep' => [ 'shape' => 'RecipeStep', ], 'StepIndex' => [ 'shape' => 'StepIndex', ], 'ClientSessionId' => [ 'shape' => 'ClientSessionId', ], 'ViewFrame' => [ 'shape' => 'ViewFrame', ], ], ], 'SendProjectSessionActionResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Result' => [ 'shape' => 'Result', ], 'Name' => [ 'shape' => 'ProjectName', ], 'ActionId' => [ 'shape' => 'ActionId', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'SessionStatus' => [ 'type' => 'string', 'enum' => [ 'ASSIGNED', 'FAILED', 'INITIALIZING', 'PROVISIONING', 'READY', 'RECYCLING', 'ROTATING', 'TERMINATED', 'TERMINATING', 'UPDATING', ], ], 'SheetIndex' => [ 'type' => 'integer', 'max' => 200, 'min' => 0, ], 'SheetIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SheetIndex', ], 'max' => 1, 'min' => 1, ], 'SheetName' => [ 'type' => 'string', 'max' => 31, 'min' => 1, ], 'SheetNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SheetName', ], 'max' => 1, 'min' => 1, ], 'Source' => [ 'type' => 'string', 'enum' => [ 'S3', 'DATA-CATALOG', 'DATABASE', ], ], 'StartColumnIndex' => [ 'type' => 'integer', 'min' => 0, ], 'StartJobRunRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'JobName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'StartJobRunResponse' => [ 'type' => 'structure', 'required' => [ 'RunId', ], 'members' => [ 'RunId' => [ 'shape' => 'JobRunId', ], ], ], 'StartProjectSessionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ProjectName', 'location' => 'uri', 'locationName' => 'name', ], 'AssumeControl' => [ 'shape' => 'AssumeControl', ], ], ], 'StartProjectSessionResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ProjectName', ], 'ClientSessionId' => [ 'shape' => 'ClientSessionId', ], ], ], 'StartRowIndex' => [ 'type' => 'integer', 'min' => 0, ], 'StartedBy' => [ 'type' => 'string', ], 'Statistic' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Z\\_]+$', ], 'StatisticList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Statistic', ], 'min' => 1, ], 'StatisticOverride' => [ 'type' => 'structure', 'required' => [ 'Statistic', 'Parameters', ], 'members' => [ 'Statistic' => [ 'shape' => 'Statistic', ], 'Parameters' => [ 'shape' => 'ParameterMap', ], ], ], 'StatisticOverrideList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatisticOverride', ], 'min' => 1, ], 'StatisticsConfiguration' => [ 'type' => 'structure', 'members' => [ 'IncludedStatistics' => [ 'shape' => 'StatisticList', ], 'Overrides' => [ 'shape' => 'StatisticOverrideList', ], ], ], 'StepIndex' => [ 'type' => 'integer', 'min' => 0, ], 'StopJobRunRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RunId', ], 'members' => [ 'Name' => [ 'shape' => 'JobName', 'location' => 'uri', 'locationName' => 'name', ], 'RunId' => [ 'shape' => 'JobRunId', 'location' => 'uri', 'locationName' => 'runId', ], ], ], 'StopJobRunResponse' => [ 'type' => 'structure', 'required' => [ 'RunId', ], 'members' => [ 'RunId' => [ 'shape' => 'JobRunId', ], ], ], 'TableName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'TargetColumn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Threshold' => [ 'type' => 'structure', 'required' => [ 'Value', ], 'members' => [ 'Value' => [ 'shape' => 'ThresholdValue', ], 'Type' => [ 'shape' => 'ThresholdType', ], 'Unit' => [ 'shape' => 'ThresholdUnit', ], ], ], 'ThresholdType' => [ 'type' => 'string', 'enum' => [ 'GREATER_THAN_OR_EQUAL', 'LESS_THAN_OR_EQUAL', 'GREATER_THAN', 'LESS_THAN', ], ], 'ThresholdUnit' => [ 'type' => 'string', 'enum' => [ 'COUNT', 'PERCENTAGE', ], ], 'ThresholdValue' => [ 'type' => 'double', 'min' => 0, ], 'Timeout' => [ 'type' => 'integer', 'min' => 0, ], 'TimezoneOffset' => [ 'type' => 'string', 'max' => 6, 'min' => 1, 'pattern' => '^(Z|[-+](\\d|\\d{2}|\\d{2}:?\\d{2}))$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Input', ], 'members' => [ 'Name' => [ 'shape' => 'DatasetName', 'location' => 'uri', 'locationName' => 'name', ], 'Format' => [ 'shape' => 'InputFormat', ], 'FormatOptions' => [ 'shape' => 'FormatOptions', ], 'Input' => [ 'shape' => 'Input', ], 'PathOptions' => [ 'shape' => 'PathOptions', ], ], ], 'UpdateDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DatasetName', ], ], ], 'UpdateProfileJobRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'OutputLocation', 'RoleArn', ], 'members' => [ 'Configuration' => [ 'shape' => 'ProfileConfiguration', ], 'EncryptionKeyArn' => [ 'shape' => 'EncryptionKeyArn', ], 'EncryptionMode' => [ 'shape' => 'EncryptionMode', ], 'Name' => [ 'shape' => 'JobName', 'location' => 'uri', 'locationName' => 'name', ], 'LogSubscription' => [ 'shape' => 'LogSubscription', ], 'MaxCapacity' => [ 'shape' => 'MaxCapacity', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'OutputLocation' => [ 'shape' => 'S3Location', ], 'ValidationConfigurations' => [ 'shape' => 'ValidationConfigurationList', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'JobSample' => [ 'shape' => 'JobSample', ], ], ], 'UpdateProfileJobResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'JobName', ], ], ], 'UpdateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'RoleArn', 'Name', ], 'members' => [ 'Sample' => [ 'shape' => 'Sample', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'ProjectName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'UpdateProjectResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'LastModifiedDate' => [ 'shape' => 'Date', ], 'Name' => [ 'shape' => 'ProjectName', ], ], ], 'UpdateRecipeJobRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RoleArn', ], 'members' => [ 'EncryptionKeyArn' => [ 'shape' => 'EncryptionKeyArn', ], 'EncryptionMode' => [ 'shape' => 'EncryptionMode', ], 'Name' => [ 'shape' => 'JobName', 'location' => 'uri', 'locationName' => 'name', ], 'LogSubscription' => [ 'shape' => 'LogSubscription', ], 'MaxCapacity' => [ 'shape' => 'MaxCapacity', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'Outputs' => [ 'shape' => 'OutputList', ], 'DataCatalogOutputs' => [ 'shape' => 'DataCatalogOutputList', ], 'DatabaseOutputs' => [ 'shape' => 'DatabaseOutputList', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'Timeout' => [ 'shape' => 'Timeout', ], ], ], 'UpdateRecipeJobResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'JobName', ], ], ], 'UpdateRecipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Description' => [ 'shape' => 'RecipeDescription', ], 'Name' => [ 'shape' => 'RecipeName', 'location' => 'uri', 'locationName' => 'name', ], 'Steps' => [ 'shape' => 'RecipeStepList', ], ], ], 'UpdateRecipeResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RecipeName', ], ], ], 'UpdateRulesetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Rules', ], 'members' => [ 'Name' => [ 'shape' => 'RulesetName', 'location' => 'uri', 'locationName' => 'name', ], 'Description' => [ 'shape' => 'RulesetDescription', ], 'Rules' => [ 'shape' => 'RuleList', ], ], ], 'UpdateRulesetResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RulesetName', ], ], ], 'UpdateScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'CronExpression', 'Name', ], 'members' => [ 'JobNames' => [ 'shape' => 'JobNameList', ], 'CronExpression' => [ 'shape' => 'CronExpression', ], 'Name' => [ 'shape' => 'ScheduleName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'UpdateScheduleResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ScheduleName', ], ], ], 'ValidationConfiguration' => [ 'type' => 'structure', 'required' => [ 'RulesetArn', ], 'members' => [ 'RulesetArn' => [ 'shape' => 'Arn', ], 'ValidationMode' => [ 'shape' => 'ValidationMode', ], ], ], 'ValidationConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationConfiguration', ], 'min' => 1, ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ValidationMode' => [ 'type' => 'string', 'enum' => [ 'CHECK_ALL', ], ], 'ValueReference' => [ 'type' => 'string', 'max' => 128, 'min' => 2, 'pattern' => '^:[A-Za-z0-9_]+$', ], 'ValuesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ValueReference', ], 'value' => [ 'shape' => 'ConditionValue', ], ], 'ViewFrame' => [ 'type' => 'structure', 'required' => [ 'StartColumnIndex', ], 'members' => [ 'StartColumnIndex' => [ 'shape' => 'StartColumnIndex', ], 'ColumnRange' => [ 'shape' => 'ColumnRange', ], 'HiddenColumns' => [ 'shape' => 'HiddenColumnList', ], 'StartRowIndex' => [ 'shape' => 'StartRowIndex', ], 'RowRange' => [ 'shape' => 'RowRange', ], 'Analytics' => [ 'shape' => 'AnalyticsMode', ], ], ], ],];
