<?php
// This file was auto-generated from sdk-root/src/data/entityresolution/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'endpointPrefix' => 'entityresolution', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'AWSEntityResolution', 'serviceFullName' => 'AWS EntityResolution', 'serviceId' => 'EntityResolution', 'signatureVersion' => 'v4', 'signingName' => 'entityresolution', 'uid' => 'entityresolution-2018-05-10', ], 'operations' => [ 'CreateIdMappingWorkflow' => [ 'name' => 'CreateIdMappingWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/idmappingworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIdMappingWorkflowInput', ], 'output' => [ 'shape' => 'CreateIdMappingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateMatchingWorkflow' => [ 'name' => 'CreateMatchingWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/matchingworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMatchingWorkflowInput', ], 'output' => [ 'shape' => 'CreateMatchingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateSchemaMapping' => [ 'name' => 'CreateSchemaMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/schemas', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSchemaMappingInput', ], 'output' => [ 'shape' => 'CreateSchemaMappingOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteIdMappingWorkflow' => [ 'name' => 'DeleteIdMappingWorkflow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/idmappingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteIdMappingWorkflowInput', ], 'output' => [ 'shape' => 'DeleteIdMappingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteMatchingWorkflow' => [ 'name' => 'DeleteMatchingWorkflow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/matchingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMatchingWorkflowInput', ], 'output' => [ 'shape' => 'DeleteMatchingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteSchemaMapping' => [ 'name' => 'DeleteSchemaMapping', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/schemas/{schemaName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSchemaMappingInput', ], 'output' => [ 'shape' => 'DeleteSchemaMappingOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'GetIdMappingJob' => [ 'name' => 'GetIdMappingJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/idmappingworkflows/{workflowName}/jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIdMappingJobInput', ], 'output' => [ 'shape' => 'GetIdMappingJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetIdMappingWorkflow' => [ 'name' => 'GetIdMappingWorkflow', 'http' => [ 'method' => 'GET', 'requestUri' => '/idmappingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIdMappingWorkflowInput', ], 'output' => [ 'shape' => 'GetIdMappingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetMatchId' => [ 'name' => 'GetMatchId', 'http' => [ 'method' => 'POST', 'requestUri' => '/matchingworkflows/{workflowName}/matches', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMatchIdInput', ], 'output' => [ 'shape' => 'GetMatchIdOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetMatchingJob' => [ 'name' => 'GetMatchingJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/matchingworkflows/{workflowName}/jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMatchingJobInput', ], 'output' => [ 'shape' => 'GetMatchingJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetMatchingWorkflow' => [ 'name' => 'GetMatchingWorkflow', 'http' => [ 'method' => 'GET', 'requestUri' => '/matchingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMatchingWorkflowInput', ], 'output' => [ 'shape' => 'GetMatchingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetProviderService' => [ 'name' => 'GetProviderService', 'http' => [ 'method' => 'GET', 'requestUri' => '/providerservices/{providerName}/{providerServiceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProviderServiceInput', ], 'output' => [ 'shape' => 'GetProviderServiceOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetSchemaMapping' => [ 'name' => 'GetSchemaMapping', 'http' => [ 'method' => 'GET', 'requestUri' => '/schemas/{schemaName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSchemaMappingInput', ], 'output' => [ 'shape' => 'GetSchemaMappingOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListIdMappingJobs' => [ 'name' => 'ListIdMappingJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/idmappingworkflows/{workflowName}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdMappingJobsInput', ], 'output' => [ 'shape' => 'ListIdMappingJobsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListIdMappingWorkflows' => [ 'name' => 'ListIdMappingWorkflows', 'http' => [ 'method' => 'GET', 'requestUri' => '/idmappingworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdMappingWorkflowsInput', ], 'output' => [ 'shape' => 'ListIdMappingWorkflowsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListMatchingJobs' => [ 'name' => 'ListMatchingJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/matchingworkflows/{workflowName}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMatchingJobsInput', ], 'output' => [ 'shape' => 'ListMatchingJobsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListMatchingWorkflows' => [ 'name' => 'ListMatchingWorkflows', 'http' => [ 'method' => 'GET', 'requestUri' => '/matchingworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMatchingWorkflowsInput', ], 'output' => [ 'shape' => 'ListMatchingWorkflowsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListProviderServices' => [ 'name' => 'ListProviderServices', 'http' => [ 'method' => 'GET', 'requestUri' => '/providerservices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProviderServicesInput', ], 'output' => [ 'shape' => 'ListProviderServicesOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListSchemaMappings' => [ 'name' => 'ListSchemaMappings', 'http' => [ 'method' => 'GET', 'requestUri' => '/schemas', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSchemaMappingsInput', ], 'output' => [ 'shape' => 'ListSchemaMappingsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartIdMappingJob' => [ 'name' => 'StartIdMappingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/idmappingworkflows/{workflowName}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartIdMappingJobInput', ], 'output' => [ 'shape' => 'StartIdMappingJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartMatchingJob' => [ 'name' => 'StartMatchingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/matchingworkflows/{workflowName}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartMatchingJobInput', ], 'output' => [ 'shape' => 'StartMatchingJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateIdMappingWorkflow' => [ 'name' => 'UpdateIdMappingWorkflow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/idmappingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIdMappingWorkflowInput', ], 'output' => [ 'shape' => 'UpdateIdMappingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateMatchingWorkflow' => [ 'name' => 'UpdateMatchingWorkflow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/matchingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMatchingWorkflowInput', ], 'output' => [ 'shape' => 'UpdateMatchingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateSchemaMapping' => [ 'name' => 'UpdateSchemaMapping', 'http' => [ 'method' => 'PUT', 'requestUri' => '/schemas/{schemaName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSchemaMappingInput', ], 'output' => [ 'shape' => 'UpdateSchemaMappingOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AttributeMatchingModel' => [ 'type' => 'string', 'enum' => [ 'ONE_TO_ONE', 'MANY_TO_MANY', ], ], 'AttributeName' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[a-zA-Z_0-9- \\t]*$', ], 'AwsAccountId' => [ 'type' => 'string', 'pattern' => '\\d{12}', ], 'AwsAccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAccountId', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CreateIdMappingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'idMappingTechniques', 'inputSourceConfig', 'outputSourceConfig', 'roleArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'CreateIdMappingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'idMappingTechniques', 'inputSourceConfig', 'outputSourceConfig', 'roleArn', 'workflowArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'workflowArn' => [ 'shape' => 'IdMappingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'CreateMatchingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'roleArn' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'CreateMatchingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', 'workflowArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'roleArn' => [ 'shape' => 'String', ], 'workflowArn' => [ 'shape' => 'MatchingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'CreateSchemaMappingInput' => [ 'type' => 'structure', 'required' => [ 'mappedInputFields', 'schemaName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], 'schemaName' => [ 'shape' => 'EntityName', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateSchemaMappingOutput' => [ 'type' => 'structure', 'required' => [ 'description', 'mappedInputFields', 'schemaArn', 'schemaName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], 'schemaArn' => [ 'shape' => 'SchemaMappingArn', ], 'schemaName' => [ 'shape' => 'EntityName', ], ], ], 'DeleteIdMappingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'DeleteIdMappingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'DeleteMatchingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'DeleteMatchingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'DeleteSchemaMappingInput' => [ 'type' => 'structure', 'required' => [ 'schemaName', ], 'members' => [ 'schemaName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'schemaName', ], ], ], 'DeleteSchemaMappingOutput' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'EntityName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z_0-9-]*$', ], 'ErrorDetails' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ExceedsLimitException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'quotaName' => [ 'shape' => 'String', ], 'quotaValue' => [ 'shape' => 'Integer', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'GetIdMappingJobInput' => [ 'type' => 'structure', 'required' => [ 'jobId', 'workflowName', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'GetIdMappingJobOutput' => [ 'type' => 'structure', 'required' => [ 'jobId', 'startTime', 'status', ], 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'errorDetails' => [ 'shape' => 'ErrorDetails', ], 'jobId' => [ 'shape' => 'JobId', ], 'metrics' => [ 'shape' => 'IdMappingJobMetrics', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'JobStatus', ], ], ], 'GetIdMappingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'GetIdMappingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'idMappingTechniques', 'inputSourceConfig', 'outputSourceConfig', 'roleArn', 'updatedAt', 'workflowArn', 'workflowName', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'workflowArn' => [ 'shape' => 'IdMappingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'GetMatchIdInput' => [ 'type' => 'structure', 'required' => [ 'record', 'workflowName', ], 'members' => [ 'record' => [ 'shape' => 'RecordAttributeMap', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'GetMatchIdOutput' => [ 'type' => 'structure', 'members' => [ 'matchId' => [ 'shape' => 'String', ], ], ], 'GetMatchingJobInput' => [ 'type' => 'structure', 'required' => [ 'jobId', 'workflowName', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'GetMatchingJobOutput' => [ 'type' => 'structure', 'required' => [ 'jobId', 'startTime', 'status', ], 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'errorDetails' => [ 'shape' => 'ErrorDetails', ], 'jobId' => [ 'shape' => 'JobId', ], 'metrics' => [ 'shape' => 'JobMetrics', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'JobStatus', ], ], ], 'GetMatchingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'GetMatchingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', 'updatedAt', 'workflowArn', 'workflowName', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'roleArn' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'workflowArn' => [ 'shape' => 'MatchingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'GetProviderServiceInput' => [ 'type' => 'structure', 'required' => [ 'providerName', 'providerServiceName', ], 'members' => [ 'providerName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'providerName', ], 'providerServiceName' => [ 'shape' => 'ProviderServiceArn', 'location' => 'uri', 'locationName' => 'providerServiceName', ], ], ], 'GetProviderServiceOutput' => [ 'type' => 'structure', 'required' => [ 'anonymizedOutput', 'providerEndpointConfiguration', 'providerEntityOutputDefinition', 'providerName', 'providerServiceArn', 'providerServiceDisplayName', 'providerServiceName', 'providerServiceType', ], 'members' => [ 'anonymizedOutput' => [ 'shape' => 'Boolean', ], 'providerConfigurationDefinition' => [ 'shape' => 'Document', ], 'providerEndpointConfiguration' => [ 'shape' => 'ProviderEndpointConfiguration', ], 'providerEntityOutputDefinition' => [ 'shape' => 'Document', ], 'providerIntermediateDataAccessConfiguration' => [ 'shape' => 'ProviderIntermediateDataAccessConfiguration', ], 'providerName' => [ 'shape' => 'EntityName', ], 'providerServiceArn' => [ 'shape' => 'ProviderServiceArn', ], 'providerServiceDisplayName' => [ 'shape' => 'ProviderServiceDisplayName', ], 'providerServiceName' => [ 'shape' => 'EntityName', ], 'providerServiceType' => [ 'shape' => 'ServiceType', ], ], ], 'GetSchemaMappingInput' => [ 'type' => 'structure', 'required' => [ 'schemaName', ], 'members' => [ 'schemaName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'schemaName', ], ], ], 'GetSchemaMappingOutput' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'hasWorkflows', 'mappedInputFields', 'schemaArn', 'schemaName', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'hasWorkflows' => [ 'shape' => 'Boolean', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], 'schemaArn' => [ 'shape' => 'SchemaMappingArn', ], 'schemaName' => [ 'shape' => 'EntityName', ], 'tags' => [ 'shape' => 'TagMap', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'IdMappingJobMetrics' => [ 'type' => 'structure', 'members' => [ 'inputRecords' => [ 'shape' => 'Integer', ], 'recordsNotProcessed' => [ 'shape' => 'Integer', ], 'totalRecordsProcessed' => [ 'shape' => 'Integer', ], ], ], 'IdMappingTechniques' => [ 'type' => 'structure', 'required' => [ 'idMappingType', 'providerProperties', ], 'members' => [ 'idMappingType' => [ 'shape' => 'IdMappingType', ], 'providerProperties' => [ 'shape' => 'ProviderProperties', ], ], ], 'IdMappingType' => [ 'type' => 'string', 'enum' => [ 'PROVIDER', ], ], 'IdMappingWorkflowArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):entityresolution:.*:[0-9]+:(idmappingworkflow/.*)$', ], 'IdMappingWorkflowInputSource' => [ 'type' => 'structure', 'required' => [ 'inputSourceARN', 'schemaName', ], 'members' => [ 'inputSourceARN' => [ 'shape' => 'IdMappingWorkflowInputSourceInputSourceARNString', ], 'schemaName' => [ 'shape' => 'EntityName', ], ], ], 'IdMappingWorkflowInputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingWorkflowInputSource', ], 'max' => 20, 'min' => 1, ], 'IdMappingWorkflowInputSourceInputSourceARNString' => [ 'type' => 'string', 'pattern' => '^arn:aws:.*:.*:[0-9]+:.*$', ], 'IdMappingWorkflowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingWorkflowSummary', ], ], 'IdMappingWorkflowOutputSource' => [ 'type' => 'structure', 'required' => [ 'outputS3Path', ], 'members' => [ 'KMSArn' => [ 'shape' => 'KMSArn', ], 'outputS3Path' => [ 'shape' => 'S3Path', ], ], ], 'IdMappingWorkflowOutputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingWorkflowOutputSource', ], 'max' => 1, 'min' => 1, ], 'IdMappingWorkflowSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'updatedAt', 'workflowArn', 'workflowName', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'workflowArn' => [ 'shape' => 'IdMappingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'IncrementalRunConfig' => [ 'type' => 'structure', 'members' => [ 'incrementalRunType' => [ 'shape' => 'IncrementalRunType', ], ], ], 'IncrementalRunType' => [ 'type' => 'string', 'enum' => [ 'IMMEDIATE', ], ], 'InputSource' => [ 'type' => 'structure', 'required' => [ 'inputSourceARN', 'schemaName', ], 'members' => [ 'applyNormalization' => [ 'shape' => 'Boolean', ], 'inputSourceARN' => [ 'shape' => 'InputSourceInputSourceARNString', ], 'schemaName' => [ 'shape' => 'EntityName', ], ], ], 'InputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputSource', ], 'max' => 20, 'min' => 1, ], 'InputSourceInputSourceARNString' => [ 'type' => 'string', 'pattern' => '^arn:aws:.*:.*:[0-9]+:.*$', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'IntermediateSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'intermediateS3Path', ], 'members' => [ 'intermediateS3Path' => [ 'shape' => 'S3Path', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'JobId' => [ 'type' => 'string', 'pattern' => '^[a-f0-9]{32}$', ], 'JobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobSummary', ], ], 'JobMetrics' => [ 'type' => 'structure', 'members' => [ 'inputRecords' => [ 'shape' => 'Integer', ], 'matchIDs' => [ 'shape' => 'Integer', ], 'recordsNotProcessed' => [ 'shape' => 'Integer', ], 'totalRecordsProcessed' => [ 'shape' => 'Integer', ], ], ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'SUCCEEDED', 'FAILED', 'QUEUED', ], ], 'JobSummary' => [ 'type' => 'structure', 'required' => [ 'jobId', 'startTime', 'status', ], 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'jobId' => [ 'shape' => 'JobId', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'JobStatus', ], ], ], 'KMSArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:kms:.*:[0-9]+:.*$', ], 'ListIdMappingJobsInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'maxResults' => [ 'shape' => 'ListIdMappingJobsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'ListIdMappingJobsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListIdMappingJobsOutput' => [ 'type' => 'structure', 'members' => [ 'jobs' => [ 'shape' => 'JobList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIdMappingWorkflowsInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListIdMappingWorkflowsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListIdMappingWorkflowsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, ], 'ListIdMappingWorkflowsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'workflowSummaries' => [ 'shape' => 'IdMappingWorkflowList', ], ], ], 'ListMatchingJobsInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'maxResults' => [ 'shape' => 'ListMatchingJobsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'ListMatchingJobsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListMatchingJobsOutput' => [ 'type' => 'structure', 'members' => [ 'jobs' => [ 'shape' => 'JobList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMatchingWorkflowsInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListMatchingWorkflowsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListMatchingWorkflowsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, ], 'ListMatchingWorkflowsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'workflowSummaries' => [ 'shape' => 'MatchingWorkflowList', ], ], ], 'ListProviderServicesInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListProviderServicesInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'providerName' => [ 'shape' => 'EntityName', 'location' => 'querystring', 'locationName' => 'providerName', ], ], ], 'ListProviderServicesInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 15, ], 'ListProviderServicesOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'providerServiceSummaries' => [ 'shape' => 'ProviderServiceList', ], ], ], 'ListSchemaMappingsInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListSchemaMappingsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSchemaMappingsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, ], 'ListSchemaMappingsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'schemaList' => [ 'shape' => 'SchemaMappingList', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'MatchingWorkflowArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):entityresolution:.*:[0-9]+:(matchingworkflow/.*)$', ], 'MatchingWorkflowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchingWorkflowSummary', ], ], 'MatchingWorkflowSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'resolutionType', 'updatedAt', 'workflowArn', 'workflowName', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'resolutionType' => [ 'shape' => 'ResolutionType', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'workflowArn' => [ 'shape' => 'MatchingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[a-zA-Z_0-9-=+/]*$', ], 'OutputAttribute' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'hashed' => [ 'shape' => 'Boolean', ], 'name' => [ 'shape' => 'AttributeName', ], ], ], 'OutputSource' => [ 'type' => 'structure', 'required' => [ 'output', 'outputS3Path', ], 'members' => [ 'KMSArn' => [ 'shape' => 'KMSArn', ], 'applyNormalization' => [ 'shape' => 'Boolean', ], 'output' => [ 'shape' => 'OutputSourceOutputList', ], 'outputS3Path' => [ 'shape' => 'S3Path', ], ], ], 'OutputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputSource', ], 'max' => 1, 'min' => 1, ], 'OutputSourceOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputAttribute', ], 'max' => 750, 'min' => 0, ], 'ProviderEndpointConfiguration' => [ 'type' => 'structure', 'members' => [ 'marketplaceConfiguration' => [ 'shape' => 'ProviderMarketplaceConfiguration', ], ], 'union' => true, ], 'ProviderIntermediateDataAccessConfiguration' => [ 'type' => 'structure', 'members' => [ 'awsAccountIds' => [ 'shape' => 'AwsAccountIdList', ], 'requiredBucketActions' => [ 'shape' => 'RequiredBucketActionsList', ], ], ], 'ProviderMarketplaceConfiguration' => [ 'type' => 'structure', 'required' => [ 'assetId', 'dataSetId', 'listingId', 'revisionId', ], 'members' => [ 'assetId' => [ 'shape' => 'String', ], 'dataSetId' => [ 'shape' => 'String', ], 'listingId' => [ 'shape' => 'String', ], 'revisionId' => [ 'shape' => 'String', ], ], ], 'ProviderProperties' => [ 'type' => 'structure', 'required' => [ 'providerServiceArn', ], 'members' => [ 'intermediateSourceConfiguration' => [ 'shape' => 'IntermediateSourceConfiguration', ], 'providerConfiguration' => [ 'shape' => 'Document', ], 'providerServiceArn' => [ 'shape' => 'ProviderServiceArn', ], ], ], 'ProviderServiceArn' => [ 'type' => 'string', 'max' => 255, 'min' => 20, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):(entityresolution):([a-z]{2}-[a-z-]+?-[0-9])::providerservice/([a-zA-Z0-9_-]+)/([a-zA-Z0-9_-]+)$', ], 'ProviderServiceDisplayName' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'ProviderServiceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProviderServiceSummary', ], ], 'ProviderServiceSummary' => [ 'type' => 'structure', 'required' => [ 'providerName', 'providerServiceArn', 'providerServiceDisplayName', 'providerServiceName', 'providerServiceType', ], 'members' => [ 'providerName' => [ 'shape' => 'EntityName', ], 'providerServiceArn' => [ 'shape' => 'ProviderServiceArn', ], 'providerServiceDisplayName' => [ 'shape' => 'ProviderServiceDisplayName', ], 'providerServiceName' => [ 'shape' => 'EntityName', ], 'providerServiceType' => [ 'shape' => 'ServiceType', ], ], ], 'RecordAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'RecordAttributeMapKeyString', ], 'value' => [ 'shape' => 'RecordAttributeMapValueString', ], 'sensitive' => true, ], 'RecordAttributeMapKeyString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[a-zA-Z_0-9- \\t]*$', ], 'RecordAttributeMapValueString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[a-zA-Z_0-9-.@ ()+\\t]*$', ], 'RequiredBucketActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ResolutionTechniques' => [ 'type' => 'structure', 'required' => [ 'resolutionType', ], 'members' => [ 'providerProperties' => [ 'shape' => 'ProviderProperties', ], 'resolutionType' => [ 'shape' => 'ResolutionType', ], 'ruleBasedProperties' => [ 'shape' => 'RuleBasedProperties', ], ], ], 'ResolutionType' => [ 'type' => 'string', 'enum' => [ 'RULE_MATCHING', 'ML_MATCHING', 'PROVIDER', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'Rule' => [ 'type' => 'structure', 'required' => [ 'matchingKeys', 'ruleName', ], 'members' => [ 'matchingKeys' => [ 'shape' => 'RuleMatchingKeysList', ], 'ruleName' => [ 'shape' => 'RuleRuleNameString', ], ], ], 'RuleBasedProperties' => [ 'type' => 'structure', 'required' => [ 'attributeMatchingModel', 'rules', ], 'members' => [ 'attributeMatchingModel' => [ 'shape' => 'AttributeMatchingModel', ], 'rules' => [ 'shape' => 'RuleBasedPropertiesRulesList', ], ], ], 'RuleBasedPropertiesRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], 'max' => 15, 'min' => 1, ], 'RuleMatchingKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeName', ], 'max' => 15, 'min' => 1, ], 'RuleRuleNameString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[a-zA-Z_0-9- \\t]*$', ], 'S3Path' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^s3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/.*)?$', ], 'SchemaAttributeType' => [ 'type' => 'string', 'enum' => [ 'NAME', 'NAME_FIRST', 'NAME_MIDDLE', 'NAME_LAST', 'ADDRESS', 'ADDRESS_STREET1', 'ADDRESS_STREET2', 'ADDRESS_STREET3', 'ADDRESS_CITY', 'ADDRESS_STATE', 'ADDRESS_COUNTRY', 'ADDRESS_POSTALCODE', 'PHONE', 'PHONE_NUMBER', 'PHONE_COUNTRYCODE', 'EMAIL_ADDRESS', 'UNIQUE_ID', 'DATE', 'STRING', 'PROVIDER_ID', ], ], 'SchemaInputAttribute' => [ 'type' => 'structure', 'required' => [ 'fieldName', 'type', ], 'members' => [ 'fieldName' => [ 'shape' => 'AttributeName', ], 'groupName' => [ 'shape' => 'AttributeName', ], 'matchKey' => [ 'shape' => 'AttributeName', ], 'subType' => [ 'shape' => 'AttributeName', ], 'type' => [ 'shape' => 'SchemaAttributeType', ], ], ], 'SchemaInputAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaInputAttribute', ], 'max' => 25, 'min' => 2, ], 'SchemaMappingArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):entityresolution:.*:[0-9]+:(schemamapping/.*)$', ], 'SchemaMappingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaMappingSummary', ], ], 'SchemaMappingSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'hasWorkflows', 'schemaArn', 'schemaName', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'hasWorkflows' => [ 'shape' => 'Boolean', ], 'schemaArn' => [ 'shape' => 'SchemaMappingArn', ], 'schemaName' => [ 'shape' => 'EntityName', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ServiceType' => [ 'type' => 'string', 'enum' => [ 'ASSIGNMENT', 'ID_MAPPING', ], ], 'StartIdMappingJobInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'StartIdMappingJobOutput' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], ], ], 'StartMatchingJobInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'StartMatchingJobOutput' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateIdMappingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'idMappingTechniques', 'inputSourceConfig', 'outputSourceConfig', 'roleArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'UpdateIdMappingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'idMappingTechniques', 'inputSourceConfig', 'outputSourceConfig', 'roleArn', 'workflowArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'workflowArn' => [ 'shape' => 'IdMappingWorkflowArn', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'UpdateMatchingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'roleArn' => [ 'shape' => 'String', ], 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'UpdateMatchingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', 'workflowName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'roleArn' => [ 'shape' => 'String', ], 'workflowName' => [ 'shape' => 'EntityName', ], ], ], 'UpdateSchemaMappingInput' => [ 'type' => 'structure', 'required' => [ 'mappedInputFields', 'schemaName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], 'schemaName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'schemaName', ], ], ], 'UpdateSchemaMappingOutput' => [ 'type' => 'structure', 'required' => [ 'mappedInputFields', 'schemaArn', 'schemaName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], 'schemaArn' => [ 'shape' => 'SchemaMappingArn', ], 'schemaName' => [ 'shape' => 'EntityName', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VeniceGlobalArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):(entityresolution):.*:[0-9]+:((schemamapping|matchingworkflow|idmappingworkflow)/[a-zA-Z0-9_-]+)$', ], ],];
