<div class="cp-app-container" data-app="invite-users">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Invite users
            </h1>
        </div>
    </div>
    <div class="card">
        <div class="header">
            <h2>
                Create new invite link
            </h2>
        </div>
        <div class="body">
            <form class="form" data-an="form">
                <div class="row align-items-center">
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label>
                                Expire after
                            </label>
                            <div class="form-line form-select">
                                <select name="expires_at" class="form-control">
                                    <option value="1">
                                        1 day
                                    </option>
                                    <option value="3">
                                        3 days
                                    </option>
                                    <option value="7">
                                        7 days
                                    </option>
                                    <option value="15">
                                        15 days
                                    </option>
                                    <option value="30">
                                        30 days
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label>
                                User role (Admin or User)
                            </label>
                            <div class="form-line form-select">
                                <select name="role" class="form-control">
                                    <option value="user">
                                        Regular user
                                    </option>
                                    <option value="admin">
                                        Admin user
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label>
                                Max number of use
                            </label>
                            <div class="form-line">
                                <input value="1" name="mnu" type="number" class="form-control" placeholder="E. g. 5">
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="form-group no-mb">
                            <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                Create invite link
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="card">
        <div class="header">
            <h2>
                Manage user invitation links
            </h2>
        </div>
        
        <div class="body">
            <div class="inline-alertbox-wrapper">
                <div class="inline-alertbox info">
                    <div class="icon">
                        <?php echo cl_ficon("info"); ?>
                    </div>
                    <div class="alert-message">
                        <p>
                            Please note that this function makes sense to be used only if you have disabled the user registration system.
                        </p>
                    </div>
                </div>
            </div>
            <table class="table" id="vue-cpanel-user-invites">
                <thead>
                    <th>
                        Link
                    </th>
                    <th>
                        Clipboard
                    </th>
                    <th>
                        User role
                    </th>
                    <th>
                        Max num of use
                    </th>
                    <th>
                        Registered users
                    </th>
                    <th>
                        Created at
                    </th>
                    <th>
                        Expires at
                    </th>
                    <th>
                        Actions
                    </th>
                </thead>
                <tbody v-if="links.length">
                    <tr v-for="item_data in links">
                        <td>
                            <a v-bind:href="item_data.link">
                                {{item_data.link_short}}
                            </a>
                        </td>
                        <td>
                            <span v-bind:data-clipboard-text="item_data.link" class="icon pointer clip-board-copy">
                                <?php echo cl_ficon("copy"); ?>
                            </span>
                        </td>   
                        <td>
                            <span v-if="item_data.role == 'admin'" class="badge">
                                Admin
                            </span>
                            <span v-else>
                                User
                            </span>
                        </td>
                        <td>
                            <b>{{item_data.mnu}}</b>
                        </td>
                        <td>
                            <span class="badge bg-grey">
                                <b>{{item_data.registered_users}}</b> users
                            </span>
                        </td>
                        <td>
                            {{item_data.time}}
                        </td>
                        <td>
                            {{item_data.expire_time}}
                        </td>
                        <td>
                            <span class="icon pointer" v-on:click="delete_link(item_data.id)">
                                <?php echo cl_ficon("delete"); ?>
                            </span>
                        </td>
                    </tr>
                </tbody>
                <tbody v-else>
                    <tr>
                        <td colspan="8">
                            <div class="empty-table">
                                <h4>
                                    No links found
                                </h4>
                                <p>
                                    It looks like you don't have invite links yet. Click the (Create) button to create a new link
                                </p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php echo cl_template('cpanel/assets/invite_users/scripts/app_master_script'); ?>