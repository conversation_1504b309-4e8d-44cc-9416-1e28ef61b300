<?php
// This file was auto-generated from sdk-root/src/data/iottwinmaker/2021-11-29/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-11-29', 'endpointPrefix' => 'iottwinmaker', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS IoT TwinMaker', 'serviceId' => 'IoTTwinMaker', 'signatureVersion' => 'v4', 'signingName' => 'iottwinmaker', 'uid' => 'iottwinmaker-2021-11-29', ], 'operations' => [ 'BatchPutPropertyValues' => [ 'name' => 'BatchPutPropertyValues', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/entity-properties', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchPutPropertyValuesRequest', ], 'output' => [ 'shape' => 'BatchPutPropertyValuesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'CancelMetadataTransferJob' => [ 'name' => 'CancelMetadataTransferJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/metadata-transfer-jobs/{metadataTransferJobId}/cancel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelMetadataTransferJobRequest', ], 'output' => [ 'shape' => 'CancelMetadataTransferJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateComponentType' => [ 'name' => 'CreateComponentType', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/component-types/{componentTypeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateComponentTypeRequest', ], 'output' => [ 'shape' => 'CreateComponentTypeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateEntity' => [ 'name' => 'CreateEntity', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/entities', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateEntityRequest', ], 'output' => [ 'shape' => 'CreateEntityResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateMetadataTransferJob' => [ 'name' => 'CreateMetadataTransferJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/metadata-transfer-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMetadataTransferJobRequest', ], 'output' => [ 'shape' => 'CreateMetadataTransferJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateScene' => [ 'name' => 'CreateScene', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/scenes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSceneRequest', ], 'output' => [ 'shape' => 'CreateSceneResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateSyncJob' => [ 'name' => 'CreateSyncJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/sync-jobs/{syncSource}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSyncJobRequest', ], 'output' => [ 'shape' => 'CreateSyncJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateWorkspace' => [ 'name' => 'CreateWorkspace', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateWorkspaceRequest', ], 'output' => [ 'shape' => 'CreateWorkspaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteComponentType' => [ 'name' => 'DeleteComponentType', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workspaces/{workspaceId}/component-types/{componentTypeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteComponentTypeRequest', ], 'output' => [ 'shape' => 'DeleteComponentTypeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteEntity' => [ 'name' => 'DeleteEntity', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workspaces/{workspaceId}/entities/{entityId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEntityRequest', ], 'output' => [ 'shape' => 'DeleteEntityResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteScene' => [ 'name' => 'DeleteScene', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workspaces/{workspaceId}/scenes/{sceneId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSceneRequest', ], 'output' => [ 'shape' => 'DeleteSceneResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteSyncJob' => [ 'name' => 'DeleteSyncJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workspaces/{workspaceId}/sync-jobs/{syncSource}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSyncJobRequest', ], 'output' => [ 'shape' => 'DeleteSyncJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteWorkspace' => [ 'name' => 'DeleteWorkspace', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workspaces/{workspaceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteWorkspaceRequest', ], 'output' => [ 'shape' => 'DeleteWorkspaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ExecuteQuery' => [ 'name' => 'ExecuteQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/queries/execution', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExecuteQueryRequest', ], 'output' => [ 'shape' => 'ExecuteQueryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'QueryTimeoutException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'GetComponentType' => [ 'name' => 'GetComponentType', 'http' => [ 'method' => 'GET', 'requestUri' => '/workspaces/{workspaceId}/component-types/{componentTypeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetComponentTypeRequest', ], 'output' => [ 'shape' => 'GetComponentTypeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'GetEntity' => [ 'name' => 'GetEntity', 'http' => [ 'method' => 'GET', 'requestUri' => '/workspaces/{workspaceId}/entities/{entityId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEntityRequest', ], 'output' => [ 'shape' => 'GetEntityResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'GetMetadataTransferJob' => [ 'name' => 'GetMetadataTransferJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/metadata-transfer-jobs/{metadataTransferJobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMetadataTransferJobRequest', ], 'output' => [ 'shape' => 'GetMetadataTransferJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'GetPricingPlan' => [ 'name' => 'GetPricingPlan', 'http' => [ 'method' => 'GET', 'requestUri' => '/pricingplan', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPricingPlanRequest', ], 'output' => [ 'shape' => 'GetPricingPlanResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'GetPropertyValue' => [ 'name' => 'GetPropertyValue', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/entity-properties/value', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPropertyValueRequest', ], 'output' => [ 'shape' => 'GetPropertyValueResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConnectorFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConnectorTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'GetPropertyValueHistory' => [ 'name' => 'GetPropertyValueHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/entity-properties/history', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPropertyValueHistoryRequest', ], 'output' => [ 'shape' => 'GetPropertyValueHistoryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConnectorFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConnectorTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'GetScene' => [ 'name' => 'GetScene', 'http' => [ 'method' => 'GET', 'requestUri' => '/workspaces/{workspaceId}/scenes/{sceneId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSceneRequest', ], 'output' => [ 'shape' => 'GetSceneResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'GetSyncJob' => [ 'name' => 'GetSyncJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/sync-jobs/{syncSource}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSyncJobRequest', ], 'output' => [ 'shape' => 'GetSyncJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'GetWorkspace' => [ 'name' => 'GetWorkspace', 'http' => [ 'method' => 'GET', 'requestUri' => '/workspaces/{workspaceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkspaceRequest', ], 'output' => [ 'shape' => 'GetWorkspaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListComponentTypes' => [ 'name' => 'ListComponentTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/component-types-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListComponentTypesRequest', ], 'output' => [ 'shape' => 'ListComponentTypesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListComponents' => [ 'name' => 'ListComponents', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/entities/{entityId}/components-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListComponentsRequest', ], 'output' => [ 'shape' => 'ListComponentsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListEntities' => [ 'name' => 'ListEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/entities-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEntitiesRequest', ], 'output' => [ 'shape' => 'ListEntitiesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListMetadataTransferJobs' => [ 'name' => 'ListMetadataTransferJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/metadata-transfer-jobs-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMetadataTransferJobsRequest', ], 'output' => [ 'shape' => 'ListMetadataTransferJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListProperties' => [ 'name' => 'ListProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/properties-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPropertiesRequest', ], 'output' => [ 'shape' => 'ListPropertiesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListScenes' => [ 'name' => 'ListScenes', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/scenes-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListScenesRequest', ], 'output' => [ 'shape' => 'ListScenesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListSyncJobs' => [ 'name' => 'ListSyncJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/sync-jobs-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSyncJobsRequest', ], 'output' => [ 'shape' => 'ListSyncJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListSyncResources' => [ 'name' => 'ListSyncResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/sync-jobs/{syncSource}/resources-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSyncResourcesRequest', ], 'output' => [ 'shape' => 'ListSyncResourcesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListWorkspaces' => [ 'name' => 'ListWorkspaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWorkspacesRequest', ], 'output' => [ 'shape' => 'ListWorkspacesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateComponentType' => [ 'name' => 'UpdateComponentType', 'http' => [ 'method' => 'PUT', 'requestUri' => '/workspaces/{workspaceId}/component-types/{componentTypeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateComponentTypeRequest', ], 'output' => [ 'shape' => 'UpdateComponentTypeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateEntity' => [ 'name' => 'UpdateEntity', 'http' => [ 'method' => 'PUT', 'requestUri' => '/workspaces/{workspaceId}/entities/{entityId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEntityRequest', ], 'output' => [ 'shape' => 'UpdateEntityResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdatePricingPlan' => [ 'name' => 'UpdatePricingPlan', 'http' => [ 'method' => 'POST', 'requestUri' => '/pricingplan', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePricingPlanRequest', ], 'output' => [ 'shape' => 'UpdatePricingPlanResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateScene' => [ 'name' => 'UpdateScene', 'http' => [ 'method' => 'PUT', 'requestUri' => '/workspaces/{workspaceId}/scenes/{sceneId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSceneRequest', ], 'output' => [ 'shape' => 'UpdateSceneResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateWorkspace' => [ 'name' => 'UpdateWorkspace', 'http' => [ 'method' => 'PUT', 'requestUri' => '/workspaces/{workspaceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateWorkspaceRequest', ], 'output' => [ 'shape' => 'UpdateWorkspaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'BatchPutPropertyError' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'entry', ], 'members' => [ 'errorCode' => [ 'shape' => 'String', ], 'errorMessage' => [ 'shape' => 'String', ], 'entry' => [ 'shape' => 'PropertyValueEntry', ], ], ], 'BatchPutPropertyErrorEntry' => [ 'type' => 'structure', 'required' => [ 'errors', ], 'members' => [ 'errors' => [ 'shape' => 'Errors', ], ], ], 'BatchPutPropertyValuesRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'entries', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'entries' => [ 'shape' => 'Entries', ], ], ], 'BatchPutPropertyValuesResponse' => [ 'type' => 'structure', 'required' => [ 'errorEntries', ], 'members' => [ 'errorEntries' => [ 'shape' => 'ErrorEntries', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BundleInformation' => [ 'type' => 'structure', 'required' => [ 'bundleNames', ], 'members' => [ 'bundleNames' => [ 'shape' => 'PricingBundles', ], 'pricingTier' => [ 'shape' => 'PricingTier', ], ], ], 'BundleName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'CancelMetadataTransferJobRequest' => [ 'type' => 'structure', 'required' => [ 'metadataTransferJobId', ], 'members' => [ 'metadataTransferJobId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'metadataTransferJobId', ], ], ], 'CancelMetadataTransferJobResponse' => [ 'type' => 'structure', 'required' => [ 'metadataTransferJobId', 'arn', 'updateDateTime', 'status', ], 'members' => [ 'metadataTransferJobId' => [ 'shape' => 'Id', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'MetadataTransferJobStatus', ], 'progress' => [ 'shape' => 'MetadataTransferJobProgress', ], ], ], 'ColumnDescription' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ColumnName', ], 'type' => [ 'shape' => 'ColumnType', ], ], ], 'ColumnDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnDescription', ], ], 'ColumnName' => [ 'type' => 'string', 'pattern' => '.*', ], 'ColumnType' => [ 'type' => 'string', 'enum' => [ 'NODE', 'EDGE', 'VALUE', ], ], 'ComponentPath' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[a-zA-Z_\\-0-9/]+', ], 'ComponentPropertyGroupRequest' => [ 'type' => 'structure', 'members' => [ 'groupType' => [ 'shape' => 'GroupType', ], 'propertyNames' => [ 'shape' => 'PropertyNames', ], 'updateType' => [ 'shape' => 'PropertyGroupUpdateType', ], ], ], 'ComponentPropertyGroupRequests' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'ComponentPropertyGroupRequest', ], ], 'ComponentPropertyGroupResponse' => [ 'type' => 'structure', 'required' => [ 'groupType', 'propertyNames', 'isInherited', ], 'members' => [ 'groupType' => [ 'shape' => 'GroupType', ], 'propertyNames' => [ 'shape' => 'PropertyNames', ], 'isInherited' => [ 'shape' => 'Boolean', ], ], ], 'ComponentPropertyGroupResponses' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'ComponentPropertyGroupResponse', ], ], 'ComponentRequest' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'Description', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'properties' => [ 'shape' => 'PropertyRequests', ], 'propertyGroups' => [ 'shape' => 'ComponentPropertyGroupRequests', ], ], ], 'ComponentResponse' => [ 'type' => 'structure', 'members' => [ 'componentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'status' => [ 'shape' => 'Status', ], 'definedIn' => [ 'shape' => 'String', ], 'properties' => [ 'shape' => 'PropertyResponses', ], 'propertyGroups' => [ 'shape' => 'ComponentPropertyGroupResponses', ], 'syncSource' => [ 'shape' => 'SyncSource', ], 'areAllPropertiesReturned' => [ 'shape' => 'Boolean', ], 'compositeComponents' => [ 'shape' => 'CompositeComponentResponse', ], 'areAllCompositeComponentsReturned' => [ 'shape' => 'Boolean', ], ], ], 'ComponentSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentSummary', ], ], 'ComponentSummary' => [ 'type' => 'structure', 'required' => [ 'componentName', 'componentTypeId', 'status', ], 'members' => [ 'componentName' => [ 'shape' => 'Name', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'definedIn' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'propertyGroups' => [ 'shape' => 'ComponentPropertyGroupResponses', ], 'status' => [ 'shape' => 'Status', ], 'syncSource' => [ 'shape' => 'SyncSource', ], 'componentPath' => [ 'shape' => 'ComponentPath', ], ], ], 'ComponentTypeId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z_\\.\\-0-9:]+', ], 'ComponentTypeName' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*[^\\u0000-\\u001F\\u007F]*.*', ], 'ComponentTypeSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentTypeSummary', ], ], 'ComponentTypeSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'componentTypeId', 'creationDateTime', 'updateDateTime', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'status' => [ 'shape' => 'Status', ], 'componentTypeName' => [ 'shape' => 'ComponentTypeName', ], ], ], 'ComponentUpdateRequest' => [ 'type' => 'structure', 'members' => [ 'updateType' => [ 'shape' => 'ComponentUpdateType', ], 'description' => [ 'shape' => 'Description', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'propertyUpdates' => [ 'shape' => 'PropertyRequests', ], 'propertyGroupUpdates' => [ 'shape' => 'ComponentPropertyGroupRequests', ], ], ], 'ComponentUpdateType' => [ 'type' => 'string', 'enum' => [ 'CREATE', 'UPDATE', 'DELETE', ], ], 'ComponentUpdatesMapRequest' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'ComponentUpdateRequest', ], ], 'ComponentsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'ComponentResponse', ], ], 'ComponentsMapRequest' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'ComponentRequest', ], ], 'CompositeComponentRequest' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'Description', ], 'properties' => [ 'shape' => 'PropertyRequests', ], 'propertyGroups' => [ 'shape' => 'ComponentPropertyGroupRequests', ], ], ], 'CompositeComponentResponse' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'ComponentSummary', ], ], 'CompositeComponentTypeRequest' => [ 'type' => 'structure', 'members' => [ 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], ], ], 'CompositeComponentTypeResponse' => [ 'type' => 'structure', 'members' => [ 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'isInherited' => [ 'shape' => 'Boolean', ], ], ], 'CompositeComponentTypesRequest' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'CompositeComponentTypeRequest', ], ], 'CompositeComponentTypesResponse' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'CompositeComponentTypeResponse', ], ], 'CompositeComponentUpdateRequest' => [ 'type' => 'structure', 'members' => [ 'updateType' => [ 'shape' => 'ComponentUpdateType', ], 'description' => [ 'shape' => 'Description', ], 'propertyUpdates' => [ 'shape' => 'PropertyRequests', ], 'propertyGroupUpdates' => [ 'shape' => 'ComponentPropertyGroupRequests', ], ], ], 'CompositeComponentUpdatesMapRequest' => [ 'type' => 'map', 'key' => [ 'shape' => 'ComponentPath', ], 'value' => [ 'shape' => 'CompositeComponentUpdateRequest', ], ], 'CompositeComponentsMapRequest' => [ 'type' => 'map', 'key' => [ 'shape' => 'ComponentPath', ], 'value' => [ 'shape' => 'CompositeComponentRequest', ], ], 'Configuration' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'Value', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConnectorFailureException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 424, 'senderFault' => true, ], 'exception' => true, ], 'ConnectorTimeoutException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 424, 'senderFault' => true, ], 'exception' => true, ], 'CreateComponentTypeRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'componentTypeId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'isSingleton' => [ 'shape' => 'Boolean', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', 'location' => 'uri', 'locationName' => 'componentTypeId', ], 'description' => [ 'shape' => 'Description', ], 'propertyDefinitions' => [ 'shape' => 'PropertyDefinitionsRequest', ], 'extendsFrom' => [ 'shape' => 'ExtendsFrom', ], 'functions' => [ 'shape' => 'FunctionsRequest', ], 'tags' => [ 'shape' => 'TagMap', ], 'propertyGroups' => [ 'shape' => 'PropertyGroupsRequest', ], 'componentTypeName' => [ 'shape' => 'ComponentTypeName', ], 'compositeComponentTypes' => [ 'shape' => 'CompositeComponentTypesRequest', ], ], ], 'CreateComponentTypeResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationDateTime', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'state' => [ 'shape' => 'State', ], ], ], 'CreateEntityRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'entityName', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'entityId' => [ 'shape' => 'EntityId', ], 'entityName' => [ 'shape' => 'EntityName', ], 'description' => [ 'shape' => 'Description', ], 'components' => [ 'shape' => 'ComponentsMapRequest', ], 'compositeComponents' => [ 'shape' => 'CompositeComponentsMapRequest', ], 'parentEntityId' => [ 'shape' => 'ParentEntityId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateEntityResponse' => [ 'type' => 'structure', 'required' => [ 'entityId', 'arn', 'creationDateTime', 'state', ], 'members' => [ 'entityId' => [ 'shape' => 'EntityId', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'state' => [ 'shape' => 'State', ], ], ], 'CreateMetadataTransferJobRequest' => [ 'type' => 'structure', 'required' => [ 'sources', 'destination', ], 'members' => [ 'metadataTransferJobId' => [ 'shape' => 'Id', ], 'description' => [ 'shape' => 'Description', ], 'sources' => [ 'shape' => 'SourceConfigurations', ], 'destination' => [ 'shape' => 'DestinationConfiguration', ], ], ], 'CreateMetadataTransferJobResponse' => [ 'type' => 'structure', 'required' => [ 'metadataTransferJobId', 'arn', 'creationDateTime', 'status', ], 'members' => [ 'metadataTransferJobId' => [ 'shape' => 'Id', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'MetadataTransferJobStatus', ], ], ], 'CreateSceneRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'sceneId', 'contentLocation', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'sceneId' => [ 'shape' => 'Id', ], 'contentLocation' => [ 'shape' => 'S3Url', ], 'description' => [ 'shape' => 'Description', ], 'capabilities' => [ 'shape' => 'SceneCapabilities', ], 'tags' => [ 'shape' => 'TagMap', ], 'sceneMetadata' => [ 'shape' => 'SceneMetadataMap', ], ], ], 'CreateSceneResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationDateTime', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateSyncJobRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'syncSource', 'syncRole', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'syncSource' => [ 'shape' => 'SyncSource', 'location' => 'uri', 'locationName' => 'syncSource', ], 'syncRole' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateSyncJobResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationDateTime', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'state' => [ 'shape' => 'SyncJobState', ], ], ], 'CreateWorkspaceRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'description' => [ 'shape' => 'Description', ], 's3Location' => [ 'shape' => 'S3Location', ], 'role' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateWorkspaceResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationDateTime', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DataConnector' => [ 'type' => 'structure', 'members' => [ 'lambda' => [ 'shape' => 'LambdaFunction', ], 'isNative' => [ 'shape' => 'Boolean', ], ], ], 'DataType' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'Type', ], 'nestedType' => [ 'shape' => 'DataType', ], 'allowedValues' => [ 'shape' => 'DataValueList', ], 'unitOfMeasure' => [ 'shape' => 'String', ], 'relationship' => [ 'shape' => 'Relationship', ], ], ], 'DataValue' => [ 'type' => 'structure', 'members' => [ 'booleanValue' => [ 'shape' => 'Boolean', ], 'doubleValue' => [ 'shape' => 'Double', ], 'integerValue' => [ 'shape' => 'Integer', ], 'longValue' => [ 'shape' => 'Long', ], 'stringValue' => [ 'shape' => 'String', ], 'listValue' => [ 'shape' => 'DataValueList', ], 'mapValue' => [ 'shape' => 'DataValueMap', ], 'relationshipValue' => [ 'shape' => 'RelationshipValue', ], 'expression' => [ 'shape' => 'Expression', ], ], ], 'DataValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataValue', ], 'max' => 50, 'min' => 0, ], 'DataValueMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'DataValue', ], 'max' => 50, 'min' => 0, ], 'DeleteComponentTypeRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'componentTypeId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', 'location' => 'uri', 'locationName' => 'componentTypeId', ], ], ], 'DeleteComponentTypeResponse' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], ], ], 'DeleteEntityRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'entityId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'entityId' => [ 'shape' => 'EntityId', 'location' => 'uri', 'locationName' => 'entityId', ], 'isRecursive' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'isRecursive', ], ], ], 'DeleteEntityResponse' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], ], ], 'DeleteSceneRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'sceneId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'sceneId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'sceneId', ], ], ], 'DeleteSceneResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSyncJobRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'syncSource', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'syncSource' => [ 'shape' => 'SyncSource', 'location' => 'uri', 'locationName' => 'syncSource', ], ], ], 'DeleteSyncJobResponse' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'SyncJobState', ], ], ], 'DeleteWorkspaceRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'DeleteWorkspaceResponse' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'WorkspaceDeleteMessage', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '.*', ], 'DestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'DestinationType', ], 's3Configuration' => [ 'shape' => 'S3DestinationConfiguration', ], 'iotTwinMakerConfiguration' => [ 'shape' => 'IotTwinMakerDestinationConfiguration', ], ], ], 'DestinationType' => [ 'type' => 'string', 'enum' => [ 's3', 'iotsitewise', 'iottwinmaker', ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'EntityId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}|^[a-zA-Z0-9][a-zA-Z_\\-0-9.:]*[a-zA-Z0-9]+', ], 'EntityName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'EntityPropertyReference' => [ 'type' => 'structure', 'required' => [ 'propertyName', ], 'members' => [ 'componentName' => [ 'shape' => 'Name', ], 'componentPath' => [ 'shape' => 'ComponentPath', ], 'externalIdProperty' => [ 'shape' => 'ExternalIdProperty', ], 'entityId' => [ 'shape' => 'EntityId', ], 'propertyName' => [ 'shape' => 'Name', ], ], ], 'EntitySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntitySummary', ], ], 'EntitySummary' => [ 'type' => 'structure', 'required' => [ 'entityId', 'entityName', 'arn', 'status', 'creationDateTime', 'updateDateTime', ], 'members' => [ 'entityId' => [ 'shape' => 'EntityId', ], 'entityName' => [ 'shape' => 'EntityName', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'parentEntityId' => [ 'shape' => 'ParentEntityId', ], 'status' => [ 'shape' => 'Status', ], 'description' => [ 'shape' => 'Description', ], 'hasChildEntities' => [ 'shape' => 'Boolean', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'Entries' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyValueEntry', ], 'max' => 10, 'min' => 1, ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'VALIDATION_ERROR', 'INTERNAL_FAILURE', 'SYNC_INITIALIZING_ERROR', 'SYNC_CREATING_ERROR', 'SYNC_PROCESSING_ERROR', 'SYNC_DELETING_ERROR', 'PROCESSING_ERROR', 'COMPOSITE_COMPONENT_FAILURE', ], ], 'ErrorDetails' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'ErrorEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutPropertyErrorEntry', ], 'max' => 10, 'min' => 1, ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'Errors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutPropertyError', ], 'max' => 10, 'min' => 1, ], 'ExceptionMessage' => [ 'type' => 'string', ], 'ExecuteQueryRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'queryStatement', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', ], 'queryStatement' => [ 'shape' => 'QueryStatement', ], 'maxResults' => [ 'shape' => 'QueryServiceMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ExecuteQueryResponse' => [ 'type' => 'structure', 'members' => [ 'columnDescriptions' => [ 'shape' => 'ColumnDescriptions', ], 'rows' => [ 'shape' => 'Rows', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'Expression' => [ 'type' => 'string', 'max' => 316, 'min' => 1, 'pattern' => '(^\\$\\{Parameters\\.[a-zA-z]+([a-zA-z_0-9]*)}$)', ], 'ExtendsFrom' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentTypeId', ], ], 'ExternalIdProperty' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'FilterByAsset' => [ 'type' => 'structure', 'members' => [ 'assetId' => [ 'shape' => 'Uuid', ], 'assetExternalId' => [ 'shape' => 'SiteWiseExternalId', ], 'includeOffspring' => [ 'shape' => 'Boolean', ], 'includeAssetModel' => [ 'shape' => 'Boolean', ], ], ], 'FilterByAssetModel' => [ 'type' => 'structure', 'members' => [ 'assetModelId' => [ 'shape' => 'Uuid', ], 'assetModelExternalId' => [ 'shape' => 'SiteWiseExternalId', ], 'includeOffspring' => [ 'shape' => 'Boolean', ], 'includeAssets' => [ 'shape' => 'Boolean', ], ], ], 'FilterByComponentType' => [ 'type' => 'structure', 'required' => [ 'componentTypeId', ], 'members' => [ 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], ], ], 'FilterByEntity' => [ 'type' => 'structure', 'required' => [ 'entityId', ], 'members' => [ 'entityId' => [ 'shape' => 'EntityId', ], ], ], 'FunctionRequest' => [ 'type' => 'structure', 'members' => [ 'requiredProperties' => [ 'shape' => 'RequiredProperties', ], 'scope' => [ 'shape' => 'Scope', ], 'implementedBy' => [ 'shape' => 'DataConnector', ], ], ], 'FunctionResponse' => [ 'type' => 'structure', 'members' => [ 'requiredProperties' => [ 'shape' => 'RequiredProperties', ], 'scope' => [ 'shape' => 'Scope', ], 'implementedBy' => [ 'shape' => 'DataConnector', ], 'isInherited' => [ 'shape' => 'Boolean', ], ], ], 'FunctionsRequest' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'FunctionRequest', ], ], 'FunctionsResponse' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'FunctionResponse', ], ], 'GeneratedSceneMetadataMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'SceneMetadataValue', ], 'max' => 50, 'min' => 0, ], 'GetComponentTypeRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'componentTypeId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', 'location' => 'uri', 'locationName' => 'componentTypeId', ], ], ], 'GetComponentTypeResponse' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'componentTypeId', 'creationDateTime', 'updateDateTime', 'arn', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', ], 'isSingleton' => [ 'shape' => 'Boolean', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'description' => [ 'shape' => 'Description', ], 'propertyDefinitions' => [ 'shape' => 'PropertyDefinitionsResponse', ], 'extendsFrom' => [ 'shape' => 'ExtendsFrom', ], 'functions' => [ 'shape' => 'FunctionsResponse', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'isAbstract' => [ 'shape' => 'Boolean', ], 'isSchemaInitialized' => [ 'shape' => 'Boolean', ], 'status' => [ 'shape' => 'Status', ], 'propertyGroups' => [ 'shape' => 'PropertyGroupsResponse', ], 'syncSource' => [ 'shape' => 'SyncSource', ], 'componentTypeName' => [ 'shape' => 'ComponentTypeName', ], 'compositeComponentTypes' => [ 'shape' => 'CompositeComponentTypesResponse', ], ], ], 'GetEntityRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'entityId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'entityId' => [ 'shape' => 'EntityId', 'location' => 'uri', 'locationName' => 'entityId', ], ], ], 'GetEntityResponse' => [ 'type' => 'structure', 'required' => [ 'entityId', 'entityName', 'arn', 'status', 'workspaceId', 'parentEntityId', 'hasChildEntities', 'creationDateTime', 'updateDateTime', ], 'members' => [ 'entityId' => [ 'shape' => 'EntityId', ], 'entityName' => [ 'shape' => 'EntityName', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'status' => [ 'shape' => 'Status', ], 'workspaceId' => [ 'shape' => 'Id', ], 'description' => [ 'shape' => 'Description', ], 'components' => [ 'shape' => 'ComponentsMap', ], 'parentEntityId' => [ 'shape' => 'ParentEntityId', ], 'hasChildEntities' => [ 'shape' => 'Boolean', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'syncSource' => [ 'shape' => 'SyncSource', ], 'areAllComponentsReturned' => [ 'shape' => 'Boolean', ], ], ], 'GetMetadataTransferJobRequest' => [ 'type' => 'structure', 'required' => [ 'metadataTransferJobId', ], 'members' => [ 'metadataTransferJobId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'metadataTransferJobId', ], ], ], 'GetMetadataTransferJobResponse' => [ 'type' => 'structure', 'required' => [ 'metadataTransferJobId', 'arn', 'sources', 'destination', 'metadataTransferJobRole', 'creationDateTime', 'updateDateTime', 'status', ], 'members' => [ 'metadataTransferJobId' => [ 'shape' => 'Id', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'description' => [ 'shape' => 'Description', ], 'sources' => [ 'shape' => 'SourceConfigurations', ], 'destination' => [ 'shape' => 'DestinationConfiguration', ], 'metadataTransferJobRole' => [ 'shape' => 'RoleArn', ], 'reportUrl' => [ 'shape' => 'String', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'MetadataTransferJobStatus', ], 'progress' => [ 'shape' => 'MetadataTransferJobProgress', ], ], ], 'GetPricingPlanRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetPricingPlanResponse' => [ 'type' => 'structure', 'required' => [ 'currentPricingPlan', ], 'members' => [ 'currentPricingPlan' => [ 'shape' => 'PricingPlan', ], 'pendingPricingPlan' => [ 'shape' => 'PricingPlan', ], ], ], 'GetPropertyValueHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'selectedProperties', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'entityId' => [ 'shape' => 'EntityId', ], 'componentName' => [ 'shape' => 'Name', ], 'componentPath' => [ 'shape' => 'ComponentPath', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'selectedProperties' => [ 'shape' => 'SelectedPropertyList', ], 'propertyFilters' => [ 'shape' => 'PropertyFilters', ], 'startDateTime' => [ 'shape' => 'Timestamp', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated and will throw an error in the future. Use startTime instead.', ], 'endDateTime' => [ 'shape' => 'Timestamp', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated and will throw an error in the future. Use endTime instead.', ], 'interpolation' => [ 'shape' => 'InterpolationParameters', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'orderByTime' => [ 'shape' => 'OrderByTime', ], 'startTime' => [ 'shape' => 'Time', ], 'endTime' => [ 'shape' => 'Time', ], ], ], 'GetPropertyValueHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'propertyValues', ], 'members' => [ 'propertyValues' => [ 'shape' => 'PropertyValueList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetPropertyValueRequest' => [ 'type' => 'structure', 'required' => [ 'selectedProperties', 'workspaceId', ], 'members' => [ 'componentName' => [ 'shape' => 'Name', ], 'componentPath' => [ 'shape' => 'ComponentPath', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'entityId' => [ 'shape' => 'EntityId', ], 'selectedProperties' => [ 'shape' => 'SelectedPropertyList', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'propertyGroupName' => [ 'shape' => 'Name', ], 'tabularConditions' => [ 'shape' => 'TabularConditions', ], ], ], 'GetPropertyValueResponse' => [ 'type' => 'structure', 'members' => [ 'propertyValues' => [ 'shape' => 'PropertyLatestValueMap', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'tabularPropertyValues' => [ 'shape' => 'TabularPropertyValues', ], ], ], 'GetSceneRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'sceneId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'sceneId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'sceneId', ], ], ], 'GetSceneResponse' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'sceneId', 'contentLocation', 'arn', 'creationDateTime', 'updateDateTime', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', ], 'sceneId' => [ 'shape' => 'Id', ], 'contentLocation' => [ 'shape' => 'S3Url', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'capabilities' => [ 'shape' => 'SceneCapabilities', ], 'sceneMetadata' => [ 'shape' => 'SceneMetadataMap', ], 'generatedSceneMetadata' => [ 'shape' => 'GeneratedSceneMetadataMap', ], 'error' => [ 'shape' => 'SceneError', ], ], ], 'GetSyncJobRequest' => [ 'type' => 'structure', 'required' => [ 'syncSource', ], 'members' => [ 'syncSource' => [ 'shape' => 'SyncSource', 'location' => 'uri', 'locationName' => 'syncSource', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'querystring', 'locationName' => 'workspace', ], ], ], 'GetSyncJobResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'workspaceId', 'syncSource', 'syncRole', 'status', 'creationDateTime', 'updateDateTime', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'workspaceId' => [ 'shape' => 'Id', ], 'syncSource' => [ 'shape' => 'SyncSource', ], 'syncRole' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'SyncJobStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetWorkspaceRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'IdOrArn', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'GetWorkspaceResponse' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'arn', 'creationDateTime', 'updateDateTime', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'description' => [ 'shape' => 'Description', ], 'linkedServices' => [ 'shape' => 'LinkedServices', ], 's3Location' => [ 'shape' => 'S3Location', ], 'role' => [ 'shape' => 'RoleArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'GroupType' => [ 'type' => 'string', 'enum' => [ 'TABULAR', ], ], 'Id' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z_0-9][a-zA-Z_\\-0-9]*[a-zA-Z0-9]+', ], 'IdOrArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[a-zA-Z_0-9][a-zA-Z_\\-0-9]*[a-zA-Z0-9]+$|^arn:((aws)|(aws-cn)|(aws-us-gov)):iottwinmaker:[a-z0-9-]+:[0-9]{12}:[\\/a-zA-Z0-9_-]+', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InterpolationParameters' => [ 'type' => 'structure', 'members' => [ 'interpolationType' => [ 'shape' => 'InterpolationType', ], 'intervalInSeconds' => [ 'shape' => 'IntervalInSeconds', ], ], ], 'InterpolationType' => [ 'type' => 'string', 'enum' => [ 'LINEAR', ], ], 'IntervalInSeconds' => [ 'type' => 'long', 'box' => true, ], 'IotSiteWiseSourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'IotSiteWiseSourceConfigurationFilters', ], ], ], 'IotSiteWiseSourceConfigurationFilter' => [ 'type' => 'structure', 'members' => [ 'filterByAssetModel' => [ 'shape' => 'FilterByAssetModel', ], 'filterByAsset' => [ 'shape' => 'FilterByAsset', ], ], 'union' => true, ], 'IotSiteWiseSourceConfigurationFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'IotSiteWiseSourceConfigurationFilter', ], ], 'IotTwinMakerDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'workspace', ], 'members' => [ 'workspace' => [ 'shape' => 'TwinMakerArn', ], ], ], 'IotTwinMakerSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'workspace', ], 'members' => [ 'workspace' => [ 'shape' => 'TwinMakerArn', ], 'filters' => [ 'shape' => 'IotTwinMakerSourceConfigurationFilters', ], ], ], 'IotTwinMakerSourceConfigurationFilter' => [ 'type' => 'structure', 'members' => [ 'filterByComponentType' => [ 'shape' => 'FilterByComponentType', ], 'filterByEntity' => [ 'shape' => 'FilterByEntity', ], ], 'union' => true, ], 'IotTwinMakerSourceConfigurationFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'IotTwinMakerSourceConfigurationFilter', ], ], 'LambdaArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:((aws)|(aws-cn)|(aws-us-gov)):lambda:[a-z0-9-]+:[0-9]{12}:function:[\\/a-zA-Z0-9_-]+', ], 'LambdaFunction' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'LambdaArn', ], ], ], 'LinkedService' => [ 'type' => 'string', 'pattern' => '[a-zA-Z_0-9]+', ], 'LinkedServices' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinkedService', ], ], 'ListComponentTypesFilter' => [ 'type' => 'structure', 'members' => [ 'extendsFrom' => [ 'shape' => 'ComponentTypeId', ], 'namespace' => [ 'shape' => 'String', ], 'isAbstract' => [ 'shape' => 'Boolean', ], ], 'union' => true, ], 'ListComponentTypesFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListComponentTypesFilter', ], ], 'ListComponentTypesRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'filters' => [ 'shape' => 'ListComponentTypesFilters', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListComponentTypesResponse' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'componentTypeSummaries', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', ], 'componentTypeSummaries' => [ 'shape' => 'ComponentTypeSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListComponentsRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'entityId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'entityId' => [ 'shape' => 'EntityId', 'location' => 'uri', 'locationName' => 'entityId', ], 'componentPath' => [ 'shape' => 'ComponentPath', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListComponentsResponse' => [ 'type' => 'structure', 'required' => [ 'componentSummaries', ], 'members' => [ 'componentSummaries' => [ 'shape' => 'ComponentSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEntitiesFilter' => [ 'type' => 'structure', 'members' => [ 'parentEntityId' => [ 'shape' => 'ParentEntityId', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'externalId' => [ 'shape' => 'String', ], ], 'union' => true, ], 'ListEntitiesFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListEntitiesFilter', ], ], 'ListEntitiesRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'filters' => [ 'shape' => 'ListEntitiesFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEntitiesResponse' => [ 'type' => 'structure', 'members' => [ 'entitySummaries' => [ 'shape' => 'EntitySummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMetadataTransferJobsFilter' => [ 'type' => 'structure', 'members' => [ 'workspaceId' => [ 'shape' => 'Id', ], 'state' => [ 'shape' => 'MetadataTransferJobState', ], ], 'union' => true, ], 'ListMetadataTransferJobsFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListMetadataTransferJobsFilter', ], ], 'ListMetadataTransferJobsRequest' => [ 'type' => 'structure', 'required' => [ 'sourceType', 'destinationType', ], 'members' => [ 'sourceType' => [ 'shape' => 'SourceType', ], 'destinationType' => [ 'shape' => 'DestinationType', ], 'filters' => [ 'shape' => 'ListMetadataTransferJobsFilters', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListMetadataTransferJobsResponse' => [ 'type' => 'structure', 'required' => [ 'metadataTransferJobSummaries', ], 'members' => [ 'metadataTransferJobSummaries' => [ 'shape' => 'MetadataTransferJobSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'entityId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'componentName' => [ 'shape' => 'Name', ], 'componentPath' => [ 'shape' => 'ComponentPath', ], 'entityId' => [ 'shape' => 'EntityId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPropertiesResponse' => [ 'type' => 'structure', 'required' => [ 'propertySummaries', ], 'members' => [ 'propertySummaries' => [ 'shape' => 'PropertySummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListScenesRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListScenesResponse' => [ 'type' => 'structure', 'members' => [ 'sceneSummaries' => [ 'shape' => 'SceneSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSyncJobsRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSyncJobsResponse' => [ 'type' => 'structure', 'members' => [ 'syncJobSummaries' => [ 'shape' => 'SyncJobSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSyncResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'syncSource', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'syncSource' => [ 'shape' => 'SyncSource', 'location' => 'uri', 'locationName' => 'syncSource', ], 'filters' => [ 'shape' => 'SyncResourceFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSyncResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'syncResources' => [ 'shape' => 'SyncResourceSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TwinMakerArn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListWorkspacesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListWorkspacesResponse' => [ 'type' => 'structure', 'members' => [ 'workspaceSummaries' => [ 'shape' => 'WorkspaceSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 200, 'min' => 0, ], 'MetadataTransferJobProgress' => [ 'type' => 'structure', 'members' => [ 'totalCount' => [ 'shape' => 'Integer', ], 'succeededCount' => [ 'shape' => 'Integer', ], 'skippedCount' => [ 'shape' => 'Integer', ], 'failedCount' => [ 'shape' => 'Integer', ], ], ], 'MetadataTransferJobState' => [ 'type' => 'string', 'enum' => [ 'VALIDATING', 'PENDING', 'RUNNING', 'CANCELLING', 'ERROR', 'COMPLETED', 'CANCELLED', ], ], 'MetadataTransferJobStatus' => [ 'type' => 'structure', 'members' => [ 'state' => [ 'shape' => 'MetadataTransferJobState', ], 'error' => [ 'shape' => 'ErrorDetails', ], 'queuedPosition' => [ 'shape' => 'Integer', ], ], ], 'MetadataTransferJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetadataTransferJobSummary', ], ], 'MetadataTransferJobSummary' => [ 'type' => 'structure', 'required' => [ 'metadataTransferJobId', 'arn', 'creationDateTime', 'updateDateTime', 'status', ], 'members' => [ 'metadataTransferJobId' => [ 'shape' => 'Id', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'MetadataTransferJobStatus', ], 'progress' => [ 'shape' => 'MetadataTransferJobProgress', ], ], ], 'Name' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z_\\-0-9]+', ], 'NextToken' => [ 'type' => 'string', 'max' => 17880, 'min' => 0, 'pattern' => '.*', ], 'Order' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'OrderBy' => [ 'type' => 'structure', 'required' => [ 'propertyName', ], 'members' => [ 'order' => [ 'shape' => 'Order', ], 'propertyName' => [ 'shape' => 'String', ], ], ], 'OrderByList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrderBy', ], 'max' => 10, 'min' => 1, ], 'OrderByTime' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'ParentEntityId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '\\$ROOT|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}|^[a-zA-Z0-9][a-zA-Z_\\-0-9.:]*[a-zA-Z0-9]+', ], 'ParentEntityUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'updateType', ], 'members' => [ 'updateType' => [ 'shape' => 'ParentEntityUpdateType', ], 'parentEntityId' => [ 'shape' => 'ParentEntityId', ], ], ], 'ParentEntityUpdateType' => [ 'type' => 'string', 'enum' => [ 'UPDATE', 'DELETE', ], ], 'PricingBundles' => [ 'type' => 'list', 'member' => [ 'shape' => 'BundleName', ], 'max' => 10, 'min' => 1, ], 'PricingMode' => [ 'type' => 'string', 'enum' => [ 'BASIC', 'STANDARD', 'TIERED_BUNDLE', ], ], 'PricingPlan' => [ 'type' => 'structure', 'required' => [ 'effectiveDateTime', 'pricingMode', 'updateDateTime', 'updateReason', ], 'members' => [ 'billableEntityCount' => [ 'shape' => 'Long', ], 'bundleInformation' => [ 'shape' => 'BundleInformation', ], 'effectiveDateTime' => [ 'shape' => 'Timestamp', ], 'pricingMode' => [ 'shape' => 'PricingMode', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'updateReason' => [ 'shape' => 'UpdateReason', ], ], ], 'PricingTier' => [ 'type' => 'string', 'enum' => [ 'TIER_1', 'TIER_2', 'TIER_3', 'TIER_4', ], ], 'PropertyDefinitionRequest' => [ 'type' => 'structure', 'members' => [ 'dataType' => [ 'shape' => 'DataType', ], 'isRequiredInEntity' => [ 'shape' => 'Boolean', ], 'isExternalId' => [ 'shape' => 'Boolean', ], 'isStoredExternally' => [ 'shape' => 'Boolean', ], 'isTimeSeries' => [ 'shape' => 'Boolean', ], 'defaultValue' => [ 'shape' => 'DataValue', ], 'configuration' => [ 'shape' => 'Configuration', ], 'displayName' => [ 'shape' => 'PropertyDisplayName', ], ], ], 'PropertyDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'dataType', 'isTimeSeries', 'isRequiredInEntity', 'isExternalId', 'isStoredExternally', 'isImported', 'isFinal', 'isInherited', ], 'members' => [ 'dataType' => [ 'shape' => 'DataType', ], 'isTimeSeries' => [ 'shape' => 'Boolean', ], 'isRequiredInEntity' => [ 'shape' => 'Boolean', ], 'isExternalId' => [ 'shape' => 'Boolean', ], 'isStoredExternally' => [ 'shape' => 'Boolean', ], 'isImported' => [ 'shape' => 'Boolean', ], 'isFinal' => [ 'shape' => 'Boolean', ], 'isInherited' => [ 'shape' => 'Boolean', ], 'defaultValue' => [ 'shape' => 'DataValue', ], 'configuration' => [ 'shape' => 'Configuration', ], 'displayName' => [ 'shape' => 'PropertyDisplayName', ], ], ], 'PropertyDefinitionsRequest' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'PropertyDefinitionRequest', ], ], 'PropertyDefinitionsResponse' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'PropertyDefinitionResponse', ], ], 'PropertyDisplayName' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*[^\\u0000-\\u001F\\u007F]*.*', ], 'PropertyFilter' => [ 'type' => 'structure', 'members' => [ 'propertyName' => [ 'shape' => 'String', ], 'operator' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'DataValue', ], ], ], 'PropertyFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyFilter', ], 'max' => 10, 'min' => 1, ], 'PropertyGroupRequest' => [ 'type' => 'structure', 'members' => [ 'groupType' => [ 'shape' => 'GroupType', ], 'propertyNames' => [ 'shape' => 'PropertyNames', ], ], ], 'PropertyGroupResponse' => [ 'type' => 'structure', 'required' => [ 'groupType', 'propertyNames', 'isInherited', ], 'members' => [ 'groupType' => [ 'shape' => 'GroupType', ], 'propertyNames' => [ 'shape' => 'PropertyNames', ], 'isInherited' => [ 'shape' => 'Boolean', ], ], ], 'PropertyGroupUpdateType' => [ 'type' => 'string', 'enum' => [ 'UPDATE', 'DELETE', 'CREATE', ], ], 'PropertyGroupsRequest' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'PropertyGroupRequest', ], ], 'PropertyGroupsResponse' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'PropertyGroupResponse', ], ], 'PropertyLatestValue' => [ 'type' => 'structure', 'required' => [ 'propertyReference', ], 'members' => [ 'propertyReference' => [ 'shape' => 'EntityPropertyReference', ], 'propertyValue' => [ 'shape' => 'DataValue', ], ], ], 'PropertyLatestValueMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'PropertyLatestValue', ], ], 'PropertyNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'Name', ], ], 'PropertyRequest' => [ 'type' => 'structure', 'members' => [ 'definition' => [ 'shape' => 'PropertyDefinitionRequest', ], 'value' => [ 'shape' => 'DataValue', ], 'updateType' => [ 'shape' => 'PropertyUpdateType', ], ], ], 'PropertyRequests' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'PropertyRequest', ], ], 'PropertyResponse' => [ 'type' => 'structure', 'members' => [ 'definition' => [ 'shape' => 'PropertyDefinitionResponse', ], 'value' => [ 'shape' => 'DataValue', ], 'areAllPropertyValuesReturned' => [ 'shape' => 'Boolean', ], ], ], 'PropertyResponses' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'PropertyResponse', ], ], 'PropertySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertySummary', ], ], 'PropertySummary' => [ 'type' => 'structure', 'required' => [ 'propertyName', ], 'members' => [ 'definition' => [ 'shape' => 'PropertyDefinitionResponse', ], 'propertyName' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'DataValue', ], 'areAllPropertyValuesReturned' => [ 'shape' => 'Boolean', ], ], ], 'PropertyTableValue' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'DataValue', ], ], 'PropertyUpdateType' => [ 'type' => 'string', 'enum' => [ 'UPDATE', 'DELETE', 'CREATE', ], ], 'PropertyValue' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'timestamp' => [ 'shape' => 'Timestamp', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated and will throw an error in the future. Use time instead.', ], 'value' => [ 'shape' => 'DataValue', ], 'time' => [ 'shape' => 'Time', ], ], ], 'PropertyValueEntry' => [ 'type' => 'structure', 'required' => [ 'entityPropertyReference', ], 'members' => [ 'entityPropertyReference' => [ 'shape' => 'EntityPropertyReference', ], 'propertyValues' => [ 'shape' => 'PropertyValues', ], ], ], 'PropertyValueHistory' => [ 'type' => 'structure', 'required' => [ 'entityPropertyReference', ], 'members' => [ 'entityPropertyReference' => [ 'shape' => 'EntityPropertyReference', ], 'values' => [ 'shape' => 'Values', ], ], ], 'PropertyValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyValueHistory', ], ], 'PropertyValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyValue', ], 'max' => 10, 'min' => 1, ], 'QueryResultValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'QueryServiceMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'QueryStatement' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[\\s\\S]+', ], 'QueryTimeoutException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'Relationship' => [ 'type' => 'structure', 'members' => [ 'targetComponentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'relationshipType' => [ 'shape' => 'String', ], ], ], 'RelationshipValue' => [ 'type' => 'structure', 'members' => [ 'targetEntityId' => [ 'shape' => 'EntityId', ], 'targetComponentName' => [ 'shape' => 'Name', ], ], ], 'RequiredProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'Name', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:((aws)|(aws-cn)|(aws-us-gov)):iam::[0-9]{12}:role/.*', ], 'Row' => [ 'type' => 'structure', 'members' => [ 'rowData' => [ 'shape' => 'RowData', ], ], ], 'RowData' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryResultValue', ], ], 'Rows' => [ 'type' => 'list', 'member' => [ 'shape' => 'Row', ], ], 'S3DestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'location', ], 'members' => [ 'location' => [ 'shape' => 'S3DestinationLocation', ], ], ], 'S3DestinationLocation' => [ 'type' => 'string', 'pattern' => '.*(^arn:((aws)|(aws-cn)|(aws-us-gov)):s3:::)([/a-zA-Z0-9_-]+$).*', ], 'S3Location' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*(^arn:((aws)|(aws-cn)|(aws-us-gov)):s3:::)([a-zA-Z0-9_-]+$).*', ], 'S3SourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'location', ], 'members' => [ 'location' => [ 'shape' => 'S3SourceLocation', ], ], ], 'S3SourceLocation' => [ 'type' => 'string', 'pattern' => '.*(^arn:((aws)|(aws-cn)|(aws-us-gov)):s3:::)([a-zA-Z0-9_-]+)\\/([/.a-zA-Z0-9_-]+$).*', ], 'S3Url' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[sS]3://[A-Za-z0-9._/-]+', ], 'SceneCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'SceneCapability', ], 'max' => 50, 'min' => 0, ], 'SceneCapability' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*', ], 'SceneError' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'SceneErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'SceneErrorCode' => [ 'type' => 'string', 'enum' => [ 'MATTERPORT_ERROR', ], ], 'SceneMetadataMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'SceneMetadataValue', ], 'max' => 50, 'min' => 0, ], 'SceneMetadataValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '.*', ], 'SceneSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SceneSummary', ], ], 'SceneSummary' => [ 'type' => 'structure', 'required' => [ 'sceneId', 'contentLocation', 'arn', 'creationDateTime', 'updateDateTime', ], 'members' => [ 'sceneId' => [ 'shape' => 'Id', ], 'contentLocation' => [ 'shape' => 'S3Url', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], ], ], 'Scope' => [ 'type' => 'string', 'enum' => [ 'ENTITY', 'WORKSPACE', ], ], 'SelectedPropertyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 10, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SiteWiseExternalId' => [ 'type' => 'string', 'max' => 128, 'min' => 2, 'pattern' => '.*[a-zA-Z0-9_][a-zA-Z_\\-0-9.:]*[a-zA-Z0-9_]+.*', ], 'SourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'SourceType', ], 's3Configuration' => [ 'shape' => 'S3SourceConfiguration', ], 'iotSiteWiseConfiguration' => [ 'shape' => 'IotSiteWiseSourceConfiguration', ], 'iotTwinMakerConfiguration' => [ 'shape' => 'IotTwinMakerSourceConfiguration', ], ], ], 'SourceConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceConfiguration', ], 'max' => 1, 'min' => 1, ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 's3', 'iotsitewise', 'iottwinmaker', ], ], 'State' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'DELETING', 'ACTIVE', 'ERROR', ], ], 'Status' => [ 'type' => 'structure', 'members' => [ 'state' => [ 'shape' => 'State', ], 'error' => [ 'shape' => 'ErrorDetails', ], ], ], 'String' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'SyncJobState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'INITIALIZING', 'ACTIVE', 'DELETING', 'ERROR', ], ], 'SyncJobStatus' => [ 'type' => 'structure', 'members' => [ 'state' => [ 'shape' => 'SyncJobState', ], 'error' => [ 'shape' => 'ErrorDetails', ], ], ], 'SyncJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SyncJobSummary', ], ], 'SyncJobSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'workspaceId' => [ 'shape' => 'Id', ], 'syncSource' => [ 'shape' => 'SyncSource', ], 'status' => [ 'shape' => 'SyncJobStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'SyncResourceFilter' => [ 'type' => 'structure', 'members' => [ 'state' => [ 'shape' => 'SyncResourceState', ], 'resourceType' => [ 'shape' => 'SyncResourceType', ], 'resourceId' => [ 'shape' => 'Id', ], 'externalId' => [ 'shape' => 'Id', ], ], 'union' => true, ], 'SyncResourceFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'SyncResourceFilter', ], ], 'SyncResourceState' => [ 'type' => 'string', 'enum' => [ 'INITIALIZING', 'PROCESSING', 'DELETED', 'IN_SYNC', 'ERROR', ], ], 'SyncResourceStatus' => [ 'type' => 'structure', 'members' => [ 'state' => [ 'shape' => 'SyncResourceState', ], 'error' => [ 'shape' => 'ErrorDetails', ], ], ], 'SyncResourceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SyncResourceSummary', ], ], 'SyncResourceSummary' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'SyncResourceType', ], 'externalId' => [ 'shape' => 'Id', ], 'resourceId' => [ 'shape' => 'Id', ], 'status' => [ 'shape' => 'SyncResourceStatus', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'SyncResourceType' => [ 'type' => 'string', 'enum' => [ 'ENTITY', 'COMPONENT_TYPE', ], ], 'SyncSource' => [ 'type' => 'string', 'pattern' => '[a-zA-Z_0-9]+', ], 'TabularConditions' => [ 'type' => 'structure', 'members' => [ 'orderBy' => [ 'shape' => 'OrderByList', ], 'propertyFilters' => [ 'shape' => 'PropertyFilters', ], ], ], 'TabularPropertyValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyTableValue', ], ], 'TabularPropertyValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'TabularPropertyValue', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tags', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TwinMakerArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Time' => [ 'type' => 'string', 'max' => 35, 'min' => 20, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TwinMakerArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:((aws)|(aws-cn)|(aws-us-gov)):iottwinmaker:[a-z0-9-]+:[0-9]{12}:[\\/a-zA-Z0-9_\\-\\.:]+', ], 'Type' => [ 'type' => 'string', 'enum' => [ 'RELATIONSHIP', 'STRING', 'LONG', 'BOOLEAN', 'INTEGER', 'DOUBLE', 'LIST', 'MAP', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tagKeys', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TwinMakerArn', 'location' => 'querystring', 'locationName' => 'resourceARN', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateComponentTypeRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'componentTypeId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'isSingleton' => [ 'shape' => 'Boolean', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', 'location' => 'uri', 'locationName' => 'componentTypeId', ], 'description' => [ 'shape' => 'Description', ], 'propertyDefinitions' => [ 'shape' => 'PropertyDefinitionsRequest', ], 'extendsFrom' => [ 'shape' => 'ExtendsFrom', ], 'functions' => [ 'shape' => 'FunctionsRequest', ], 'propertyGroups' => [ 'shape' => 'PropertyGroupsRequest', ], 'componentTypeName' => [ 'shape' => 'ComponentTypeName', ], 'compositeComponentTypes' => [ 'shape' => 'CompositeComponentTypesRequest', ], ], ], 'UpdateComponentTypeResponse' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'arn', 'componentTypeId', 'state', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'state' => [ 'shape' => 'State', ], ], ], 'UpdateEntityRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'entityId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'entityId' => [ 'shape' => 'EntityId', 'location' => 'uri', 'locationName' => 'entityId', ], 'entityName' => [ 'shape' => 'EntityName', ], 'description' => [ 'shape' => 'Description', ], 'componentUpdates' => [ 'shape' => 'ComponentUpdatesMapRequest', ], 'compositeComponentUpdates' => [ 'shape' => 'CompositeComponentUpdatesMapRequest', ], 'parentEntityUpdate' => [ 'shape' => 'ParentEntityUpdateRequest', ], ], ], 'UpdateEntityResponse' => [ 'type' => 'structure', 'required' => [ 'updateDateTime', 'state', ], 'members' => [ 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'state' => [ 'shape' => 'State', ], ], ], 'UpdatePricingPlanRequest' => [ 'type' => 'structure', 'required' => [ 'pricingMode', ], 'members' => [ 'pricingMode' => [ 'shape' => 'PricingMode', ], 'bundleNames' => [ 'shape' => 'PricingBundles', ], ], ], 'UpdatePricingPlanResponse' => [ 'type' => 'structure', 'required' => [ 'currentPricingPlan', ], 'members' => [ 'currentPricingPlan' => [ 'shape' => 'PricingPlan', ], 'pendingPricingPlan' => [ 'shape' => 'PricingPlan', ], ], ], 'UpdateReason' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'PRICING_TIER_UPDATE', 'ENTITY_COUNT_UPDATE', 'PRICING_MODE_UPDATE', 'OVERWRITTEN', ], ], 'UpdateSceneRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'sceneId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'sceneId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'sceneId', ], 'contentLocation' => [ 'shape' => 'S3Url', ], 'description' => [ 'shape' => 'Description', ], 'capabilities' => [ 'shape' => 'SceneCapabilities', ], 'sceneMetadata' => [ 'shape' => 'SceneMetadataMap', ], ], ], 'UpdateSceneResponse' => [ 'type' => 'structure', 'required' => [ 'updateDateTime', ], 'members' => [ 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateWorkspaceRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], 'description' => [ 'shape' => 'Description', ], 'role' => [ 'shape' => 'RoleArn', ], 's3Location' => [ 'shape' => 'S3Location', ], ], ], 'UpdateWorkspaceResponse' => [ 'type' => 'structure', 'required' => [ 'updateDateTime', ], 'members' => [ 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'Uuid' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Value' => [ 'type' => 'string', 'pattern' => '.*', ], 'Values' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyValue', ], ], 'WorkspaceDeleteMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '.*', ], 'WorkspaceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceSummary', ], ], 'WorkspaceSummary' => [ 'type' => 'structure', 'required' => [ 'workspaceId', 'arn', 'creationDateTime', 'updateDateTime', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', ], 'arn' => [ 'shape' => 'TwinMakerArn', ], 'description' => [ 'shape' => 'Description', ], 'linkedServices' => [ 'shape' => 'LinkedServices', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], ],];
