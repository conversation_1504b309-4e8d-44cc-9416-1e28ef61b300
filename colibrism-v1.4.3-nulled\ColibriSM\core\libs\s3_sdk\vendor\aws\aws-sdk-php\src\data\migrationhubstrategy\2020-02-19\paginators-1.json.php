<?php
// This file was auto-generated from sdk-root/src/data/migrationhubstrategy/2020-02-19/paginators-1.json
return [ 'pagination' => [ 'GetServerDetails' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'associatedApplications', ], 'ListAnalyzableServers' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'analyzableServers', ], 'ListApplicationComponents' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'applicationComponentInfos', ], 'ListCollectors' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'Collectors', ], 'ListImportFileTask' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'taskInfos', ], 'ListServers' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'serverInfos', ], ],];
