<?php
// This file was auto-generated from sdk-root/src/data/kafka/2018-11-14/api-2.json
return [ 'metadata' => [ 'apiVersion' => '2018-11-14', 'endpointPrefix' => 'kafka', 'signingName' => 'kafka', 'serviceFullName' => 'Managed Streaming for Kafka', 'serviceAbbreviation' => 'Kafka', 'serviceId' => 'Kafka', 'protocol' => 'rest-json', 'jsonVersion' => '1.1', 'uid' => 'kafka-2018-11-14', 'signatureVersion' => 'v4', ], 'operations' => [ 'BatchAssociateScramSecret' => [ 'name' => 'BatchAssociateScramSecret', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/clusters/{clusterArn}/scram-secrets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchAssociateScramSecretRequest', ], 'output' => [ 'shape' => 'BatchAssociateScramSecretResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateCluster' => [ 'name' => 'CreateCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/clusters', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateClusterRequest', ], 'output' => [ 'shape' => 'CreateClusterResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateClusterV2' => [ 'name' => 'CreateClusterV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/v2/clusters', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateClusterV2Request', ], 'output' => [ 'shape' => 'CreateClusterV2Response', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateConfiguration' => [ 'name' => 'CreateConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfigurationRequest', ], 'output' => [ 'shape' => 'CreateConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateReplicator' => [ 'name' => 'CreateReplicator', 'http' => [ 'method' => 'POST', 'requestUri' => '/replication/v1/replicators', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateReplicatorRequest', ], 'output' => [ 'shape' => 'CreateReplicatorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateVpcConnection' => [ 'name' => 'CreateVpcConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/vpc-connection', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateVpcConnectionRequest', ], 'output' => [ 'shape' => 'CreateVpcConnectionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteCluster' => [ 'name' => 'DeleteCluster', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/clusters/{clusterArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteClusterRequest', ], 'output' => [ 'shape' => 'DeleteClusterResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'DeleteConfiguration' => [ 'name' => 'DeleteConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/configurations/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteConfigurationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'DeleteReplicator' => [ 'name' => 'DeleteReplicator', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/replication/v1/replicators/{replicatorArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteReplicatorRequest', ], 'output' => [ 'shape' => 'DeleteReplicatorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteVpcConnection' => [ 'name' => 'DeleteVpcConnection', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/vpc-connection/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteVpcConnectionRequest', ], 'output' => [ 'shape' => 'DeleteVpcConnectionResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'DescribeCluster' => [ 'name' => 'DescribeCluster', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/clusters/{clusterArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeClusterRequest', ], 'output' => [ 'shape' => 'DescribeClusterResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'DescribeClusterV2' => [ 'name' => 'DescribeClusterV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v2/clusters/{clusterArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeClusterV2Request', ], 'output' => [ 'shape' => 'DescribeClusterV2Response', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'DescribeClusterOperation' => [ 'name' => 'DescribeClusterOperation', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/operations/{clusterOperationArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeClusterOperationRequest', ], 'output' => [ 'shape' => 'DescribeClusterOperationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'DescribeClusterOperationV2' => [ 'name' => 'DescribeClusterOperationV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v2/operations/{clusterOperationArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeClusterOperationV2Request', ], 'output' => [ 'shape' => 'DescribeClusterOperationV2Response', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeConfiguration' => [ 'name' => 'DescribeConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/configurations/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeConfigurationRevision' => [ 'name' => 'DescribeConfigurationRevision', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/configurations/{arn}/revisions/{revision}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeConfigurationRevisionRequest', ], 'output' => [ 'shape' => 'DescribeConfigurationRevisionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeReplicator' => [ 'name' => 'DescribeReplicator', 'http' => [ 'method' => 'GET', 'requestUri' => '/replication/v1/replicators/{replicatorArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeReplicatorRequest', ], 'output' => [ 'shape' => 'DescribeReplicatorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeVpcConnection' => [ 'name' => 'DescribeVpcConnection', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/vpc-connection/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeVpcConnectionRequest', ], 'output' => [ 'shape' => 'DescribeVpcConnectionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'BatchDisassociateScramSecret' => [ 'name' => 'BatchDisassociateScramSecret', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v1/clusters/{clusterArn}/scram-secrets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDisassociateScramSecretRequest', ], 'output' => [ 'shape' => 'BatchDisassociateScramSecretResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetBootstrapBrokers' => [ 'name' => 'GetBootstrapBrokers', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/clusters/{clusterArn}/bootstrap-brokers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBootstrapBrokersRequest', ], 'output' => [ 'shape' => 'GetBootstrapBrokersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'GetCompatibleKafkaVersions' => [ 'name' => 'GetCompatibleKafkaVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/compatible-kafka-versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCompatibleKafkaVersionsRequest', ], 'output' => [ 'shape' => 'GetCompatibleKafkaVersionsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListClusterOperations' => [ 'name' => 'ListClusterOperations', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/clusters/{clusterArn}/operations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListClusterOperationsRequest', ], 'output' => [ 'shape' => 'ListClusterOperationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'ListClusterOperationsV2' => [ 'name' => 'ListClusterOperationsV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v2/clusters/{clusterArn}/operations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListClusterOperationsV2Request', ], 'output' => [ 'shape' => 'ListClusterOperationsV2Response', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListClusters' => [ 'name' => 'ListClusters', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/clusters', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListClustersRequest', ], 'output' => [ 'shape' => 'ListClustersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'ListClustersV2' => [ 'name' => 'ListClustersV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v2/clusters', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListClustersV2Request', ], 'output' => [ 'shape' => 'ListClustersV2Response', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'ListConfigurationRevisions' => [ 'name' => 'ListConfigurationRevisions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/configurations/{arn}/revisions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfigurationRevisionsRequest', ], 'output' => [ 'shape' => 'ListConfigurationRevisionsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListConfigurations' => [ 'name' => 'ListConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfigurationsRequest', ], 'output' => [ 'shape' => 'ListConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'ListKafkaVersions' => [ 'name' => 'ListKafkaVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/kafka-versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListKafkaVersionsRequest', ], 'output' => [ 'shape' => 'ListKafkaVersionsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'ListNodes' => [ 'name' => 'ListNodes', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/clusters/{clusterArn}/nodes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNodesRequest', ], 'output' => [ 'shape' => 'ListNodesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'ListReplicators' => [ 'name' => 'ListReplicators', 'http' => [ 'method' => 'GET', 'requestUri' => '/replication/v1/replicators', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReplicatorsRequest', ], 'output' => [ 'shape' => 'ListReplicatorsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListScramSecrets' => [ 'name' => 'ListScramSecrets', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/clusters/{clusterArn}/scram-secrets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListScramSecretsRequest', ], 'output' => [ 'shape' => 'ListScramSecretsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListClientVpcConnections' => [ 'name' => 'ListClientVpcConnections', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/clusters/{clusterArn}/client-vpc-connections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListClientVpcConnectionsRequest', ], 'output' => [ 'shape' => 'ListClientVpcConnectionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'ListVpcConnections' => [ 'name' => 'ListVpcConnections', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/vpc-connections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVpcConnectionsRequest', ], 'output' => [ 'shape' => 'ListVpcConnectionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'RejectClientVpcConnection' => [ 'name' => 'RejectClientVpcConnection', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/clusters/{clusterArn}/client-vpc-connection', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RejectClientVpcConnectionRequest', ], 'output' => [ 'shape' => 'RejectClientVpcConnectionResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'DeleteClusterPolicy' => [ 'name' => 'DeleteClusterPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/clusters/{clusterArn}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteClusterPolicyRequest', ], 'output' => [ 'shape' => 'DeleteClusterPolicyResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'GetClusterPolicy' => [ 'name' => 'GetClusterPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/clusters/{clusterArn}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetClusterPolicyRequest', ], 'output' => [ 'shape' => 'GetClusterPolicyResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'PutClusterPolicy' => [ 'name' => 'PutClusterPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/clusters/{clusterArn}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutClusterPolicyRequest', ], 'output' => [ 'shape' => 'PutClusterPolicyResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'RebootBroker' => [ 'name' => 'RebootBroker', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/clusters/{clusterArn}/reboot-broker', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RebootBrokerRequest', ], 'output' => [ 'shape' => 'RebootBrokerResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdateBrokerCount' => [ 'name' => 'UpdateBrokerCount', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/clusters/{clusterArn}/nodes/count', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBrokerCountRequest', ], 'output' => [ 'shape' => 'UpdateBrokerCountResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'UpdateBrokerType' => [ 'name' => 'UpdateBrokerType', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/clusters/{clusterArn}/nodes/type', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBrokerTypeRequest', ], 'output' => [ 'shape' => 'UpdateBrokerTypeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateBrokerStorage' => [ 'name' => 'UpdateBrokerStorage', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/clusters/{clusterArn}/nodes/storage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBrokerStorageRequest', ], 'output' => [ 'shape' => 'UpdateBrokerStorageResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'UpdateConfiguration' => [ 'name' => 'UpdateConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/configurations/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'UpdateClusterConfiguration' => [ 'name' => 'UpdateClusterConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/clusters/{clusterArn}/configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateClusterConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateClusterConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateClusterKafkaVersion' => [ 'name' => 'UpdateClusterKafkaVersion', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/clusters/{clusterArn}/version', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateClusterKafkaVersionRequest', ], 'output' => [ 'shape' => 'UpdateClusterKafkaVersionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateConnectivity' => [ 'name' => 'UpdateConnectivity', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/clusters/{clusterArn}/connectivity', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConnectivityRequest', ], 'output' => [ 'shape' => 'UpdateConnectivityResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateMonitoring' => [ 'name' => 'UpdateMonitoring', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/clusters/{clusterArn}/monitoring', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMonitoringRequest', ], 'output' => [ 'shape' => 'UpdateMonitoringResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'UpdateReplicationInfo' => [ 'name' => 'UpdateReplicationInfo', 'http' => [ 'method' => 'PUT', 'requestUri' => '/replication/v1/replicators/{replicatorArn}/replication-info', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateReplicationInfoRequest', ], 'output' => [ 'shape' => 'UpdateReplicationInfoResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateSecurity' => [ 'name' => 'UpdateSecurity', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v1/clusters/{clusterArn}/security', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSecurityRequest', ], 'output' => [ 'shape' => 'UpdateSecurityResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateStorage' => [ 'name' => 'UpdateStorage', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/clusters/{clusterArn}/storage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateStorageRequest', ], 'output' => [ 'shape' => 'UpdateStorageResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], ], 'shapes' => [ 'AmazonMskCluster' => [ 'type' => 'structure', 'members' => [ 'MskClusterArn' => [ 'shape' => '__string', 'locationName' => 'mskClusterArn', ], ], 'required' => [ 'MskClusterArn', ], ], 'BatchAssociateScramSecretRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'SecretArnList' => [ 'shape' => '__listOf__string', 'locationName' => 'secretArnList', ], ], 'required' => [ 'ClusterArn', 'SecretArnList', ], ], 'BatchAssociateScramSecretResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'UnprocessedScramSecrets' => [ 'shape' => '__listOfUnprocessedScramSecret', 'locationName' => 'unprocessedScramSecrets', ], ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'InvalidParameter' => [ 'shape' => '__string', 'locationName' => 'invalidParameter', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 400, ], ], 'BrokerAZDistribution' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', ], ], 'BrokerEBSVolumeInfo' => [ 'type' => 'structure', 'members' => [ 'KafkaBrokerNodeId' => [ 'shape' => '__string', 'locationName' => 'kafkaBrokerNodeId', ], 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughput', 'locationName' => 'provisionedThroughput', ], 'VolumeSizeGB' => [ 'shape' => '__integer', 'locationName' => 'volumeSizeGB', ], ], 'required' => [ 'KafkaBrokerNodeId', ], ], 'BrokerLogs' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogs' => [ 'shape' => 'CloudWatchLogs', 'locationName' => 'cloudWatchLogs', ], 'Firehose' => [ 'shape' => 'Firehose', 'locationName' => 'firehose', ], 'S3' => [ 'shape' => 'S3', 'locationName' => 's3', ], ], ], 'BrokerNodeGroupInfo' => [ 'type' => 'structure', 'members' => [ 'BrokerAZDistribution' => [ 'shape' => 'BrokerAZDistribution', 'locationName' => 'brokerAZDistribution', ], 'ClientSubnets' => [ 'shape' => '__listOf__string', 'locationName' => 'clientSubnets', ], 'InstanceType' => [ 'shape' => '__stringMin5Max32', 'locationName' => 'instanceType', ], 'SecurityGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'securityGroups', ], 'StorageInfo' => [ 'shape' => 'StorageInfo', 'locationName' => 'storageInfo', ], 'ConnectivityInfo' => [ 'shape' => 'ConnectivityInfo', 'locationName' => 'connectivityInfo', ], 'ZoneIds' => [ 'shape' => '__listOf__string', 'locationName' => 'zoneIds', ], ], 'required' => [ 'ClientSubnets', 'InstanceType', ], ], 'BrokerNodeInfo' => [ 'type' => 'structure', 'members' => [ 'AttachedENIId' => [ 'shape' => '__string', 'locationName' => 'attachedENIId', ], 'BrokerId' => [ 'shape' => '__double', 'locationName' => 'brokerId', ], 'ClientSubnet' => [ 'shape' => '__string', 'locationName' => 'clientSubnet', ], 'ClientVpcIpAddress' => [ 'shape' => '__string', 'locationName' => 'clientVpcIpAddress', ], 'CurrentBrokerSoftwareInfo' => [ 'shape' => 'BrokerSoftwareInfo', 'locationName' => 'currentBrokerSoftwareInfo', ], 'Endpoints' => [ 'shape' => '__listOf__string', 'locationName' => 'endpoints', ], ], ], 'BrokerSoftwareInfo' => [ 'type' => 'structure', 'members' => [ 'ConfigurationArn' => [ 'shape' => '__string', 'locationName' => 'configurationArn', ], 'ConfigurationRevision' => [ 'shape' => '__long', 'locationName' => 'configurationRevision', ], 'KafkaVersion' => [ 'shape' => '__string', 'locationName' => 'kafkaVersion', ], ], ], 'ClientAuthentication' => [ 'type' => 'structure', 'members' => [ 'Sasl' => [ 'shape' => 'Sasl', 'locationName' => 'sasl', ], 'Tls' => [ 'shape' => 'Tls', 'locationName' => 'tls', ], 'Unauthenticated' => [ 'shape' => 'Unauthenticated', 'locationName' => 'unauthenticated', ], ], ], 'VpcConnectivityClientAuthentication' => [ 'type' => 'structure', 'members' => [ 'Sasl' => [ 'shape' => 'VpcConnectivitySasl', 'locationName' => 'sasl', ], 'Tls' => [ 'shape' => 'VpcConnectivityTls', 'locationName' => 'tls', ], ], ], 'ClientBroker' => [ 'type' => 'string', 'enum' => [ 'TLS', 'TLS_PLAINTEXT', 'PLAINTEXT', ], ], 'CloudWatchLogs' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => '__boolean', 'locationName' => 'enabled', ], 'LogGroup' => [ 'shape' => '__string', 'locationName' => 'logGroup', ], ], 'required' => [ 'Enabled', ], ], 'ClusterInfo' => [ 'type' => 'structure', 'members' => [ 'ActiveOperationArn' => [ 'shape' => '__string', 'locationName' => 'activeOperationArn', ], 'BrokerNodeGroupInfo' => [ 'shape' => 'BrokerNodeGroupInfo', 'locationName' => 'brokerNodeGroupInfo', ], 'ClientAuthentication' => [ 'shape' => 'ClientAuthentication', 'locationName' => 'clientAuthentication', ], 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterName' => [ 'shape' => '__string', 'locationName' => 'clusterName', ], 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'CurrentBrokerSoftwareInfo' => [ 'shape' => 'BrokerSoftwareInfo', 'locationName' => 'currentBrokerSoftwareInfo', ], 'LoggingInfo' => [ 'shape' => 'LoggingInfo', 'locationName' => 'loggingInfo', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'EncryptionInfo' => [ 'shape' => 'EncryptionInfo', 'locationName' => 'encryptionInfo', ], 'EnhancedMonitoring' => [ 'shape' => 'EnhancedMonitoring', 'locationName' => 'enhancedMonitoring', ], 'NumberOfBrokerNodes' => [ 'shape' => '__integer', 'locationName' => 'numberOfBrokerNodes', ], 'OpenMonitoring' => [ 'shape' => 'OpenMonitoring', 'locationName' => 'openMonitoring', ], 'State' => [ 'shape' => 'ClusterState', 'locationName' => 'state', ], 'StateInfo' => [ 'shape' => 'StateInfo', 'locationName' => 'stateInfo', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'ZookeeperConnectString' => [ 'shape' => '__string', 'locationName' => 'zookeeperConnectString', ], 'ZookeeperConnectStringTls' => [ 'shape' => '__string', 'locationName' => 'zookeeperConnectStringTls', ], 'StorageMode' => [ 'shape' => 'StorageMode', 'locationName' => 'storageMode', ], 'CustomerActionStatus' => [ 'shape' => 'CustomerActionStatus', 'locationName' => 'customerActionStatus', ], ], ], 'ClusterOperationInfo' => [ 'type' => 'structure', 'members' => [ 'ClientRequestId' => [ 'shape' => '__string', 'locationName' => 'clientRequestId', ], 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'EndTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'endTime', ], 'ErrorInfo' => [ 'shape' => 'ErrorInfo', 'locationName' => 'errorInfo', ], 'OperationSteps' => [ 'shape' => '__listOfClusterOperationStep', 'locationName' => 'operationSteps', ], 'OperationArn' => [ 'shape' => '__string', 'locationName' => 'operationArn', ], 'OperationState' => [ 'shape' => '__string', 'locationName' => 'operationState', ], 'OperationType' => [ 'shape' => '__string', 'locationName' => 'operationType', ], 'SourceClusterInfo' => [ 'shape' => 'MutableClusterInfo', 'locationName' => 'sourceClusterInfo', ], 'TargetClusterInfo' => [ 'shape' => 'MutableClusterInfo', 'locationName' => 'targetClusterInfo', ], 'VpcConnectionInfo' => [ 'shape' => 'VpcConnectionInfo', 'locationName' => 'vpcConnectionInfo', ], ], ], 'ClusterOperationStep' => [ 'type' => 'structure', 'members' => [ 'StepInfo' => [ 'shape' => 'ClusterOperationStepInfo', 'locationName' => 'stepInfo', ], 'StepName' => [ 'shape' => '__string', 'locationName' => 'stepName', ], ], ], 'ClusterOperationStepInfo' => [ 'type' => 'structure', 'members' => [ 'StepStatus' => [ 'shape' => '__string', 'locationName' => 'stepStatus', ], ], ], 'ClusterOperationV2' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterType' => [ 'shape' => 'ClusterType', 'locationName' => 'clusterType', ], 'StartTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'startTime', ], 'EndTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'endTime', ], 'OperationArn' => [ 'shape' => '__string', 'locationName' => 'operationArn', ], 'OperationState' => [ 'shape' => '__string', 'locationName' => 'operationState', ], 'OperationType' => [ 'shape' => '__string', 'locationName' => 'operationType', ], 'Provisioned' => [ 'shape' => 'ClusterOperationV2Provisioned', 'locationName' => 'provisioned', ], 'Serverless' => [ 'shape' => 'ClusterOperationV2Serverless', 'locationName' => 'serverless', ], ], ], 'ClusterOperationV2Provisioned' => [ 'type' => 'structure', 'members' => [ 'OperationSteps' => [ 'shape' => '__listOfClusterOperationStep', 'locationName' => 'operationSteps', ], 'SourceClusterInfo' => [ 'shape' => 'MutableClusterInfo', 'locationName' => 'sourceClusterInfo', ], 'TargetClusterInfo' => [ 'shape' => 'MutableClusterInfo', 'locationName' => 'targetClusterInfo', ], 'VpcConnectionInfo' => [ 'shape' => 'VpcConnectionInfo', 'locationName' => 'vpcConnectionInfo', ], ], ], 'ClusterOperationV2Serverless' => [ 'type' => 'structure', 'members' => [ 'VpcConnectionInfo' => [ 'shape' => 'VpcConnectionInfoServerless', 'locationName' => 'vpcConnectionInfo', ], ], ], 'ClusterOperationV2Summary' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterType' => [ 'shape' => 'ClusterType', 'locationName' => 'clusterType', ], 'StartTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'startTime', ], 'EndTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'endTime', ], 'OperationArn' => [ 'shape' => '__string', 'locationName' => 'operationArn', ], 'OperationState' => [ 'shape' => '__string', 'locationName' => 'operationState', ], 'OperationType' => [ 'shape' => '__string', 'locationName' => 'operationType', ], ], ], 'ClusterState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATING', 'DELETING', 'FAILED', 'HEALING', 'MAINTENANCE', 'REBOOTING_BROKER', 'UPDATING', ], ], 'ClientVpcConnection' => [ 'type' => 'structure', 'members' => [ 'Authentication' => [ 'shape' => '__string', 'locationName' => 'authentication', ], 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'State' => [ 'shape' => 'VpcConnectionState', 'locationName' => 'state', ], 'VpcConnectionArn' => [ 'shape' => '__string', 'locationName' => 'vpcConnectionArn', ], 'Owner' => [ 'shape' => '__string', 'locationName' => 'owner', ], ], 'required' => [ 'VpcConnectionArn', ], ], 'VpcConnection' => [ 'type' => 'structure', 'members' => [ 'VpcConnectionArn' => [ 'shape' => '__string', 'locationName' => 'vpcConnectionArn', ], 'TargetClusterArn' => [ 'shape' => '__string', 'locationName' => 'targetClusterArn', ], 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'Authentication' => [ 'shape' => '__string', 'locationName' => 'authentication', ], 'VpcId' => [ 'shape' => '__string', 'locationName' => 'vpcId', ], 'State' => [ 'shape' => 'VpcConnectionState', 'locationName' => 'state', ], ], 'required' => [ 'VpcConnectionArn', 'TargetClusterArn', ], ], 'CompatibleKafkaVersion' => [ 'type' => 'structure', 'members' => [ 'SourceVersion' => [ 'shape' => '__string', 'locationName' => 'sourceVersion', ], 'TargetVersions' => [ 'shape' => '__listOf__string', 'locationName' => 'targetVersions', ], ], ], 'Configuration' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'KafkaVersions' => [ 'shape' => '__listOf__string', 'locationName' => 'kafkaVersions', ], 'LatestRevision' => [ 'shape' => 'ConfigurationRevision', 'locationName' => 'latestRevision', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'State' => [ 'shape' => 'ConfigurationState', 'locationName' => 'state', ], ], 'required' => [ 'Description', 'LatestRevision', 'CreationTime', 'KafkaVersions', 'Arn', 'Name', 'State', ], ], 'ConfigurationInfo' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Revision' => [ 'shape' => '__long', 'locationName' => 'revision', ], ], 'required' => [ 'Revision', 'Arn', ], ], 'ConfigurationRevision' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'Revision' => [ 'shape' => '__long', 'locationName' => 'revision', ], ], 'required' => [ 'Revision', 'CreationTime', ], ], 'ConfigurationState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DELETING', 'DELETE_FAILED', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'InvalidParameter' => [ 'shape' => '__string', 'locationName' => 'invalidParameter', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 409, ], ], 'ConnectivityInfo' => [ 'type' => 'structure', 'members' => [ 'PublicAccess' => [ 'shape' => 'PublicAccess', 'locationName' => 'publicAccess', ], 'VpcConnectivity' => [ 'shape' => 'VpcConnectivity', 'locationName' => 'vpcConnectivity', ], ], ], 'ConsumerGroupReplication' => [ 'type' => 'structure', 'members' => [ 'ConsumerGroupsToExclude' => [ 'shape' => '__listOf__stringMax256', 'locationName' => 'consumerGroupsToExclude', ], 'ConsumerGroupsToReplicate' => [ 'shape' => '__listOf__stringMax256', 'locationName' => 'consumerGroupsToReplicate', ], 'DetectAndCopyNewConsumerGroups' => [ 'shape' => '__boolean', 'locationName' => 'detectAndCopyNewConsumerGroups', ], 'SynchroniseConsumerGroupOffsets' => [ 'shape' => '__boolean', 'locationName' => 'synchroniseConsumerGroupOffsets', ], ], 'required' => [ 'ConsumerGroupsToReplicate', ], ], 'ConsumerGroupReplicationUpdate' => [ 'type' => 'structure', 'members' => [ 'ConsumerGroupsToExclude' => [ 'shape' => '__listOf__stringMax256', 'locationName' => 'consumerGroupsToExclude', ], 'ConsumerGroupsToReplicate' => [ 'shape' => '__listOf__stringMax256', 'locationName' => 'consumerGroupsToReplicate', ], 'DetectAndCopyNewConsumerGroups' => [ 'shape' => '__boolean', 'locationName' => 'detectAndCopyNewConsumerGroups', ], 'SynchroniseConsumerGroupOffsets' => [ 'shape' => '__boolean', 'locationName' => 'synchroniseConsumerGroupOffsets', ], ], 'required' => [ 'ConsumerGroupsToReplicate', 'ConsumerGroupsToExclude', 'SynchroniseConsumerGroupOffsets', 'DetectAndCopyNewConsumerGroups', ], ], 'CreateClusterRequest' => [ 'type' => 'structure', 'members' => [ 'BrokerNodeGroupInfo' => [ 'shape' => 'BrokerNodeGroupInfo', 'locationName' => 'brokerNodeGroupInfo', ], 'ClientAuthentication' => [ 'shape' => 'ClientAuthentication', 'locationName' => 'clientAuthentication', ], 'ClusterName' => [ 'shape' => '__stringMin1Max64', 'locationName' => 'clusterName', ], 'ConfigurationInfo' => [ 'shape' => 'ConfigurationInfo', 'locationName' => 'configurationInfo', ], 'EncryptionInfo' => [ 'shape' => 'EncryptionInfo', 'locationName' => 'encryptionInfo', ], 'EnhancedMonitoring' => [ 'shape' => 'EnhancedMonitoring', 'locationName' => 'enhancedMonitoring', ], 'KafkaVersion' => [ 'shape' => '__stringMin1Max128', 'locationName' => 'kafkaVersion', ], 'LoggingInfo' => [ 'shape' => 'LoggingInfo', 'locationName' => 'loggingInfo', ], 'NumberOfBrokerNodes' => [ 'shape' => '__integerMin1Max15', 'locationName' => 'numberOfBrokerNodes', ], 'OpenMonitoring' => [ 'shape' => 'OpenMonitoringInfo', 'locationName' => 'openMonitoring', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'StorageMode' => [ 'shape' => 'StorageMode', 'locationName' => 'storageMode', ], ], 'required' => [ 'BrokerNodeGroupInfo', 'KafkaVersion', 'NumberOfBrokerNodes', 'ClusterName', ], ], 'CreateClusterResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterName' => [ 'shape' => '__string', 'locationName' => 'clusterName', ], 'State' => [ 'shape' => 'ClusterState', 'locationName' => 'state', ], ], ], 'CreateConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'KafkaVersions' => [ 'shape' => '__listOf__string', 'locationName' => 'kafkaVersions', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'ServerProperties' => [ 'shape' => '__blob', 'locationName' => 'serverProperties', ], ], 'required' => [ 'ServerProperties', 'Name', ], ], 'CreateConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'LatestRevision' => [ 'shape' => 'ConfigurationRevision', 'locationName' => 'latestRevision', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'State' => [ 'shape' => 'ConfigurationState', 'locationName' => 'state', ], ], ], 'CreateReplicatorRequest' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMax1024', 'locationName' => 'description', ], 'KafkaClusters' => [ 'shape' => '__listOfKafkaCluster', 'locationName' => 'kafkaClusters', ], 'ReplicationInfoList' => [ 'shape' => '__listOfReplicationInfo', 'locationName' => 'replicationInfoList', ], 'ReplicatorName' => [ 'shape' => '__stringMin1Max128Pattern09AZaZ09AZaZ0', 'locationName' => 'replicatorName', ], 'ServiceExecutionRoleArn' => [ 'shape' => '__string', 'locationName' => 'serviceExecutionRoleArn', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], 'required' => [ 'ServiceExecutionRoleArn', 'ReplicatorName', 'ReplicationInfoList', 'KafkaClusters', ], ], 'CreateReplicatorResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicatorArn' => [ 'shape' => '__string', 'locationName' => 'replicatorArn', ], 'ReplicatorName' => [ 'shape' => '__string', 'locationName' => 'replicatorName', ], 'ReplicatorState' => [ 'shape' => 'ReplicatorState', 'locationName' => 'replicatorState', ], ], ], 'CreateVpcConnectionRequest' => [ 'type' => 'structure', 'members' => [ 'TargetClusterArn' => [ 'shape' => '__string', 'locationName' => 'targetClusterArn', ], 'Authentication' => [ 'shape' => '__string', 'locationName' => 'authentication', ], 'VpcId' => [ 'shape' => '__string', 'locationName' => 'vpcId', ], 'ClientSubnets' => [ 'shape' => '__listOf__string', 'locationName' => 'clientSubnets', ], 'SecurityGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'securityGroups', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', 'documentation' => ' <p>A map of tags for the VPC connection.</p> ', ], ], 'required' => [ 'TargetClusterArn', 'Authentication', 'VpcId', 'ClientSubnets', 'SecurityGroups', ], ], 'CreateVpcConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'VpcConnectionArn' => [ 'shape' => '__string', 'locationName' => 'vpcConnectionArn', ], 'State' => [ 'shape' => 'VpcConnectionState', 'locationName' => 'state', ], 'Authentication' => [ 'shape' => '__string', 'locationName' => 'authentication', ], 'VpcId' => [ 'shape' => '__string', 'locationName' => 'vpcId', ], 'ClientSubnets' => [ 'shape' => '__listOf__string', 'locationName' => 'clientSubnets', ], 'SecurityGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'securityGroups', ], 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', 'documentation' => ' <p>A map of tags that you want the vpc connection to have.</p> ', ], ], ], 'DeleteClusterRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'CurrentVersion' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'currentVersion', ], ], 'required' => [ 'ClusterArn', ], ], 'DeleteClusterResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'State' => [ 'shape' => 'ClusterState', 'locationName' => 'state', ], ], ], 'DeleteConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'arn', ], ], 'required' => [ 'Arn', ], ], 'DeleteConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'State' => [ 'shape' => 'ConfigurationState', 'locationName' => 'state', ], ], ], 'DeleteReplicatorRequest' => [ 'type' => 'structure', 'members' => [ 'CurrentVersion' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'currentVersion', ], 'ReplicatorArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'replicatorArn', ], ], 'required' => [ 'ReplicatorArn', ], ], 'DeleteReplicatorResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicatorArn' => [ 'shape' => '__string', 'locationName' => 'replicatorArn', ], 'ReplicatorState' => [ 'shape' => 'ReplicatorState', 'locationName' => 'replicatorState', ], ], ], 'DeleteVpcConnectionRequest' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'arn', ], ], 'required' => [ 'Arn', ], ], 'DeleteVpcConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'VpcConnectionArn' => [ 'shape' => '__string', 'locationName' => 'vpcConnectionArn', ], 'State' => [ 'shape' => 'VpcConnectionState', 'locationName' => 'state', ], ], ], 'DescribeClusterOperationRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterOperationArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterOperationArn', ], ], 'required' => [ 'ClusterOperationArn', ], ], 'DescribeClusterOperationV2Request' => [ 'type' => 'structure', 'members' => [ 'ClusterOperationArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterOperationArn', ], ], 'required' => [ 'ClusterOperationArn', ], ], 'DescribeClusterOperationResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterOperationInfo' => [ 'shape' => 'ClusterOperationInfo', 'locationName' => 'clusterOperationInfo', ], ], ], 'DescribeClusterOperationV2Response' => [ 'type' => 'structure', 'members' => [ 'ClusterOperationInfo' => [ 'shape' => 'ClusterOperationV2', 'locationName' => 'clusterOperationInfo', ], ], ], 'DescribeClusterRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], ], 'required' => [ 'ClusterArn', ], ], 'DescribeClusterResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterInfo' => [ 'shape' => 'ClusterInfo', 'locationName' => 'clusterInfo', ], ], ], 'DescribeConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'arn', ], ], 'required' => [ 'Arn', ], ], 'DescribeConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'KafkaVersions' => [ 'shape' => '__listOf__string', 'locationName' => 'kafkaVersions', ], 'LatestRevision' => [ 'shape' => 'ConfigurationRevision', 'locationName' => 'latestRevision', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'State' => [ 'shape' => 'ConfigurationState', 'locationName' => 'state', ], ], ], 'DescribeConfigurationRevisionRequest' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'arn', ], 'Revision' => [ 'shape' => '__long', 'location' => 'uri', 'locationName' => 'revision', ], ], 'required' => [ 'Revision', 'Arn', ], ], 'DescribeConfigurationRevisionResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'Revision' => [ 'shape' => '__long', 'locationName' => 'revision', ], 'ServerProperties' => [ 'shape' => '__blob', 'locationName' => 'serverProperties', ], ], ], 'DescribeReplicatorRequest' => [ 'type' => 'structure', 'members' => [ 'ReplicatorArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'replicatorArn', ], ], 'required' => [ 'ReplicatorArn', ], ], 'DescribeReplicatorResponse' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'IsReplicatorReference' => [ 'shape' => '__boolean', 'locationName' => 'isReplicatorReference', ], 'KafkaClusters' => [ 'shape' => '__listOfKafkaClusterDescription', 'locationName' => 'kafkaClusters', ], 'ReplicationInfoList' => [ 'shape' => '__listOfReplicationInfoDescription', 'locationName' => 'replicationInfoList', ], 'ReplicatorArn' => [ 'shape' => '__string', 'locationName' => 'replicatorArn', ], 'ReplicatorDescription' => [ 'shape' => '__string', 'locationName' => 'replicatorDescription', ], 'ReplicatorName' => [ 'shape' => '__string', 'locationName' => 'replicatorName', ], 'ReplicatorResourceArn' => [ 'shape' => '__string', 'locationName' => 'replicatorResourceArn', ], 'ReplicatorState' => [ 'shape' => 'ReplicatorState', 'locationName' => 'replicatorState', ], 'ServiceExecutionRoleArn' => [ 'shape' => '__string', 'locationName' => 'serviceExecutionRoleArn', ], 'StateInfo' => [ 'shape' => 'ReplicationStateInfo', 'locationName' => 'stateInfo', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'DescribeVpcConnectionRequest' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'arn', ], ], 'required' => [ 'Arn', ], ], 'DescribeVpcConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'VpcConnectionArn' => [ 'shape' => '__string', 'locationName' => 'vpcConnectionArn', ], 'TargetClusterArn' => [ 'shape' => '__string', 'locationName' => 'targetClusterArn', ], 'State' => [ 'shape' => 'VpcConnectionState', 'locationName' => 'state', ], 'Authentication' => [ 'shape' => '__string', 'locationName' => 'authentication', ], 'VpcId' => [ 'shape' => '__string', 'locationName' => 'vpcId', ], 'Subnets' => [ 'shape' => '__listOf__string', 'locationName' => 'subnets', ], 'SecurityGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'securityGroups', ], 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', 'documentation' => ' <p>A map of tags that you want the vpc connection to have.</p> ', ], ], ], 'BatchDisassociateScramSecretRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'SecretArnList' => [ 'shape' => '__listOf__string', 'locationName' => 'secretArnList', ], ], 'required' => [ 'ClusterArn', 'SecretArnList', ], ], 'BatchDisassociateScramSecretResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'UnprocessedScramSecrets' => [ 'shape' => '__listOfUnprocessedScramSecret', 'locationName' => 'unprocessedScramSecrets', ], ], ], 'EBSStorageInfo' => [ 'type' => 'structure', 'members' => [ 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughput', 'locationName' => 'provisionedThroughput', ], 'VolumeSize' => [ 'shape' => '__integerMin1Max16384', 'locationName' => 'volumeSize', ], ], ], 'EncryptionAtRest' => [ 'type' => 'structure', 'members' => [ 'DataVolumeKMSKeyId' => [ 'shape' => '__string', 'locationName' => 'dataVolumeKMSKeyId', ], ], 'required' => [ 'DataVolumeKMSKeyId', ], ], 'EncryptionInTransit' => [ 'type' => 'structure', 'members' => [ 'ClientBroker' => [ 'shape' => 'ClientBroker', 'locationName' => 'clientBroker', ], 'InCluster' => [ 'shape' => '__boolean', 'locationName' => 'inCluster', ], ], ], 'EncryptionInfo' => [ 'type' => 'structure', 'members' => [ 'EncryptionAtRest' => [ 'shape' => 'EncryptionAtRest', 'locationName' => 'encryptionAtRest', ], 'EncryptionInTransit' => [ 'shape' => 'EncryptionInTransit', 'locationName' => 'encryptionInTransit', ], ], ], 'EnhancedMonitoring' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'PER_BROKER', 'PER_TOPIC_PER_BROKER', 'PER_TOPIC_PER_PARTITION', ], ], 'Error' => [ 'type' => 'structure', 'members' => [ 'InvalidParameter' => [ 'shape' => '__string', 'locationName' => 'invalidParameter', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'Firehose' => [ 'type' => 'structure', 'members' => [ 'DeliveryStream' => [ 'shape' => '__string', 'locationName' => 'deliveryStream', ], 'Enabled' => [ 'shape' => '__boolean', 'locationName' => 'enabled', ], ], 'required' => [ 'Enabled', ], ], 'ErrorInfo' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => '__string', 'locationName' => 'errorCode', ], 'ErrorString' => [ 'shape' => '__string', 'locationName' => 'errorString', ], ], ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'InvalidParameter' => [ 'shape' => '__string', 'locationName' => 'invalidParameter', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 403, ], ], 'GetBootstrapBrokersRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], ], 'required' => [ 'ClusterArn', ], ], 'GetBootstrapBrokersResponse' => [ 'type' => 'structure', 'members' => [ 'BootstrapBrokerString' => [ 'shape' => '__string', 'locationName' => 'bootstrapBrokerString', ], 'BootstrapBrokerStringPublicSaslIam' => [ 'shape' => '__string', 'locationName' => 'bootstrapBrokerStringPublicSaslIam', ], 'BootstrapBrokerStringPublicSaslScram' => [ 'shape' => '__string', 'locationName' => 'bootstrapBrokerStringPublicSaslScram', ], 'BootstrapBrokerStringPublicTls' => [ 'shape' => '__string', 'locationName' => 'bootstrapBrokerStringPublicTls', ], 'BootstrapBrokerStringTls' => [ 'shape' => '__string', 'locationName' => 'bootstrapBrokerStringTls', ], 'BootstrapBrokerStringSaslScram' => [ 'shape' => '__string', 'locationName' => 'bootstrapBrokerStringSaslScram', ], 'BootstrapBrokerStringSaslIam' => [ 'shape' => '__string', 'locationName' => 'bootstrapBrokerStringSaslIam', ], 'BootstrapBrokerStringVpcConnectivityTls' => [ 'shape' => '__string', 'locationName' => 'bootstrapBrokerStringVpcConnectivityTls', ], 'BootstrapBrokerStringVpcConnectivitySaslScram' => [ 'shape' => '__string', 'locationName' => 'bootstrapBrokerStringVpcConnectivitySaslScram', ], 'BootstrapBrokerStringVpcConnectivitySaslIam' => [ 'shape' => '__string', 'locationName' => 'bootstrapBrokerStringVpcConnectivitySaslIam', ], ], ], 'GetCompatibleKafkaVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'clusterArn', ], ], ], 'GetCompatibleKafkaVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'CompatibleKafkaVersions' => [ 'shape' => '__listOfCompatibleKafkaVersion', 'locationName' => 'compatibleKafkaVersions', ], ], ], 'InternalServerErrorException' => [ 'type' => 'structure', 'members' => [ 'InvalidParameter' => [ 'shape' => '__string', 'locationName' => 'invalidParameter', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 500, ], ], 'KafkaCluster' => [ 'type' => 'structure', 'members' => [ 'AmazonMskCluster' => [ 'shape' => 'AmazonMskCluster', 'locationName' => 'amazonMskCluster', ], 'VpcConfig' => [ 'shape' => 'KafkaClusterClientVpcConfig', 'locationName' => 'vpcConfig', ], ], 'required' => [ 'VpcConfig', 'AmazonMskCluster', ], ], 'KafkaClusterClientVpcConfig' => [ 'type' => 'structure', 'members' => [ 'SecurityGroupIds' => [ 'shape' => '__listOf__string', 'locationName' => 'securityGroupIds', ], 'SubnetIds' => [ 'shape' => '__listOf__string', 'locationName' => 'subnetIds', ], ], 'required' => [ 'SubnetIds', ], ], 'KafkaClusterDescription' => [ 'type' => 'structure', 'members' => [ 'AmazonMskCluster' => [ 'shape' => 'AmazonMskCluster', 'locationName' => 'amazonMskCluster', ], 'KafkaClusterAlias' => [ 'shape' => '__string', 'locationName' => 'kafkaClusterAlias', ], 'VpcConfig' => [ 'shape' => 'KafkaClusterClientVpcConfig', 'locationName' => 'vpcConfig', ], ], ], 'KafkaClusterSummary' => [ 'type' => 'structure', 'members' => [ 'AmazonMskCluster' => [ 'shape' => 'AmazonMskCluster', 'locationName' => 'amazonMskCluster', ], 'KafkaClusterAlias' => [ 'shape' => '__string', 'locationName' => 'kafkaClusterAlias', ], ], ], 'KafkaVersion' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => '__string', 'locationName' => 'version', ], 'Status' => [ 'shape' => 'KafkaVersionStatus', 'locationName' => 'status', ], ], ], 'KafkaVersionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DEPRECATED', ], ], 'ListClusterOperationsRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], 'required' => [ 'ClusterArn', ], ], 'ListClusterOperationsV2Request' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], 'required' => [ 'ClusterArn', ], ], 'ListClusterOperationsResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterOperationInfoList' => [ 'shape' => '__listOfClusterOperationInfo', 'locationName' => 'clusterOperationInfoList', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListClusterOperationsV2Response' => [ 'type' => 'structure', 'members' => [ 'ClusterOperationInfoList' => [ 'shape' => '__listOfClusterOperationV2Summary', 'locationName' => 'clusterOperationInfoList', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListClustersV2Request' => [ 'type' => 'structure', 'members' => [ 'ClusterNameFilter' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'clusterNameFilter', 'documentation' => ' <p>Specify a prefix of the names of the clusters that you want to list. The service lists all the clusters whose names start with this prefix.</p> ', ], 'ClusterTypeFilter' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'clusterTypeFilter', 'documentation' => ' <p>Specify either PROVISIONED or SERVERLESS.</p> ', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', 'documentation' => ' <p>The maximum number of results to return in the response. If there are more results, the response includes a NextToken parameter.</p> ', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', 'documentation' => ' <p>The paginated results marker. When the result of the operation is truncated, the call returns NextToken in the response. To get the next batch, provide this token in your next request.</p> ', ], ], ], 'ListClustersV2Response' => [ 'type' => 'structure', 'members' => [ 'ClusterInfoList' => [ 'shape' => '__listOfCluster', 'locationName' => 'clusterInfoList', 'documentation' => ' <p>Information on each of the MSK clusters in the response.</p> ', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', 'documentation' => ' <p>The paginated results marker. When the result of a ListClusters operation is truncated, the call returns NextToken in the response. To get another batch of clusters, provide this token in your next request.</p> ', ], ], ], 'CreateClusterV2Request' => [ 'type' => 'structure', 'members' => [ 'ClusterName' => [ 'shape' => '__stringMin1Max64', 'locationName' => 'clusterName', 'documentation' => ' <p>The name of the cluster.</p> ', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', 'documentation' => ' <p>A map of tags that you want the cluster to have.</p> ', ], 'Provisioned' => [ 'shape' => 'ProvisionedRequest', 'locationName' => 'provisioned', 'documentation' => ' <p>Information about the provisioned cluster.</p> ', ], 'Serverless' => [ 'shape' => 'ServerlessRequest', 'locationName' => 'serverless', 'documentation' => ' <p>Information about the serverless cluster.</p> ', ], ], 'required' => [ 'ClusterName', ], ], 'CreateClusterV2Response' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', 'documentation' => ' <p>The Amazon Resource Name (ARN) of the cluster.</p> ', ], 'ClusterName' => [ 'shape' => '__string', 'locationName' => 'clusterName', 'documentation' => ' <p>The name of the MSK cluster.</p> ', ], 'State' => [ 'shape' => 'ClusterState', 'locationName' => 'state', 'documentation' => ' <p>The state of the cluster. The possible states are ACTIVE, CREATING, DELETING, FAILED, HEALING, MAINTENANCE, REBOOTING_BROKER, and UPDATING.</p> ', ], 'ClusterType' => [ 'shape' => 'ClusterType', 'locationName' => 'clusterType', 'documentation' => ' <p>The type of the cluster. The possible types are PROVISIONED or SERVERLESS.</p> ', ], ], ], 'CustomerActionStatus' => [ 'type' => 'string', 'enum' => [ 'CRITICAL_ACTION_REQUIRED', 'ACTION_RECOMMENDED', 'NONE', ], ], 'DescribeClusterV2Request' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', 'documentation' => ' <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p> ', ], ], 'required' => [ 'ClusterArn', ], ], 'DescribeClusterV2Response' => [ 'type' => 'structure', 'members' => [ 'ClusterInfo' => [ 'shape' => 'Cluster', 'locationName' => 'clusterInfo', 'documentation' => ' <p>The cluster information.</p> ', ], ], ], 'DeleteClusterPolicyRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], ], 'required' => [ 'ClusterArn', ], ], 'DeleteClusterPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'GetClusterPolicyRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], ], 'required' => [ 'ClusterArn', ], ], 'GetClusterPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'Policy' => [ 'shape' => '__string', 'locationName' => 'policy', ], ], ], 'PutClusterPolicyRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'Policy' => [ 'shape' => '__string', 'locationName' => 'policy', ], ], 'required' => [ 'ClusterArn', 'Policy', ], ], 'PutClusterPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], ], ], 'Cluster' => [ 'type' => 'structure', 'members' => [ 'ActiveOperationArn' => [ 'shape' => '__string', 'locationName' => 'activeOperationArn', 'documentation' => ' <p>The Amazon Resource Name (ARN) that uniquely identifies a cluster operation.</p> ', ], 'ClusterType' => [ 'shape' => 'ClusterType', 'locationName' => 'clusterType', 'documentation' => ' <p>Cluster Type.</p> ', ], 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', 'documentation' => ' <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p> ', ], 'ClusterName' => [ 'shape' => '__string', 'locationName' => 'clusterName', 'documentation' => ' <p>The name of the cluster.</p> ', ], 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', 'documentation' => ' <p>The time when the cluster was created.</p> ', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', 'documentation' => ' <p>The current version of the MSK cluster.</p> ', ], 'State' => [ 'shape' => 'ClusterState', 'locationName' => 'state', 'documentation' => ' <p>The state of the cluster. The possible states are ACTIVE, CREATING, DELETING, FAILED, HEALING, MAINTENANCE, REBOOTING_BROKER, and UPDATING.</p> ', ], 'StateInfo' => [ 'shape' => 'StateInfo', 'locationName' => 'stateInfo', 'documentation' => ' <p>State Info for the Amazon MSK cluster.</p> ', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', 'documentation' => ' <p>Tags attached to the cluster.</p> ', ], 'Provisioned' => [ 'shape' => 'Provisioned', 'locationName' => 'provisioned', 'documentation' => ' <p>Information about the provisioned cluster.</p> ', ], 'Serverless' => [ 'shape' => 'Serverless', 'locationName' => 'serverless', 'documentation' => ' <p>Information about the serverless cluster.</p> ', ], ], 'documentation' => ' <p>Returns information about a cluster.</p> ', ], 'ClusterType' => [ 'type' => 'string', 'documentation' => ' <p>The type of cluster.</p> ', 'enum' => [ 'PROVISIONED', 'SERVERLESS', ], ], 'ProvisionedRequest' => [ 'type' => 'structure', 'documentation' => ' <p>Provisioned cluster request.</p> ', 'members' => [ 'BrokerNodeGroupInfo' => [ 'shape' => 'BrokerNodeGroupInfo', 'locationName' => 'brokerNodeGroupInfo', 'documentation' => ' <p>Information about the brokers.</p> ', ], 'ClientAuthentication' => [ 'shape' => 'ClientAuthentication', 'locationName' => 'clientAuthentication', 'documentation' => ' <p>Includes all client authentication information.</p> ', ], 'ConfigurationInfo' => [ 'shape' => 'ConfigurationInfo', 'locationName' => 'configurationInfo', 'documentation' => ' <p>Represents the configuration that you want Amazon MSK to use for the brokers in a cluster.</p> ', ], 'EncryptionInfo' => [ 'shape' => 'EncryptionInfo', 'locationName' => 'encryptionInfo', 'documentation' => ' <p>Includes all encryption-related information.</p> ', ], 'EnhancedMonitoring' => [ 'shape' => 'EnhancedMonitoring', 'locationName' => 'enhancedMonitoring', 'documentation' => ' <p>Specifies the level of monitoring for the MSK cluster. The possible values are DEFAULT, PER_BROKER, PER_TOPIC_PER_BROKER, and PER_TOPIC_PER_PARTITION.</p> ', ], 'OpenMonitoring' => [ 'shape' => 'OpenMonitoringInfo', 'locationName' => 'openMonitoring', 'documentation' => ' <p>The settings for open monitoring.</p> ', ], 'KafkaVersion' => [ 'shape' => '__stringMin1Max128', 'locationName' => 'kafkaVersion', 'documentation' => ' <p>The Apache Kafka version that you want for the cluster.</p> ', ], 'LoggingInfo' => [ 'shape' => 'LoggingInfo', 'locationName' => 'loggingInfo', 'documentation' => ' <p>Log delivery information for the cluster.</p> ', ], 'NumberOfBrokerNodes' => [ 'shape' => '__integerMin1Max15', 'locationName' => 'numberOfBrokerNodes', 'documentation' => ' <p>The number of brokers in the cluster.</p> ', ], 'StorageMode' => [ 'shape' => 'StorageMode', 'locationName' => 'storageMode', 'documentation' => ' <p>This controls storage mode for supported storage tiers.</p> ', ], ], 'required' => [ 'BrokerNodeGroupInfo', 'KafkaVersion', 'NumberOfBrokerNodes', ], ], 'Provisioned' => [ 'type' => 'structure', 'documentation' => ' <p>Provisioned cluster.</p> ', 'members' => [ 'BrokerNodeGroupInfo' => [ 'shape' => 'BrokerNodeGroupInfo', 'locationName' => 'brokerNodeGroupInfo', 'documentation' => ' <p>Information about the brokers.</p> ', ], 'CurrentBrokerSoftwareInfo' => [ 'shape' => 'BrokerSoftwareInfo', 'locationName' => 'currentBrokerSoftwareInfo', 'documentation' => ' <p>Information about the Apache Kafka version deployed on the brokers.</p> ', ], 'ClientAuthentication' => [ 'shape' => 'ClientAuthentication', 'locationName' => 'clientAuthentication', 'documentation' => ' <p>Includes all client authentication information.</p> ', ], 'EncryptionInfo' => [ 'shape' => 'EncryptionInfo', 'locationName' => 'encryptionInfo', 'documentation' => ' <p>Includes all encryption-related information.</p> ', ], 'EnhancedMonitoring' => [ 'shape' => 'EnhancedMonitoring', 'locationName' => 'enhancedMonitoring', 'documentation' => ' <p>Specifies the level of monitoring for the MSK cluster. The possible values are DEFAULT, PER_BROKER, PER_TOPIC_PER_BROKER, and PER_TOPIC_PER_PARTITION.</p> ', ], 'OpenMonitoring' => [ 'shape' => 'OpenMonitoringInfo', 'locationName' => 'openMonitoring', 'documentation' => ' <p>The settings for open monitoring.</p> ', ], 'LoggingInfo' => [ 'shape' => 'LoggingInfo', 'locationName' => 'loggingInfo', 'documentation' => ' <p>Log delivery information for the cluster.</p> ', ], 'NumberOfBrokerNodes' => [ 'shape' => '__integerMin1Max15', 'locationName' => 'numberOfBrokerNodes', 'documentation' => ' <p>The number of brokers in the cluster.</p> ', ], 'ZookeeperConnectString' => [ 'shape' => '__string', 'locationName' => 'zookeeperConnectString', 'documentation' => ' <p>The connection string to use to connect to the Apache ZooKeeper cluster.</p> ', ], 'ZookeeperConnectStringTls' => [ 'shape' => '__string', 'locationName' => 'zookeeperConnectStringTls', 'documentation' => ' <p>The connection string to use to connect to the Apache ZooKeeper cluster on a TLS port.</p> ', ], 'StorageMode' => [ 'shape' => 'StorageMode', 'locationName' => 'storageMode', 'documentation' => ' <p>This controls storage mode for supported storage tiers.</p> ', ], 'CustomerActionStatus' => [ 'shape' => 'CustomerActionStatus', 'locationName' => 'customerActionStatus', 'documentation' => ' <p>Determines if there is an action required from the customer.</p> ', ], ], 'required' => [ 'BrokerNodeGroupInfo', 'NumberOfBrokerNodes', ], ], 'VpcConfig' => [ 'type' => 'structure', 'documentation' => ' <p>The configuration of the Amazon VPCs for the cluster.</p> ', 'members' => [ 'SubnetIds' => [ 'shape' => '__listOf__string', 'locationName' => 'subnetIds', 'documentation' => ' <p>The IDs of the subnets associated with the cluster.</p> ', ], 'SecurityGroupIds' => [ 'shape' => '__listOf__string', 'locationName' => 'securityGroupIds', 'documentation' => ' <p>The IDs of the security groups associated with the cluster.</p> ', ], ], 'required' => [ 'SubnetIds', ], ], 'ServerlessRequest' => [ 'type' => 'structure', 'documentation' => ' <p>Serverless cluster request.</p> ', 'members' => [ 'VpcConfigs' => [ 'shape' => '__listOfVpcConfig', 'locationName' => 'vpcConfigs', 'documentation' => ' <p>The configuration of the Amazon VPCs for the cluster.</p> ', ], 'ClientAuthentication' => [ 'shape' => 'ServerlessClientAuthentication', 'locationName' => 'clientAuthentication', 'documentation' => ' <p>Includes all client authentication information.</p> ', ], ], 'required' => [ 'VpcConfigs', ], ], 'ServerlessClientAuthentication' => [ 'type' => 'structure', 'members' => [ 'Sasl' => [ 'shape' => 'ServerlessSasl', 'locationName' => 'sasl', 'documentation' => ' <p>Details for client authentication using SASL.</p> ', ], ], 'documentation' => ' <p>Includes all client authentication information.</p> ', ], 'ServerlessSasl' => [ 'type' => 'structure', 'members' => [ 'Iam' => [ 'shape' => 'Iam', 'locationName' => 'iam', 'documentation' => ' <p>Indicates whether IAM access control is enabled.</p> ', ], ], 'documentation' => ' <p>Details for client authentication using SASL.</p> ', ], 'Serverless' => [ 'type' => 'structure', 'documentation' => ' <p>Serverless cluster.</p> ', 'members' => [ 'VpcConfigs' => [ 'shape' => '__listOfVpcConfig', 'locationName' => 'vpcConfigs', 'documentation' => ' <p>The configuration of the Amazon VPCs for the cluster.</p> ', ], 'ClientAuthentication' => [ 'shape' => 'ServerlessClientAuthentication', 'locationName' => 'clientAuthentication', 'documentation' => ' <p>Includes all client authentication information.</p> ', ], ], 'required' => [ 'VpcConfigs', ], ], 'ListClustersRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterNameFilter' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'clusterNameFilter', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListClustersResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterInfoList' => [ 'shape' => '__listOfClusterInfo', 'locationName' => 'clusterInfoList', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListConfigurationRevisionsRequest' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], 'required' => [ 'Arn', ], ], 'ListConfigurationRevisionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Revisions' => [ 'shape' => '__listOfConfigurationRevision', 'locationName' => 'revisions', ], ], ], 'ListConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'Configurations' => [ 'shape' => '__listOfConfiguration', 'locationName' => 'configurations', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListKafkaVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListKafkaVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'KafkaVersions' => [ 'shape' => '__listOfKafkaVersion', 'locationName' => 'kafkaVersions', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListNodesRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], 'required' => [ 'ClusterArn', ], ], 'ListNodesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'NodeInfoList' => [ 'shape' => '__listOfNodeInfo', 'locationName' => 'nodeInfoList', ], ], ], 'ListScramSecretsRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], 'required' => [ 'ClusterArn', ], ], 'ListScramSecretsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'SecretArnList' => [ 'shape' => '__listOf__string', 'locationName' => 'secretArnList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], 'required' => [ 'ResourceArn', ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'min' => 1, 'max' => 100, ], 'LoggingInfo' => [ 'type' => 'structure', 'members' => [ 'BrokerLogs' => [ 'shape' => 'BrokerLogs', 'locationName' => 'brokerLogs', ], ], 'required' => [ 'BrokerLogs', ], ], 'ListClientVpcConnectionsRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], 'required' => [ 'ClusterArn', ], ], 'ListClientVpcConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'ClientVpcConnections' => [ 'shape' => '__listOfClientVpcConnection', 'locationName' => 'clientVpcConnections', ], ], ], 'ListReplicatorsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'ReplicatorNameFilter' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'replicatorNameFilter', ], ], ], 'ListReplicatorsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Replicators' => [ 'shape' => '__listOfReplicatorSummary', 'locationName' => 'replicators', ], ], ], 'ListVpcConnectionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListVpcConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'VpcConnections' => [ 'shape' => '__listOfVpcConnection', 'locationName' => 'vpcConnections', ], ], ], 'RejectClientVpcConnectionRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'VpcConnectionArn' => [ 'shape' => '__string', 'locationName' => 'vpcConnectionArn', ], ], 'required' => [ 'VpcConnectionArn', 'ClusterArn', ], ], 'RejectClientVpcConnectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'MutableClusterInfo' => [ 'type' => 'structure', 'members' => [ 'BrokerEBSVolumeInfo' => [ 'shape' => '__listOfBrokerEBSVolumeInfo', 'locationName' => 'brokerEBSVolumeInfo', ], 'ConfigurationInfo' => [ 'shape' => 'ConfigurationInfo', 'locationName' => 'configurationInfo', ], 'NumberOfBrokerNodes' => [ 'shape' => '__integer', 'locationName' => 'numberOfBrokerNodes', ], 'OpenMonitoring' => [ 'shape' => 'OpenMonitoring', 'locationName' => 'openMonitoring', ], 'EnhancedMonitoring' => [ 'shape' => 'EnhancedMonitoring', 'locationName' => 'enhancedMonitoring', ], 'KafkaVersion' => [ 'shape' => '__string', 'locationName' => 'kafkaVersion', ], 'LoggingInfo' => [ 'shape' => 'LoggingInfo', 'locationName' => 'loggingInfo', ], 'InstanceType' => [ 'shape' => '__string', 'locationName' => 'instanceType', ], 'ClientAuthentication' => [ 'shape' => 'ClientAuthentication', 'locationName' => 'clientAuthentication', ], 'EncryptionInfo' => [ 'shape' => 'EncryptionInfo', 'locationName' => 'encryptionInfo', ], 'ConnectivityInfo' => [ 'shape' => 'ConnectivityInfo', 'locationName' => 'connectivityInfo', ], 'StorageMode' => [ 'shape' => 'StorageMode', 'locationName' => 'storageMode', ], ], ], 'NodeInfo' => [ 'type' => 'structure', 'members' => [ 'AddedToClusterTime' => [ 'shape' => '__string', 'locationName' => 'addedToClusterTime', ], 'BrokerNodeInfo' => [ 'shape' => 'BrokerNodeInfo', 'locationName' => 'brokerNodeInfo', ], 'InstanceType' => [ 'shape' => '__string', 'locationName' => 'instanceType', ], 'NodeARN' => [ 'shape' => '__string', 'locationName' => 'nodeARN', ], 'NodeType' => [ 'shape' => 'NodeType', 'locationName' => 'nodeType', ], 'ZookeeperNodeInfo' => [ 'shape' => 'ZookeeperNodeInfo', 'locationName' => 'zookeeperNodeInfo', ], ], ], 'NodeType' => [ 'type' => 'string', 'enum' => [ 'BROKER', ], ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'InvalidParameter' => [ 'shape' => '__string', 'locationName' => 'invalidParameter', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 404, ], ], 'ReplicationInfo' => [ 'type' => 'structure', 'members' => [ 'ConsumerGroupReplication' => [ 'shape' => 'ConsumerGroupReplication', 'locationName' => 'consumerGroupReplication', ], 'SourceKafkaClusterArn' => [ 'shape' => '__string', 'locationName' => 'sourceKafkaClusterArn', ], 'TargetCompressionType' => [ 'shape' => 'TargetCompressionType', 'locationName' => 'targetCompressionType', ], 'TargetKafkaClusterArn' => [ 'shape' => '__string', 'locationName' => 'targetKafkaClusterArn', ], 'TopicReplication' => [ 'shape' => 'TopicReplication', 'locationName' => 'topicReplication', ], ], 'required' => [ 'TargetCompressionType', 'TopicReplication', 'ConsumerGroupReplication', 'SourceKafkaClusterArn', 'TargetKafkaClusterArn', ], ], 'ReplicationInfoDescription' => [ 'type' => 'structure', 'members' => [ 'ConsumerGroupReplication' => [ 'shape' => 'ConsumerGroupReplication', 'locationName' => 'consumerGroupReplication', ], 'SourceKafkaClusterAlias' => [ 'shape' => '__string', 'locationName' => 'sourceKafkaClusterAlias', ], 'TargetCompressionType' => [ 'shape' => 'TargetCompressionType', 'locationName' => 'targetCompressionType', ], 'TargetKafkaClusterAlias' => [ 'shape' => '__string', 'locationName' => 'targetKafkaClusterAlias', ], 'TopicReplication' => [ 'shape' => 'TopicReplication', 'locationName' => 'topicReplication', ], ], ], 'ReplicationInfoSummary' => [ 'type' => 'structure', 'members' => [ 'SourceKafkaClusterAlias' => [ 'shape' => '__string', 'locationName' => 'sourceKafkaClusterAlias', ], 'TargetKafkaClusterAlias' => [ 'shape' => '__string', 'locationName' => 'targetKafkaClusterAlias', ], ], ], 'ReplicationStateInfo' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => '__string', 'locationName' => 'code', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'ReplicatorState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'CREATING', 'UPDATING', 'DELETING', 'FAILED', ], ], 'ReplicatorSummary' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'IsReplicatorReference' => [ 'shape' => '__boolean', 'locationName' => 'isReplicatorReference', ], 'KafkaClustersSummary' => [ 'shape' => '__listOfKafkaClusterSummary', 'locationName' => 'kafkaClustersSummary', ], 'ReplicationInfoSummaryList' => [ 'shape' => '__listOfReplicationInfoSummary', 'locationName' => 'replicationInfoSummaryList', ], 'ReplicatorArn' => [ 'shape' => '__string', 'locationName' => 'replicatorArn', ], 'ReplicatorName' => [ 'shape' => '__string', 'locationName' => 'replicatorName', ], 'ReplicatorResourceArn' => [ 'shape' => '__string', 'locationName' => 'replicatorResourceArn', ], 'ReplicatorState' => [ 'shape' => 'ReplicatorState', 'locationName' => 'replicatorState', ], ], ], 'Sasl' => [ 'type' => 'structure', 'members' => [ 'Scram' => [ 'shape' => 'Scram', 'locationName' => 'scram', ], 'Iam' => [ 'shape' => 'Iam', 'locationName' => 'iam', ], ], ], 'VpcConnectivitySasl' => [ 'type' => 'structure', 'members' => [ 'Scram' => [ 'shape' => 'VpcConnectivityScram', 'locationName' => 'scram', ], 'Iam' => [ 'shape' => 'VpcConnectivityIam', 'locationName' => 'iam', ], ], ], 'Scram' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => '__boolean', 'locationName' => 'enabled', ], ], ], 'VpcConnectivityScram' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => '__boolean', 'locationName' => 'enabled', ], ], ], 'Iam' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => '__boolean', 'locationName' => 'enabled', ], ], ], 'VpcConnectivityIam' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => '__boolean', 'locationName' => 'enabled', ], ], ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'InvalidParameter' => [ 'shape' => '__string', 'locationName' => 'invalidParameter', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 503, ], ], 'StateInfo' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => '__string', 'locationName' => 'code', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'StorageInfo' => [ 'type' => 'structure', 'members' => [ 'EbsStorageInfo' => [ 'shape' => 'EBSStorageInfo', 'locationName' => 'ebsStorageInfo', ], ], ], 'StorageMode' => [ 'type' => 'string', 'enum' => [ 'LOCAL', 'TIERED', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], 'required' => [ 'ResourceArn', 'Tags', ], ], 'TargetCompressionType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'GZIP', 'SNAPPY', 'LZ4', 'ZSTD', ], ], 'TopicReplication' => [ 'type' => 'structure', 'members' => [ 'CopyAccessControlListsForTopics' => [ 'shape' => '__boolean', 'locationName' => 'copyAccessControlListsForTopics', ], 'CopyTopicConfigurations' => [ 'shape' => '__boolean', 'locationName' => 'copyTopicConfigurations', ], 'DetectAndCopyNewTopics' => [ 'shape' => '__boolean', 'locationName' => 'detectAndCopyNewTopics', ], 'TopicsToExclude' => [ 'shape' => '__listOf__stringMax249', 'locationName' => 'topicsToExclude', ], 'TopicsToReplicate' => [ 'shape' => '__listOf__stringMax249', 'locationName' => 'topicsToReplicate', ], ], 'required' => [ 'TopicsToReplicate', ], ], 'TopicReplicationUpdate' => [ 'type' => 'structure', 'members' => [ 'CopyAccessControlListsForTopics' => [ 'shape' => '__boolean', 'locationName' => 'copyAccessControlListsForTopics', ], 'CopyTopicConfigurations' => [ 'shape' => '__boolean', 'locationName' => 'copyTopicConfigurations', ], 'DetectAndCopyNewTopics' => [ 'shape' => '__boolean', 'locationName' => 'detectAndCopyNewTopics', ], 'TopicsToExclude' => [ 'shape' => '__listOf__stringMax249', 'locationName' => 'topicsToExclude', ], 'TopicsToReplicate' => [ 'shape' => '__listOf__stringMax249', 'locationName' => 'topicsToReplicate', ], ], 'required' => [ 'TopicsToReplicate', 'TopicsToExclude', 'CopyTopicConfigurations', 'DetectAndCopyNewTopics', 'CopyAccessControlListsForTopics', ], ], 'Tls' => [ 'type' => 'structure', 'members' => [ 'CertificateAuthorityArnList' => [ 'shape' => '__listOf__string', 'locationName' => 'certificateAuthorityArnList', ], 'Enabled' => [ 'shape' => '__boolean', 'locationName' => 'enabled', ], ], ], 'VpcConnectivityTls' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => '__boolean', 'locationName' => 'enabled', ], ], ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'InvalidParameter' => [ 'shape' => '__string', 'locationName' => 'invalidParameter', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 429, ], ], 'Unauthenticated' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => '__boolean', 'locationName' => 'enabled', ], ], ], 'UnauthorizedException' => [ 'type' => 'structure', 'members' => [ 'InvalidParameter' => [ 'shape' => '__string', 'locationName' => 'invalidParameter', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 401, ], ], 'UnprocessedScramSecret' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => '__string', 'locationName' => 'errorCode', ], 'ErrorMessage' => [ 'shape' => '__string', 'locationName' => 'errorMessage', ], 'SecretArn' => [ 'shape' => '__string', 'locationName' => 'secretArn', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'TagKeys' => [ 'shape' => '__listOf__string', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], 'required' => [ 'TagKeys', 'ResourceArn', ], ], 'UpdateBrokerTypeRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'TargetInstanceType' => [ 'shape' => '__string', 'locationName' => 'targetInstanceType', ], ], 'required' => [ 'ClusterArn', 'CurrentVersion', 'TargetInstanceType', ], ], 'UpdateBrokerTypeResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterOperationArn' => [ 'shape' => '__string', 'locationName' => 'clusterOperationArn', ], ], ], 'UpdateBrokerCountRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'TargetNumberOfBrokerNodes' => [ 'shape' => '__integerMin1Max15', 'locationName' => 'targetNumberOfBrokerNodes', ], ], 'required' => [ 'ClusterArn', 'CurrentVersion', 'TargetNumberOfBrokerNodes', ], ], 'UpdateBrokerCountResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterOperationArn' => [ 'shape' => '__string', 'locationName' => 'clusterOperationArn', ], ], ], 'UpdateBrokerStorageRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'TargetBrokerEBSVolumeInfo' => [ 'shape' => '__listOfBrokerEBSVolumeInfo', 'locationName' => 'targetBrokerEBSVolumeInfo', ], ], 'required' => [ 'ClusterArn', 'TargetBrokerEBSVolumeInfo', 'CurrentVersion', ], ], 'UpdateBrokerStorageResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterOperationArn' => [ 'shape' => '__string', 'locationName' => 'clusterOperationArn', ], ], ], 'UpdateClusterConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'ConfigurationInfo' => [ 'shape' => 'ConfigurationInfo', 'locationName' => 'configurationInfo', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], ], 'required' => [ 'ClusterArn', 'CurrentVersion', 'ConfigurationInfo', ], ], 'UpdateClusterConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterOperationArn' => [ 'shape' => '__string', 'locationName' => 'clusterOperationArn', ], ], ], 'UpdateClusterKafkaVersionRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'ConfigurationInfo' => [ 'shape' => 'ConfigurationInfo', 'locationName' => 'configurationInfo', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'TargetKafkaVersion' => [ 'shape' => '__string', 'locationName' => 'targetKafkaVersion', ], ], 'required' => [ 'ClusterArn', 'TargetKafkaVersion', 'CurrentVersion', ], ], 'UpdateClusterKafkaVersionResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterOperationArn' => [ 'shape' => '__string', 'locationName' => 'clusterOperationArn', ], ], ], 'UpdateConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'arn', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'ServerProperties' => [ 'shape' => '__blob', 'locationName' => 'serverProperties', ], ], 'required' => [ 'Arn', 'ServerProperties', ], ], 'UpdateConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'LatestRevision' => [ 'shape' => 'ConfigurationRevision', 'locationName' => 'latestRevision', ], ], ], 'UpdateConnectivityRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'ConnectivityInfo' => [ 'shape' => 'ConnectivityInfo', 'locationName' => 'connectivityInfo', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], ], 'required' => [ 'ClusterArn', 'ConnectivityInfo', 'CurrentVersion', ], ], 'UpdateConnectivityResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterOperationArn' => [ 'shape' => '__string', 'locationName' => 'clusterOperationArn', ], ], ], 'UpdateMonitoringRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'EnhancedMonitoring' => [ 'shape' => 'EnhancedMonitoring', 'locationName' => 'enhancedMonitoring', ], 'OpenMonitoring' => [ 'shape' => 'OpenMonitoringInfo', 'locationName' => 'openMonitoring', ], 'LoggingInfo' => [ 'shape' => 'LoggingInfo', 'locationName' => 'loggingInfo', ], ], 'required' => [ 'ClusterArn', 'CurrentVersion', ], ], 'UpdateMonitoringResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterOperationArn' => [ 'shape' => '__string', 'locationName' => 'clusterOperationArn', ], ], ], 'UpdateReplicationInfoRequest' => [ 'type' => 'structure', 'members' => [ 'ConsumerGroupReplication' => [ 'shape' => 'ConsumerGroupReplicationUpdate', 'locationName' => 'consumerGroupReplication', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'ReplicatorArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'replicatorArn', ], 'SourceKafkaClusterArn' => [ 'shape' => '__string', 'locationName' => 'sourceKafkaClusterArn', ], 'TargetKafkaClusterArn' => [ 'shape' => '__string', 'locationName' => 'targetKafkaClusterArn', ], 'TopicReplication' => [ 'shape' => 'TopicReplicationUpdate', 'locationName' => 'topicReplication', ], ], 'required' => [ 'ReplicatorArn', 'SourceKafkaClusterArn', 'CurrentVersion', 'TargetKafkaClusterArn', ], ], 'UpdateReplicationInfoResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicatorArn' => [ 'shape' => '__string', 'locationName' => 'replicatorArn', ], 'ReplicatorState' => [ 'shape' => 'ReplicatorState', 'locationName' => 'replicatorState', ], ], ], 'UpdateSecurityRequest' => [ 'type' => 'structure', 'members' => [ 'ClientAuthentication' => [ 'shape' => 'ClientAuthentication', 'locationName' => 'clientAuthentication', ], 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'EncryptionInfo' => [ 'shape' => 'EncryptionInfo', 'locationName' => 'encryptionInfo', ], ], 'required' => [ 'ClusterArn', 'CurrentVersion', ], ], 'UpdateSecurityResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterOperationArn' => [ 'shape' => '__string', 'locationName' => 'clusterOperationArn', ], ], ], 'UpdateStorageRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], 'CurrentVersion' => [ 'shape' => '__string', 'locationName' => 'currentVersion', ], 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughput', 'locationName' => 'provisionedThroughput', ], 'StorageMode' => [ 'shape' => 'StorageMode', 'locationName' => 'storageMode', ], 'VolumeSizeGB' => [ 'shape' => '__integer', 'locationName' => 'volumeSizeGB', ], ], 'required' => [ 'ClusterArn', 'CurrentVersion', ], ], 'UpdateStorageResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterOperationArn' => [ 'shape' => '__string', 'locationName' => 'clusterOperationArn', ], ], ], 'UserIdentity' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'UserIdentityType', 'locationName' => 'type', ], 'PrincipalId' => [ 'shape' => '__string', 'locationName' => 'principalId', ], ], ], 'UserIdentityType' => [ 'type' => 'string', 'enum' => [ 'AWSACCOUNT', 'AWSSERVICE', ], ], 'ZookeeperNodeInfo' => [ 'type' => 'structure', 'members' => [ 'AttachedENIId' => [ 'shape' => '__string', 'locationName' => 'attachedENIId', ], 'ClientVpcIpAddress' => [ 'shape' => '__string', 'locationName' => 'clientVpcIpAddress', ], 'Endpoints' => [ 'shape' => '__listOf__string', 'locationName' => 'endpoints', ], 'ZookeeperId' => [ 'shape' => '__double', 'locationName' => 'zookeeperId', ], 'ZookeeperVersion' => [ 'shape' => '__string', 'locationName' => 'zookeeperVersion', ], ], ], 'OpenMonitoring' => [ 'type' => 'structure', 'members' => [ 'Prometheus' => [ 'shape' => 'Prometheus', 'locationName' => 'prometheus', ], ], 'required' => [ 'Prometheus', ], ], 'OpenMonitoringInfo' => [ 'type' => 'structure', 'members' => [ 'Prometheus' => [ 'shape' => 'PrometheusInfo', 'locationName' => 'prometheus', ], ], 'required' => [ 'Prometheus', ], ], 'Prometheus' => [ 'type' => 'structure', 'members' => [ 'JmxExporter' => [ 'shape' => 'JmxExporter', 'locationName' => 'jmxExporter', ], 'NodeExporter' => [ 'shape' => 'NodeExporter', 'locationName' => 'nodeExporter', ], ], ], 'PrometheusInfo' => [ 'type' => 'structure', 'members' => [ 'JmxExporter' => [ 'shape' => 'JmxExporterInfo', 'locationName' => 'jmxExporter', ], 'NodeExporter' => [ 'shape' => 'NodeExporterInfo', 'locationName' => 'nodeExporter', ], ], ], 'ProvisionedThroughput' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => '__boolean', 'locationName' => 'enabled', ], 'VolumeThroughput' => [ 'shape' => '__integer', 'locationName' => 'volumeThroughput', ], ], ], 'PublicAccess' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => '__string', 'locationName' => 'type', ], ], ], 'VpcConnectivity' => [ 'type' => 'structure', 'members' => [ 'ClientAuthentication' => [ 'shape' => 'VpcConnectivityClientAuthentication', 'locationName' => 'clientAuthentication', ], ], ], 'VpcConnectionInfo' => [ 'type' => 'structure', 'members' => [ 'VpcConnectionArn' => [ 'shape' => '__string', 'locationName' => 'vpcConnectionArn', ], 'Owner' => [ 'shape' => '__string', 'locationName' => 'owner', ], 'UserIdentity' => [ 'shape' => 'UserIdentity', 'locationName' => 'userIdentity', ], 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], ], ], 'VpcConnectionInfoServerless' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationTime', ], 'Owner' => [ 'shape' => '__string', 'locationName' => 'owner', ], 'UserIdentity' => [ 'shape' => 'UserIdentity', 'locationName' => 'userIdentity', ], 'VpcConnectionArn' => [ 'shape' => '__string', 'locationName' => 'vpcConnectionArn', ], ], ], 'VpcConnectionState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'AVAILABLE', 'INACTIVE', 'DEACTIVATING', 'DELETING', 'FAILED', 'REJECTED', 'REJECTING', ], ], 'RebootBrokerRequest' => [ 'type' => 'structure', 'members' => [ 'BrokerIds' => [ 'shape' => '__listOf__string', 'locationName' => 'brokerIds', ], 'ClusterArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterArn', ], ], 'required' => [ 'ClusterArn', 'BrokerIds', ], ], 'RebootBrokerResponse' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => '__string', 'locationName' => 'clusterArn', ], 'ClusterOperationArn' => [ 'shape' => '__string', 'locationName' => 'clusterOperationArn', ], ], ], 'S3' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => '__string', 'locationName' => 'bucket', ], 'Enabled' => [ 'shape' => '__boolean', 'locationName' => 'enabled', ], 'Prefix' => [ 'shape' => '__string', 'locationName' => 'prefix', ], ], 'required' => [ 'Enabled', ], ], 'JmxExporter' => [ 'type' => 'structure', 'members' => [ 'EnabledInBroker' => [ 'shape' => '__boolean', 'locationName' => 'enabledInBroker', ], ], 'required' => [ 'EnabledInBroker', ], ], 'JmxExporterInfo' => [ 'type' => 'structure', 'members' => [ 'EnabledInBroker' => [ 'shape' => '__boolean', 'locationName' => 'enabledInBroker', ], ], 'required' => [ 'EnabledInBroker', ], ], 'NodeExporter' => [ 'type' => 'structure', 'members' => [ 'EnabledInBroker' => [ 'shape' => '__boolean', 'locationName' => 'enabledInBroker', ], ], 'required' => [ 'EnabledInBroker', ], ], 'NodeExporterInfo' => [ 'type' => 'structure', 'members' => [ 'EnabledInBroker' => [ 'shape' => '__boolean', 'locationName' => 'enabledInBroker', ], ], 'required' => [ 'EnabledInBroker', ], ], '__boolean' => [ 'type' => 'boolean', ], '__blob' => [ 'type' => 'blob', ], '__double' => [ 'type' => 'double', ], '__integer' => [ 'type' => 'integer', ], '__integerMin1Max15' => [ 'type' => 'integer', 'min' => 1, 'max' => 15, ], '__integerMin1Max16384' => [ 'type' => 'integer', 'min' => 1, 'max' => 16384, ], '__listOfBrokerEBSVolumeInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'BrokerEBSVolumeInfo', ], ], '__listOfClusterInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClusterInfo', ], ], '__listOfClusterOperationV2Summary' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClusterOperationV2Summary', ], ], '__listOfClusterOperationInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClusterOperationInfo', ], ], '__listOfClusterOperationStep' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClusterOperationStep', ], ], '__listOfCompatibleKafkaVersion' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompatibleKafkaVersion', ], ], '__listOfCluster' => [ 'type' => 'list', 'member' => [ 'shape' => 'Cluster', ], ], '__listOfVpcConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcConfig', ], ], '__listOfConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'Configuration', ], ], '__listOfConfigurationRevision' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationRevision', ], ], '__listOfKafkaCluster' => [ 'type' => 'list', 'member' => [ 'shape' => 'KafkaCluster', ], ], '__listOfKafkaClusterDescription' => [ 'type' => 'list', 'member' => [ 'shape' => 'KafkaClusterDescription', ], ], '__listOfKafkaClusterSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'KafkaClusterSummary', ], ], '__listOfKafkaVersion' => [ 'type' => 'list', 'member' => [ 'shape' => 'KafkaVersion', ], ], '__listOfNodeInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeInfo', ], ], '__listOfClientVpcConnection' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClientVpcConnection', ], ], '__listOfReplicationInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationInfo', ], ], '__listOfReplicationInfoDescription' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationInfoDescription', ], ], '__listOfReplicationInfoSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationInfoSummary', ], ], '__listOfReplicatorSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicatorSummary', ], ], '__listOfVpcConnection' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcConnection', ], ], '__listOfUnprocessedScramSecret' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedScramSecret', ], ], '__listOf__string' => [ 'type' => 'list', 'member' => [ 'shape' => '__string', ], ], '__listOf__stringMax249' => [ 'type' => 'list', 'member' => [ 'shape' => '__stringMax249', ], ], '__listOf__stringMax256' => [ 'type' => 'list', 'member' => [ 'shape' => '__stringMax256', ], ], '__long' => [ 'type' => 'long', ], '__mapOf__string' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__string', ], ], '__string' => [ 'type' => 'string', ], '__stringMax1024' => [ 'type' => 'string', 'max' => 1024, ], '__stringMax249' => [ 'type' => 'string', 'max' => 249, ], '__stringMax256' => [ 'type' => 'string', 'max' => 256, ], '__stringMin1Max128' => [ 'type' => 'string', 'min' => 1, 'max' => 128, ], '__stringMin1Max128Pattern09AZaZ09AZaZ0' => [ 'type' => 'string', 'min' => 1, 'max' => 128, 'pattern' => '^[0-9A-Za-z][0-9A-Za-z-]{0,}$', ], '__stringMin1Max64' => [ 'type' => 'string', 'min' => 1, 'max' => 64, ], '__stringMin5Max32' => [ 'type' => 'string', 'min' => 5, 'max' => 32, ], '__timestampIso8601' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], '__timestampUnix' => [ 'type' => 'timestamp', 'timestampFormat' => 'unixTimestamp', ], ],];
