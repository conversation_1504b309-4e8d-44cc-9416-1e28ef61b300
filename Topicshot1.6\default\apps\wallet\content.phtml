<div class="timeline-container" data-app="wallet">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link('wallet'); ?>" data-spa="true">
						<?php echo cl_translate("Wallet"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link('/'); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>

	<div class="account-wallet">
		<div class="account-wallet__status">
			<div class="account-wallet__card">
				<div class="account-wallet__card-title">
					<?php echo cl_translate("Your credit"); ?>
				</div>
				<div class="wallet-balance">
					<div class="wallet-balance__amount">
						<span>
							<?php echo cl_money($me['wallet']); ?>
						</span>
						<span>
							<?php echo cl_translate("Your account's available funds"); ?>
						</span>
					</div>
					<div class="wallet-balance__icon">
						<?php echo cl_ficon("wallet"); ?>
					</div> 
				</div>

                <?php if (in_array("on", array($cl['config']['paypal_method_status'], $cl['config']['paystack_method_status'], $cl['config']['stripe_method_status'], $cl['config']['bank_method_status'], $cl['config']['yookassa_status'], $cl['config']['coinpayments_method_status'], $cl['config']['rzp_method_status'], $cl['config']['moneypoolscash_status'], $cl['config']['alipay_method_status']))): ?>
                    <div class="wallet-footer">
						<div class="wallet-footer__text">
							<?php echo cl_translate("You may withdraw funds or use them in your account for advertising, subscription or other purposes"); ?>
						</div>

						<div class="wallet-footer__ctrls">
							<a href="<?php echo cl_link("wallet_add"); ?>" data-spa="true" class="wallet-footer__ctrls-item">
								<div class="btn btn-custom main-inline lg">
									<?php echo cl_translate("Add money"); ?>
								</div>
							</a>
							<a href="<?php echo cl_link("wallet_send"); ?>" data-spa="true" class="wallet-footer__ctrls-item">
								<div class="btn btn-custom main-grey lg">
									<?php echo cl_translate("Send money"); ?>
								</div>
							</a>
						</div>
					</div>
                <?php else: ?>
                	<div class="wallet-footer">
						<div class="wallet-footer__text">
							<?php echo cl_translate("Wallet replenishment is temporarily unavailable"); ?>
						</div>
						<div class="wallet-footer__ctrls">
							<button type="button" class="btn btn-custom main-grey lg" disabled="disabled">
								<?php echo cl_translate("Top-up my wallet"); ?>
							</button>
						</div>
					</div>
                <?php endif; ?>
			</div>
			<div class="account-wallet__withdrawal">
				<a href="<?php echo cl_link("wallet_withdrawal"); ?>" data-spa="true">
					<?php echo cl_translate("Submit a withdrawal request"); ?>
				</a>
			</div>
		</div>
		<div class="account-wallet__history">
			<div class="account-wallet__history-topline">
				<h4>
					<?php echo cl_translate("Wallet transactions"); ?>
				</h4>
			</div>

			<?php if (not_empty($cl["wallet_history"])): ?>
				<div class="wallet-transactions">
					<?php foreach ($cl["wallet_history"] as $row): ?>
						<div class="wallet-transactions__item">
							<div class="lp">
								<div class="lp__icon">
									<?php if (in_array($row["operation"], array("yookassa_wallet_tup", "coinpaym_wallet_tup", "mooneypc_wallet_tup", "paypal_wallet_tup", "stripe_wallet_tup", "razorpay_wallet_tup", "paystack_wallet_tup", "banktrans_wallet_tup", "affiliate_wallet_tup", "wallet_local_receipt"))): ?>
										<?php if ($row["status"] == "success"): ?>
											<img src="<?php echo cl_link(cl_strf("themes/%s/statics/img/pgws/avatars/income-success.png", $cl["config"]["theme"])); ?>" alt="IMG">
										<?php elseif($row["status"] == "declined"): ?>
											<img src="<?php echo cl_link(cl_strf("themes/%s/statics/img/pgws/avatars/income-error.png", $cl["config"]["theme"])); ?>" alt="IMG">
										<?php elseif($row["status"] == "pending_approval"): ?>
											<img src="<?php echo cl_link(cl_strf("themes/%s/statics/img/pgws/avatars/income-pending.png", $cl["config"]["theme"])); ?>" alt="IMG">
										<?php endif; ?>
									<?php elseif(in_array($row["operation"], array("wallet_local_transfer", "premium_account_purchase", "wallet_withdrawal_req"))): ?>
										<img src="<?php echo cl_link(cl_strf("themes/%s/statics/img/pgws/avatars/outgo-success.png", $cl["config"]["theme"])); ?>" alt="IMG">
									<?php endif; ?>
								</div>
							</div>
							<div class="rp">
								<div class="mp__amount">
									<div class="flex-item-left">
										<?php if ($row["status"] == "declined"): ?><s><?php endif; ?>

										<?php if (in_array($row["operation"], array("yookassa_wallet_tup", "mooneypc_wallet_tup", "paypal_wallet_tup", "stripe_wallet_tup", "paystack_wallet_tup", "banktrans_wallet_tup", "affiliate_wallet_tup", "razorpay_wallet_tup", "wallet_local_receipt"))) {echo "+";} else {echo "-";} ?><?php echo cl_money($row['amount']); ?>

										<?php if ($row["status"] == "declined"): ?></s><?php endif; ?>
									</div>
									<div class="flex-item-right">
										<?php echo $row['time']; ?>
									</div>
								</div>
								<div class="mp__text">
									<?php if ($row["operation"] == "paypal_wallet_tup"): ?>
										<?php echo cl_translate("Account replenishment via PayPal"); ?>
									<?php elseif ($row["operation"] == "mooneypc_wallet_tup"): ?>
										<?php echo cl_translate("Account replenishment via Moneypoolscash"); ?>
									<?php elseif ($row["operation"] == "yookassa_wallet_tup"): ?>
										<?php echo cl_translate("Account replenishment via ЮKassa"); ?>
									<?php elseif($row["operation"] == "stripe_wallet_tup"): ?>
										<?php echo cl_translate("Account replenishment via Stripe"); ?>
									<?php elseif($row["operation"] == "paystack_wallet_tup"): ?>
										<?php echo cl_translate("Account replenishment via PayStack"); ?>
									<?php elseif($row["operation"] == "razorpay_wallet_tup"): ?>
										<?php echo cl_translate("Account replenishment via RazorPay"); ?>
									<?php elseif($row["operation"] == "coinpaym_wallet_tup"): ?>
										<?php echo cl_translate("Account replenishment via CoinPayments"); ?>
									<?php elseif($row["operation"] == "banktrans_wallet_tup"): ?>
										<?php echo cl_translate("Account replenishment via Bank transfer"); ?>
									<?php elseif($row["operation"] == "affiliate_wallet_tup"): ?>
										<?php echo cl_translate("Account replenishment via affiliate bonuses transfer"); ?>
									<?php elseif($row["operation"] == "wallet_local_transfer"): ?>
										<?php if (not_empty($row["json_data"])): ?>
											<?php echo cl_translate("Local transfer to {@user_name@}", array("user_name" => $row["json_data"]["username"])); ?>
										<?php else: ?>
											<?php echo cl_translate("Local transfer to {@user_name@}", array("user_name" => "X")); ?>
										<?php endif; ?>
									<?php elseif($row["operation"] == "wallet_local_receipt"): ?>
										<?php if (not_empty($row["json_data"])): ?>
											<?php echo cl_translate("Local translation from {@user_name@}", array("user_name" => $row["json_data"]["username"])); ?>
										<?php else: ?>
											<?php echo cl_translate("Local translation from {@user_name@}", array("user_name" => "X")); ?>
										<?php endif; ?>
									<?php elseif($row["operation"] == "premium_account_purchase"): ?>
										<?php echo cl_translate("Payment for the premium account subscription"); ?>
									<?php elseif($row["operation"] == "wallet_withdrawal_req"): ?>
										<?php echo cl_translate("Money withdrawal from wallet to {@pmethod@}", array("pmethod" => $row["json_data"]["withdrawal_method"])); ?>
									<?php endif; ?>
								</div>
								<?php if ($row["status"] == "pending_approval"): ?>
									<?php if ($row["operation"] == "wallet_withdrawal_req"): ?>
										<div class="mp__status mp__status_pending">
											<span><?php echo cl_translate("Request pending approval"); ?></span>
										</div>
									<?php else: ?>
										<div class="mp__status mp__status_pending">
											<span><?php echo cl_translate("Transaction pending approval"); ?></span>
										</div>
									<?php endif; ?>
								<?php elseif ($row["status"] == "declined"): ?>
									<?php if ($row["operation"] == "wallet_withdrawal_req"): ?>
										<div class="mp__status mp__status_declined">
											<span><?php echo cl_translate("Withdrawal request was declined"); ?></span>
										</div>
									<?php else: ?>
										<div class="mp__status mp__status_declined">
											<span><?php echo cl_translate("Transaction was declined"); ?></span>
										</div>
									<?php endif; ?>
								<?php endif; ?>
							</div>
						</div>
					<?php endforeach; ?>
				</div>
			<?php else: ?>
				<div class="timeline-placeholder">
					<div class="icon">
						<div class="icon__bg">
							<?php echo cl_ficon('history'); ?>
						</div>
					</div>
					<div class="pl-message">
						<h4>
							<?php echo cl_translate("No transactions"); ?>
						</h4>
						<p>
							<?php echo cl_translate("There are no transactions in your wallet history yet. All information about your wallet transactions will be available here"); ?>
						</p>
					</div>
				</div>
			<?php endif; ?>
		</div>
	</div>
</div>