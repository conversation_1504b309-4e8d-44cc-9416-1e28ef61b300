<!DOCTYPE html>
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
	<head>
		<title></title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]-->
		<!--[if !mso]><!-->
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
		<!--<![endif]-->
		<style>
			* {
				box-sizing: border-box;
			}

			body {
				margin: 0;
				padding: 0;
				font-family: "Inter", sans-serif;
			}

			a[x-apple-data-detectors] {
				color: inherit !important;
				text-decoration: inherit !important;
			}

			#MessageViewBody a {
				color: inherit;
				text-decoration: none;
			}

			p {
				line-height: inherit
			}

			.desktop_hide,
			.desktop_hide table {
				mso-hide: all;
				display: none;
				max-height: 0px;
				overflow: hidden;
			}

			@media (max-width:500px) {
				.desktop_hide table.icons-inner {
					display: inline-block !important;
				}

				.icons-inner {
					text-align: center;
				}

				.icons-inner td {
					margin: 0 auto;
				}

				.row-content {
					width: 100% !important;
				}

				.mobile_hide {
					display: none;
				}

				.stack .column {
					width: 100%;
					display: block;
				}

				.mobile_hide {
					min-height: 0;
					max-height: 0;
					max-width: 0;
					overflow: hidden;
					font-size: 0px;
				}

				.desktop_hide,
				.desktop_hide table {
					display: table !important;
					max-height: none !important;
				}
			}
		</style>
	</head>

	<body style="background-color: #f0f2f5; margin: 0; padding: 0; -webkit-text-size-adjust: none; text-size-adjust: none;">
		<table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #f0f2f5;">
			<tbody>
				<tr>
					<td>
						<table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
							<tbody>
								<tr>
									<td>
										<table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff; color: #000000; border-radius: 0; width: 480px;" width="480">
											<tbody>
												<tr>
													<td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; background-color: #f0f2f5; vertical-align: top; padding-top: 5px; padding-bottom: 5px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
														<div class="spacer_block" style="height:60px;line-height:60px;font-size:1px;">&#8202;</div>
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</tbody>
						</table>
						<table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
							<tbody>
								<tr>
									<td>
										<table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff; color: #000000; border-top: 0 solid #000000; border-right: 0px solid #000000; border-left: 0 solid #000000; border-bottom: 0 solid #000000; width: 480px;" width="480">
											<tbody>
												<tr>
													<td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
														<table class="icons_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
															<tr>
																<td class="pad" style="vertical-align: middle; color: #000000; font-size: 18px; padding-bottom: 10px; padding-left: 30px; padding-right: 30px; padding-top: 10px; text-align: left;">
																	<table class="alignment" cellpadding="0" cellspacing="0" role="presentation" align="left" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
																		<tr>
																			<td style="vertical-align: middle; text-align: center; padding-top: 5px; padding-bottom: 5px; padding-left: 5px; padding-right: 5px;"><img class="icon" src="{%config site_logo%}" alt="{%config name%}" height="32" width="39" align="center" style="display: block; height: auto; margin: 0 auto; border: 0;"></td>
																		</tr>
																	</table>
																</td>
															</tr>
														</table>
														<table class="divider_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
															<tr>
																<td class="pad">
																	<div class="alignment" align="center">
																		<table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
																			<tr>
																				<td class="divider_inner" style="font-size: 1px; line-height: 1px; border-top: 1px solid #E4EAEF;"><span>&#8202;</span></td>
																			</tr>
																		</table>
																	</div>
																</td>
															</tr>
														</table>
														<table class="text_block block-3" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
															<tr>
																<td class="pad" style="padding-bottom:15px;padding-left:30px;padding-right:30px;padding-top:30px;">
																	<div>
																		<div class style="font-size: 12px; mso-line-height-alt: 14px; color: #14171a; line-height: 1.2;">
																			<p style="margin: 0; font-size: 22px; text-align: left; mso-line-height-alt: 26.4px;"><span style="font-size:22px;"><strong><?php echo cl_translate("Hello"); ?>, <?php echo($cl["me"]["name"]); ?>!</strong></span></p>
																		</div>
																	</div>
																</td>
															</tr>
														</table>
														<table class="text_block block-4" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
															<tr>
																<td class="pad" style="padding-bottom:15px;padding-left:30px;padding-right:30px;padding-top:5px;">
																	<div>
																		<div class style="font-size: 12px; mso-line-height-alt: 18px; color: #14171a; line-height: 1.5;">
																			<p style="margin: 0; font-size: 14px; text-align: left; mso-line-height-alt: 21px;"><span style="font-size:14px;"><?php echo cl_translate("We have receive a request to change the password for your {@site_name@} account.", array("site_name" => cl_html_el("strong", $cl["config"]["name"]))); ?></span></p>
																			<p style="margin: 0; font-size: 14px; text-align: left; mso-line-height-alt: 18px;">&nbsp;</p>
																			<p style="margin: 0; font-size: 14px; text-align: left; mso-line-height-alt: 21px;"><span style="font-size:14px;"><?php echo cl_translate("Tap the botton below to reset your account password"); ?></span></p>
																		</div>
																	</div>
																</td>
															</tr>
														</table>
														<table class="button_block block-5" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
															<tr>
																<td class="pad" style="padding-left:30px;padding-right:30px;text-align:left;">
																	<div class="alignment" align="left">
																		<!--[if mso]><v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" style="height:52px;width:222px;v-text-anchor:middle;" arcsize="4%" stroke="false" fillcolor="#009eff"><w:anchorlock/><v:textbox inset="0px,0px,0px,0px"><center style="color:#ffffff; font-size:16px"><![endif]-->
																		<a href="<?php echo ($cl['reset_url']); ?>" style="text-decoration: none;"><div style="text-decoration:none;display:inline-block;color:#ffffff;background-color:#009eff;border-radius:2px;width:auto;border-top:0px solid transparent;font-weight:400;border-right:0px solid transparent;border-bottom:0px solid transparent;border-left:0px solid transparent;padding-top:10px;padding-bottom:10px; font-size:16px;text-align:center;mso-border-alt:none;word-break:keep-all;"><span style="padding-left:30px;padding-right:30px;font-size:16px;display:inline-block;letter-spacing:normal;"><span dir="ltr" style="margin: 0; word-break: break-word; line-height: 32px; text-transform: uppercase;"><strong><?php echo cl_translate("Reset my password"); ?></strong></span></span></div></a>
																		<!--[if mso]></center></v:textbox></v:roundrect><![endif]-->
																	</div>
																</td>
															</tr>
														</table>
														<table class="text_block block-6" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
															<tr>
																<td class="pad" style="padding-bottom:15px;padding-left:30px;padding-right:30px;padding-top:30px;">
																	<div>
																		<div class style="font-size: 12px; mso-line-height-alt: 14px; color: #4c5e6e; line-height: 1.2;">
																			<p style="margin: 0; font-size: 14px; text-align: left; mso-line-height-alt: 16.8px;"><span style="font-size:14px;"><em><span style><?php echo cl_translate("If you did not initiate this request. please contact our support team at:"); ?> <strong><a href="mailto:<EMAIL>" target="_blank" style="text-decoration: underline; color: #14171a;" rel="noopener"><?php echo $cl["config"]["email"]; ?></a></strong></span></em></span></p>
																		</div>
																	</div>
																</td>
															</tr>
														</table>
														<table class="text_block block-8" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
															<tr>
																<td class="pad" style="padding-bottom:20px;padding-left:30px;padding-right:30px;padding-top:20px;">
																	<div>
																		<div class style="font-size: 12px; mso-line-height-alt: 14px; color: #4c5e6e; line-height: 1.2;">
																			<p style="margin: 0; font-size: 13px; text-align: left; mso-line-height-alt: 15.6px;"><span style="font-size:13px;"><?php echo cl_translate("Thank you"); ?>,</span></p>
																			<p style="margin: 0; font-size: 13px; text-align: left; mso-line-height-alt: 15.6px;"><span style="font-size:13px;"><?php echo cl_translate("The {@site_name@} support team", array("site_name" => $cl["config"]["name"])); ?></span></p>
																		</div>
																	</div>
																</td>
															</tr>
														</table>
														<table class="divider_block block-7" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
															<tr>
																<td class="pad">
																	<div class="alignment" align="center">
																		<table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
																			<tr>
																				<td class="divider_inner" style="font-size: 1px; line-height: 1px; border-top: 1px solid #E4EAEF;"><span>&#8202;</span></td>
																			</tr>
																		</table>
																	</div>
																</td>
															</tr>
														</table>
														<table class="text_block block-8" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
															<tr>
																<td class="pad" style="padding-bottom:20px;padding-left:30px;padding-right:30px;padding-top:20px;">
																	<div>
																		<div class style="font-size: 10px; mso-line-height-alt: 14px; color: #4c5e6e; line-height: 1.2;">
																			<p style="margin: 0; font-size: 11px; text-align: left; mso-line-height-alt: 15.6px;"><span><?php echo cl_translate('You received this email because you are a registered <a href="{@site_url@}" style="color: #1b95e0;">{@site_name@}</a> user. <br> If you have any questions, the answers can be found in the F.A.Qs section.', array("site_name" => $cl["config"]["name"],"site_url" => $cl["config"]["url"])); ?></span></p>
																		</div>
																	</div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</tbody>
						</table>
						<table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
							<tbody>
								<tr>
									<td>
										<table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff; color: #000000; border-radius: 0; width: 480px;" width="480">
											<tbody>
												<tr>
													<td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; background-color: #f0f2f5; vertical-align: top; padding-top: 5px; padding-bottom: 5px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
														<div class="spacer_block" style="height:60px;line-height:60px;font-size:1px;">&#8202;</div>
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</tbody>
						</table>
					</td>
				</tr>
			</tbody>
		</table><!-- End -->
	</body>
</html>