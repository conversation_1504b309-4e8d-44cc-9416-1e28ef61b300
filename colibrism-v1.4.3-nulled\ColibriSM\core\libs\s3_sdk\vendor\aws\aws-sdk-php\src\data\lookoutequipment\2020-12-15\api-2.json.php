<?php
// This file was auto-generated from sdk-root/src/data/lookoutequipment/2020-12-15/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-12-15', 'endpointPrefix' => 'lookoutequipment', 'jsonVersion' => '1.0', 'protocol' => 'json', 'serviceAbbreviation' => 'LookoutEquipment', 'serviceFullName' => 'Amazon Lookout for Equipment', 'serviceId' => 'LookoutEquipment', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSLookoutEquipmentFrontendService', 'uid' => 'lookoutequipment-2020-12-15', ], 'operations' => [ 'CreateDataset' => [ 'name' => 'CreateDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetRequest', ], 'output' => [ 'shape' => 'CreateDatasetResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateInferenceScheduler' => [ 'name' => 'CreateInferenceScheduler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInferenceSchedulerRequest', ], 'output' => [ 'shape' => 'CreateInferenceSchedulerResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateLabel' => [ 'name' => 'CreateLabel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLabelRequest', ], 'output' => [ 'shape' => 'CreateLabelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateLabelGroup' => [ 'name' => 'CreateLabelGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLabelGroupRequest', ], 'output' => [ 'shape' => 'CreateLabelGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateModel' => [ 'name' => 'CreateModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateModelRequest', ], 'output' => [ 'shape' => 'CreateModelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateRetrainingScheduler' => [ 'name' => 'CreateRetrainingScheduler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRetrainingSchedulerRequest', ], 'output' => [ 'shape' => 'CreateRetrainingSchedulerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteDataset' => [ 'name' => 'DeleteDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDatasetRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteInferenceScheduler' => [ 'name' => 'DeleteInferenceScheduler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInferenceSchedulerRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteLabel' => [ 'name' => 'DeleteLabel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLabelRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteLabelGroup' => [ 'name' => 'DeleteLabelGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLabelGroupRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteModel' => [ 'name' => 'DeleteModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteModelRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteRetrainingScheduler' => [ 'name' => 'DeleteRetrainingScheduler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRetrainingSchedulerRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeDataIngestionJob' => [ 'name' => 'DescribeDataIngestionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDataIngestionJobRequest', ], 'output' => [ 'shape' => 'DescribeDataIngestionJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeDataset' => [ 'name' => 'DescribeDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetRequest', ], 'output' => [ 'shape' => 'DescribeDatasetResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeInferenceScheduler' => [ 'name' => 'DescribeInferenceScheduler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInferenceSchedulerRequest', ], 'output' => [ 'shape' => 'DescribeInferenceSchedulerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeLabel' => [ 'name' => 'DescribeLabel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLabelRequest', ], 'output' => [ 'shape' => 'DescribeLabelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeLabelGroup' => [ 'name' => 'DescribeLabelGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLabelGroupRequest', ], 'output' => [ 'shape' => 'DescribeLabelGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeModel' => [ 'name' => 'DescribeModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeModelRequest', ], 'output' => [ 'shape' => 'DescribeModelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeModelVersion' => [ 'name' => 'DescribeModelVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeModelVersionRequest', ], 'output' => [ 'shape' => 'DescribeModelVersionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeResourcePolicy' => [ 'name' => 'DescribeResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeResourcePolicyRequest', ], 'output' => [ 'shape' => 'DescribeResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeRetrainingScheduler' => [ 'name' => 'DescribeRetrainingScheduler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRetrainingSchedulerRequest', ], 'output' => [ 'shape' => 'DescribeRetrainingSchedulerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ImportDataset' => [ 'name' => 'ImportDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportDatasetRequest', ], 'output' => [ 'shape' => 'ImportDatasetResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ImportModelVersion' => [ 'name' => 'ImportModelVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportModelVersionRequest', ], 'output' => [ 'shape' => 'ImportModelVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDataIngestionJobs' => [ 'name' => 'ListDataIngestionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDataIngestionJobsRequest', ], 'output' => [ 'shape' => 'ListDataIngestionJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDatasets' => [ 'name' => 'ListDatasets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetsRequest', ], 'output' => [ 'shape' => 'ListDatasetsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListInferenceEvents' => [ 'name' => 'ListInferenceEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInferenceEventsRequest', ], 'output' => [ 'shape' => 'ListInferenceEventsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListInferenceExecutions' => [ 'name' => 'ListInferenceExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInferenceExecutionsRequest', ], 'output' => [ 'shape' => 'ListInferenceExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListInferenceSchedulers' => [ 'name' => 'ListInferenceSchedulers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInferenceSchedulersRequest', ], 'output' => [ 'shape' => 'ListInferenceSchedulersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListLabelGroups' => [ 'name' => 'ListLabelGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLabelGroupsRequest', ], 'output' => [ 'shape' => 'ListLabelGroupsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListLabels' => [ 'name' => 'ListLabels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLabelsRequest', ], 'output' => [ 'shape' => 'ListLabelsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListModelVersions' => [ 'name' => 'ListModelVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListModelVersionsRequest', ], 'output' => [ 'shape' => 'ListModelVersionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListModels' => [ 'name' => 'ListModels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListModelsRequest', ], 'output' => [ 'shape' => 'ListModelsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRetrainingSchedulers' => [ 'name' => 'ListRetrainingSchedulers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRetrainingSchedulersRequest', ], 'output' => [ 'shape' => 'ListRetrainingSchedulersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSensorStatistics' => [ 'name' => 'ListSensorStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSensorStatisticsRequest', ], 'output' => [ 'shape' => 'ListSensorStatisticsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartDataIngestionJob' => [ 'name' => 'StartDataIngestionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDataIngestionJobRequest', ], 'output' => [ 'shape' => 'StartDataIngestionJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartInferenceScheduler' => [ 'name' => 'StartInferenceScheduler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartInferenceSchedulerRequest', ], 'output' => [ 'shape' => 'StartInferenceSchedulerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartRetrainingScheduler' => [ 'name' => 'StartRetrainingScheduler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartRetrainingSchedulerRequest', ], 'output' => [ 'shape' => 'StartRetrainingSchedulerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopInferenceScheduler' => [ 'name' => 'StopInferenceScheduler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopInferenceSchedulerRequest', ], 'output' => [ 'shape' => 'StopInferenceSchedulerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopRetrainingScheduler' => [ 'name' => 'StopRetrainingScheduler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopRetrainingSchedulerRequest', ], 'output' => [ 'shape' => 'StopRetrainingSchedulerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateActiveModelVersion' => [ 'name' => 'UpdateActiveModelVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateActiveModelVersionRequest', ], 'output' => [ 'shape' => 'UpdateActiveModelVersionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateInferenceScheduler' => [ 'name' => 'UpdateInferenceScheduler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateInferenceSchedulerRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateLabelGroup' => [ 'name' => 'UpdateLabelGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLabelGroupRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateModel' => [ 'name' => 'UpdateModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateModelRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateRetrainingScheduler' => [ 'name' => 'UpdateRetrainingScheduler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRetrainingSchedulerRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'BoundedLengthString', ], ], 'exception' => true, ], 'AmazonResourceArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'AutoPromotionResult' => [ 'type' => 'string', 'enum' => [ 'MODEL_PROMOTED', 'MODEL_NOT_PROMOTED', 'RETRAINING_INTERNAL_ERROR', 'RETRAINING_CUSTOMER_ERROR', 'RETRAINING_CANCELLED', ], ], 'AutoPromotionResultReason' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Boolean' => [ 'type' => 'boolean', ], 'BoundedLengthString' => [ 'type' => 'string', 'max' => 5000, 'min' => 1, 'pattern' => '[\\P{M}\\p{M}]{1,5000}', ], 'CategoricalValues' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'StatisticalIssueStatus', ], 'NumberOfCategory' => [ 'shape' => 'Integer', ], ], ], 'Comments' => [ 'type' => 'string', 'max' => 2560, 'min' => 1, 'pattern' => '[\\P{M}\\p{M}]{1,2560}', ], 'ComponentName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._\\-]{1,200}$', ], 'ComponentTimestampDelimiter' => [ 'type' => 'string', 'max' => 1, 'min' => 0, 'pattern' => '^(\\-|\\_|\\s)?$', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'BoundedLengthString', ], ], 'exception' => true, ], 'CountPercent' => [ 'type' => 'structure', 'required' => [ 'Count', 'Percentage', ], 'members' => [ 'Count' => [ 'shape' => 'Integer', ], 'Percentage' => [ 'shape' => 'Float', ], ], ], 'CreateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetName', 'ClientToken', ], 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetName', ], 'DatasetSchema' => [ 'shape' => 'DatasetSchema', ], 'ServerSideKmsKeyId' => [ 'shape' => 'NameOrArn', ], 'ClientToken' => [ 'shape' => 'IdempotenceToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetName', ], 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'Status' => [ 'shape' => 'DatasetStatus', ], ], ], 'CreateInferenceSchedulerRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', 'InferenceSchedulerName', 'DataUploadFrequency', 'DataInputConfiguration', 'DataOutputConfiguration', 'RoleArn', 'ClientToken', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerName', ], 'DataDelayOffsetInMinutes' => [ 'shape' => 'DataDelayOffsetInMinutes', ], 'DataUploadFrequency' => [ 'shape' => 'DataUploadFrequency', ], 'DataInputConfiguration' => [ 'shape' => 'InferenceInputConfiguration', ], 'DataOutputConfiguration' => [ 'shape' => 'InferenceOutputConfiguration', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'ServerSideKmsKeyId' => [ 'shape' => 'NameOrArn', ], 'ClientToken' => [ 'shape' => 'IdempotenceToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateInferenceSchedulerResponse' => [ 'type' => 'structure', 'members' => [ 'InferenceSchedulerArn' => [ 'shape' => 'InferenceSchedulerArn', ], 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerName', ], 'Status' => [ 'shape' => 'InferenceSchedulerStatus', ], ], ], 'CreateLabelGroupRequest' => [ 'type' => 'structure', 'required' => [ 'LabelGroupName', 'ClientToken', ], 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], 'FaultCodes' => [ 'shape' => 'FaultCodes', ], 'ClientToken' => [ 'shape' => 'IdempotenceToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateLabelGroupResponse' => [ 'type' => 'structure', 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], 'LabelGroupArn' => [ 'shape' => 'LabelGroupArn', ], ], ], 'CreateLabelRequest' => [ 'type' => 'structure', 'required' => [ 'LabelGroupName', 'StartTime', 'EndTime', 'Rating', 'ClientToken', ], 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Rating' => [ 'shape' => 'LabelRating', ], 'FaultCode' => [ 'shape' => 'FaultCode', ], 'Notes' => [ 'shape' => 'Comments', ], 'Equipment' => [ 'shape' => 'Equipment', ], 'ClientToken' => [ 'shape' => 'IdempotenceToken', 'idempotencyToken' => true, ], ], ], 'CreateLabelResponse' => [ 'type' => 'structure', 'members' => [ 'LabelId' => [ 'shape' => 'LabelId', ], ], ], 'CreateModelRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', 'DatasetName', 'ClientToken', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'DatasetName' => [ 'shape' => 'DatasetIdentifier', ], 'DatasetSchema' => [ 'shape' => 'DatasetSchema', ], 'LabelsInputConfiguration' => [ 'shape' => 'LabelsInputConfiguration', ], 'ClientToken' => [ 'shape' => 'IdempotenceToken', 'idempotencyToken' => true, ], 'TrainingDataStartTime' => [ 'shape' => 'Timestamp', ], 'TrainingDataEndTime' => [ 'shape' => 'Timestamp', ], 'EvaluationDataStartTime' => [ 'shape' => 'Timestamp', ], 'EvaluationDataEndTime' => [ 'shape' => 'Timestamp', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'DataPreProcessingConfiguration' => [ 'shape' => 'DataPreProcessingConfiguration', ], 'ServerSideKmsKeyId' => [ 'shape' => 'NameOrArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'OffCondition' => [ 'shape' => 'OffCondition', ], ], ], 'CreateModelResponse' => [ 'type' => 'structure', 'members' => [ 'ModelArn' => [ 'shape' => 'ModelArn', ], 'Status' => [ 'shape' => 'ModelStatus', ], ], ], 'CreateRetrainingSchedulerRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', 'RetrainingFrequency', 'LookbackWindow', 'ClientToken', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'RetrainingStartDate' => [ 'shape' => 'Timestamp', ], 'RetrainingFrequency' => [ 'shape' => 'RetrainingFrequency', ], 'LookbackWindow' => [ 'shape' => 'LookbackWindow', ], 'PromoteMode' => [ 'shape' => 'ModelPromoteMode', ], 'ClientToken' => [ 'shape' => 'IdempotenceToken', 'idempotencyToken' => true, ], ], ], 'CreateRetrainingSchedulerResponse' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'Status' => [ 'shape' => 'RetrainingSchedulerStatus', ], ], ], 'DataDelayOffsetInMinutes' => [ 'type' => 'long', 'max' => 60, 'min' => 0, ], 'DataIngestionJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataIngestionJobSummary', ], ], 'DataIngestionJobSummary' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'IngestionJobId', ], 'DatasetName' => [ 'shape' => 'DatasetName', ], 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'IngestionInputConfiguration' => [ 'shape' => 'IngestionInputConfiguration', ], 'Status' => [ 'shape' => 'IngestionJobStatus', ], ], ], 'DataPreProcessingConfiguration' => [ 'type' => 'structure', 'members' => [ 'TargetSamplingRate' => [ 'shape' => 'TargetSamplingRate', ], ], ], 'DataQualitySummary' => [ 'type' => 'structure', 'required' => [ 'InsufficientSensorData', 'MissingSensorData', 'InvalidSensorData', 'UnsupportedTimestamps', 'DuplicateTimestamps', ], 'members' => [ 'InsufficientSensorData' => [ 'shape' => 'InsufficientSensorData', ], 'MissingSensorData' => [ 'shape' => 'MissingSensorData', ], 'InvalidSensorData' => [ 'shape' => 'InvalidSensorData', ], 'UnsupportedTimestamps' => [ 'shape' => 'UnsupportedTimestamps', ], 'DuplicateTimestamps' => [ 'shape' => 'DuplicateTimestamps', ], ], ], 'DataSizeInBytes' => [ 'type' => 'long', 'min' => 0, ], 'DataUploadFrequency' => [ 'type' => 'string', 'enum' => [ 'PT5M', 'PT10M', 'PT15M', 'PT30M', 'PT1H', ], ], 'DatasetArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:lookoutequipment:[a-zA-Z0-9\\-]*:[0-9]{12}:dataset\\/.+', ], 'DatasetIdentifier' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z_-]{1,200}$', ], 'DatasetName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z_-]{1,200}$', ], 'DatasetSchema' => [ 'type' => 'structure', 'members' => [ 'InlineDataSchema' => [ 'shape' => 'InlineDataSchema', 'jsonvalue' => true, ], ], ], 'DatasetStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'INGESTION_IN_PROGRESS', 'ACTIVE', 'IMPORT_IN_PROGRESS', ], ], 'DatasetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetSummary', ], ], 'DatasetSummary' => [ 'type' => 'structure', 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetName', ], 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'Status' => [ 'shape' => 'DatasetStatus', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetName', ], 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetIdentifier', ], ], ], 'DeleteInferenceSchedulerRequest' => [ 'type' => 'structure', 'required' => [ 'InferenceSchedulerName', ], 'members' => [ 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerIdentifier', ], ], ], 'DeleteLabelGroupRequest' => [ 'type' => 'structure', 'required' => [ 'LabelGroupName', ], 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], ], ], 'DeleteLabelRequest' => [ 'type' => 'structure', 'required' => [ 'LabelGroupName', 'LabelId', ], 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], 'LabelId' => [ 'shape' => 'LabelId', ], ], ], 'DeleteModelRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], ], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DeleteRetrainingSchedulerRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], ], ], 'DescribeDataIngestionJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'IngestionJobId', ], ], ], 'DescribeDataIngestionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'IngestionJobId', ], 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'IngestionInputConfiguration' => [ 'shape' => 'IngestionInputConfiguration', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'IngestionJobStatus', ], 'FailedReason' => [ 'shape' => 'BoundedLengthString', ], 'DataQualitySummary' => [ 'shape' => 'DataQualitySummary', ], 'IngestedFilesSummary' => [ 'shape' => 'IngestedFilesSummary', ], 'StatusDetail' => [ 'shape' => 'BoundedLengthString', ], 'IngestedDataSize' => [ 'shape' => 'DataSizeInBytes', ], 'DataStartTime' => [ 'shape' => 'Timestamp', ], 'DataEndTime' => [ 'shape' => 'Timestamp', ], 'SourceDatasetArn' => [ 'shape' => 'DatasetArn', ], ], ], 'DescribeDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetName', ], 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetIdentifier', ], ], ], 'DescribeDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetName', ], 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'DatasetStatus', ], 'Schema' => [ 'shape' => 'InlineDataSchema', 'jsonvalue' => true, ], 'ServerSideKmsKeyId' => [ 'shape' => 'KmsKeyArn', ], 'IngestionInputConfiguration' => [ 'shape' => 'IngestionInputConfiguration', ], 'DataQualitySummary' => [ 'shape' => 'DataQualitySummary', ], 'IngestedFilesSummary' => [ 'shape' => 'IngestedFilesSummary', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'DataStartTime' => [ 'shape' => 'Timestamp', ], 'DataEndTime' => [ 'shape' => 'Timestamp', ], 'SourceDatasetArn' => [ 'shape' => 'DatasetArn', ], ], ], 'DescribeInferenceSchedulerRequest' => [ 'type' => 'structure', 'required' => [ 'InferenceSchedulerName', ], 'members' => [ 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerIdentifier', ], ], ], 'DescribeInferenceSchedulerResponse' => [ 'type' => 'structure', 'members' => [ 'ModelArn' => [ 'shape' => 'ModelArn', ], 'ModelName' => [ 'shape' => 'ModelName', ], 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerName', ], 'InferenceSchedulerArn' => [ 'shape' => 'InferenceSchedulerArn', ], 'Status' => [ 'shape' => 'InferenceSchedulerStatus', ], 'DataDelayOffsetInMinutes' => [ 'shape' => 'DataDelayOffsetInMinutes', ], 'DataUploadFrequency' => [ 'shape' => 'DataUploadFrequency', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'DataInputConfiguration' => [ 'shape' => 'InferenceInputConfiguration', ], 'DataOutputConfiguration' => [ 'shape' => 'InferenceOutputConfiguration', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'ServerSideKmsKeyId' => [ 'shape' => 'KmsKeyArn', ], 'LatestInferenceResult' => [ 'shape' => 'LatestInferenceResult', ], ], ], 'DescribeLabelGroupRequest' => [ 'type' => 'structure', 'required' => [ 'LabelGroupName', ], 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], ], ], 'DescribeLabelGroupResponse' => [ 'type' => 'structure', 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], 'LabelGroupArn' => [ 'shape' => 'LabelGroupArn', ], 'FaultCodes' => [ 'shape' => 'FaultCodes', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeLabelRequest' => [ 'type' => 'structure', 'required' => [ 'LabelGroupName', 'LabelId', ], 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], 'LabelId' => [ 'shape' => 'LabelId', ], ], ], 'DescribeLabelResponse' => [ 'type' => 'structure', 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], 'LabelGroupArn' => [ 'shape' => 'LabelGroupArn', ], 'LabelId' => [ 'shape' => 'LabelId', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Rating' => [ 'shape' => 'LabelRating', ], 'FaultCode' => [ 'shape' => 'FaultCode', ], 'Notes' => [ 'shape' => 'Comments', ], 'Equipment' => [ 'shape' => 'Equipment', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeModelRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], ], ], 'DescribeModelResponse' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'DatasetName' => [ 'shape' => 'DatasetName', ], 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'Schema' => [ 'shape' => 'InlineDataSchema', 'jsonvalue' => true, ], 'LabelsInputConfiguration' => [ 'shape' => 'LabelsInputConfiguration', ], 'TrainingDataStartTime' => [ 'shape' => 'Timestamp', ], 'TrainingDataEndTime' => [ 'shape' => 'Timestamp', ], 'EvaluationDataStartTime' => [ 'shape' => 'Timestamp', ], 'EvaluationDataEndTime' => [ 'shape' => 'Timestamp', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'DataPreProcessingConfiguration' => [ 'shape' => 'DataPreProcessingConfiguration', ], 'Status' => [ 'shape' => 'ModelStatus', ], 'TrainingExecutionStartTime' => [ 'shape' => 'Timestamp', ], 'TrainingExecutionEndTime' => [ 'shape' => 'Timestamp', ], 'FailedReason' => [ 'shape' => 'BoundedLengthString', ], 'ModelMetrics' => [ 'shape' => 'ModelMetrics', 'jsonvalue' => true, ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ServerSideKmsKeyId' => [ 'shape' => 'KmsKeyArn', ], 'OffCondition' => [ 'shape' => 'OffCondition', ], 'SourceModelVersionArn' => [ 'shape' => 'ModelVersionArn', ], 'ImportJobStartTime' => [ 'shape' => 'Timestamp', ], 'ImportJobEndTime' => [ 'shape' => 'Timestamp', ], 'ActiveModelVersion' => [ 'shape' => 'ModelVersion', ], 'ActiveModelVersionArn' => [ 'shape' => 'ModelVersionArn', ], 'ModelVersionActivatedAt' => [ 'shape' => 'Timestamp', ], 'PreviousActiveModelVersion' => [ 'shape' => 'ModelVersion', ], 'PreviousActiveModelVersionArn' => [ 'shape' => 'ModelVersionArn', ], 'PreviousModelVersionActivatedAt' => [ 'shape' => 'Timestamp', ], 'PriorModelMetrics' => [ 'shape' => 'ModelMetrics', 'jsonvalue' => true, ], 'LatestScheduledRetrainingFailedReason' => [ 'shape' => 'BoundedLengthString', ], 'LatestScheduledRetrainingStatus' => [ 'shape' => 'ModelVersionStatus', ], 'LatestScheduledRetrainingModelVersion' => [ 'shape' => 'ModelVersion', ], 'LatestScheduledRetrainingStartTime' => [ 'shape' => 'Timestamp', ], 'LatestScheduledRetrainingAvailableDataInDays' => [ 'shape' => 'Integer', ], 'NextScheduledRetrainingStartDate' => [ 'shape' => 'Timestamp', ], 'AccumulatedInferenceDataStartTime' => [ 'shape' => 'Timestamp', ], 'AccumulatedInferenceDataEndTime' => [ 'shape' => 'Timestamp', ], 'RetrainingSchedulerStatus' => [ 'shape' => 'RetrainingSchedulerStatus', ], ], ], 'DescribeModelVersionRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', 'ModelVersion', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelVersion' => [ 'shape' => 'ModelVersion', ], ], ], 'DescribeModelVersionResponse' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'ModelVersion' => [ 'shape' => 'ModelVersion', ], 'ModelVersionArn' => [ 'shape' => 'ModelVersionArn', ], 'Status' => [ 'shape' => 'ModelVersionStatus', ], 'SourceType' => [ 'shape' => 'ModelVersionSourceType', ], 'DatasetName' => [ 'shape' => 'DatasetName', ], 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'Schema' => [ 'shape' => 'InlineDataSchema', ], 'LabelsInputConfiguration' => [ 'shape' => 'LabelsInputConfiguration', ], 'TrainingDataStartTime' => [ 'shape' => 'Timestamp', ], 'TrainingDataEndTime' => [ 'shape' => 'Timestamp', ], 'EvaluationDataStartTime' => [ 'shape' => 'Timestamp', ], 'EvaluationDataEndTime' => [ 'shape' => 'Timestamp', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'DataPreProcessingConfiguration' => [ 'shape' => 'DataPreProcessingConfiguration', ], 'TrainingExecutionStartTime' => [ 'shape' => 'Timestamp', ], 'TrainingExecutionEndTime' => [ 'shape' => 'Timestamp', ], 'FailedReason' => [ 'shape' => 'BoundedLengthString', ], 'ModelMetrics' => [ 'shape' => 'ModelMetrics', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ServerSideKmsKeyId' => [ 'shape' => 'KmsKeyArn', ], 'OffCondition' => [ 'shape' => 'OffCondition', ], 'SourceModelVersionArn' => [ 'shape' => 'ModelVersionArn', ], 'ImportJobStartTime' => [ 'shape' => 'Timestamp', ], 'ImportJobEndTime' => [ 'shape' => 'Timestamp', ], 'ImportedDataSizeInBytes' => [ 'shape' => 'DataSizeInBytes', ], 'PriorModelMetrics' => [ 'shape' => 'ModelMetrics', ], 'RetrainingAvailableDataInDays' => [ 'shape' => 'Integer', ], 'AutoPromotionResult' => [ 'shape' => 'AutoPromotionResult', ], 'AutoPromotionResultReason' => [ 'shape' => 'AutoPromotionResultReason', ], ], ], 'DescribeResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DescribeResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyRevisionId' => [ 'shape' => 'PolicyRevisionId', ], 'ResourcePolicy' => [ 'shape' => 'Policy', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeRetrainingSchedulerRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], ], ], 'DescribeRetrainingSchedulerResponse' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'RetrainingStartDate' => [ 'shape' => 'Timestamp', ], 'RetrainingFrequency' => [ 'shape' => 'RetrainingFrequency', ], 'LookbackWindow' => [ 'shape' => 'LookbackWindow', ], 'Status' => [ 'shape' => 'RetrainingSchedulerStatus', ], 'PromoteMode' => [ 'shape' => 'ModelPromoteMode', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'DuplicateTimestamps' => [ 'type' => 'structure', 'required' => [ 'TotalNumberOfDuplicateTimestamps', ], 'members' => [ 'TotalNumberOfDuplicateTimestamps' => [ 'shape' => 'Integer', ], ], ], 'Equipment' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[\\P{M}\\p{M}]{1,200}', ], 'EventDurationInSeconds' => [ 'type' => 'long', 'min' => 0, ], 'FaultCode' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\P{M}\\p{M}]{1,100}', ], 'FaultCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'FaultCode', ], 'max' => 50, 'min' => 0, ], 'FileNameTimestampFormat' => [ 'type' => 'string', 'pattern' => '^EPOCH|yyyy-MM-dd-HH-mm-ss|yyyyMMddHHmmss$', ], 'Float' => [ 'type' => 'float', ], 'IamRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:iam::[0-9]{12}:role/.+', ], 'IdempotenceToken' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '\\p{ASCII}{1,256}', ], 'ImportDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'SourceDatasetArn', 'ClientToken', ], 'members' => [ 'SourceDatasetArn' => [ 'shape' => 'DatasetArn', ], 'DatasetName' => [ 'shape' => 'DatasetName', ], 'ClientToken' => [ 'shape' => 'IdempotenceToken', 'idempotencyToken' => true, ], 'ServerSideKmsKeyId' => [ 'shape' => 'NameOrArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ImportDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetName', ], 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'Status' => [ 'shape' => 'DatasetStatus', ], 'JobId' => [ 'shape' => 'IngestionJobId', ], ], ], 'ImportModelVersionRequest' => [ 'type' => 'structure', 'required' => [ 'SourceModelVersionArn', 'DatasetName', 'ClientToken', ], 'members' => [ 'SourceModelVersionArn' => [ 'shape' => 'ModelVersionArn', ], 'ModelName' => [ 'shape' => 'ModelName', ], 'DatasetName' => [ 'shape' => 'DatasetIdentifier', ], 'LabelsInputConfiguration' => [ 'shape' => 'LabelsInputConfiguration', ], 'ClientToken' => [ 'shape' => 'IdempotenceToken', 'idempotencyToken' => true, ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'ServerSideKmsKeyId' => [ 'shape' => 'NameOrArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'InferenceDataImportStrategy' => [ 'shape' => 'InferenceDataImportStrategy', ], ], ], 'ImportModelVersionResponse' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'ModelVersionArn' => [ 'shape' => 'ModelVersionArn', ], 'ModelVersion' => [ 'shape' => 'ModelVersion', ], 'Status' => [ 'shape' => 'ModelVersionStatus', ], ], ], 'InferenceDataImportStrategy' => [ 'type' => 'string', 'enum' => [ 'NO_IMPORT', 'ADD_WHEN_EMPTY', 'OVERWRITE', ], ], 'InferenceEventSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'InferenceEventSummary', ], ], 'InferenceEventSummary' => [ 'type' => 'structure', 'members' => [ 'InferenceSchedulerArn' => [ 'shape' => 'InferenceSchedulerArn', ], 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerName', ], 'EventStartTime' => [ 'shape' => 'Timestamp', ], 'EventEndTime' => [ 'shape' => 'Timestamp', ], 'Diagnostics' => [ 'shape' => 'ModelMetrics', ], 'EventDurationInSeconds' => [ 'shape' => 'EventDurationInSeconds', ], ], ], 'InferenceExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCESS', 'FAILED', ], ], 'InferenceExecutionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'InferenceExecutionSummary', ], ], 'InferenceExecutionSummary' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerName', ], 'InferenceSchedulerArn' => [ 'shape' => 'InferenceSchedulerArn', ], 'ScheduledStartTime' => [ 'shape' => 'Timestamp', ], 'DataStartTime' => [ 'shape' => 'Timestamp', ], 'DataEndTime' => [ 'shape' => 'Timestamp', ], 'DataInputConfiguration' => [ 'shape' => 'InferenceInputConfiguration', ], 'DataOutputConfiguration' => [ 'shape' => 'InferenceOutputConfiguration', ], 'CustomerResultObject' => [ 'shape' => 'S3Object', ], 'Status' => [ 'shape' => 'InferenceExecutionStatus', ], 'FailedReason' => [ 'shape' => 'BoundedLengthString', ], 'ModelVersion' => [ 'shape' => 'ModelVersion', ], 'ModelVersionArn' => [ 'shape' => 'ModelVersionArn', ], ], ], 'InferenceInputConfiguration' => [ 'type' => 'structure', 'members' => [ 'S3InputConfiguration' => [ 'shape' => 'InferenceS3InputConfiguration', ], 'InputTimeZoneOffset' => [ 'shape' => 'TimeZoneOffset', ], 'InferenceInputNameConfiguration' => [ 'shape' => 'InferenceInputNameConfiguration', ], ], ], 'InferenceInputNameConfiguration' => [ 'type' => 'structure', 'members' => [ 'TimestampFormat' => [ 'shape' => 'FileNameTimestampFormat', ], 'ComponentTimestampDelimiter' => [ 'shape' => 'ComponentTimestampDelimiter', ], ], ], 'InferenceOutputConfiguration' => [ 'type' => 'structure', 'required' => [ 'S3OutputConfiguration', ], 'members' => [ 'S3OutputConfiguration' => [ 'shape' => 'InferenceS3OutputConfiguration', ], 'KmsKeyId' => [ 'shape' => 'NameOrArn', ], ], ], 'InferenceS3InputConfiguration' => [ 'type' => 'structure', 'required' => [ 'Bucket', ], 'members' => [ 'Bucket' => [ 'shape' => 'S3Bucket', ], 'Prefix' => [ 'shape' => 'S3Prefix', ], ], ], 'InferenceS3OutputConfiguration' => [ 'type' => 'structure', 'required' => [ 'Bucket', ], 'members' => [ 'Bucket' => [ 'shape' => 'S3Bucket', ], 'Prefix' => [ 'shape' => 'S3Prefix', ], ], ], 'InferenceSchedulerArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:lookoutequipment:[a-zA-Z0-9\\-]*:[0-9]{12}:inference-scheduler\\/.+', ], 'InferenceSchedulerIdentifier' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z_-]{1,200}$', ], 'InferenceSchedulerName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z_-]{1,200}$', ], 'InferenceSchedulerStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'RUNNING', 'STOPPING', 'STOPPED', ], ], 'InferenceSchedulerSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'InferenceSchedulerSummary', ], ], 'InferenceSchedulerSummary' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerName', ], 'InferenceSchedulerArn' => [ 'shape' => 'InferenceSchedulerArn', ], 'Status' => [ 'shape' => 'InferenceSchedulerStatus', ], 'DataDelayOffsetInMinutes' => [ 'shape' => 'DataDelayOffsetInMinutes', ], 'DataUploadFrequency' => [ 'shape' => 'DataUploadFrequency', ], 'LatestInferenceResult' => [ 'shape' => 'LatestInferenceResult', ], ], ], 'IngestedFilesSummary' => [ 'type' => 'structure', 'required' => [ 'TotalNumberOfFiles', 'IngestedNumberOfFiles', ], 'members' => [ 'TotalNumberOfFiles' => [ 'shape' => 'Integer', ], 'IngestedNumberOfFiles' => [ 'shape' => 'Integer', ], 'DiscardedFiles' => [ 'shape' => 'ListOfDiscardedFiles', ], ], ], 'IngestionInputConfiguration' => [ 'type' => 'structure', 'required' => [ 'S3InputConfiguration', ], 'members' => [ 'S3InputConfiguration' => [ 'shape' => 'IngestionS3InputConfiguration', ], ], ], 'IngestionJobId' => [ 'type' => 'string', 'max' => 32, 'pattern' => '[A-Fa-f0-9]{0,32}', ], 'IngestionJobStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCESS', 'FAILED', 'IMPORT_IN_PROGRESS', ], ], 'IngestionS3InputConfiguration' => [ 'type' => 'structure', 'required' => [ 'Bucket', ], 'members' => [ 'Bucket' => [ 'shape' => 'S3Bucket', ], 'Prefix' => [ 'shape' => 'S3Prefix', ], 'KeyPattern' => [ 'shape' => 'KeyPattern', ], ], ], 'InlineDataSchema' => [ 'type' => 'string', 'max' => 1000000, 'min' => 1, ], 'InsufficientSensorData' => [ 'type' => 'structure', 'required' => [ 'MissingCompleteSensorData', 'SensorsWithShortDateRange', ], 'members' => [ 'MissingCompleteSensorData' => [ 'shape' => 'MissingCompleteSensorData', ], 'SensorsWithShortDateRange' => [ 'shape' => 'SensorsWithShortDateRange', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'BoundedLengthString', ], ], 'exception' => true, 'fault' => true, ], 'InvalidSensorData' => [ 'type' => 'structure', 'required' => [ 'AffectedSensorCount', 'TotalNumberOfInvalidValues', ], 'members' => [ 'AffectedSensorCount' => [ 'shape' => 'Integer', ], 'TotalNumberOfInvalidValues' => [ 'shape' => 'Integer', ], ], ], 'KeyPattern' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:kms:[a-z0-9\\-]*:\\d{12}:[\\w\\-\\/]+', ], 'LabelGroupArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:lookoutequipment:[a-zA-Z0-9\\-]*:[0-9]{12}:label-group\\/.+', ], 'LabelGroupName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z_-]{1,200}$', ], 'LabelGroupSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'LabelGroupSummary', ], ], 'LabelGroupSummary' => [ 'type' => 'structure', 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], 'LabelGroupArn' => [ 'shape' => 'LabelGroupArn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'LabelId' => [ 'type' => 'string', 'max' => 32, 'pattern' => '[A-Fa-f0-9]{0,32}', ], 'LabelRating' => [ 'type' => 'string', 'enum' => [ 'ANOMALY', 'NO_ANOMALY', 'NEUTRAL', ], ], 'LabelSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'LabelSummary', ], ], 'LabelSummary' => [ 'type' => 'structure', 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], 'LabelId' => [ 'shape' => 'LabelId', ], 'LabelGroupArn' => [ 'shape' => 'LabelGroupArn', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Rating' => [ 'shape' => 'LabelRating', ], 'FaultCode' => [ 'shape' => 'FaultCode', ], 'Equipment' => [ 'shape' => 'Equipment', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'LabelsInputConfiguration' => [ 'type' => 'structure', 'members' => [ 'S3InputConfiguration' => [ 'shape' => 'LabelsS3InputConfiguration', ], 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], ], ], 'LabelsS3InputConfiguration' => [ 'type' => 'structure', 'required' => [ 'Bucket', ], 'members' => [ 'Bucket' => [ 'shape' => 'S3Bucket', ], 'Prefix' => [ 'shape' => 'S3Prefix', ], ], ], 'LargeTimestampGaps' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'StatisticalIssueStatus', ], 'NumberOfLargeTimestampGaps' => [ 'shape' => 'Integer', ], 'MaxTimestampGapInDays' => [ 'shape' => 'Integer', ], ], ], 'LatestInferenceResult' => [ 'type' => 'string', 'enum' => [ 'ANOMALOUS', 'NORMAL', ], ], 'ListDataIngestionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Status' => [ 'shape' => 'IngestionJobStatus', ], ], ], 'ListDataIngestionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'DataIngestionJobSummaries' => [ 'shape' => 'DataIngestionJobSummaries', ], ], ], 'ListDatasetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'DatasetNameBeginsWith' => [ 'shape' => 'DatasetName', ], ], ], 'ListDatasetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'DatasetSummaries' => [ 'shape' => 'DatasetSummaries', ], ], ], 'ListInferenceEventsRequest' => [ 'type' => 'structure', 'required' => [ 'InferenceSchedulerName', 'IntervalStartTime', 'IntervalEndTime', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerIdentifier', ], 'IntervalStartTime' => [ 'shape' => 'Timestamp', ], 'IntervalEndTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListInferenceEventsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'InferenceEventSummaries' => [ 'shape' => 'InferenceEventSummaries', ], ], ], 'ListInferenceExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'InferenceSchedulerName', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerIdentifier', ], 'DataStartTimeAfter' => [ 'shape' => 'Timestamp', ], 'DataEndTimeBefore' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'InferenceExecutionStatus', ], ], ], 'ListInferenceExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'InferenceExecutionSummaries' => [ 'shape' => 'InferenceExecutionSummaries', ], ], ], 'ListInferenceSchedulersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'InferenceSchedulerNameBeginsWith' => [ 'shape' => 'InferenceSchedulerIdentifier', ], 'ModelName' => [ 'shape' => 'ModelName', ], 'Status' => [ 'shape' => 'InferenceSchedulerStatus', ], ], ], 'ListInferenceSchedulersResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'InferenceSchedulerSummaries' => [ 'shape' => 'InferenceSchedulerSummaries', ], ], ], 'ListLabelGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'LabelGroupNameBeginsWith' => [ 'shape' => 'LabelGroupName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListLabelGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'LabelGroupSummaries' => [ 'shape' => 'LabelGroupSummaries', ], ], ], 'ListLabelsRequest' => [ 'type' => 'structure', 'required' => [ 'LabelGroupName', ], 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], 'IntervalStartTime' => [ 'shape' => 'Timestamp', ], 'IntervalEndTime' => [ 'shape' => 'Timestamp', ], 'FaultCode' => [ 'shape' => 'FaultCode', ], 'Equipment' => [ 'shape' => 'Equipment', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListLabelsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'LabelSummaries' => [ 'shape' => 'LabelSummaries', ], ], ], 'ListModelVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Status' => [ 'shape' => 'ModelVersionStatus', ], 'SourceType' => [ 'shape' => 'ModelVersionSourceType', ], 'CreatedAtEndTime' => [ 'shape' => 'Timestamp', ], 'CreatedAtStartTime' => [ 'shape' => 'Timestamp', ], 'MaxModelVersion' => [ 'shape' => 'ModelVersion', ], 'MinModelVersion' => [ 'shape' => 'ModelVersion', ], ], ], 'ListModelVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ModelVersionSummaries' => [ 'shape' => 'ModelVersionSummaries', ], ], ], 'ListModelsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Status' => [ 'shape' => 'ModelStatus', ], 'ModelNameBeginsWith' => [ 'shape' => 'ModelName', ], 'DatasetNameBeginsWith' => [ 'shape' => 'DatasetName', ], ], ], 'ListModelsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ModelSummaries' => [ 'shape' => 'ModelSummaries', ], ], ], 'ListOfDiscardedFiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Object', ], 'min' => 0, ], 'ListRetrainingSchedulersRequest' => [ 'type' => 'structure', 'members' => [ 'ModelNameBeginsWith' => [ 'shape' => 'ModelName', ], 'Status' => [ 'shape' => 'RetrainingSchedulerStatus', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListRetrainingSchedulersResponse' => [ 'type' => 'structure', 'members' => [ 'RetrainingSchedulerSummaries' => [ 'shape' => 'RetrainingSchedulerSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSensorStatisticsRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetName', ], 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetName', ], 'IngestionJobId' => [ 'shape' => 'IngestionJobId', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSensorStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'SensorStatisticsSummaries' => [ 'shape' => 'SensorStatisticsSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'LookbackWindow' => [ 'type' => 'string', 'pattern' => '^P180D$|^P360D$|^P540D$|^P720D$', ], 'MaxResults' => [ 'type' => 'integer', 'max' => 500, 'min' => 1, ], 'MissingCompleteSensorData' => [ 'type' => 'structure', 'required' => [ 'AffectedSensorCount', ], 'members' => [ 'AffectedSensorCount' => [ 'shape' => 'Integer', ], ], ], 'MissingSensorData' => [ 'type' => 'structure', 'required' => [ 'AffectedSensorCount', 'TotalNumberOfMissingValues', ], 'members' => [ 'AffectedSensorCount' => [ 'shape' => 'Integer', ], 'TotalNumberOfMissingValues' => [ 'shape' => 'Integer', ], ], ], 'ModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:lookoutequipment:[a-zA-Z0-9\\-]*:[0-9]{12}:model\\/.+', ], 'ModelMetrics' => [ 'type' => 'string', 'max' => 50000, 'min' => 1, ], 'ModelName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z_-]{1,200}$', ], 'ModelPromoteMode' => [ 'type' => 'string', 'enum' => [ 'MANAGED', 'MANUAL', ], ], 'ModelStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCESS', 'FAILED', 'IMPORT_IN_PROGRESS', ], ], 'ModelSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelSummary', ], ], 'ModelSummary' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'DatasetName' => [ 'shape' => 'DatasetName', ], 'DatasetArn' => [ 'shape' => 'DatasetArn', ], 'Status' => [ 'shape' => 'ModelStatus', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ActiveModelVersion' => [ 'shape' => 'ModelVersion', ], 'ActiveModelVersionArn' => [ 'shape' => 'ModelVersionArn', ], 'LatestScheduledRetrainingStatus' => [ 'shape' => 'ModelVersionStatus', ], 'LatestScheduledRetrainingModelVersion' => [ 'shape' => 'ModelVersion', ], 'LatestScheduledRetrainingStartTime' => [ 'shape' => 'Timestamp', ], 'NextScheduledRetrainingStartDate' => [ 'shape' => 'Timestamp', ], 'RetrainingSchedulerStatus' => [ 'shape' => 'RetrainingSchedulerStatus', ], ], ], 'ModelVersion' => [ 'type' => 'long', 'min' => 1, ], 'ModelVersionArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws(-[^:]+)?:lookoutequipment:[a-zA-Z0-9\\-]*:[0-9]{12}:model\\/.+\\/.+\\/model-version\\/[0-9]{1,}$', ], 'ModelVersionSourceType' => [ 'type' => 'string', 'enum' => [ 'TRAINING', 'RETRAINING', 'IMPORT', ], ], 'ModelVersionStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCESS', 'FAILED', 'IMPORT_IN_PROGRESS', 'CANCELED', ], ], 'ModelVersionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelVersionSummary', ], ], 'ModelVersionSummary' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'ModelVersion' => [ 'shape' => 'ModelVersion', ], 'ModelVersionArn' => [ 'shape' => 'ModelVersionArn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'ModelVersionStatus', ], 'SourceType' => [ 'shape' => 'ModelVersionSourceType', ], ], ], 'MonotonicValues' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'StatisticalIssueStatus', ], 'Monotonicity' => [ 'shape' => 'Monotonicity', ], ], ], 'Monotonicity' => [ 'type' => 'string', 'enum' => [ 'DECREASING', 'INCREASING', 'STATIC', ], ], 'MultipleOperatingModes' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'StatisticalIssueStatus', ], ], ], 'NameOrArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,2048}$', ], 'NextToken' => [ 'type' => 'string', 'max' => 8192, 'pattern' => '\\p{ASCII}{0,8192}', ], 'OffCondition' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'Policy' => [ 'type' => 'string', 'max' => 20000, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u00FF]+', ], 'PolicyRevisionId' => [ 'type' => 'string', 'max' => 50, 'pattern' => '[0-9A-Fa-f]+', ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'ResourcePolicy', 'ClientToken', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'ResourcePolicy' => [ 'shape' => 'Policy', ], 'PolicyRevisionId' => [ 'shape' => 'PolicyRevisionId', ], 'ClientToken' => [ 'shape' => 'IdempotenceToken', 'idempotencyToken' => true, ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'PolicyRevisionId' => [ 'shape' => 'PolicyRevisionId', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:lookoutequipment:[a-zA-Z0-9\\-]*:[0-9]{12}:.+', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'BoundedLengthString', ], ], 'exception' => true, ], 'RetrainingFrequency' => [ 'type' => 'string', 'max' => 10, 'min' => 1, 'pattern' => '^P(\\dY)?(\\d{1,2}M)?(\\d{1,3}D)?$', ], 'RetrainingSchedulerStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'RUNNING', 'STOPPING', 'STOPPED', ], ], 'RetrainingSchedulerSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetrainingSchedulerSummary', ], ], 'RetrainingSchedulerSummary' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'Status' => [ 'shape' => 'RetrainingSchedulerStatus', ], 'RetrainingStartDate' => [ 'shape' => 'Timestamp', ], 'RetrainingFrequency' => [ 'shape' => 'RetrainingFrequency', ], 'LookbackWindow' => [ 'shape' => 'LookbackWindow', ], ], ], 'S3Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]$', ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\P{M}\\p{M}]{1,1024}[^/]$', ], 'S3Object' => [ 'type' => 'structure', 'required' => [ 'Bucket', 'Key', ], 'members' => [ 'Bucket' => [ 'shape' => 'S3Bucket', ], 'Key' => [ 'shape' => 'S3Key', ], ], ], 'S3Prefix' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '(^$)|([\\u0009\\u000A\\u000D\\u0020-\\u00FF]{1,1023}/$)', ], 'SensorName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z:#$.\\-_]{1,200}$', ], 'SensorStatisticsSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SensorStatisticsSummary', ], ], 'SensorStatisticsSummary' => [ 'type' => 'structure', 'members' => [ 'ComponentName' => [ 'shape' => 'ComponentName', ], 'SensorName' => [ 'shape' => 'SensorName', ], 'DataExists' => [ 'shape' => 'Boolean', ], 'MissingValues' => [ 'shape' => 'CountPercent', ], 'InvalidValues' => [ 'shape' => 'CountPercent', ], 'InvalidDateEntries' => [ 'shape' => 'CountPercent', ], 'DuplicateTimestamps' => [ 'shape' => 'CountPercent', ], 'CategoricalValues' => [ 'shape' => 'CategoricalValues', ], 'MultipleOperatingModes' => [ 'shape' => 'MultipleOperatingModes', ], 'LargeTimestampGaps' => [ 'shape' => 'LargeTimestampGaps', ], 'MonotonicValues' => [ 'shape' => 'MonotonicValues', ], 'DataStartTime' => [ 'shape' => 'Timestamp', ], 'DataEndTime' => [ 'shape' => 'Timestamp', ], ], ], 'SensorsWithShortDateRange' => [ 'type' => 'structure', 'required' => [ 'AffectedSensorCount', ], 'members' => [ 'AffectedSensorCount' => [ 'shape' => 'Integer', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'BoundedLengthString', ], ], 'exception' => true, ], 'StartDataIngestionJobRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetName', 'IngestionInputConfiguration', 'RoleArn', 'ClientToken', ], 'members' => [ 'DatasetName' => [ 'shape' => 'DatasetIdentifier', ], 'IngestionInputConfiguration' => [ 'shape' => 'IngestionInputConfiguration', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'ClientToken' => [ 'shape' => 'IdempotenceToken', 'idempotencyToken' => true, ], ], ], 'StartDataIngestionJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'IngestionJobId', ], 'Status' => [ 'shape' => 'IngestionJobStatus', ], ], ], 'StartInferenceSchedulerRequest' => [ 'type' => 'structure', 'required' => [ 'InferenceSchedulerName', ], 'members' => [ 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerIdentifier', ], ], ], 'StartInferenceSchedulerResponse' => [ 'type' => 'structure', 'members' => [ 'ModelArn' => [ 'shape' => 'ModelArn', ], 'ModelName' => [ 'shape' => 'ModelName', ], 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerName', ], 'InferenceSchedulerArn' => [ 'shape' => 'InferenceSchedulerArn', ], 'Status' => [ 'shape' => 'InferenceSchedulerStatus', ], ], ], 'StartRetrainingSchedulerRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], ], ], 'StartRetrainingSchedulerResponse' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'Status' => [ 'shape' => 'RetrainingSchedulerStatus', ], ], ], 'StatisticalIssueStatus' => [ 'type' => 'string', 'enum' => [ 'POTENTIAL_ISSUE_DETECTED', 'NO_ISSUE_DETECTED', ], ], 'StopInferenceSchedulerRequest' => [ 'type' => 'structure', 'required' => [ 'InferenceSchedulerName', ], 'members' => [ 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerIdentifier', ], ], ], 'StopInferenceSchedulerResponse' => [ 'type' => 'structure', 'members' => [ 'ModelArn' => [ 'shape' => 'ModelArn', ], 'ModelName' => [ 'shape' => 'ModelName', ], 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerName', ], 'InferenceSchedulerArn' => [ 'shape' => 'InferenceSchedulerArn', ], 'Status' => [ 'shape' => 'InferenceSchedulerStatus', ], ], ], 'StopRetrainingSchedulerRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], ], ], 'StopRetrainingSchedulerResponse' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'Status' => [ 'shape' => 'RetrainingSchedulerStatus', ], ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\s\\w+-=\\.:/@]*', ], 'TargetSamplingRate' => [ 'type' => 'string', 'enum' => [ 'PT1S', 'PT5S', 'PT10S', 'PT15S', 'PT30S', 'PT1M', 'PT5M', 'PT10M', 'PT15M', 'PT30M', 'PT1H', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'BoundedLengthString', ], ], 'exception' => true, ], 'TimeZoneOffset' => [ 'type' => 'string', 'pattern' => '^(\\+|\\-)[0-9]{2}\\:[0-9]{2}$', ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UnsupportedTimestamps' => [ 'type' => 'structure', 'required' => [ 'TotalNumberOfUnsupportedTimestamps', ], 'members' => [ 'TotalNumberOfUnsupportedTimestamps' => [ 'shape' => 'Integer', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateActiveModelVersionRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', 'ModelVersion', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelVersion' => [ 'shape' => 'ModelVersion', ], ], ], 'UpdateActiveModelVersionResponse' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'CurrentActiveVersion' => [ 'shape' => 'ModelVersion', ], 'PreviousActiveVersion' => [ 'shape' => 'ModelVersion', ], 'CurrentActiveVersionArn' => [ 'shape' => 'ModelVersionArn', ], 'PreviousActiveVersionArn' => [ 'shape' => 'ModelVersionArn', ], ], ], 'UpdateInferenceSchedulerRequest' => [ 'type' => 'structure', 'required' => [ 'InferenceSchedulerName', ], 'members' => [ 'InferenceSchedulerName' => [ 'shape' => 'InferenceSchedulerIdentifier', ], 'DataDelayOffsetInMinutes' => [ 'shape' => 'DataDelayOffsetInMinutes', ], 'DataUploadFrequency' => [ 'shape' => 'DataUploadFrequency', ], 'DataInputConfiguration' => [ 'shape' => 'InferenceInputConfiguration', ], 'DataOutputConfiguration' => [ 'shape' => 'InferenceOutputConfiguration', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'UpdateLabelGroupRequest' => [ 'type' => 'structure', 'required' => [ 'LabelGroupName', ], 'members' => [ 'LabelGroupName' => [ 'shape' => 'LabelGroupName', ], 'FaultCodes' => [ 'shape' => 'FaultCodes', ], ], ], 'UpdateModelRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'LabelsInputConfiguration' => [ 'shape' => 'LabelsInputConfiguration', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'UpdateRetrainingSchedulerRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'RetrainingStartDate' => [ 'shape' => 'Timestamp', ], 'RetrainingFrequency' => [ 'shape' => 'RetrainingFrequency', ], 'LookbackWindow' => [ 'shape' => 'LookbackWindow', ], 'PromoteMode' => [ 'shape' => 'ModelPromoteMode', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'BoundedLengthString', ], ], 'exception' => true, ], ],];
