<?php
// This file was auto-generated from sdk-root/src/data/evidently/2021-02-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-02-01', 'endpointPrefix' => 'evidently', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon CloudWatch Evidently', 'serviceId' => 'Evidently', 'signatureVersion' => 'v4', 'signingName' => 'evidently', 'uid' => 'evidently-2021-02-01', ], 'operations' => [ 'BatchEvaluateFeature' => [ 'name' => 'BatchEvaluateFeature', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{project}/evaluations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchEvaluateFeatureRequest', ], 'output' => [ 'shape' => 'BatchEvaluateFeatureResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'dataplane.', ], ], 'CreateExperiment' => [ 'name' => 'CreateExperiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{project}/experiments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateExperimentRequest', ], 'output' => [ 'shape' => 'CreateExperimentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateFeature' => [ 'name' => 'CreateFeature', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{project}/features', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFeatureRequest', ], 'output' => [ 'shape' => 'CreateFeatureResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateLaunch' => [ 'name' => 'CreateLaunch', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{project}/launches', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateLaunchRequest', ], 'output' => [ 'shape' => 'CreateLaunchResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateProject' => [ 'name' => 'CreateProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateProjectRequest', ], 'output' => [ 'shape' => 'CreateProjectResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateSegment' => [ 'name' => 'CreateSegment', 'http' => [ 'method' => 'POST', 'requestUri' => '/segments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSegmentRequest', ], 'output' => [ 'shape' => 'CreateSegmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteExperiment' => [ 'name' => 'DeleteExperiment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/projects/{project}/experiments/{experiment}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteExperimentRequest', ], 'output' => [ 'shape' => 'DeleteExperimentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteFeature' => [ 'name' => 'DeleteFeature', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/projects/{project}/features/{feature}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFeatureRequest', ], 'output' => [ 'shape' => 'DeleteFeatureResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteLaunch' => [ 'name' => 'DeleteLaunch', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/projects/{project}/launches/{launch}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteLaunchRequest', ], 'output' => [ 'shape' => 'DeleteLaunchResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteProject' => [ 'name' => 'DeleteProject', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/projects/{project}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteProjectRequest', ], 'output' => [ 'shape' => 'DeleteProjectResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteSegment' => [ 'name' => 'DeleteSegment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/segments/{segment}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSegmentRequest', ], 'output' => [ 'shape' => 'DeleteSegmentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'EvaluateFeature' => [ 'name' => 'EvaluateFeature', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{project}/evaluations/{feature}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EvaluateFeatureRequest', ], 'output' => [ 'shape' => 'EvaluateFeatureResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'dataplane.', ], ], 'GetExperiment' => [ 'name' => 'GetExperiment', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects/{project}/experiments/{experiment}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetExperimentRequest', ], 'output' => [ 'shape' => 'GetExperimentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetExperimentResults' => [ 'name' => 'GetExperimentResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{project}/experiments/{experiment}/results', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetExperimentResultsRequest', ], 'output' => [ 'shape' => 'GetExperimentResultsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetFeature' => [ 'name' => 'GetFeature', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects/{project}/features/{feature}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFeatureRequest', ], 'output' => [ 'shape' => 'GetFeatureResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetLaunch' => [ 'name' => 'GetLaunch', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects/{project}/launches/{launch}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLaunchRequest', ], 'output' => [ 'shape' => 'GetLaunchResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetProject' => [ 'name' => 'GetProject', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects/{project}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProjectRequest', ], 'output' => [ 'shape' => 'GetProjectResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSegment' => [ 'name' => 'GetSegment', 'http' => [ 'method' => 'GET', 'requestUri' => '/segments/{segment}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSegmentRequest', ], 'output' => [ 'shape' => 'GetSegmentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListExperiments' => [ 'name' => 'ListExperiments', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects/{project}/experiments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListExperimentsRequest', ], 'output' => [ 'shape' => 'ListExperimentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListFeatures' => [ 'name' => 'ListFeatures', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects/{project}/features', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFeaturesRequest', ], 'output' => [ 'shape' => 'ListFeaturesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListLaunches' => [ 'name' => 'ListLaunches', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects/{project}/launches', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLaunchesRequest', ], 'output' => [ 'shape' => 'ListLaunchesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListProjects' => [ 'name' => 'ListProjects', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProjectsRequest', ], 'output' => [ 'shape' => 'ListProjectsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSegmentReferences' => [ 'name' => 'ListSegmentReferences', 'http' => [ 'method' => 'GET', 'requestUri' => '/segments/{segment}/references', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSegmentReferencesRequest', ], 'output' => [ 'shape' => 'ListSegmentReferencesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSegments' => [ 'name' => 'ListSegments', 'http' => [ 'method' => 'GET', 'requestUri' => '/segments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSegmentsRequest', ], 'output' => [ 'shape' => 'ListSegmentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'PutProjectEvents' => [ 'name' => 'PutProjectEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/events/projects/{project}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutProjectEventsRequest', ], 'output' => [ 'shape' => 'PutProjectEventsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'dataplane.', ], ], 'StartExperiment' => [ 'name' => 'StartExperiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{project}/experiments/{experiment}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartExperimentRequest', ], 'output' => [ 'shape' => 'StartExperimentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartLaunch' => [ 'name' => 'StartLaunch', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{project}/launches/{launch}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartLaunchRequest', ], 'output' => [ 'shape' => 'StartLaunchResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StopExperiment' => [ 'name' => 'StopExperiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{project}/experiments/{experiment}/cancel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopExperimentRequest', ], 'output' => [ 'shape' => 'StopExperimentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StopLaunch' => [ 'name' => 'StopLaunch', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{project}/launches/{launch}/cancel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopLaunchRequest', ], 'output' => [ 'shape' => 'StopLaunchResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'TestSegmentPattern' => [ 'name' => 'TestSegmentPattern', 'http' => [ 'method' => 'POST', 'requestUri' => '/test-segment-pattern', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TestSegmentPatternRequest', ], 'output' => [ 'shape' => 'TestSegmentPatternResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateExperiment' => [ 'name' => 'UpdateExperiment', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/projects/{project}/experiments/{experiment}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateExperimentRequest', ], 'output' => [ 'shape' => 'UpdateExperimentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateFeature' => [ 'name' => 'UpdateFeature', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/projects/{project}/features/{feature}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFeatureRequest', ], 'output' => [ 'shape' => 'UpdateFeatureResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateLaunch' => [ 'name' => 'UpdateLaunch', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/projects/{project}/launches/{launch}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLaunchRequest', ], 'output' => [ 'shape' => 'UpdateLaunchResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateProject' => [ 'name' => 'UpdateProject', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/projects/{project}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateProjectRequest', ], 'output' => [ 'shape' => 'UpdateProjectResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateProjectDataDelivery' => [ 'name' => 'UpdateProjectDataDelivery', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/projects/{project}/data-delivery', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateProjectDataDeliveryRequest', ], 'output' => [ 'shape' => 'UpdateProjectDataDeliveryResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AppConfigResourceId' => [ 'type' => 'string', 'pattern' => '[a-z0-9]{4,7}', ], 'Arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:[^:]*:[^:]*:[^:]*:[^:]*:.*', ], 'BatchEvaluateFeatureRequest' => [ 'type' => 'structure', 'required' => [ 'project', 'requests', ], 'members' => [ 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 'requests' => [ 'shape' => 'EvaluationRequestsList', ], ], ], 'BatchEvaluateFeatureResponse' => [ 'type' => 'structure', 'members' => [ 'results' => [ 'shape' => 'EvaluationResultsList', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ChangeDirectionEnum' => [ 'type' => 'string', 'enum' => [ 'INCREASE', 'DECREASE', ], ], 'CloudWatchLogsDestination' => [ 'type' => 'structure', 'members' => [ 'logGroup' => [ 'shape' => 'CwLogGroupSafeName', ], ], ], 'CloudWatchLogsDestinationConfig' => [ 'type' => 'structure', 'members' => [ 'logGroup' => [ 'shape' => 'CwLogGroupSafeName', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'metricGoals', 'name', 'project', 'treatments', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'metricGoals' => [ 'shape' => 'MetricGoalConfigList', ], 'name' => [ 'shape' => 'ExperimentName', ], 'onlineAbConfig' => [ 'shape' => 'OnlineAbConfig', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 'randomizationSalt' => [ 'shape' => 'RandomizationSalt', ], 'samplingRate' => [ 'shape' => 'SplitWeight', 'box' => true, ], 'segment' => [ 'shape' => 'SegmentRef', ], 'tags' => [ 'shape' => 'TagMap', ], 'treatments' => [ 'shape' => 'TreatmentConfigList', ], ], ], 'CreateExperimentResponse' => [ 'type' => 'structure', 'required' => [ 'experiment', ], 'members' => [ 'experiment' => [ 'shape' => 'Experiment', ], ], ], 'CreateFeatureRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'project', 'variations', ], 'members' => [ 'defaultVariation' => [ 'shape' => 'VariationName', ], 'description' => [ 'shape' => 'Description', ], 'entityOverrides' => [ 'shape' => 'EntityOverrideMap', ], 'evaluationStrategy' => [ 'shape' => 'FeatureEvaluationStrategy', ], 'name' => [ 'shape' => 'FeatureName', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 'tags' => [ 'shape' => 'TagMap', ], 'variations' => [ 'shape' => 'VariationConfigsList', ], ], ], 'CreateFeatureResponse' => [ 'type' => 'structure', 'members' => [ 'feature' => [ 'shape' => 'Feature', ], ], ], 'CreateLaunchRequest' => [ 'type' => 'structure', 'required' => [ 'groups', 'name', 'project', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'groups' => [ 'shape' => 'LaunchGroupConfigList', ], 'metricMonitors' => [ 'shape' => 'MetricMonitorConfigList', ], 'name' => [ 'shape' => 'LaunchName', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 'randomizationSalt' => [ 'shape' => 'RandomizationSalt', ], 'scheduledSplitsConfig' => [ 'shape' => 'ScheduledSplitsLaunchConfig', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateLaunchResponse' => [ 'type' => 'structure', 'required' => [ 'launch', ], 'members' => [ 'launch' => [ 'shape' => 'Launch', ], ], ], 'CreateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'appConfigResource' => [ 'shape' => 'ProjectAppConfigResourceConfig', ], 'dataDelivery' => [ 'shape' => 'ProjectDataDeliveryConfig', ], 'description' => [ 'shape' => 'Description', ], 'name' => [ 'shape' => 'ProjectName', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateProjectResponse' => [ 'type' => 'structure', 'required' => [ 'project', ], 'members' => [ 'project' => [ 'shape' => 'Project', ], ], ], 'CreateSegmentRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'pattern', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'name' => [ 'shape' => 'SegmentName', ], 'pattern' => [ 'shape' => 'SegmentPattern', 'jsonvalue' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateSegmentResponse' => [ 'type' => 'structure', 'required' => [ 'segment', ], 'members' => [ 'segment' => [ 'shape' => 'Segment', ], ], ], 'CwDimensionSafeName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[\\S]+$', ], 'CwLogGroupSafeName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^[-a-zA-Z0-9._/]+$', ], 'DeleteExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'experiment', 'project', ], 'members' => [ 'experiment' => [ 'shape' => 'ExperimentName', 'location' => 'uri', 'locationName' => 'experiment', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'DeleteExperimentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFeatureRequest' => [ 'type' => 'structure', 'required' => [ 'feature', 'project', ], 'members' => [ 'feature' => [ 'shape' => 'FeatureName', 'location' => 'uri', 'locationName' => 'feature', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'DeleteFeatureResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLaunchRequest' => [ 'type' => 'structure', 'required' => [ 'launch', 'project', ], 'members' => [ 'launch' => [ 'shape' => 'LaunchName', 'location' => 'uri', 'locationName' => 'launch', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'DeleteLaunchResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProjectRequest' => [ 'type' => 'structure', 'required' => [ 'project', ], 'members' => [ 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'DeleteProjectResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSegmentRequest' => [ 'type' => 'structure', 'required' => [ 'segment', ], 'members' => [ 'segment' => [ 'shape' => 'SegmentRef', 'location' => 'uri', 'locationName' => 'segment', ], ], ], 'DeleteSegmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', 'max' => 160, 'min' => 0, 'pattern' => '.*', ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'DoubleValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 100800, 'min' => 0, ], 'EntityId' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '.*', ], 'EntityOverrideMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'EntityId', ], 'value' => [ 'shape' => 'VariationName', ], 'max' => 2500, 'min' => 0, ], 'ErrorCodeEnum' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*', ], 'EvaluateFeatureRequest' => [ 'type' => 'structure', 'required' => [ 'entityId', 'feature', 'project', ], 'members' => [ 'entityId' => [ 'shape' => 'EntityId', ], 'evaluationContext' => [ 'shape' => 'JsonValue', 'jsonvalue' => true, ], 'feature' => [ 'shape' => 'FeatureName', 'location' => 'uri', 'locationName' => 'feature', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'EvaluateFeatureResponse' => [ 'type' => 'structure', 'members' => [ 'details' => [ 'shape' => 'JsonValue', 'jsonvalue' => true, ], 'reason' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'VariableValue', ], 'variation' => [ 'shape' => 'String', ], ], ], 'EvaluationRequest' => [ 'type' => 'structure', 'required' => [ 'entityId', 'feature', ], 'members' => [ 'entityId' => [ 'shape' => 'EntityId', ], 'evaluationContext' => [ 'shape' => 'JsonValue', 'jsonvalue' => true, ], 'feature' => [ 'shape' => 'FeatureName', ], ], ], 'EvaluationRequestsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationRequest', ], 'max' => 20, 'min' => 1, ], 'EvaluationResult' => [ 'type' => 'structure', 'required' => [ 'entityId', 'feature', ], 'members' => [ 'details' => [ 'shape' => 'JsonValue', 'jsonvalue' => true, ], 'entityId' => [ 'shape' => 'EntityId', ], 'feature' => [ 'shape' => 'FeatureName', ], 'project' => [ 'shape' => 'Arn', ], 'reason' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'VariableValue', ], 'variation' => [ 'shape' => 'String', ], ], ], 'EvaluationResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationResult', ], ], 'EvaluationRule' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'name' => [ 'shape' => 'RuleName', ], 'type' => [ 'shape' => 'RuleType', ], ], ], 'EvaluationRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationRule', ], ], 'Event' => [ 'type' => 'structure', 'required' => [ 'data', 'timestamp', 'type', ], 'members' => [ 'data' => [ 'shape' => 'JsonValue', 'jsonvalue' => true, ], 'timestamp' => [ 'shape' => 'Timestamp', ], 'type' => [ 'shape' => 'EventType', ], ], ], 'EventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', ], 'max' => 50, 'min' => 0, ], 'EventType' => [ 'type' => 'string', 'enum' => [ 'aws.evidently.evaluation', 'aws.evidently.custom', ], ], 'Experiment' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdTime', 'lastUpdatedTime', 'name', 'status', 'type', ], 'members' => [ 'arn' => [ 'shape' => 'ExperimentArn', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'execution' => [ 'shape' => 'ExperimentExecution', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'metricGoals' => [ 'shape' => 'MetricGoalsList', ], 'name' => [ 'shape' => 'ExperimentName', ], 'onlineAbDefinition' => [ 'shape' => 'OnlineAbDefinition', ], 'project' => [ 'shape' => 'ProjectArn', ], 'randomizationSalt' => [ 'shape' => 'RandomizationSalt', ], 'samplingRate' => [ 'shape' => 'SplitWeight', ], 'schedule' => [ 'shape' => 'ExperimentSchedule', ], 'segment' => [ 'shape' => 'SegmentArn', ], 'status' => [ 'shape' => 'ExperimentStatus', ], 'statusReason' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'TagMap', ], 'treatments' => [ 'shape' => 'TreatmentList', ], 'type' => [ 'shape' => 'ExperimentType', ], ], ], 'ExperimentArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:[^:]*:[^:]*:[^:]*:[^:]*:project/[-a-zA-Z0-9._]*/experiment/[-a-zA-Z0-9._]*', ], 'ExperimentBaseStat' => [ 'type' => 'string', 'enum' => [ 'Mean', ], ], 'ExperimentExecution' => [ 'type' => 'structure', 'members' => [ 'endedTime' => [ 'shape' => 'Timestamp', ], 'startedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ExperimentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Experiment', ], ], 'ExperimentName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '^[-a-zA-Z0-9._]*$', ], 'ExperimentReport' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'JsonValue', 'jsonvalue' => true, ], 'metricName' => [ 'shape' => 'CwDimensionSafeName', ], 'reportName' => [ 'shape' => 'ExperimentReportName', ], 'treatmentName' => [ 'shape' => 'TreatmentName', ], ], ], 'ExperimentReportList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentReport', ], 'max' => 1000, 'min' => 0, ], 'ExperimentReportName' => [ 'type' => 'string', 'enum' => [ 'BayesianInference', ], ], 'ExperimentReportNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentReportName', ], 'max' => 5, 'min' => 0, ], 'ExperimentResultRequestType' => [ 'type' => 'string', 'enum' => [ 'BaseStat', 'TreatmentEffect', 'ConfidenceInterval', 'PValue', ], ], 'ExperimentResultRequestTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentResultRequestType', ], 'max' => 5, 'min' => 0, ], 'ExperimentResultResponseType' => [ 'type' => 'string', 'enum' => [ 'Mean', 'TreatmentEffect', 'ConfidenceIntervalUpperBound', 'ConfidenceIntervalLowerBound', 'PValue', ], ], 'ExperimentResultsData' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'CwDimensionSafeName', ], 'resultStat' => [ 'shape' => 'ExperimentResultResponseType', ], 'treatmentName' => [ 'shape' => 'TreatmentName', ], 'values' => [ 'shape' => 'DoubleValueList', ], ], ], 'ExperimentResultsDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentResultsData', ], 'max' => 1000, 'min' => 0, ], 'ExperimentSchedule' => [ 'type' => 'structure', 'members' => [ 'analysisCompleteTime' => [ 'shape' => 'Timestamp', ], ], ], 'ExperimentStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'UPDATING', 'RUNNING', 'COMPLETED', 'CANCELLED', ], ], 'ExperimentStopDesiredState' => [ 'type' => 'string', 'enum' => [ 'COMPLETED', 'CANCELLED', ], ], 'ExperimentType' => [ 'type' => 'string', 'enum' => [ 'aws.evidently.onlineab', ], ], 'Feature' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdTime', 'evaluationStrategy', 'lastUpdatedTime', 'name', 'status', 'valueType', 'variations', ], 'members' => [ 'arn' => [ 'shape' => 'FeatureArn', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'defaultVariation' => [ 'shape' => 'VariationName', ], 'description' => [ 'shape' => 'Description', ], 'entityOverrides' => [ 'shape' => 'EntityOverrideMap', ], 'evaluationRules' => [ 'shape' => 'EvaluationRulesList', ], 'evaluationStrategy' => [ 'shape' => 'FeatureEvaluationStrategy', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'FeatureName', ], 'project' => [ 'shape' => 'ProjectArn', ], 'status' => [ 'shape' => 'FeatureStatus', ], 'tags' => [ 'shape' => 'TagMap', ], 'valueType' => [ 'shape' => 'VariationValueType', ], 'variations' => [ 'shape' => 'VariationsList', ], ], ], 'FeatureArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:[^:]*:[^:]*:[^:]*:[^:]*:project/[-a-zA-Z0-9._]*/feature/[-a-zA-Z0-9._]*', ], 'FeatureEvaluationStrategy' => [ 'type' => 'string', 'enum' => [ 'ALL_RULES', 'DEFAULT_VARIATION', ], ], 'FeatureName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '^[-a-zA-Z0-9._]*$', ], 'FeatureStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'UPDATING', ], ], 'FeatureSummariesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FeatureSummary', ], ], 'FeatureSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdTime', 'evaluationStrategy', 'lastUpdatedTime', 'name', 'status', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'defaultVariation' => [ 'shape' => 'VariationName', ], 'evaluationRules' => [ 'shape' => 'EvaluationRulesList', ], 'evaluationStrategy' => [ 'shape' => 'FeatureEvaluationStrategy', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'FeatureName', ], 'project' => [ 'shape' => 'ProjectRef', ], 'status' => [ 'shape' => 'FeatureStatus', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'FeatureToVariationMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'FeatureName', ], 'value' => [ 'shape' => 'VariationName', ], ], 'GetExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'experiment', 'project', ], 'members' => [ 'experiment' => [ 'shape' => 'ExperimentName', 'location' => 'uri', 'locationName' => 'experiment', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'GetExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'experiment' => [ 'shape' => 'Experiment', ], ], ], 'GetExperimentResultsRequest' => [ 'type' => 'structure', 'required' => [ 'experiment', 'metricNames', 'project', 'treatmentNames', ], 'members' => [ 'baseStat' => [ 'shape' => 'ExperimentBaseStat', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'experiment' => [ 'shape' => 'ExperimentName', 'location' => 'uri', 'locationName' => 'experiment', ], 'metricNames' => [ 'shape' => 'MetricNameList', ], 'period' => [ 'shape' => 'ResultsPeriod', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 'reportNames' => [ 'shape' => 'ExperimentReportNameList', ], 'resultStats' => [ 'shape' => 'ExperimentResultRequestTypeList', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'treatmentNames' => [ 'shape' => 'TreatmentNameList', ], ], ], 'GetExperimentResultsResponse' => [ 'type' => 'structure', 'members' => [ 'details' => [ 'shape' => 'String', ], 'reports' => [ 'shape' => 'ExperimentReportList', ], 'resultsData' => [ 'shape' => 'ExperimentResultsDataList', ], 'timestamps' => [ 'shape' => 'TimestampList', ], ], ], 'GetFeatureRequest' => [ 'type' => 'structure', 'required' => [ 'feature', 'project', ], 'members' => [ 'feature' => [ 'shape' => 'FeatureName', 'location' => 'uri', 'locationName' => 'feature', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'GetFeatureResponse' => [ 'type' => 'structure', 'required' => [ 'feature', ], 'members' => [ 'feature' => [ 'shape' => 'Feature', ], ], ], 'GetLaunchRequest' => [ 'type' => 'structure', 'required' => [ 'launch', 'project', ], 'members' => [ 'launch' => [ 'shape' => 'LaunchName', 'location' => 'uri', 'locationName' => 'launch', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'GetLaunchResponse' => [ 'type' => 'structure', 'members' => [ 'launch' => [ 'shape' => 'Launch', ], ], ], 'GetProjectRequest' => [ 'type' => 'structure', 'required' => [ 'project', ], 'members' => [ 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'GetProjectResponse' => [ 'type' => 'structure', 'required' => [ 'project', ], 'members' => [ 'project' => [ 'shape' => 'Project', ], ], ], 'GetSegmentRequest' => [ 'type' => 'structure', 'required' => [ 'segment', ], 'members' => [ 'segment' => [ 'shape' => 'SegmentRef', 'location' => 'uri', 'locationName' => 'segment', ], ], ], 'GetSegmentResponse' => [ 'type' => 'structure', 'required' => [ 'segment', ], 'members' => [ 'segment' => [ 'shape' => 'Segment', ], ], ], 'GroupName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '^[-a-zA-Z0-9._]*$', ], 'GroupToWeightMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'GroupName', ], 'value' => [ 'shape' => 'SplitWeight', ], 'max' => 5, 'min' => 0, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'JsonPath' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'JsonValue' => [ 'type' => 'string', ], 'Launch' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdTime', 'lastUpdatedTime', 'name', 'status', 'type', ], 'members' => [ 'arn' => [ 'shape' => 'LaunchArn', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'execution' => [ 'shape' => 'LaunchExecution', ], 'groups' => [ 'shape' => 'LaunchGroupList', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'metricMonitors' => [ 'shape' => 'MetricMonitorList', ], 'name' => [ 'shape' => 'LaunchName', ], 'project' => [ 'shape' => 'ProjectRef', ], 'randomizationSalt' => [ 'shape' => 'RandomizationSalt', ], 'scheduledSplitsDefinition' => [ 'shape' => 'ScheduledSplitsLaunchDefinition', ], 'status' => [ 'shape' => 'LaunchStatus', ], 'statusReason' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'TagMap', ], 'type' => [ 'shape' => 'LaunchType', ], ], ], 'LaunchArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:[^:]*:[^:]*:[^:]*:[^:]*:project/[-a-zA-Z0-9._]*/launch/[-a-zA-Z0-9._]*', ], 'LaunchExecution' => [ 'type' => 'structure', 'members' => [ 'endedTime' => [ 'shape' => 'Timestamp', ], 'startedTime' => [ 'shape' => 'Timestamp', ], ], ], 'LaunchGroup' => [ 'type' => 'structure', 'required' => [ 'featureVariations', 'name', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'featureVariations' => [ 'shape' => 'FeatureToVariationMap', ], 'name' => [ 'shape' => 'GroupName', ], ], ], 'LaunchGroupConfig' => [ 'type' => 'structure', 'required' => [ 'feature', 'name', 'variation', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'feature' => [ 'shape' => 'FeatureName', ], 'name' => [ 'shape' => 'GroupName', ], 'variation' => [ 'shape' => 'VariationName', ], ], ], 'LaunchGroupConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchGroupConfig', ], 'max' => 5, 'min' => 1, ], 'LaunchGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchGroup', ], ], 'LaunchName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '^[-a-zA-Z0-9._]*$', ], 'LaunchStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'UPDATING', 'RUNNING', 'COMPLETED', 'CANCELLED', ], ], 'LaunchStopDesiredState' => [ 'type' => 'string', 'enum' => [ 'COMPLETED', 'CANCELLED', ], ], 'LaunchType' => [ 'type' => 'string', 'enum' => [ 'aws.evidently.splits', ], ], 'LaunchesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Launch', ], ], 'ListExperimentsRequest' => [ 'type' => 'structure', 'required' => [ 'project', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxExperiments', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 'status' => [ 'shape' => 'ExperimentStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListExperimentsResponse' => [ 'type' => 'structure', 'members' => [ 'experiments' => [ 'shape' => 'ExperimentList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFeaturesRequest' => [ 'type' => 'structure', 'required' => [ 'project', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxFeatures', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'ListFeaturesResponse' => [ 'type' => 'structure', 'members' => [ 'features' => [ 'shape' => 'FeatureSummariesList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLaunchesRequest' => [ 'type' => 'structure', 'required' => [ 'project', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxLaunches', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 'status' => [ 'shape' => 'LaunchStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListLaunchesResponse' => [ 'type' => 'structure', 'members' => [ 'launches' => [ 'shape' => 'LaunchesList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListProjectsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxProjects', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListProjectsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'projects' => [ 'shape' => 'ProjectSummariesList', ], ], ], 'ListSegmentReferencesRequest' => [ 'type' => 'structure', 'required' => [ 'segment', 'type', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxReferences', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'segment' => [ 'shape' => 'SegmentRef', 'location' => 'uri', 'locationName' => 'segment', ], 'type' => [ 'shape' => 'SegmentReferenceResourceType', 'location' => 'querystring', 'locationName' => 'type', ], ], ], 'ListSegmentReferencesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'referencedBy' => [ 'shape' => 'RefResourceList', ], ], ], 'ListSegmentsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxSegments', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSegmentsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'segments' => [ 'shape' => 'SegmentList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MaxExperiments' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxFeatures' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxLaunches' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxProjects' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MaxReferences' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxSegments' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MetricDefinition' => [ 'type' => 'structure', 'members' => [ 'entityIdKey' => [ 'shape' => 'JsonPath', ], 'eventPattern' => [ 'shape' => 'JsonValue', 'jsonvalue' => true, ], 'name' => [ 'shape' => 'CwDimensionSafeName', ], 'unitLabel' => [ 'shape' => 'MetricUnitLabel', ], 'valueKey' => [ 'shape' => 'JsonPath', ], ], ], 'MetricDefinitionConfig' => [ 'type' => 'structure', 'required' => [ 'entityIdKey', 'name', 'valueKey', ], 'members' => [ 'entityIdKey' => [ 'shape' => 'JsonPath', ], 'eventPattern' => [ 'shape' => 'MetricDefinitionConfigEventPatternString', 'jsonvalue' => true, ], 'name' => [ 'shape' => 'CwDimensionSafeName', ], 'unitLabel' => [ 'shape' => 'MetricUnitLabel', ], 'valueKey' => [ 'shape' => 'JsonPath', ], ], ], 'MetricDefinitionConfigEventPatternString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'MetricGoal' => [ 'type' => 'structure', 'required' => [ 'metricDefinition', ], 'members' => [ 'desiredChange' => [ 'shape' => 'ChangeDirectionEnum', ], 'metricDefinition' => [ 'shape' => 'MetricDefinition', ], ], ], 'MetricGoalConfig' => [ 'type' => 'structure', 'required' => [ 'metricDefinition', ], 'members' => [ 'desiredChange' => [ 'shape' => 'ChangeDirectionEnum', ], 'metricDefinition' => [ 'shape' => 'MetricDefinitionConfig', ], ], ], 'MetricGoalConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricGoalConfig', ], 'max' => 3, 'min' => 1, ], 'MetricGoalsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricGoal', ], 'max' => 3, 'min' => 1, ], 'MetricMonitor' => [ 'type' => 'structure', 'required' => [ 'metricDefinition', ], 'members' => [ 'metricDefinition' => [ 'shape' => 'MetricDefinition', ], ], ], 'MetricMonitorConfig' => [ 'type' => 'structure', 'required' => [ 'metricDefinition', ], 'members' => [ 'metricDefinition' => [ 'shape' => 'MetricDefinitionConfig', ], ], ], 'MetricMonitorConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricMonitorConfig', ], 'max' => 3, 'min' => 0, ], 'MetricMonitorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricMonitor', ], 'max' => 3, 'min' => 0, ], 'MetricNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CwDimensionSafeName', ], 'max' => 1, 'min' => 1, ], 'MetricUnitLabel' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'NextToken' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, 'pattern' => '.*', ], 'OnlineAbConfig' => [ 'type' => 'structure', 'members' => [ 'controlTreatmentName' => [ 'shape' => 'TreatmentName', ], 'treatmentWeights' => [ 'shape' => 'TreatmentToWeightMap', ], ], ], 'OnlineAbDefinition' => [ 'type' => 'structure', 'members' => [ 'controlTreatmentName' => [ 'shape' => 'TreatmentName', ], 'treatmentWeights' => [ 'shape' => 'TreatmentToWeightMap', ], ], ], 'PrimitiveBoolean' => [ 'type' => 'boolean', ], 'Project' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdTime', 'lastUpdatedTime', 'name', 'status', ], 'members' => [ 'activeExperimentCount' => [ 'shape' => 'Long', ], 'activeLaunchCount' => [ 'shape' => 'Long', ], 'appConfigResource' => [ 'shape' => 'ProjectAppConfigResource', ], 'arn' => [ 'shape' => 'ProjectArn', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'dataDelivery' => [ 'shape' => 'ProjectDataDelivery', ], 'description' => [ 'shape' => 'Description', ], 'experimentCount' => [ 'shape' => 'Long', ], 'featureCount' => [ 'shape' => 'Long', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'launchCount' => [ 'shape' => 'Long', ], 'name' => [ 'shape' => 'ProjectName', ], 'status' => [ 'shape' => 'ProjectStatus', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ProjectAppConfigResource' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'configurationProfileId', 'environmentId', ], 'members' => [ 'applicationId' => [ 'shape' => 'AppConfigResourceId', ], 'configurationProfileId' => [ 'shape' => 'AppConfigResourceId', ], 'environmentId' => [ 'shape' => 'AppConfigResourceId', ], ], ], 'ProjectAppConfigResourceConfig' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'AppConfigResourceId', ], 'environmentId' => [ 'shape' => 'AppConfigResourceId', ], ], ], 'ProjectArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:[^:]*:[^:]*:[^:]*:[^:]*:project/[-a-zA-Z0-9._]*', ], 'ProjectDataDelivery' => [ 'type' => 'structure', 'members' => [ 'cloudWatchLogs' => [ 'shape' => 'CloudWatchLogsDestination', ], 's3Destination' => [ 'shape' => 'S3Destination', ], ], ], 'ProjectDataDeliveryConfig' => [ 'type' => 'structure', 'members' => [ 'cloudWatchLogs' => [ 'shape' => 'CloudWatchLogsDestinationConfig', ], 's3Destination' => [ 'shape' => 'S3DestinationConfig', ], ], ], 'ProjectName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '^[-a-zA-Z0-9._]*$', ], 'ProjectRef' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '(^[a-zA-Z0-9._-]*$)|(arn:[^:]*:[^:]*:[^:]*:[^:]*:project/[a-zA-Z0-9._-]*)', ], 'ProjectStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'UPDATING', ], ], 'ProjectSummariesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectSummary', ], ], 'ProjectSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdTime', 'lastUpdatedTime', 'name', 'status', ], 'members' => [ 'activeExperimentCount' => [ 'shape' => 'Long', ], 'activeLaunchCount' => [ 'shape' => 'Long', ], 'arn' => [ 'shape' => 'ProjectArn', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'experimentCount' => [ 'shape' => 'Long', ], 'featureCount' => [ 'shape' => 'Long', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'launchCount' => [ 'shape' => 'Long', ], 'name' => [ 'shape' => 'ProjectName', ], 'status' => [ 'shape' => 'ProjectStatus', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'PutProjectEventsRequest' => [ 'type' => 'structure', 'required' => [ 'events', 'project', ], 'members' => [ 'events' => [ 'shape' => 'EventList', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'PutProjectEventsResponse' => [ 'type' => 'structure', 'members' => [ 'eventResults' => [ 'shape' => 'PutProjectEventsResultEntryList', ], 'failedEventCount' => [ 'shape' => 'Integer', ], ], ], 'PutProjectEventsResultEntry' => [ 'type' => 'structure', 'members' => [ 'errorCode' => [ 'shape' => 'ErrorCodeEnum', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'eventId' => [ 'shape' => 'Uuid', ], ], ], 'PutProjectEventsResultEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PutProjectEventsResultEntry', ], ], 'RandomizationSalt' => [ 'type' => 'string', 'max' => 127, 'min' => 0, 'pattern' => '.*', ], 'RefResource' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'arn' => [ 'shape' => 'String', ], 'endTime' => [ 'shape' => 'String', ], 'lastUpdatedOn' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'startTime' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], ], ], 'RefResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RefResource', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResultsPeriod' => [ 'type' => 'long', 'max' => 90000, 'min' => 300, ], 'RuleName' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'RuleType' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'S3BucketSafeName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9][-a-z0-9]*[a-z0-9]$', ], 'S3Destination' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'S3BucketSafeName', ], 'prefix' => [ 'shape' => 'S3PrefixSafeName', ], ], ], 'S3DestinationConfig' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'S3BucketSafeName', ], 'prefix' => [ 'shape' => 'S3PrefixSafeName', ], ], ], 'S3PrefixSafeName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[-a-zA-Z0-9!_.*\'()/]*$', ], 'ScheduledSplit' => [ 'type' => 'structure', 'required' => [ 'startTime', ], 'members' => [ 'groupWeights' => [ 'shape' => 'GroupToWeightMap', ], 'segmentOverrides' => [ 'shape' => 'SegmentOverridesList', ], 'startTime' => [ 'shape' => 'Timestamp', ], ], ], 'ScheduledSplitConfig' => [ 'type' => 'structure', 'required' => [ 'groupWeights', 'startTime', ], 'members' => [ 'groupWeights' => [ 'shape' => 'GroupToWeightMap', ], 'segmentOverrides' => [ 'shape' => 'SegmentOverridesList', ], 'startTime' => [ 'shape' => 'Timestamp', ], ], ], 'ScheduledSplitConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduledSplitConfig', ], 'max' => 6, 'min' => 1, ], 'ScheduledSplitsLaunchConfig' => [ 'type' => 'structure', 'required' => [ 'steps', ], 'members' => [ 'steps' => [ 'shape' => 'ScheduledSplitConfigList', ], ], ], 'ScheduledSplitsLaunchDefinition' => [ 'type' => 'structure', 'members' => [ 'steps' => [ 'shape' => 'ScheduledStepList', ], ], ], 'ScheduledStepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduledSplit', ], 'max' => 6, 'min' => 1, ], 'Segment' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdTime', 'lastUpdatedTime', 'name', 'pattern', ], 'members' => [ 'arn' => [ 'shape' => 'SegmentArn', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'experimentCount' => [ 'shape' => 'Long', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'launchCount' => [ 'shape' => 'Long', ], 'name' => [ 'shape' => 'SegmentName', ], 'pattern' => [ 'shape' => 'SegmentPattern', 'jsonvalue' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'SegmentArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:[^:]*:[^:]*:[^:]*:[^:]*:segment/[-a-zA-Z0-9._]*', ], 'SegmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Segment', ], ], 'SegmentName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[-a-zA-Z0-9._]*$', ], 'SegmentOverride' => [ 'type' => 'structure', 'required' => [ 'evaluationOrder', 'segment', 'weights', ], 'members' => [ 'evaluationOrder' => [ 'shape' => 'Long', ], 'segment' => [ 'shape' => 'SegmentRef', ], 'weights' => [ 'shape' => 'GroupToWeightMap', ], ], ], 'SegmentOverridesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SegmentOverride', ], 'max' => 6, 'min' => 0, ], 'SegmentPattern' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'SegmentRef' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '(^[-a-zA-Z0-9._]*$)|(arn:[^:]*:[^:]*:[^:]*:[^:]*:segment/[-a-zA-Z0-9._]*)', ], 'SegmentReferenceResourceType' => [ 'type' => 'string', 'enum' => [ 'EXPERIMENT', 'LAUNCH', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'SplitWeight' => [ 'type' => 'long', 'max' => 100000, 'min' => 0, ], 'StartExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'analysisCompleteTime', 'experiment', 'project', ], 'members' => [ 'analysisCompleteTime' => [ 'shape' => 'Timestamp', ], 'experiment' => [ 'shape' => 'ExperimentName', 'location' => 'uri', 'locationName' => 'experiment', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'StartExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'startedTime' => [ 'shape' => 'Timestamp', ], ], ], 'StartLaunchRequest' => [ 'type' => 'structure', 'required' => [ 'launch', 'project', ], 'members' => [ 'launch' => [ 'shape' => 'LaunchName', 'location' => 'uri', 'locationName' => 'launch', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'StartLaunchResponse' => [ 'type' => 'structure', 'required' => [ 'launch', ], 'members' => [ 'launch' => [ 'shape' => 'Launch', ], ], ], 'StopExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'experiment', 'project', ], 'members' => [ 'desiredState' => [ 'shape' => 'ExperimentStopDesiredState', ], 'experiment' => [ 'shape' => 'ExperimentName', 'location' => 'uri', 'locationName' => 'experiment', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 'reason' => [ 'shape' => 'Description', ], ], ], 'StopExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'endedTime' => [ 'shape' => 'Timestamp', ], ], ], 'StopLaunchRequest' => [ 'type' => 'structure', 'required' => [ 'launch', 'project', ], 'members' => [ 'desiredState' => [ 'shape' => 'LaunchStopDesiredState', ], 'launch' => [ 'shape' => 'LaunchName', 'location' => 'uri', 'locationName' => 'launch', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 'reason' => [ 'shape' => 'Description', ], ], ], 'StopLaunchResponse' => [ 'type' => 'structure', 'members' => [ 'endedTime' => [ 'shape' => 'Timestamp', ], ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TestSegmentPatternRequest' => [ 'type' => 'structure', 'required' => [ 'pattern', 'payload', ], 'members' => [ 'pattern' => [ 'shape' => 'SegmentPattern', 'jsonvalue' => true, ], 'payload' => [ 'shape' => 'JsonValue', 'jsonvalue' => true, ], ], ], 'TestSegmentPatternResponse' => [ 'type' => 'structure', 'required' => [ 'match', ], 'members' => [ 'match' => [ 'shape' => 'Boolean', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Timestamp', ], 'max' => 100800, 'min' => 0, ], 'Treatment' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'featureVariations' => [ 'shape' => 'FeatureToVariationMap', ], 'name' => [ 'shape' => 'TreatmentName', ], ], ], 'TreatmentConfig' => [ 'type' => 'structure', 'required' => [ 'feature', 'name', 'variation', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'feature' => [ 'shape' => 'FeatureName', ], 'name' => [ 'shape' => 'TreatmentName', ], 'variation' => [ 'shape' => 'VariationName', ], ], ], 'TreatmentConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TreatmentConfig', ], 'max' => 5, 'min' => 0, ], 'TreatmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Treatment', ], 'max' => 5, 'min' => 2, ], 'TreatmentName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '^[-a-zA-Z0-9._]*$', ], 'TreatmentNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TreatmentName', ], 'max' => 5, 'min' => 1, ], 'TreatmentToWeightMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TreatmentName', ], 'value' => [ 'shape' => 'SplitWeight', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'experiment', 'project', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'experiment' => [ 'shape' => 'ExperimentName', 'location' => 'uri', 'locationName' => 'experiment', ], 'metricGoals' => [ 'shape' => 'MetricGoalConfigList', ], 'onlineAbConfig' => [ 'shape' => 'OnlineAbConfig', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 'randomizationSalt' => [ 'shape' => 'RandomizationSalt', ], 'removeSegment' => [ 'shape' => 'PrimitiveBoolean', ], 'samplingRate' => [ 'shape' => 'SplitWeight', 'box' => true, ], 'segment' => [ 'shape' => 'SegmentRef', ], 'treatments' => [ 'shape' => 'TreatmentConfigList', ], ], ], 'UpdateExperimentResponse' => [ 'type' => 'structure', 'required' => [ 'experiment', ], 'members' => [ 'experiment' => [ 'shape' => 'Experiment', ], ], ], 'UpdateFeatureRequest' => [ 'type' => 'structure', 'required' => [ 'feature', 'project', ], 'members' => [ 'addOrUpdateVariations' => [ 'shape' => 'VariationConfigsList', ], 'defaultVariation' => [ 'shape' => 'VariationName', ], 'description' => [ 'shape' => 'Description', ], 'entityOverrides' => [ 'shape' => 'EntityOverrideMap', ], 'evaluationStrategy' => [ 'shape' => 'FeatureEvaluationStrategy', ], 'feature' => [ 'shape' => 'FeatureName', 'location' => 'uri', 'locationName' => 'feature', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 'removeVariations' => [ 'shape' => 'VariationNameList', ], ], ], 'UpdateFeatureResponse' => [ 'type' => 'structure', 'required' => [ 'feature', ], 'members' => [ 'feature' => [ 'shape' => 'Feature', ], ], ], 'UpdateLaunchRequest' => [ 'type' => 'structure', 'required' => [ 'launch', 'project', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'groups' => [ 'shape' => 'LaunchGroupConfigList', ], 'launch' => [ 'shape' => 'LaunchName', 'location' => 'uri', 'locationName' => 'launch', ], 'metricMonitors' => [ 'shape' => 'MetricMonitorConfigList', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 'randomizationSalt' => [ 'shape' => 'RandomizationSalt', ], 'scheduledSplitsConfig' => [ 'shape' => 'ScheduledSplitsLaunchConfig', ], ], ], 'UpdateLaunchResponse' => [ 'type' => 'structure', 'required' => [ 'launch', ], 'members' => [ 'launch' => [ 'shape' => 'Launch', ], ], ], 'UpdateProjectDataDeliveryRequest' => [ 'type' => 'structure', 'required' => [ 'project', ], 'members' => [ 'cloudWatchLogs' => [ 'shape' => 'CloudWatchLogsDestinationConfig', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], 's3Destination' => [ 'shape' => 'S3DestinationConfig', ], ], ], 'UpdateProjectDataDeliveryResponse' => [ 'type' => 'structure', 'required' => [ 'project', ], 'members' => [ 'project' => [ 'shape' => 'Project', ], ], ], 'UpdateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'project', ], 'members' => [ 'appConfigResource' => [ 'shape' => 'ProjectAppConfigResourceConfig', ], 'description' => [ 'shape' => 'Description', ], 'project' => [ 'shape' => 'ProjectRef', 'location' => 'uri', 'locationName' => 'project', ], ], ], 'UpdateProjectResponse' => [ 'type' => 'structure', 'required' => [ 'project', ], 'members' => [ 'project' => [ 'shape' => 'Project', ], ], ], 'Uuid' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], 'VariableValue' => [ 'type' => 'structure', 'members' => [ 'boolValue' => [ 'shape' => 'Boolean', ], 'doubleValue' => [ 'shape' => 'Double', ], 'longValue' => [ 'shape' => 'VariableValueLongValueLong', ], 'stringValue' => [ 'shape' => 'VariableValueStringValueString', ], ], 'union' => true, ], 'VariableValueLongValueLong' => [ 'type' => 'long', 'box' => true, 'max' => 9007199254740991, 'min' => -9007199254740991, ], 'VariableValueStringValueString' => [ 'type' => 'string', 'max' => 512, 'min' => 0, ], 'Variation' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'VariationName', ], 'value' => [ 'shape' => 'VariableValue', ], ], ], 'VariationConfig' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'VariationName', ], 'value' => [ 'shape' => 'VariableValue', ], ], ], 'VariationConfigsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VariationConfig', ], 'max' => 5, 'min' => 1, ], 'VariationName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '^[-a-zA-Z0-9._]*$', ], 'VariationNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VariationName', ], 'max' => 5, 'min' => 0, ], 'VariationValueType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'LONG', 'DOUBLE', 'BOOLEAN', ], ], 'VariationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Variation', ], ], ],];
