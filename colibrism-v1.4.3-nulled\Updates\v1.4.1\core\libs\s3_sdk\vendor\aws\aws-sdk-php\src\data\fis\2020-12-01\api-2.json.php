<?php
// This file was auto-generated from sdk-root/src/data/fis/2020-12-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-12-01', 'endpointPrefix' => 'fis', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'FIS', 'serviceFullName' => 'AWS Fault Injection Simulator', 'serviceId' => 'fis', 'signatureVersion' => 'v4', 'signingName' => 'fis', 'uid' => 'fis-2020-12-01', ], 'operations' => [ 'CreateExperimentTemplate' => [ 'name' => 'CreateExperimentTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/experimentTemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateExperimentTemplateRequest', ], 'output' => [ 'shape' => 'CreateExperimentTemplateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateTargetAccountConfiguration' => [ 'name' => 'CreateTargetAccountConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/experimentTemplates/{id}/targetAccountConfigurations/{accountId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTargetAccountConfigurationRequest', ], 'output' => [ 'shape' => 'CreateTargetAccountConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'DeleteExperimentTemplate' => [ 'name' => 'DeleteExperimentTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/experimentTemplates/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteExperimentTemplateRequest', ], 'output' => [ 'shape' => 'DeleteExperimentTemplateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteTargetAccountConfiguration' => [ 'name' => 'DeleteTargetAccountConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/experimentTemplates/{id}/targetAccountConfigurations/{accountId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTargetAccountConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteTargetAccountConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAction' => [ 'name' => 'GetAction', 'http' => [ 'method' => 'GET', 'requestUri' => '/actions/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetActionRequest', ], 'output' => [ 'shape' => 'GetActionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetExperiment' => [ 'name' => 'GetExperiment', 'http' => [ 'method' => 'GET', 'requestUri' => '/experiments/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetExperimentRequest', ], 'output' => [ 'shape' => 'GetExperimentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetExperimentTargetAccountConfiguration' => [ 'name' => 'GetExperimentTargetAccountConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/experiments/{id}/targetAccountConfigurations/{accountId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetExperimentTargetAccountConfigurationRequest', ], 'output' => [ 'shape' => 'GetExperimentTargetAccountConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetExperimentTemplate' => [ 'name' => 'GetExperimentTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/experimentTemplates/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetExperimentTemplateRequest', ], 'output' => [ 'shape' => 'GetExperimentTemplateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetTargetAccountConfiguration' => [ 'name' => 'GetTargetAccountConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/experimentTemplates/{id}/targetAccountConfigurations/{accountId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTargetAccountConfigurationRequest', ], 'output' => [ 'shape' => 'GetTargetAccountConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetTargetResourceType' => [ 'name' => 'GetTargetResourceType', 'http' => [ 'method' => 'GET', 'requestUri' => '/targetResourceTypes/{resourceType}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTargetResourceTypeRequest', ], 'output' => [ 'shape' => 'GetTargetResourceTypeResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListActions' => [ 'name' => 'ListActions', 'http' => [ 'method' => 'GET', 'requestUri' => '/actions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListActionsRequest', ], 'output' => [ 'shape' => 'ListActionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListExperimentResolvedTargets' => [ 'name' => 'ListExperimentResolvedTargets', 'http' => [ 'method' => 'GET', 'requestUri' => '/experiments/{id}/resolvedTargets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListExperimentResolvedTargetsRequest', ], 'output' => [ 'shape' => 'ListExperimentResolvedTargetsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListExperimentTargetAccountConfigurations' => [ 'name' => 'ListExperimentTargetAccountConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/experiments/{id}/targetAccountConfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListExperimentTargetAccountConfigurationsRequest', ], 'output' => [ 'shape' => 'ListExperimentTargetAccountConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListExperimentTemplates' => [ 'name' => 'ListExperimentTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/experimentTemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListExperimentTemplatesRequest', ], 'output' => [ 'shape' => 'ListExperimentTemplatesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListExperiments' => [ 'name' => 'ListExperiments', 'http' => [ 'method' => 'GET', 'requestUri' => '/experiments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListExperimentsRequest', ], 'output' => [ 'shape' => 'ListExperimentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], ], 'ListTargetAccountConfigurations' => [ 'name' => 'ListTargetAccountConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/experimentTemplates/{id}/targetAccountConfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTargetAccountConfigurationsRequest', ], 'output' => [ 'shape' => 'ListTargetAccountConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTargetResourceTypes' => [ 'name' => 'ListTargetResourceTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/targetResourceTypes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTargetResourceTypesRequest', ], 'output' => [ 'shape' => 'ListTargetResourceTypesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'StartExperiment' => [ 'name' => 'StartExperiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/experiments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartExperimentRequest', ], 'output' => [ 'shape' => 'StartExperimentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'StopExperiment' => [ 'name' => 'StopExperiment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/experiments/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopExperimentRequest', ], 'output' => [ 'shape' => 'StopExperimentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], ], 'UpdateExperimentTemplate' => [ 'name' => 'UpdateExperimentTemplate', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/experimentTemplates/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateExperimentTemplateRequest', ], 'output' => [ 'shape' => 'UpdateExperimentTemplateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'UpdateTargetAccountConfiguration' => [ 'name' => 'UpdateTargetAccountConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/experimentTemplates/{id}/targetAccountConfigurations/{accountId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTargetAccountConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateTargetAccountConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AccountTargeting' => [ 'type' => 'string', 'enum' => [ 'single-account', 'multi-account', ], ], 'Action' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ActionId', ], 'description' => [ 'shape' => 'ActionDescription', ], 'parameters' => [ 'shape' => 'ActionParameterMap', ], 'targets' => [ 'shape' => 'ActionTargetMap', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ActionDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ActionId' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\S]+', ], 'ActionParameter' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'ActionParameterDescription', ], 'required' => [ 'shape' => 'ActionParameterRequired', 'box' => true, ], ], ], 'ActionParameterDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ActionParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ActionParameterName', ], 'value' => [ 'shape' => 'ActionParameter', ], ], 'ActionParameterName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ActionParameterRequired' => [ 'type' => 'boolean', ], 'ActionSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ActionId', ], 'description' => [ 'shape' => 'ActionDescription', ], 'targets' => [ 'shape' => 'ActionTargetMap', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ActionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionSummary', ], ], 'ActionTarget' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'TargetResourceTypeId', ], ], ], 'ActionTargetMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ActionTargetName', ], 'value' => [ 'shape' => 'ActionTarget', ], ], 'ActionTargetName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ClientToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\S]+', ], 'CloudWatchLogGroupArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '[\\S]+', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CreateExperimentTemplateActionInput' => [ 'type' => 'structure', 'required' => [ 'actionId', ], 'members' => [ 'actionId' => [ 'shape' => 'ActionId', ], 'description' => [ 'shape' => 'ExperimentTemplateActionDescription', ], 'parameters' => [ 'shape' => 'ExperimentTemplateActionParameterMap', ], 'targets' => [ 'shape' => 'ExperimentTemplateActionTargetMap', ], 'startAfter' => [ 'shape' => 'ExperimentTemplateActionStartAfterList', ], ], ], 'CreateExperimentTemplateActionInputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateActionName', ], 'value' => [ 'shape' => 'CreateExperimentTemplateActionInput', ], ], 'CreateExperimentTemplateExperimentOptionsInput' => [ 'type' => 'structure', 'members' => [ 'accountTargeting' => [ 'shape' => 'AccountTargeting', ], 'emptyTargetResolutionMode' => [ 'shape' => 'EmptyTargetResolutionMode', ], ], ], 'CreateExperimentTemplateLogConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'logSchemaVersion', ], 'members' => [ 'cloudWatchLogsConfiguration' => [ 'shape' => 'ExperimentTemplateCloudWatchLogsLogConfigurationInput', ], 's3Configuration' => [ 'shape' => 'ExperimentTemplateS3LogConfigurationInput', ], 'logSchemaVersion' => [ 'shape' => 'LogSchemaVersion', ], ], ], 'CreateExperimentTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'clientToken', 'description', 'stopConditions', 'actions', 'roleArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'ExperimentTemplateDescription', ], 'stopConditions' => [ 'shape' => 'CreateExperimentTemplateStopConditionInputList', ], 'targets' => [ 'shape' => 'CreateExperimentTemplateTargetInputMap', ], 'actions' => [ 'shape' => 'CreateExperimentTemplateActionInputMap', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'logConfiguration' => [ 'shape' => 'CreateExperimentTemplateLogConfigurationInput', ], 'experimentOptions' => [ 'shape' => 'CreateExperimentTemplateExperimentOptionsInput', ], ], ], 'CreateExperimentTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'experimentTemplate' => [ 'shape' => 'ExperimentTemplate', ], ], ], 'CreateExperimentTemplateStopConditionInput' => [ 'type' => 'structure', 'required' => [ 'source', ], 'members' => [ 'source' => [ 'shape' => 'StopConditionSource', ], 'value' => [ 'shape' => 'StopConditionValue', ], ], ], 'CreateExperimentTemplateStopConditionInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateExperimentTemplateStopConditionInput', ], ], 'CreateExperimentTemplateTargetInput' => [ 'type' => 'structure', 'required' => [ 'resourceType', 'selectionMode', ], 'members' => [ 'resourceType' => [ 'shape' => 'TargetResourceTypeId', ], 'resourceArns' => [ 'shape' => 'ResourceArnList', ], 'resourceTags' => [ 'shape' => 'TagMap', ], 'filters' => [ 'shape' => 'ExperimentTemplateTargetFilterInputList', ], 'selectionMode' => [ 'shape' => 'ExperimentTemplateTargetSelectionMode', ], 'parameters' => [ 'shape' => 'ExperimentTemplateTargetParameterMap', ], ], ], 'CreateExperimentTemplateTargetInputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateTargetName', ], 'value' => [ 'shape' => 'CreateExperimentTemplateTargetInput', ], ], 'CreateTargetAccountConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'experimentTemplateId', 'accountId', 'roleArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'experimentTemplateId' => [ 'shape' => 'ExperimentTemplateId', 'location' => 'uri', 'locationName' => 'id', ], 'accountId' => [ 'shape' => 'TargetAccountId', 'location' => 'uri', 'locationName' => 'accountId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'description' => [ 'shape' => 'TargetAccountConfigurationDescription', ], ], ], 'CreateTargetAccountConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'targetAccountConfiguration' => [ 'shape' => 'TargetAccountConfiguration', ], ], ], 'CreationTime' => [ 'type' => 'timestamp', ], 'DeleteExperimentTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ExperimentTemplateId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteExperimentTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'experimentTemplate' => [ 'shape' => 'ExperimentTemplate', ], ], ], 'DeleteTargetAccountConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'experimentTemplateId', 'accountId', ], 'members' => [ 'experimentTemplateId' => [ 'shape' => 'ExperimentTemplateId', 'location' => 'uri', 'locationName' => 'id', ], 'accountId' => [ 'shape' => 'TargetAccountId', 'location' => 'uri', 'locationName' => 'accountId', ], ], ], 'DeleteTargetAccountConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'targetAccountConfiguration' => [ 'shape' => 'TargetAccountConfiguration', ], ], ], 'EmptyTargetResolutionMode' => [ 'type' => 'string', 'enum' => [ 'fail', 'skip', ], ], 'ExceptionMessage' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\s\\S]+', ], 'Experiment' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ExperimentId', ], 'experimentTemplateId' => [ 'shape' => 'ExperimentTemplateId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'state' => [ 'shape' => 'ExperimentState', ], 'targets' => [ 'shape' => 'ExperimentTargetMap', ], 'actions' => [ 'shape' => 'ExperimentActionMap', ], 'stopConditions' => [ 'shape' => 'ExperimentStopConditionList', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'startTime' => [ 'shape' => 'ExperimentStartTime', ], 'endTime' => [ 'shape' => 'ExperimentEndTime', ], 'tags' => [ 'shape' => 'TagMap', ], 'logConfiguration' => [ 'shape' => 'ExperimentLogConfiguration', ], 'experimentOptions' => [ 'shape' => 'ExperimentOptions', ], 'targetAccountConfigurationsCount' => [ 'shape' => 'TargetAccountConfigurationsCount', ], ], ], 'ExperimentAction' => [ 'type' => 'structure', 'members' => [ 'actionId' => [ 'shape' => 'ActionId', ], 'description' => [ 'shape' => 'ExperimentActionDescription', ], 'parameters' => [ 'shape' => 'ExperimentActionParameterMap', ], 'targets' => [ 'shape' => 'ExperimentActionTargetMap', ], 'startAfter' => [ 'shape' => 'ExperimentActionStartAfterList', ], 'state' => [ 'shape' => 'ExperimentActionState', ], 'startTime' => [ 'shape' => 'ExperimentActionStartTime', ], 'endTime' => [ 'shape' => 'ExperimentActionEndTime', ], ], ], 'ExperimentActionDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ExperimentActionEndTime' => [ 'type' => 'timestamp', ], 'ExperimentActionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentActionName', ], 'value' => [ 'shape' => 'ExperimentAction', ], ], 'ExperimentActionName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentActionParameter' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\S]+', ], 'ExperimentActionParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentActionParameterName', ], 'value' => [ 'shape' => 'ExperimentActionParameter', ], ], 'ExperimentActionParameterName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentActionStartAfter' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentActionStartAfterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentActionStartAfter', ], ], 'ExperimentActionStartTime' => [ 'type' => 'timestamp', ], 'ExperimentActionState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ExperimentActionStatus', ], 'reason' => [ 'shape' => 'ExperimentActionStatusReason', ], ], ], 'ExperimentActionStatus' => [ 'type' => 'string', 'enum' => [ 'pending', 'initiating', 'running', 'completed', 'cancelled', 'stopping', 'stopped', 'failed', 'skipped', ], ], 'ExperimentActionStatusReason' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ExperimentActionTargetMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentActionTargetName', ], 'value' => [ 'shape' => 'ExperimentTargetName', ], ], 'ExperimentActionTargetName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentCloudWatchLogsLogConfiguration' => [ 'type' => 'structure', 'members' => [ 'logGroupArn' => [ 'shape' => 'CloudWatchLogGroupArn', ], ], ], 'ExperimentEndTime' => [ 'type' => 'timestamp', ], 'ExperimentId' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentLogConfiguration' => [ 'type' => 'structure', 'members' => [ 'cloudWatchLogsConfiguration' => [ 'shape' => 'ExperimentCloudWatchLogsLogConfiguration', ], 's3Configuration' => [ 'shape' => 'ExperimentS3LogConfiguration', ], 'logSchemaVersion' => [ 'shape' => 'LogSchemaVersion', ], ], ], 'ExperimentOptions' => [ 'type' => 'structure', 'members' => [ 'accountTargeting' => [ 'shape' => 'AccountTargeting', ], 'emptyTargetResolutionMode' => [ 'shape' => 'EmptyTargetResolutionMode', ], ], ], 'ExperimentS3LogConfiguration' => [ 'type' => 'structure', 'members' => [ 'bucketName' => [ 'shape' => 'S3BucketName', ], 'prefix' => [ 'shape' => 'S3ObjectKey', ], ], ], 'ExperimentStartTime' => [ 'type' => 'timestamp', ], 'ExperimentState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ExperimentStatus', ], 'reason' => [ 'shape' => 'ExperimentStatusReason', ], ], ], 'ExperimentStatus' => [ 'type' => 'string', 'enum' => [ 'pending', 'initiating', 'running', 'completed', 'stopping', 'stopped', 'failed', ], ], 'ExperimentStatusReason' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ExperimentStopCondition' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'StopConditionSource', ], 'value' => [ 'shape' => 'StopConditionValue', ], ], ], 'ExperimentStopConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentStopCondition', ], ], 'ExperimentSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ExperimentId', ], 'experimentTemplateId' => [ 'shape' => 'ExperimentTemplateId', ], 'state' => [ 'shape' => 'ExperimentState', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ExperimentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentSummary', ], ], 'ExperimentTarget' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'TargetResourceTypeId', ], 'resourceArns' => [ 'shape' => 'ResourceArnList', ], 'resourceTags' => [ 'shape' => 'TagMap', ], 'filters' => [ 'shape' => 'ExperimentTargetFilterList', ], 'selectionMode' => [ 'shape' => 'ExperimentTargetSelectionMode', ], 'parameters' => [ 'shape' => 'ExperimentTargetParameterMap', ], ], ], 'ExperimentTargetAccountConfiguration' => [ 'type' => 'structure', 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], 'accountId' => [ 'shape' => 'TargetAccountId', ], 'description' => [ 'shape' => 'TargetAccountConfigurationDescription', ], ], ], 'ExperimentTargetAccountConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTargetAccountConfigurationSummary', ], ], 'ExperimentTargetAccountConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], 'accountId' => [ 'shape' => 'TargetAccountId', ], 'description' => [ 'shape' => 'TargetAccountConfigurationDescription', ], ], ], 'ExperimentTargetFilter' => [ 'type' => 'structure', 'members' => [ 'path' => [ 'shape' => 'ExperimentTargetFilterPath', ], 'values' => [ 'shape' => 'ExperimentTargetFilterValues', ], ], ], 'ExperimentTargetFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTargetFilter', ], ], 'ExperimentTargetFilterPath' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\S]+', ], 'ExperimentTargetFilterValue' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\S]+', ], 'ExperimentTargetFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTargetFilterValue', ], ], 'ExperimentTargetMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTargetName', ], 'value' => [ 'shape' => 'ExperimentTarget', ], ], 'ExperimentTargetName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTargetParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTargetParameterName', ], 'value' => [ 'shape' => 'ExperimentTargetParameterValue', ], ], 'ExperimentTargetParameterName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTargetParameterValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ExperimentTargetSelectionMode' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplate' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ExperimentTemplateId', ], 'description' => [ 'shape' => 'ExperimentTemplateDescription', ], 'targets' => [ 'shape' => 'ExperimentTemplateTargetMap', ], 'actions' => [ 'shape' => 'ExperimentTemplateActionMap', ], 'stopConditions' => [ 'shape' => 'ExperimentTemplateStopConditionList', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'lastUpdateTime' => [ 'shape' => 'LastUpdateTime', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'logConfiguration' => [ 'shape' => 'ExperimentTemplateLogConfiguration', ], 'experimentOptions' => [ 'shape' => 'ExperimentTemplateExperimentOptions', ], 'targetAccountConfigurationsCount' => [ 'shape' => 'TargetAccountConfigurationsCount', ], ], ], 'ExperimentTemplateAction' => [ 'type' => 'structure', 'members' => [ 'actionId' => [ 'shape' => 'ActionId', ], 'description' => [ 'shape' => 'ExperimentTemplateActionDescription', ], 'parameters' => [ 'shape' => 'ExperimentTemplateActionParameterMap', ], 'targets' => [ 'shape' => 'ExperimentTemplateActionTargetMap', ], 'startAfter' => [ 'shape' => 'ExperimentTemplateActionStartAfterList', ], ], ], 'ExperimentTemplateActionDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ExperimentTemplateActionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateActionName', ], 'value' => [ 'shape' => 'ExperimentTemplateAction', ], ], 'ExperimentTemplateActionName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateActionParameter' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\S]+', ], 'ExperimentTemplateActionParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateActionParameterName', ], 'value' => [ 'shape' => 'ExperimentTemplateActionParameter', ], ], 'ExperimentTemplateActionParameterName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateActionStartAfter' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateActionStartAfterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTemplateActionStartAfter', ], ], 'ExperimentTemplateActionTargetMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateActionTargetName', ], 'value' => [ 'shape' => 'ExperimentTemplateTargetName', ], ], 'ExperimentTemplateActionTargetName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateCloudWatchLogsLogConfiguration' => [ 'type' => 'structure', 'members' => [ 'logGroupArn' => [ 'shape' => 'CloudWatchLogGroupArn', ], ], ], 'ExperimentTemplateCloudWatchLogsLogConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'logGroupArn', ], 'members' => [ 'logGroupArn' => [ 'shape' => 'CloudWatchLogGroupArn', ], ], ], 'ExperimentTemplateDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ExperimentTemplateExperimentOptions' => [ 'type' => 'structure', 'members' => [ 'accountTargeting' => [ 'shape' => 'AccountTargeting', ], 'emptyTargetResolutionMode' => [ 'shape' => 'EmptyTargetResolutionMode', ], ], ], 'ExperimentTemplateId' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateLogConfiguration' => [ 'type' => 'structure', 'members' => [ 'cloudWatchLogsConfiguration' => [ 'shape' => 'ExperimentTemplateCloudWatchLogsLogConfiguration', ], 's3Configuration' => [ 'shape' => 'ExperimentTemplateS3LogConfiguration', ], 'logSchemaVersion' => [ 'shape' => 'LogSchemaVersion', ], ], ], 'ExperimentTemplateS3LogConfiguration' => [ 'type' => 'structure', 'members' => [ 'bucketName' => [ 'shape' => 'S3BucketName', ], 'prefix' => [ 'shape' => 'S3ObjectKey', ], ], ], 'ExperimentTemplateS3LogConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'S3BucketName', ], 'prefix' => [ 'shape' => 'S3ObjectKey', ], ], ], 'ExperimentTemplateStopCondition' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'StopConditionSource', ], 'value' => [ 'shape' => 'StopConditionValue', ], ], ], 'ExperimentTemplateStopConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTemplateStopCondition', ], ], 'ExperimentTemplateSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ExperimentTemplateId', ], 'description' => [ 'shape' => 'ExperimentTemplateDescription', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'lastUpdateTime' => [ 'shape' => 'LastUpdateTime', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ExperimentTemplateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTemplateSummary', ], ], 'ExperimentTemplateTarget' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'TargetResourceTypeId', ], 'resourceArns' => [ 'shape' => 'ResourceArnList', ], 'resourceTags' => [ 'shape' => 'TagMap', ], 'filters' => [ 'shape' => 'ExperimentTemplateTargetFilterList', ], 'selectionMode' => [ 'shape' => 'ExperimentTemplateTargetSelectionMode', ], 'parameters' => [ 'shape' => 'ExperimentTemplateTargetParameterMap', ], ], ], 'ExperimentTemplateTargetFilter' => [ 'type' => 'structure', 'members' => [ 'path' => [ 'shape' => 'ExperimentTemplateTargetFilterPath', ], 'values' => [ 'shape' => 'ExperimentTemplateTargetFilterValues', ], ], ], 'ExperimentTemplateTargetFilterInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTemplateTargetInputFilter', ], ], 'ExperimentTemplateTargetFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTemplateTargetFilter', ], ], 'ExperimentTemplateTargetFilterPath' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\S]+', ], 'ExperimentTemplateTargetFilterValue' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\S]+', ], 'ExperimentTemplateTargetFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTemplateTargetFilterValue', ], ], 'ExperimentTemplateTargetInputFilter' => [ 'type' => 'structure', 'required' => [ 'path', 'values', ], 'members' => [ 'path' => [ 'shape' => 'ExperimentTemplateTargetFilterPath', ], 'values' => [ 'shape' => 'ExperimentTemplateTargetFilterValues', ], ], ], 'ExperimentTemplateTargetMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateTargetName', ], 'value' => [ 'shape' => 'ExperimentTemplateTarget', ], ], 'ExperimentTemplateTargetName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateTargetParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateTargetParameterName', ], 'value' => [ 'shape' => 'ExperimentTemplateTargetParameterValue', ], ], 'ExperimentTemplateTargetParameterName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateTargetParameterValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]+$', ], 'ExperimentTemplateTargetSelectionMode' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'GetActionRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ActionId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetActionResponse' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'Action', ], ], ], 'GetExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ExperimentId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'experiment' => [ 'shape' => 'Experiment', ], ], ], 'GetExperimentTargetAccountConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'experimentId', 'accountId', ], 'members' => [ 'experimentId' => [ 'shape' => 'ExperimentId', 'location' => 'uri', 'locationName' => 'id', ], 'accountId' => [ 'shape' => 'TargetAccountId', 'location' => 'uri', 'locationName' => 'accountId', ], ], ], 'GetExperimentTargetAccountConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'targetAccountConfiguration' => [ 'shape' => 'ExperimentTargetAccountConfiguration', ], ], ], 'GetExperimentTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ExperimentTemplateId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetExperimentTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'experimentTemplate' => [ 'shape' => 'ExperimentTemplate', ], ], ], 'GetTargetAccountConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'experimentTemplateId', 'accountId', ], 'members' => [ 'experimentTemplateId' => [ 'shape' => 'ExperimentTemplateId', 'location' => 'uri', 'locationName' => 'id', ], 'accountId' => [ 'shape' => 'TargetAccountId', 'location' => 'uri', 'locationName' => 'accountId', ], ], ], 'GetTargetAccountConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'targetAccountConfiguration' => [ 'shape' => 'TargetAccountConfiguration', ], ], ], 'GetTargetResourceTypeRequest' => [ 'type' => 'structure', 'required' => [ 'resourceType', ], 'members' => [ 'resourceType' => [ 'shape' => 'TargetResourceTypeId', 'location' => 'uri', 'locationName' => 'resourceType', ], ], ], 'GetTargetResourceTypeResponse' => [ 'type' => 'structure', 'members' => [ 'targetResourceType' => [ 'shape' => 'TargetResourceType', ], ], ], 'LastUpdateTime' => [ 'type' => 'timestamp', ], 'ListActionsMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListActionsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListActionsMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListActionsResponse' => [ 'type' => 'structure', 'members' => [ 'actions' => [ 'shape' => 'ActionSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExperimentResolvedTargetsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListExperimentResolvedTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'experimentId', ], 'members' => [ 'experimentId' => [ 'shape' => 'ExperimentId', 'location' => 'uri', 'locationName' => 'id', ], 'maxResults' => [ 'shape' => 'ListExperimentResolvedTargetsMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'targetName' => [ 'shape' => 'TargetName', 'location' => 'querystring', 'locationName' => 'targetName', ], ], ], 'ListExperimentResolvedTargetsResponse' => [ 'type' => 'structure', 'members' => [ 'resolvedTargets' => [ 'shape' => 'ResolvedTargetList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExperimentTargetAccountConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'experimentId', ], 'members' => [ 'experimentId' => [ 'shape' => 'ExperimentId', 'location' => 'uri', 'locationName' => 'id', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListExperimentTargetAccountConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'targetAccountConfigurations' => [ 'shape' => 'ExperimentTargetAccountConfigurationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExperimentTemplatesMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListExperimentTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListExperimentTemplatesMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListExperimentTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'experimentTemplates' => [ 'shape' => 'ExperimentTemplateSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExperimentsMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListExperimentsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListExperimentsMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListExperimentsResponse' => [ 'type' => 'structure', 'members' => [ 'experiments' => [ 'shape' => 'ExperimentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTargetAccountConfigurationsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListTargetAccountConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'experimentTemplateId', ], 'members' => [ 'experimentTemplateId' => [ 'shape' => 'ExperimentTemplateId', 'location' => 'uri', 'locationName' => 'id', ], 'maxResults' => [ 'shape' => 'ListTargetAccountConfigurationsMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListTargetAccountConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'targetAccountConfigurations' => [ 'shape' => 'TargetAccountConfigurationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTargetResourceTypesMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListTargetResourceTypesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListTargetResourceTypesMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListTargetResourceTypesResponse' => [ 'type' => 'structure', 'members' => [ 'targetResourceTypes' => [ 'shape' => 'TargetResourceTypeSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'LogSchemaVersion' => [ 'type' => 'integer', ], 'NextToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\S]+', ], 'ResolvedTarget' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'TargetResourceTypeId', ], 'targetName' => [ 'shape' => 'TargetName', ], 'targetInformation' => [ 'shape' => 'TargetInformationMap', ], ], ], 'ResolvedTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResolvedTarget', ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '[\\S]+', ], 'ResourceArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceArn', ], 'max' => 5, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '[\\S]+', ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '[\\S]+', ], 'S3ObjectKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\s\\S]+', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'StartExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'clientToken', 'experimentTemplateId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'experimentTemplateId' => [ 'shape' => 'ExperimentTemplateId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'experiment' => [ 'shape' => 'Experiment', ], ], ], 'StopConditionSource' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'StopConditionValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '[\\s\\S]+', ], 'StopExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ExperimentId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'StopExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'experiment' => [ 'shape' => 'Experiment', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\s\\S]+', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\s\\S]*', ], 'TargetAccountConfiguration' => [ 'type' => 'structure', 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], 'accountId' => [ 'shape' => 'TargetAccountId', ], 'description' => [ 'shape' => 'TargetAccountConfigurationDescription', ], ], ], 'TargetAccountConfigurationDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]*', ], 'TargetAccountConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetAccountConfigurationSummary', ], ], 'TargetAccountConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], 'accountId' => [ 'shape' => 'TargetAccountId', ], 'description' => [ 'shape' => 'TargetAccountConfigurationDescription', ], ], ], 'TargetAccountConfigurationsCount' => [ 'type' => 'long', 'min' => 0, ], 'TargetAccountId' => [ 'type' => 'string', 'max' => 48, 'min' => 12, 'pattern' => '[\\S]+', ], 'TargetInformationKey' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'TargetInformationMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TargetInformationKey', ], 'value' => [ 'shape' => 'TargetInformationValue', ], ], 'TargetInformationValue' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\S]+', ], 'TargetName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'TargetResourceType' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'TargetResourceTypeId', ], 'description' => [ 'shape' => 'TargetResourceTypeDescription', ], 'parameters' => [ 'shape' => 'TargetResourceTypeParameterMap', ], ], ], 'TargetResourceTypeDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'TargetResourceTypeId' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\S]+', ], 'TargetResourceTypeParameter' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'TargetResourceTypeParameterDescription', ], 'required' => [ 'shape' => 'TargetResourceTypeParameterRequired', 'box' => true, ], ], ], 'TargetResourceTypeParameterDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'TargetResourceTypeParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TargetResourceTypeParameterName', ], 'value' => [ 'shape' => 'TargetResourceTypeParameter', ], ], 'TargetResourceTypeParameterName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'TargetResourceTypeParameterRequired' => [ 'type' => 'boolean', ], 'TargetResourceTypeSummary' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'TargetResourceTypeId', ], 'description' => [ 'shape' => 'TargetResourceTypeDescription', ], ], ], 'TargetResourceTypeSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetResourceTypeSummary', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateExperimentTemplateActionInputItem' => [ 'type' => 'structure', 'members' => [ 'actionId' => [ 'shape' => 'ActionId', ], 'description' => [ 'shape' => 'ExperimentTemplateActionDescription', ], 'parameters' => [ 'shape' => 'ExperimentTemplateActionParameterMap', ], 'targets' => [ 'shape' => 'ExperimentTemplateActionTargetMap', ], 'startAfter' => [ 'shape' => 'ExperimentTemplateActionStartAfterList', ], ], ], 'UpdateExperimentTemplateActionInputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateActionName', ], 'value' => [ 'shape' => 'UpdateExperimentTemplateActionInputItem', ], ], 'UpdateExperimentTemplateExperimentOptionsInput' => [ 'type' => 'structure', 'members' => [ 'emptyTargetResolutionMode' => [ 'shape' => 'EmptyTargetResolutionMode', ], ], ], 'UpdateExperimentTemplateLogConfigurationInput' => [ 'type' => 'structure', 'members' => [ 'cloudWatchLogsConfiguration' => [ 'shape' => 'ExperimentTemplateCloudWatchLogsLogConfigurationInput', ], 's3Configuration' => [ 'shape' => 'ExperimentTemplateS3LogConfigurationInput', ], 'logSchemaVersion' => [ 'shape' => 'LogSchemaVersion', ], ], ], 'UpdateExperimentTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ExperimentTemplateId', 'location' => 'uri', 'locationName' => 'id', ], 'description' => [ 'shape' => 'ExperimentTemplateDescription', ], 'stopConditions' => [ 'shape' => 'UpdateExperimentTemplateStopConditionInputList', ], 'targets' => [ 'shape' => 'UpdateExperimentTemplateTargetInputMap', ], 'actions' => [ 'shape' => 'UpdateExperimentTemplateActionInputMap', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'logConfiguration' => [ 'shape' => 'UpdateExperimentTemplateLogConfigurationInput', ], 'experimentOptions' => [ 'shape' => 'UpdateExperimentTemplateExperimentOptionsInput', ], ], ], 'UpdateExperimentTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'experimentTemplate' => [ 'shape' => 'ExperimentTemplate', ], ], ], 'UpdateExperimentTemplateStopConditionInput' => [ 'type' => 'structure', 'required' => [ 'source', ], 'members' => [ 'source' => [ 'shape' => 'StopConditionSource', ], 'value' => [ 'shape' => 'StopConditionValue', ], ], ], 'UpdateExperimentTemplateStopConditionInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateExperimentTemplateStopConditionInput', ], ], 'UpdateExperimentTemplateTargetInput' => [ 'type' => 'structure', 'required' => [ 'resourceType', 'selectionMode', ], 'members' => [ 'resourceType' => [ 'shape' => 'TargetResourceTypeId', ], 'resourceArns' => [ 'shape' => 'ResourceArnList', ], 'resourceTags' => [ 'shape' => 'TagMap', ], 'filters' => [ 'shape' => 'ExperimentTemplateTargetFilterInputList', ], 'selectionMode' => [ 'shape' => 'ExperimentTemplateTargetSelectionMode', ], 'parameters' => [ 'shape' => 'ExperimentTemplateTargetParameterMap', ], ], ], 'UpdateExperimentTemplateTargetInputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateTargetName', ], 'value' => [ 'shape' => 'UpdateExperimentTemplateTargetInput', ], ], 'UpdateTargetAccountConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'experimentTemplateId', 'accountId', ], 'members' => [ 'experimentTemplateId' => [ 'shape' => 'ExperimentTemplateId', 'location' => 'uri', 'locationName' => 'id', ], 'accountId' => [ 'shape' => 'TargetAccountId', 'location' => 'uri', 'locationName' => 'accountId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'description' => [ 'shape' => 'TargetAccountConfigurationDescription', ], ], ], 'UpdateTargetAccountConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'targetAccountConfiguration' => [ 'shape' => 'TargetAccountConfiguration', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], ],];
