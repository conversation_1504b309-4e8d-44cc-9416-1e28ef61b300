<?php
// This file was auto-generated from sdk-root/src/data/mediatailor/2018-04-23/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-04-23', 'endpointPrefix' => 'api.mediatailor', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'MediaTailor', 'serviceFullName' => 'AWS MediaTailor', 'serviceId' => 'MediaTailor', 'signatureVersion' => 'v4', 'signingName' => 'mediatailor', 'uid' => 'mediatailor-2018-04-23', ], 'operations' => [ 'ConfigureLogsForChannel' => [ 'name' => 'ConfigureLogsForChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/configureLogs/channel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ConfigureLogsForChannelRequest', ], 'output' => [ 'shape' => 'ConfigureLogsForChannelResponse', ], ], 'ConfigureLogsForPlaybackConfiguration' => [ 'name' => 'ConfigureLogsForPlaybackConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/configureLogs/playbackConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ConfigureLogsForPlaybackConfigurationRequest', ], 'output' => [ 'shape' => 'ConfigureLogsForPlaybackConfigurationResponse', ], 'idempotent' => true, ], 'CreateChannel' => [ 'name' => 'CreateChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/channel/{ChannelName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateChannelRequest', ], 'output' => [ 'shape' => 'CreateChannelResponse', ], 'idempotent' => true, ], 'CreateLiveSource' => [ 'name' => 'CreateLiveSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateLiveSourceRequest', ], 'output' => [ 'shape' => 'CreateLiveSourceResponse', ], 'idempotent' => true, ], 'CreatePrefetchSchedule' => [ 'name' => 'CreatePrefetchSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/prefetchSchedule/{PlaybackConfigurationName}/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePrefetchScheduleRequest', ], 'output' => [ 'shape' => 'CreatePrefetchScheduleResponse', ], 'idempotent' => true, ], 'CreateProgram' => [ 'name' => 'CreateProgram', 'http' => [ 'method' => 'POST', 'requestUri' => '/channel/{ChannelName}/program/{ProgramName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateProgramRequest', ], 'output' => [ 'shape' => 'CreateProgramResponse', ], 'idempotent' => true, ], 'CreateSourceLocation' => [ 'name' => 'CreateSourceLocation', 'http' => [ 'method' => 'POST', 'requestUri' => '/sourceLocation/{SourceLocationName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSourceLocationRequest', ], 'output' => [ 'shape' => 'CreateSourceLocationResponse', ], 'idempotent' => true, ], 'CreateVodSource' => [ 'name' => 'CreateVodSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateVodSourceRequest', ], 'output' => [ 'shape' => 'CreateVodSourceResponse', ], 'idempotent' => true, ], 'DeleteChannel' => [ 'name' => 'DeleteChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channel/{ChannelName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteChannelRequest', ], 'output' => [ 'shape' => 'DeleteChannelResponse', ], 'idempotent' => true, ], 'DeleteChannelPolicy' => [ 'name' => 'DeleteChannelPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channel/{ChannelName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteChannelPolicyRequest', ], 'output' => [ 'shape' => 'DeleteChannelPolicyResponse', ], 'idempotent' => true, ], 'DeleteLiveSource' => [ 'name' => 'DeleteLiveSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteLiveSourceRequest', ], 'output' => [ 'shape' => 'DeleteLiveSourceResponse', ], 'idempotent' => true, ], 'DeletePlaybackConfiguration' => [ 'name' => 'DeletePlaybackConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/playbackConfiguration/{Name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeletePlaybackConfigurationRequest', ], 'output' => [ 'shape' => 'DeletePlaybackConfigurationResponse', ], 'idempotent' => true, ], 'DeletePrefetchSchedule' => [ 'name' => 'DeletePrefetchSchedule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prefetchSchedule/{PlaybackConfigurationName}/{Name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeletePrefetchScheduleRequest', ], 'output' => [ 'shape' => 'DeletePrefetchScheduleResponse', ], 'idempotent' => true, ], 'DeleteProgram' => [ 'name' => 'DeleteProgram', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channel/{ChannelName}/program/{ProgramName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteProgramRequest', ], 'output' => [ 'shape' => 'DeleteProgramResponse', ], 'idempotent' => true, ], 'DeleteSourceLocation' => [ 'name' => 'DeleteSourceLocation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sourceLocation/{SourceLocationName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSourceLocationRequest', ], 'output' => [ 'shape' => 'DeleteSourceLocationResponse', ], 'idempotent' => true, ], 'DeleteVodSource' => [ 'name' => 'DeleteVodSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteVodSourceRequest', ], 'output' => [ 'shape' => 'DeleteVodSourceResponse', ], 'idempotent' => true, ], 'DescribeChannel' => [ 'name' => 'DescribeChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/channel/{ChannelName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelRequest', ], 'output' => [ 'shape' => 'DescribeChannelResponse', ], ], 'DescribeLiveSource' => [ 'name' => 'DescribeLiveSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeLiveSourceRequest', ], 'output' => [ 'shape' => 'DescribeLiveSourceResponse', ], ], 'DescribeProgram' => [ 'name' => 'DescribeProgram', 'http' => [ 'method' => 'GET', 'requestUri' => '/channel/{ChannelName}/program/{ProgramName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeProgramRequest', ], 'output' => [ 'shape' => 'DescribeProgramResponse', ], ], 'DescribeSourceLocation' => [ 'name' => 'DescribeSourceLocation', 'http' => [ 'method' => 'GET', 'requestUri' => '/sourceLocation/{SourceLocationName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSourceLocationRequest', ], 'output' => [ 'shape' => 'DescribeSourceLocationResponse', ], ], 'DescribeVodSource' => [ 'name' => 'DescribeVodSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeVodSourceRequest', ], 'output' => [ 'shape' => 'DescribeVodSourceResponse', ], ], 'GetChannelPolicy' => [ 'name' => 'GetChannelPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/channel/{ChannelName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelPolicyRequest', ], 'output' => [ 'shape' => 'GetChannelPolicyResponse', ], ], 'GetChannelSchedule' => [ 'name' => 'GetChannelSchedule', 'http' => [ 'method' => 'GET', 'requestUri' => '/channel/{ChannelName}/schedule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelScheduleRequest', ], 'output' => [ 'shape' => 'GetChannelScheduleResponse', ], ], 'GetPlaybackConfiguration' => [ 'name' => 'GetPlaybackConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/playbackConfiguration/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPlaybackConfigurationRequest', ], 'output' => [ 'shape' => 'GetPlaybackConfigurationResponse', ], ], 'GetPrefetchSchedule' => [ 'name' => 'GetPrefetchSchedule', 'http' => [ 'method' => 'GET', 'requestUri' => '/prefetchSchedule/{PlaybackConfigurationName}/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPrefetchScheduleRequest', ], 'output' => [ 'shape' => 'GetPrefetchScheduleResponse', ], ], 'ListAlerts' => [ 'name' => 'ListAlerts', 'http' => [ 'method' => 'GET', 'requestUri' => '/alerts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAlertsRequest', ], 'output' => [ 'shape' => 'ListAlertsResponse', ], ], 'ListChannels' => [ 'name' => 'ListChannels', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelsRequest', ], 'output' => [ 'shape' => 'ListChannelsResponse', ], ], 'ListLiveSources' => [ 'name' => 'ListLiveSources', 'http' => [ 'method' => 'GET', 'requestUri' => '/sourceLocation/{SourceLocationName}/liveSources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLiveSourcesRequest', ], 'output' => [ 'shape' => 'ListLiveSourcesResponse', ], ], 'ListPlaybackConfigurations' => [ 'name' => 'ListPlaybackConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/playbackConfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPlaybackConfigurationsRequest', ], 'output' => [ 'shape' => 'ListPlaybackConfigurationsResponse', ], ], 'ListPrefetchSchedules' => [ 'name' => 'ListPrefetchSchedules', 'http' => [ 'method' => 'POST', 'requestUri' => '/prefetchSchedule/{PlaybackConfigurationName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPrefetchSchedulesRequest', ], 'output' => [ 'shape' => 'ListPrefetchSchedulesResponse', ], ], 'ListSourceLocations' => [ 'name' => 'ListSourceLocations', 'http' => [ 'method' => 'GET', 'requestUri' => '/sourceLocations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSourceLocationsRequest', ], 'output' => [ 'shape' => 'ListSourceLocationsResponse', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], ], ], 'ListVodSources' => [ 'name' => 'ListVodSources', 'http' => [ 'method' => 'GET', 'requestUri' => '/sourceLocation/{SourceLocationName}/vodSources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVodSourcesRequest', ], 'output' => [ 'shape' => 'ListVodSourcesResponse', ], ], 'PutChannelPolicy' => [ 'name' => 'PutChannelPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channel/{ChannelName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutChannelPolicyRequest', ], 'output' => [ 'shape' => 'PutChannelPolicyResponse', ], 'idempotent' => true, ], 'PutPlaybackConfiguration' => [ 'name' => 'PutPlaybackConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/playbackConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutPlaybackConfigurationRequest', ], 'output' => [ 'shape' => 'PutPlaybackConfigurationResponse', ], 'idempotent' => true, ], 'StartChannel' => [ 'name' => 'StartChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channel/{ChannelName}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartChannelRequest', ], 'output' => [ 'shape' => 'StartChannelResponse', ], 'idempotent' => true, ], 'StopChannel' => [ 'name' => 'StopChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channel/{ChannelName}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopChannelRequest', ], 'output' => [ 'shape' => 'StopChannelResponse', ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], ], 'idempotent' => true, ], 'UpdateChannel' => [ 'name' => 'UpdateChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channel/{ChannelName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelRequest', ], 'output' => [ 'shape' => 'UpdateChannelResponse', ], 'idempotent' => true, ], 'UpdateLiveSource' => [ 'name' => 'UpdateLiveSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLiveSourceRequest', ], 'output' => [ 'shape' => 'UpdateLiveSourceResponse', ], 'idempotent' => true, ], 'UpdateProgram' => [ 'name' => 'UpdateProgram', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channel/{ChannelName}/program/{ProgramName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateProgramRequest', ], 'output' => [ 'shape' => 'UpdateProgramResponse', ], 'idempotent' => true, ], 'UpdateSourceLocation' => [ 'name' => 'UpdateSourceLocation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sourceLocation/{SourceLocationName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSourceLocationRequest', ], 'output' => [ 'shape' => 'UpdateSourceLocationResponse', ], 'idempotent' => true, ], 'UpdateVodSource' => [ 'name' => 'UpdateVodSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateVodSourceRequest', ], 'output' => [ 'shape' => 'UpdateVodSourceResponse', ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessConfiguration' => [ 'type' => 'structure', 'members' => [ 'AccessType' => [ 'shape' => 'AccessType', ], 'SecretsManagerAccessTokenConfiguration' => [ 'shape' => 'SecretsManagerAccessTokenConfiguration', ], ], ], 'AccessType' => [ 'type' => 'string', 'enum' => [ 'S3_SIGV4', 'SECRETS_MANAGER_ACCESS_TOKEN', 'AUTODETECT_SIGV4', ], ], 'AdBreak' => [ 'type' => 'structure', 'members' => [ 'AdBreakMetadata' => [ 'shape' => 'AdBreakMetadataList', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'OffsetMillis' => [ 'shape' => '__long', ], 'Slate' => [ 'shape' => 'SlateSource', ], 'SpliceInsertMessage' => [ 'shape' => 'SpliceInsertMessage', ], 'TimeSignalMessage' => [ 'shape' => 'TimeSignalMessage', ], ], ], 'AdBreakMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyValuePair', ], ], 'AdBreakOpportunities' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdBreakOpportunity', ], ], 'AdBreakOpportunity' => [ 'type' => 'structure', 'required' => [ 'OffsetMillis', ], 'members' => [ 'OffsetMillis' => [ 'shape' => '__long', ], ], ], 'AdMarkerPassthrough' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => '__boolean', ], ], ], 'AdMarkupType' => [ 'type' => 'string', 'enum' => [ 'DATERANGE', 'SCTE35_ENHANCED', ], ], 'Alert' => [ 'type' => 'structure', 'required' => [ 'AlertCode', 'AlertMessage', 'LastModifiedTime', 'RelatedResourceArns', 'ResourceArn', ], 'members' => [ 'AlertCode' => [ 'shape' => '__string', ], 'AlertMessage' => [ 'shape' => '__string', ], 'Category' => [ 'shape' => 'AlertCategory', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'RelatedResourceArns' => [ 'shape' => '__listOf__string', ], 'ResourceArn' => [ 'shape' => '__string', ], ], ], 'AlertCategory' => [ 'type' => 'string', 'enum' => [ 'SCHEDULING_ERROR', 'PLAYBACK_WARNING', 'INFO', ], ], 'AvailMatchingCriteria' => [ 'type' => 'structure', 'required' => [ 'DynamicVariable', 'Operator', ], 'members' => [ 'DynamicVariable' => [ 'shape' => '__string', ], 'Operator' => [ 'shape' => 'Operator', ], ], ], 'AvailSuppression' => [ 'type' => 'structure', 'members' => [ 'FillPolicy' => [ 'shape' => 'FillPolicy', ], 'Mode' => [ 'shape' => 'Mode', ], 'Value' => [ 'shape' => '__string', ], ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Bumper' => [ 'type' => 'structure', 'members' => [ 'EndUrl' => [ 'shape' => '__string', ], 'StartUrl' => [ 'shape' => '__string', ], ], ], 'CdnConfiguration' => [ 'type' => 'structure', 'members' => [ 'AdSegmentUrlPrefix' => [ 'shape' => '__string', ], 'ContentSegmentUrlPrefix' => [ 'shape' => '__string', ], ], ], 'Channel' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ChannelName', 'ChannelState', 'LogConfiguration', 'Outputs', 'PlaybackMode', 'Tier', ], 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'ChannelState' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'FillerSlate' => [ 'shape' => 'SlateSource', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'LogConfiguration' => [ 'shape' => 'LogConfigurationForChannel', ], 'Outputs' => [ 'shape' => 'ResponseOutputs', ], 'PlaybackMode' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'Tier' => [ 'shape' => '__string', ], ], ], 'ChannelState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'STOPPED', ], ], 'ClipRange' => [ 'type' => 'structure', 'required' => [ 'EndOffsetMillis', ], 'members' => [ 'EndOffsetMillis' => [ 'shape' => '__long', ], ], ], 'ConfigurationAliasesRequest' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__mapOf__string', ], ], 'ConfigurationAliasesResponse' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__mapOf__string', ], ], 'ConfigureLogsForChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'LogTypes', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', ], 'LogTypes' => [ 'shape' => 'LogTypes', ], ], ], 'ConfigureLogsForChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelName' => [ 'shape' => '__string', ], 'LogTypes' => [ 'shape' => 'LogTypes', ], ], ], 'ConfigureLogsForPlaybackConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'PercentEnabled', 'PlaybackConfigurationName', ], 'members' => [ 'PercentEnabled' => [ 'shape' => '__integer', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', ], ], ], 'ConfigureLogsForPlaybackConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'PercentEnabled', ], 'members' => [ 'PercentEnabled' => [ 'shape' => '__integer', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', ], ], ], 'CreateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'Outputs', 'PlaybackMode', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'FillerSlate' => [ 'shape' => 'SlateSource', ], 'Outputs' => [ 'shape' => 'RequestOutputs', ], 'PlaybackMode' => [ 'shape' => 'PlaybackMode', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'Tier' => [ 'shape' => 'Tier', ], ], ], 'CreateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'ChannelState' => [ 'shape' => 'ChannelState', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'FillerSlate' => [ 'shape' => 'SlateSource', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'Outputs' => [ 'shape' => 'ResponseOutputs', ], 'PlaybackMode' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'Tier' => [ 'shape' => '__string', ], ], ], 'CreateLiveSourceRequest' => [ 'type' => 'structure', 'required' => [ 'HttpPackageConfigurations', 'LiveSourceName', 'SourceLocationName', ], 'members' => [ 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LiveSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'LiveSourceName', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'CreateLiveSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'CreatePrefetchScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'Consumption', 'Name', 'PlaybackConfigurationName', 'Retrieval', ], 'members' => [ 'Consumption' => [ 'shape' => 'PrefetchConsumption', ], 'Name' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'Name', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'PlaybackConfigurationName', ], 'Retrieval' => [ 'shape' => 'PrefetchRetrieval', ], 'StreamId' => [ 'shape' => '__string', ], ], ], 'CreatePrefetchScheduleResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Consumption' => [ 'shape' => 'PrefetchConsumption', ], 'Name' => [ 'shape' => '__string', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', ], 'Retrieval' => [ 'shape' => 'PrefetchRetrieval', ], 'StreamId' => [ 'shape' => '__string', ], ], ], 'CreateProgramRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'ProgramName', 'ScheduleConfiguration', 'SourceLocationName', ], 'members' => [ 'AdBreaks' => [ 'shape' => '__listOfAdBreak', ], 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'ProgramName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ProgramName', ], 'ScheduleConfiguration' => [ 'shape' => 'ScheduleConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'CreateProgramResponse' => [ 'type' => 'structure', 'members' => [ 'AdBreaks' => [ 'shape' => '__listOfAdBreak', ], 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'ClipRange' => [ 'shape' => 'ClipRange', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'DurationMillis' => [ 'shape' => '__long', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'ProgramName' => [ 'shape' => '__string', ], 'ScheduledStartTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'CreateSourceLocationRequest' => [ 'type' => 'structure', 'required' => [ 'HttpConfiguration', 'SourceLocationName', ], 'members' => [ 'AccessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'DefaultSegmentDeliveryConfiguration' => [ 'shape' => 'DefaultSegmentDeliveryConfiguration', ], 'HttpConfiguration' => [ 'shape' => 'HttpConfiguration', ], 'SegmentDeliveryConfigurations' => [ 'shape' => '__listOfSegmentDeliveryConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'CreateSourceLocationResponse' => [ 'type' => 'structure', 'members' => [ 'AccessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'DefaultSegmentDeliveryConfiguration' => [ 'shape' => 'DefaultSegmentDeliveryConfiguration', ], 'HttpConfiguration' => [ 'shape' => 'HttpConfiguration', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SegmentDeliveryConfigurations' => [ 'shape' => '__listOfSegmentDeliveryConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'CreateVodSourceRequest' => [ 'type' => 'structure', 'required' => [ 'HttpPackageConfigurations', 'SourceLocationName', 'VodSourceName', ], 'members' => [ 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'VodSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'VodSourceName', ], ], ], 'CreateVodSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'DashConfiguration' => [ 'type' => 'structure', 'members' => [ 'ManifestEndpointPrefix' => [ 'shape' => '__string', ], 'MpdLocation' => [ 'shape' => '__string', ], 'OriginManifestType' => [ 'shape' => 'OriginManifestType', ], ], ], 'DashConfigurationForPut' => [ 'type' => 'structure', 'members' => [ 'MpdLocation' => [ 'shape' => '__string', ], 'OriginManifestType' => [ 'shape' => 'OriginManifestType', ], ], ], 'DashPlaylistSettings' => [ 'type' => 'structure', 'members' => [ 'ManifestWindowSeconds' => [ 'shape' => '__integer', ], 'MinBufferTimeSeconds' => [ 'shape' => '__integer', ], 'MinUpdatePeriodSeconds' => [ 'shape' => '__integer', ], 'SuggestedPresentationDelaySeconds' => [ 'shape' => '__integer', ], ], ], 'DefaultSegmentDeliveryConfiguration' => [ 'type' => 'structure', 'members' => [ 'BaseUrl' => [ 'shape' => '__string', ], ], ], 'DeleteChannelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'DeleteChannelPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'DeleteChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLiveSourceRequest' => [ 'type' => 'structure', 'required' => [ 'LiveSourceName', 'SourceLocationName', ], 'members' => [ 'LiveSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'LiveSourceName', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'DeleteLiveSourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePlaybackConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DeletePlaybackConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePrefetchScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'PlaybackConfigurationName', ], 'members' => [ 'Name' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'Name', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'PlaybackConfigurationName', ], ], ], 'DeletePrefetchScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProgramRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'ProgramName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'ProgramName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ProgramName', ], ], ], 'DeleteProgramResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSourceLocationRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationName', ], 'members' => [ 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'DeleteSourceLocationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteVodSourceRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationName', 'VodSourceName', ], 'members' => [ 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], 'VodSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'VodSourceName', ], ], ], 'DeleteVodSourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'DescribeChannelResponse' => [ 'type' => 'structure', 'required' => [ 'LogConfiguration', ], 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'ChannelState' => [ 'shape' => 'ChannelState', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'FillerSlate' => [ 'shape' => 'SlateSource', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'LogConfiguration' => [ 'shape' => 'LogConfigurationForChannel', ], 'Outputs' => [ 'shape' => 'ResponseOutputs', ], 'PlaybackMode' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'Tier' => [ 'shape' => '__string', ], ], ], 'DescribeLiveSourceRequest' => [ 'type' => 'structure', 'required' => [ 'LiveSourceName', 'SourceLocationName', ], 'members' => [ 'LiveSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'LiveSourceName', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'DescribeLiveSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'DescribeProgramRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'ProgramName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'ProgramName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ProgramName', ], ], ], 'DescribeProgramResponse' => [ 'type' => 'structure', 'members' => [ 'AdBreaks' => [ 'shape' => '__listOfAdBreak', ], 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'ClipRange' => [ 'shape' => 'ClipRange', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'DurationMillis' => [ 'shape' => 'Long', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'ProgramName' => [ 'shape' => '__string', ], 'ScheduledStartTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'DescribeSourceLocationRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationName', ], 'members' => [ 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'DescribeSourceLocationResponse' => [ 'type' => 'structure', 'members' => [ 'AccessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'DefaultSegmentDeliveryConfiguration' => [ 'shape' => 'DefaultSegmentDeliveryConfiguration', ], 'HttpConfiguration' => [ 'shape' => 'HttpConfiguration', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SegmentDeliveryConfigurations' => [ 'shape' => '__listOfSegmentDeliveryConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'DescribeVodSourceRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationName', 'VodSourceName', ], 'members' => [ 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], 'VodSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'VodSourceName', ], ], ], 'DescribeVodSourceResponse' => [ 'type' => 'structure', 'members' => [ 'AdBreakOpportunities' => [ 'shape' => 'AdBreakOpportunities', ], 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'FillPolicy' => [ 'type' => 'string', 'enum' => [ 'FULL_AVAIL_ONLY', 'PARTIAL_AVAIL', ], ], 'GetChannelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'GetChannelPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => '__string', ], ], ], 'GetChannelScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'DurationMinutes' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'durationMinutes', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetChannelScheduleResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfScheduleEntry', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'GetPlaybackConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'GetPlaybackConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'AdDecisionServerUrl' => [ 'shape' => '__string', ], 'AvailSuppression' => [ 'shape' => 'AvailSuppression', ], 'Bumper' => [ 'shape' => 'Bumper', ], 'CdnConfiguration' => [ 'shape' => 'CdnConfiguration', ], 'ConfigurationAliases' => [ 'shape' => 'ConfigurationAliasesResponse', ], 'DashConfiguration' => [ 'shape' => 'DashConfiguration', ], 'HlsConfiguration' => [ 'shape' => 'HlsConfiguration', ], 'LivePreRollConfiguration' => [ 'shape' => 'LivePreRollConfiguration', ], 'LogConfiguration' => [ 'shape' => 'LogConfiguration', ], 'ManifestProcessingRules' => [ 'shape' => 'ManifestProcessingRules', ], 'Name' => [ 'shape' => '__string', ], 'PersonalizationThresholdSeconds' => [ 'shape' => '__integerMin1', ], 'PlaybackConfigurationArn' => [ 'shape' => '__string', ], 'PlaybackEndpointPrefix' => [ 'shape' => '__string', ], 'SessionInitializationEndpointPrefix' => [ 'shape' => '__string', ], 'SlateAdUrl' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'TranscodeProfileName' => [ 'shape' => '__string', ], 'VideoContentSourceUrl' => [ 'shape' => '__string', ], ], ], 'GetPrefetchScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'PlaybackConfigurationName', ], 'members' => [ 'Name' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'Name', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'PlaybackConfigurationName', ], ], ], 'GetPrefetchScheduleResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Consumption' => [ 'shape' => 'PrefetchConsumption', ], 'Name' => [ 'shape' => '__string', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', ], 'Retrieval' => [ 'shape' => 'PrefetchRetrieval', ], 'StreamId' => [ 'shape' => '__string', ], ], ], 'HlsConfiguration' => [ 'type' => 'structure', 'members' => [ 'ManifestEndpointPrefix' => [ 'shape' => '__string', ], ], ], 'HlsPlaylistSettings' => [ 'type' => 'structure', 'members' => [ 'AdMarkupType' => [ 'shape' => 'adMarkupTypes', ], 'ManifestWindowSeconds' => [ 'shape' => '__integer', ], ], ], 'HttpConfiguration' => [ 'type' => 'structure', 'required' => [ 'BaseUrl', ], 'members' => [ 'BaseUrl' => [ 'shape' => '__string', ], ], ], 'HttpPackageConfiguration' => [ 'type' => 'structure', 'required' => [ 'Path', 'SourceGroup', 'Type', ], 'members' => [ 'Path' => [ 'shape' => '__string', ], 'SourceGroup' => [ 'shape' => '__string', ], 'Type' => [ 'shape' => 'Type', ], ], ], 'HttpPackageConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'HttpPackageConfiguration', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'KeyValuePair' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'ListAlertsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'ResourceArn' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'ListAlertsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfAlert', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'ListChannelsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfChannel', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'ListLiveSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationName', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'ListLiveSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfLiveSource', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'ListPlaybackConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListPlaybackConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfPlaybackConfiguration', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'ListPrefetchSchedulesRequest' => [ 'type' => 'structure', 'required' => [ 'PlaybackConfigurationName', ], 'members' => [ 'MaxResults' => [ 'shape' => '__integerMin1Max100', ], 'NextToken' => [ 'shape' => '__string', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'PlaybackConfigurationName', ], 'StreamId' => [ 'shape' => '__string', ], ], ], 'ListPrefetchSchedulesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfPrefetchSchedule', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'ListSourceLocationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSourceLocationsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfSourceLocation', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'ListVodSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationName', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'ListVodSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfVodSource', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'LivePreRollConfiguration' => [ 'type' => 'structure', 'members' => [ 'AdDecisionServerUrl' => [ 'shape' => '__string', ], 'MaxDurationSeconds' => [ 'shape' => '__integer', ], ], ], 'LiveSource' => [ 'type' => 'structure', 'required' => [ 'Arn', 'HttpPackageConfigurations', 'LiveSourceName', 'SourceLocationName', ], 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'LogConfiguration' => [ 'type' => 'structure', 'required' => [ 'PercentEnabled', ], 'members' => [ 'PercentEnabled' => [ 'shape' => '__integer', ], ], ], 'LogConfigurationForChannel' => [ 'type' => 'structure', 'members' => [ 'LogTypes' => [ 'shape' => 'LogTypes', ], ], ], 'LogType' => [ 'type' => 'string', 'enum' => [ 'AS_RUN', ], ], 'LogTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogType', ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'ManifestProcessingRules' => [ 'type' => 'structure', 'members' => [ 'AdMarkerPassthrough' => [ 'shape' => 'AdMarkerPassthrough', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MessageType' => [ 'type' => 'string', 'enum' => [ 'SPLICE_INSERT', 'TIME_SIGNAL', ], ], 'Mode' => [ 'type' => 'string', 'enum' => [ 'OFF', 'BEHIND_LIVE_EDGE', 'AFTER_LIVE_EDGE', ], ], 'Operator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'OriginManifestType' => [ 'type' => 'string', 'enum' => [ 'SINGLE_PERIOD', 'MULTI_PERIOD', ], ], 'PlaybackConfiguration' => [ 'type' => 'structure', 'members' => [ 'AdDecisionServerUrl' => [ 'shape' => '__string', ], 'AvailSuppression' => [ 'shape' => 'AvailSuppression', ], 'Bumper' => [ 'shape' => 'Bumper', ], 'CdnConfiguration' => [ 'shape' => 'CdnConfiguration', ], 'ConfigurationAliases' => [ 'shape' => 'ConfigurationAliasesResponse', ], 'DashConfiguration' => [ 'shape' => 'DashConfiguration', ], 'HlsConfiguration' => [ 'shape' => 'HlsConfiguration', ], 'LivePreRollConfiguration' => [ 'shape' => 'LivePreRollConfiguration', ], 'LogConfiguration' => [ 'shape' => 'LogConfiguration', ], 'ManifestProcessingRules' => [ 'shape' => 'ManifestProcessingRules', ], 'Name' => [ 'shape' => '__string', ], 'PersonalizationThresholdSeconds' => [ 'shape' => '__integerMin1', ], 'PlaybackConfigurationArn' => [ 'shape' => '__string', ], 'PlaybackEndpointPrefix' => [ 'shape' => '__string', ], 'SessionInitializationEndpointPrefix' => [ 'shape' => '__string', ], 'SlateAdUrl' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'TranscodeProfileName' => [ 'shape' => '__string', ], 'VideoContentSourceUrl' => [ 'shape' => '__string', ], ], ], 'PlaybackMode' => [ 'type' => 'string', 'enum' => [ 'LOOP', 'LINEAR', ], ], 'PrefetchConsumption' => [ 'type' => 'structure', 'required' => [ 'EndTime', ], 'members' => [ 'AvailMatchingCriteria' => [ 'shape' => '__listOfAvailMatchingCriteria', ], 'EndTime' => [ 'shape' => '__timestampUnix', ], 'StartTime' => [ 'shape' => '__timestampUnix', ], ], ], 'PrefetchRetrieval' => [ 'type' => 'structure', 'required' => [ 'EndTime', ], 'members' => [ 'DynamicVariables' => [ 'shape' => '__mapOf__string', ], 'EndTime' => [ 'shape' => '__timestampUnix', ], 'StartTime' => [ 'shape' => '__timestampUnix', ], ], ], 'PrefetchSchedule' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Consumption', 'Name', 'PlaybackConfigurationName', 'Retrieval', ], 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Consumption' => [ 'shape' => 'PrefetchConsumption', ], 'Name' => [ 'shape' => '__string', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', ], 'Retrieval' => [ 'shape' => 'PrefetchRetrieval', ], 'StreamId' => [ 'shape' => '__string', ], ], ], 'PutChannelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'Policy', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'Policy' => [ 'shape' => '__string', ], ], ], 'PutChannelPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutPlaybackConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'AdDecisionServerUrl' => [ 'shape' => '__string', ], 'AvailSuppression' => [ 'shape' => 'AvailSuppression', ], 'Bumper' => [ 'shape' => 'Bumper', ], 'CdnConfiguration' => [ 'shape' => 'CdnConfiguration', ], 'ConfigurationAliases' => [ 'shape' => 'ConfigurationAliasesRequest', ], 'DashConfiguration' => [ 'shape' => 'DashConfigurationForPut', ], 'LivePreRollConfiguration' => [ 'shape' => 'LivePreRollConfiguration', ], 'ManifestProcessingRules' => [ 'shape' => 'ManifestProcessingRules', ], 'Name' => [ 'shape' => '__string', ], 'PersonalizationThresholdSeconds' => [ 'shape' => '__integerMin1', ], 'SlateAdUrl' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'TranscodeProfileName' => [ 'shape' => '__string', ], 'VideoContentSourceUrl' => [ 'shape' => '__string', ], ], ], 'PutPlaybackConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'AdDecisionServerUrl' => [ 'shape' => '__string', ], 'AvailSuppression' => [ 'shape' => 'AvailSuppression', ], 'Bumper' => [ 'shape' => 'Bumper', ], 'CdnConfiguration' => [ 'shape' => 'CdnConfiguration', ], 'ConfigurationAliases' => [ 'shape' => 'ConfigurationAliasesResponse', ], 'DashConfiguration' => [ 'shape' => 'DashConfiguration', ], 'HlsConfiguration' => [ 'shape' => 'HlsConfiguration', ], 'LivePreRollConfiguration' => [ 'shape' => 'LivePreRollConfiguration', ], 'LogConfiguration' => [ 'shape' => 'LogConfiguration', ], 'ManifestProcessingRules' => [ 'shape' => 'ManifestProcessingRules', ], 'Name' => [ 'shape' => '__string', ], 'PersonalizationThresholdSeconds' => [ 'shape' => '__integerMin1', ], 'PlaybackConfigurationArn' => [ 'shape' => '__string', ], 'PlaybackEndpointPrefix' => [ 'shape' => '__string', ], 'SessionInitializationEndpointPrefix' => [ 'shape' => '__string', ], 'SlateAdUrl' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'TranscodeProfileName' => [ 'shape' => '__string', ], 'VideoContentSourceUrl' => [ 'shape' => '__string', ], ], ], 'RelativePosition' => [ 'type' => 'string', 'enum' => [ 'BEFORE_PROGRAM', 'AFTER_PROGRAM', ], ], 'RequestOutputItem' => [ 'type' => 'structure', 'required' => [ 'ManifestName', 'SourceGroup', ], 'members' => [ 'DashPlaylistSettings' => [ 'shape' => 'DashPlaylistSettings', ], 'HlsPlaylistSettings' => [ 'shape' => 'HlsPlaylistSettings', ], 'ManifestName' => [ 'shape' => '__string', ], 'SourceGroup' => [ 'shape' => '__string', ], ], ], 'RequestOutputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'RequestOutputItem', ], ], 'ResponseOutputItem' => [ 'type' => 'structure', 'required' => [ 'ManifestName', 'PlaybackUrl', 'SourceGroup', ], 'members' => [ 'DashPlaylistSettings' => [ 'shape' => 'DashPlaylistSettings', ], 'HlsPlaylistSettings' => [ 'shape' => 'HlsPlaylistSettings', ], 'ManifestName' => [ 'shape' => '__string', ], 'PlaybackUrl' => [ 'shape' => '__string', ], 'SourceGroup' => [ 'shape' => '__string', ], ], ], 'ResponseOutputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseOutputItem', ], ], 'ScheduleAdBreak' => [ 'type' => 'structure', 'members' => [ 'ApproximateDurationSeconds' => [ 'shape' => '__long', ], 'ApproximateStartTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'ScheduleConfiguration' => [ 'type' => 'structure', 'required' => [ 'Transition', ], 'members' => [ 'ClipRange' => [ 'shape' => 'ClipRange', ], 'Transition' => [ 'shape' => 'Transition', ], ], ], 'ScheduleEntry' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ChannelName', 'ProgramName', 'SourceLocationName', ], 'members' => [ 'ApproximateDurationSeconds' => [ 'shape' => '__long', ], 'ApproximateStartTime' => [ 'shape' => '__timestampUnix', ], 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'ProgramName' => [ 'shape' => '__string', ], 'ScheduleAdBreaks' => [ 'shape' => '__listOfScheduleAdBreak', ], 'ScheduleEntryType' => [ 'shape' => 'ScheduleEntryType', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'ScheduleEntryType' => [ 'type' => 'string', 'enum' => [ 'PROGRAM', 'FILLER_SLATE', ], ], 'SecretsManagerAccessTokenConfiguration' => [ 'type' => 'structure', 'members' => [ 'HeaderName' => [ 'shape' => '__string', ], 'SecretArn' => [ 'shape' => '__string', ], 'SecretStringKey' => [ 'shape' => '__string', ], ], ], 'SegmentDeliveryConfiguration' => [ 'type' => 'structure', 'members' => [ 'BaseUrl' => [ 'shape' => '__string', ], 'Name' => [ 'shape' => '__string', ], ], ], 'SegmentationDescriptor' => [ 'type' => 'structure', 'members' => [ 'SegmentNum' => [ 'shape' => 'Integer', ], 'SegmentationEventId' => [ 'shape' => 'Integer', ], 'SegmentationTypeId' => [ 'shape' => 'Integer', ], 'SegmentationUpid' => [ 'shape' => 'String', ], 'SegmentationUpidType' => [ 'shape' => 'Integer', ], 'SegmentsExpected' => [ 'shape' => 'Integer', ], 'SubSegmentNum' => [ 'shape' => 'Integer', ], 'SubSegmentsExpected' => [ 'shape' => 'Integer', ], ], ], 'SegmentationDescriptorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SegmentationDescriptor', ], ], 'SlateSource' => [ 'type' => 'structure', 'members' => [ 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'SourceLocation' => [ 'type' => 'structure', 'required' => [ 'Arn', 'HttpConfiguration', 'SourceLocationName', ], 'members' => [ 'AccessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'DefaultSegmentDeliveryConfiguration' => [ 'shape' => 'DefaultSegmentDeliveryConfiguration', ], 'HttpConfiguration' => [ 'shape' => 'HttpConfiguration', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SegmentDeliveryConfigurations' => [ 'shape' => '__listOfSegmentDeliveryConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'SpliceInsertMessage' => [ 'type' => 'structure', 'members' => [ 'AvailNum' => [ 'shape' => '__integer', ], 'AvailsExpected' => [ 'shape' => '__integer', ], 'SpliceEventId' => [ 'shape' => '__integer', ], 'UniqueProgramId' => [ 'shape' => '__integer', ], ], ], 'StartChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'StartChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'StopChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'Tier' => [ 'type' => 'string', 'enum' => [ 'BASIC', 'STANDARD', ], ], 'TimeSignalMessage' => [ 'type' => 'structure', 'members' => [ 'SegmentationDescriptors' => [ 'shape' => 'SegmentationDescriptorList', ], ], ], 'Transition' => [ 'type' => 'structure', 'required' => [ 'RelativePosition', 'Type', ], 'members' => [ 'DurationMillis' => [ 'shape' => '__long', ], 'RelativePosition' => [ 'shape' => 'RelativePosition', ], 'RelativeProgram' => [ 'shape' => '__string', ], 'ScheduledStartTimeMillis' => [ 'shape' => '__long', ], 'Type' => [ 'shape' => '__string', ], ], ], 'Type' => [ 'type' => 'string', 'enum' => [ 'DASH', 'HLS', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => '__listOf__string', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'Outputs', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'FillerSlate' => [ 'shape' => 'SlateSource', ], 'Outputs' => [ 'shape' => 'RequestOutputs', ], ], ], 'UpdateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'ChannelState' => [ 'shape' => 'ChannelState', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'FillerSlate' => [ 'shape' => 'SlateSource', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'Outputs' => [ 'shape' => 'ResponseOutputs', ], 'PlaybackMode' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'Tier' => [ 'shape' => '__string', ], ], ], 'UpdateLiveSourceRequest' => [ 'type' => 'structure', 'required' => [ 'HttpPackageConfigurations', 'LiveSourceName', 'SourceLocationName', ], 'members' => [ 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LiveSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'LiveSourceName', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'UpdateLiveSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'UpdateProgramRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'ProgramName', 'ScheduleConfiguration', ], 'members' => [ 'AdBreaks' => [ 'shape' => '__listOfAdBreak', ], 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'ProgramName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ProgramName', ], 'ScheduleConfiguration' => [ 'shape' => 'UpdateProgramScheduleConfiguration', ], ], ], 'UpdateProgramResponse' => [ 'type' => 'structure', 'members' => [ 'AdBreaks' => [ 'shape' => '__listOfAdBreak', ], 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'ClipRange' => [ 'shape' => 'ClipRange', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'DurationMillis' => [ 'shape' => '__long', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'ProgramName' => [ 'shape' => '__string', ], 'ScheduledStartTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'UpdateProgramScheduleConfiguration' => [ 'type' => 'structure', 'members' => [ 'ClipRange' => [ 'shape' => 'ClipRange', ], 'Transition' => [ 'shape' => 'UpdateProgramTransition', ], ], ], 'UpdateProgramTransition' => [ 'type' => 'structure', 'members' => [ 'DurationMillis' => [ 'shape' => '__long', ], 'ScheduledStartTimeMillis' => [ 'shape' => '__long', ], ], ], 'UpdateSourceLocationRequest' => [ 'type' => 'structure', 'required' => [ 'HttpConfiguration', 'SourceLocationName', ], 'members' => [ 'AccessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'DefaultSegmentDeliveryConfiguration' => [ 'shape' => 'DefaultSegmentDeliveryConfiguration', ], 'HttpConfiguration' => [ 'shape' => 'HttpConfiguration', ], 'SegmentDeliveryConfigurations' => [ 'shape' => '__listOfSegmentDeliveryConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'UpdateSourceLocationResponse' => [ 'type' => 'structure', 'members' => [ 'AccessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'DefaultSegmentDeliveryConfiguration' => [ 'shape' => 'DefaultSegmentDeliveryConfiguration', ], 'HttpConfiguration' => [ 'shape' => 'HttpConfiguration', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SegmentDeliveryConfigurations' => [ 'shape' => '__listOfSegmentDeliveryConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'UpdateVodSourceRequest' => [ 'type' => 'structure', 'required' => [ 'HttpPackageConfigurations', 'SourceLocationName', 'VodSourceName', ], 'members' => [ 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], 'VodSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'VodSourceName', ], ], ], 'UpdateVodSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'VodSource' => [ 'type' => 'structure', 'required' => [ 'Arn', 'HttpPackageConfigurations', 'SourceLocationName', 'VodSourceName', ], 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], '__boolean' => [ 'type' => 'boolean', 'box' => true, ], '__integer' => [ 'type' => 'integer', 'box' => true, ], '__integerMin1' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], '__integerMin1Max100' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], '__listOfAdBreak' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdBreak', ], ], '__listOfAlert' => [ 'type' => 'list', 'member' => [ 'shape' => 'Alert', ], ], '__listOfAvailMatchingCriteria' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailMatchingCriteria', ], ], '__listOfChannel' => [ 'type' => 'list', 'member' => [ 'shape' => 'Channel', ], ], '__listOfLiveSource' => [ 'type' => 'list', 'member' => [ 'shape' => 'LiveSource', ], ], '__listOfPlaybackConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlaybackConfiguration', ], ], '__listOfPrefetchSchedule' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrefetchSchedule', ], ], '__listOfScheduleAdBreak' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduleAdBreak', ], ], '__listOfScheduleEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduleEntry', ], ], '__listOfSegmentDeliveryConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'SegmentDeliveryConfiguration', ], ], '__listOfSourceLocation' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceLocation', ], ], '__listOfVodSource' => [ 'type' => 'list', 'member' => [ 'shape' => 'VodSource', ], ], '__listOf__string' => [ 'type' => 'list', 'member' => [ 'shape' => '__string', ], ], '__long' => [ 'type' => 'long', 'box' => true, ], '__mapOf__string' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__string', ], ], '__string' => [ 'type' => 'string', ], '__timestampUnix' => [ 'type' => 'timestamp', 'timestampFormat' => 'unixTimestamp', ], 'adMarkupTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdMarkupType', ], ], ],];
