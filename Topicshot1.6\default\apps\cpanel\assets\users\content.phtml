<div class="cp-app-container" data-app="users">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Users
            </h1>
        </div>
    </div>

    <div class="d-block" id="vue-cpanel-users-app">    
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <form class="form" v-on:submit="filter_table">
                    <div class="card">
                        <div class="header">
                            <h2>
                                Filter users
                            </h2>
                        </div>
                        <div class="body no-pb">
                            <div class="row">
                                <div class="col-xxl-3">
                                    <div class="form-group">
                                        <label>Status</label>
                                        <div class="form-select">
                                            <select v-on:change="sorting.status = $event.target.value" name="status" class="form-control">
                                                <option value="all">
                                                    All
                                                </option>
                                                <option value="active">
                                                    Active
                                                </option>
                                                <option value="blocked">
                                                    Blocked
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xxl-3">
                                    <div class="form-group">
                                        <label>Account type</label>
                                        <div class="form-select">
                                            <select v-on:change="sorting.type = $event.target.value" name="type" class="form-control">
                                                <option value="all">
                                                    All
                                                </option>
                                                <option value="admin">
                                                    Admin
                                                </option>
                                                <option value="user">
                                                    User
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xxl-3">
                                    <div class="form-group">
                                        <label>User name</label>
                                        <input v-on:input="sorting.username = $event.target.value" class="form-control" type="text" name="username" placeholder="Search by user name">
                                    </div>
                                </div>
                                <div class="col-xxl-3">
                                    <div class="form-group">
                                        <label>User e-mail</label>
                                        <input v-on:input="sorting.email = $event.target.value" class="form-control" type="email" name="email" placeholder="Search by user E-mail">
                                    </div>
                                </div>
                                
                                <div class="col-xxl-3">
                                    <div class="form-group">
                                        <label>User IP address</label>
                                        <input v-on:input="sorting.ip = $event.target.value" class="form-control" type="text" name="ip" placeholder="Search by user IP">
                                    </div>
                                </div>

                                <div class="col-xxl-3">
                                    <div class="form-group">
                                        <label>Country</label>
                                        <div class="form-select">
                                            <select v-on:change="sorting.country = $event.target.value" name="country" class="form-control">
                                                <option value="all">
                                                    All
                                                </option>
                                                <?php foreach ($cl['countries'] as $cid => $cname): ?>
                                                    <option value="<?php echo($cid); ?>">
                                                        <?php echo($cname); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="footer footer__btns">
                            <button type="submit" class="btn btn-primary">
                                Aplly filter
                            </button>
                            
                            <button type="reset" v-bind:disabled="allow2reset != true" v-on:click="reset_form" class="btn btn-secondary">
                                Reset filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="card" data-an="info-content">
                    <div class="header">
                        <h2>
                            Manage site users / {{total_users}}
                        </h2>
                    </div>
                    <div class="body">
                        <div class="regular-table">
                            <table class="table table-hover no-mb">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>
                                            Username
                                        </th>
                                        <th>
                                            E-mail address
                                        </th>
                                        <th>
                                            Phone
                                        </th>
                                        <th>
                                            Country
                                        </th>
                                        <th>IP</th>
                                        <th>
                                            Type
                                        </th>
                                        <th>
                                            Verified
                                        </th>
                                        <th>
                                            Status
                                        </th>
                                        <th>
                                            Last seen
                                        </th>
                                        <th>
                                            Action
                                        </th>
                                    </tr>
                                </thead>
                                <tbody data-an="users-list">
                                    <?php if (not_empty($cl['site_users'])): ?>
                                        <?php foreach ($cl['site_users'] as $cl['li']): ?>
                                            <?php echo cl_template('cpanel/assets/users/includes/list_item'); ?>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <?php echo cl_template('cpanel/assets/users/includes/empty_table'); ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                            
                            <div class="table-pagination">
                                <a v-if="show_ctrls" v-bind:class="{'disabled': dis_prev_ctrl}" v-on:click="paginate('up')" href="javascript:void(0);" class="pagination-ctrls prev">
                                    <?php echo cl_ficon("arrow_left"); ?>
                                </a>

                                <a v-if="show_ctrls" v-bind:class="{'disabled': dis_next_ctrl}" v-on:click="paginate('down')" href="javascript:void(0);" class="pagination-ctrls next">
                                    <?php echo cl_ficon("arrow_right"); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/users/scripts/app_master_script'); ?>