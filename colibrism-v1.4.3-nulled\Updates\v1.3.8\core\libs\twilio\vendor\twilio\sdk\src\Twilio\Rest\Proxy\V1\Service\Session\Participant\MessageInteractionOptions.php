<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Proxy
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Proxy\V1\Service\Session\Participant;

use Twilio\Options;
use Twilio\Values;

abstract class MessageInteractionOptions
{
    /**
     * @param string $body The message to send to the participant
     * @param string[] $mediaUrl Reserved. Not currently supported.
     * @return CreateMessageInteractionOptions Options builder
     */
    public static function create(
        
        string $body = Values::NONE,
        array $mediaUrl = Values::ARRAY_NONE

    ): CreateMessageInteractionOptions
    {
        return new CreateMessageInteractionOptions(
            $body,
            $mediaUrl
        );
    }



}

class CreateMessageInteractionOptions extends Options
    {
    /**
     * @param string $body The message to send to the participant
     * @param string[] $mediaUrl Reserved. Not currently supported.
     */
    public function __construct(
        
        string $body = Values::NONE,
        array $mediaUrl = Values::ARRAY_NONE

    ) {
        $this->options['body'] = $body;
        $this->options['mediaUrl'] = $mediaUrl;
    }

    /**
     * The message to send to the participant
     *
     * @param string $body The message to send to the participant
     * @return $this Fluent Builder
     */
    public function setBody(string $body): self
    {
        $this->options['body'] = $body;
        return $this;
    }

    /**
     * Reserved. Not currently supported.
     *
     * @param string[] $mediaUrl Reserved. Not currently supported.
     * @return $this Fluent Builder
     */
    public function setMediaUrl(array $mediaUrl): self
    {
        $this->options['mediaUrl'] = $mediaUrl;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Proxy.V1.CreateMessageInteractionOptions ' . $options . ']';
    }
}



