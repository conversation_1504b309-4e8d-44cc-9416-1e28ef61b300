<?php
// This file was auto-generated from sdk-root/src/data/kinesisvideo/2017-09-30/paginators-1.json
return [ 'pagination' => [ 'DescribeMappedResourceConfiguration' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'MappedResourceConfigurationList', ], 'ListEdgeAgentConfigurations' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'EdgeConfigs', ], 'ListSignalingChannels' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ChannelInfoList', ], 'ListStreams' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'StreamInfoList', ], ],];
