<?php
// This file was auto-generated from sdk-root/src/data/finspace-data/2020-07-13/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-07-13', 'endpointPrefix' => 'finspace-api', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'FinSpace Data', 'serviceFullName' => 'FinSpace Public API', 'serviceId' => 'finspace data', 'signatureVersion' => 'v4', 'signingName' => 'finspace-api', 'uid' => 'finspace-2020-07-13', ], 'operations' => [ 'AssociateUserToPermissionGroup' => [ 'name' => 'AssociateUserToPermissionGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/permission-group/{permissionGroupId}/users/{userId}', ], 'input' => [ 'shape' => 'AssociateUserToPermissionGroupRequest', ], 'output' => [ 'shape' => 'AssociateUserToPermissionGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'CreateChangeset' => [ 'name' => 'CreateChangeset', 'http' => [ 'method' => 'POST', 'requestUri' => '/datasets/{datasetId}/changesetsv2', ], 'input' => [ 'shape' => 'CreateChangesetRequest', ], 'output' => [ 'shape' => 'CreateChangesetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'CreateDataView' => [ 'name' => 'CreateDataView', 'http' => [ 'method' => 'POST', 'requestUri' => '/datasets/{datasetId}/dataviewsv2', ], 'input' => [ 'shape' => 'CreateDataViewRequest', ], 'output' => [ 'shape' => 'CreateDataViewResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'CreateDataset' => [ 'name' => 'CreateDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/datasetsv2', ], 'input' => [ 'shape' => 'CreateDatasetRequest', ], 'output' => [ 'shape' => 'CreateDatasetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'CreatePermissionGroup' => [ 'name' => 'CreatePermissionGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/permission-group', ], 'input' => [ 'shape' => 'CreatePermissionGroupRequest', ], 'output' => [ 'shape' => 'CreatePermissionGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/user', ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'DeleteDataset' => [ 'name' => 'DeleteDataset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/datasetsv2/{datasetId}', ], 'input' => [ 'shape' => 'DeleteDatasetRequest', ], 'output' => [ 'shape' => 'DeleteDatasetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'DeletePermissionGroup' => [ 'name' => 'DeletePermissionGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/permission-group/{permissionGroupId}', ], 'input' => [ 'shape' => 'DeletePermissionGroupRequest', ], 'output' => [ 'shape' => 'DeletePermissionGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'DisableUser' => [ 'name' => 'DisableUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/user/{userId}/disable', ], 'input' => [ 'shape' => 'DisableUserRequest', ], 'output' => [ 'shape' => 'DisableUserResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'DisassociateUserFromPermissionGroup' => [ 'name' => 'DisassociateUserFromPermissionGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/permission-group/{permissionGroupId}/users/{userId}', ], 'input' => [ 'shape' => 'DisassociateUserFromPermissionGroupRequest', ], 'output' => [ 'shape' => 'DisassociateUserFromPermissionGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'EnableUser' => [ 'name' => 'EnableUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/user/{userId}/enable', ], 'input' => [ 'shape' => 'EnableUserRequest', ], 'output' => [ 'shape' => 'EnableUserResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'GetChangeset' => [ 'name' => 'GetChangeset', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasets/{datasetId}/changesetsv2/{changesetId}', ], 'input' => [ 'shape' => 'GetChangesetRequest', ], 'output' => [ 'shape' => 'GetChangesetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'GetDataView' => [ 'name' => 'GetDataView', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasets/{datasetId}/dataviewsv2/{dataviewId}', ], 'input' => [ 'shape' => 'GetDataViewRequest', ], 'output' => [ 'shape' => 'GetDataViewResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'GetDataset' => [ 'name' => 'GetDataset', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasetsv2/{datasetId}', ], 'input' => [ 'shape' => 'GetDatasetRequest', ], 'output' => [ 'shape' => 'GetDatasetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'GetExternalDataViewAccessDetails' => [ 'name' => 'GetExternalDataViewAccessDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/datasets/{datasetId}/dataviewsv2/{dataviewId}/external-access-details', ], 'input' => [ 'shape' => 'GetExternalDataViewAccessDetailsRequest', ], 'output' => [ 'shape' => 'GetExternalDataViewAccessDetailsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'GetPermissionGroup' => [ 'name' => 'GetPermissionGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/permission-group/{permissionGroupId}', ], 'input' => [ 'shape' => 'GetPermissionGroupRequest', ], 'output' => [ 'shape' => 'GetPermissionGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'GetProgrammaticAccessCredentials' => [ 'name' => 'GetProgrammaticAccessCredentials', 'http' => [ 'method' => 'GET', 'requestUri' => '/credentials/programmatic', ], 'input' => [ 'shape' => 'GetProgrammaticAccessCredentialsRequest', ], 'output' => [ 'shape' => 'GetProgrammaticAccessCredentialsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'GetUser' => [ 'name' => 'GetUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/user/{userId}', ], 'input' => [ 'shape' => 'GetUserRequest', ], 'output' => [ 'shape' => 'GetUserResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'GetWorkingLocation' => [ 'name' => 'GetWorkingLocation', 'http' => [ 'method' => 'POST', 'requestUri' => '/workingLocationV1', ], 'input' => [ 'shape' => 'GetWorkingLocationRequest', ], 'output' => [ 'shape' => 'GetWorkingLocationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'ListChangesets' => [ 'name' => 'ListChangesets', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasets/{datasetId}/changesetsv2', ], 'input' => [ 'shape' => 'ListChangesetsRequest', ], 'output' => [ 'shape' => 'ListChangesetsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'ListDataViews' => [ 'name' => 'ListDataViews', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasets/{datasetId}/dataviewsv2', ], 'input' => [ 'shape' => 'ListDataViewsRequest', ], 'output' => [ 'shape' => 'ListDataViewsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'ListDatasets' => [ 'name' => 'ListDatasets', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasetsv2', ], 'input' => [ 'shape' => 'ListDatasetsRequest', ], 'output' => [ 'shape' => 'ListDatasetsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'ListPermissionGroups' => [ 'name' => 'ListPermissionGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/permission-group', ], 'input' => [ 'shape' => 'ListPermissionGroupsRequest', ], 'output' => [ 'shape' => 'ListPermissionGroupsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'ListPermissionGroupsByUser' => [ 'name' => 'ListPermissionGroupsByUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/user/{userId}/permission-groups', ], 'input' => [ 'shape' => 'ListPermissionGroupsByUserRequest', ], 'output' => [ 'shape' => 'ListPermissionGroupsByUserResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'ListUsers' => [ 'name' => 'ListUsers', 'http' => [ 'method' => 'GET', 'requestUri' => '/user', ], 'input' => [ 'shape' => 'ListUsersRequest', ], 'output' => [ 'shape' => 'ListUsersResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'ListUsersByPermissionGroup' => [ 'name' => 'ListUsersByPermissionGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/permission-group/{permissionGroupId}/users', ], 'input' => [ 'shape' => 'ListUsersByPermissionGroupRequest', ], 'output' => [ 'shape' => 'ListUsersByPermissionGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'ResetUserPassword' => [ 'name' => 'ResetUserPassword', 'http' => [ 'method' => 'POST', 'requestUri' => '/user/{userId}/password', ], 'input' => [ 'shape' => 'ResetUserPasswordRequest', ], 'output' => [ 'shape' => 'ResetUserPasswordResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'UpdateChangeset' => [ 'name' => 'UpdateChangeset', 'http' => [ 'method' => 'PUT', 'requestUri' => '/datasets/{datasetId}/changesetsv2/{changesetId}', ], 'input' => [ 'shape' => 'UpdateChangesetRequest', ], 'output' => [ 'shape' => 'UpdateChangesetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'UpdateDataset' => [ 'name' => 'UpdateDataset', 'http' => [ 'method' => 'PUT', 'requestUri' => '/datasetsv2/{datasetId}', ], 'input' => [ 'shape' => 'UpdateDatasetRequest', ], 'output' => [ 'shape' => 'UpdateDatasetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'UpdatePermissionGroup' => [ 'name' => 'UpdatePermissionGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/permission-group/{permissionGroupId}', ], 'input' => [ 'shape' => 'UpdatePermissionGroupRequest', ], 'output' => [ 'shape' => 'UpdatePermissionGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'UpdateUser' => [ 'name' => 'UpdateUser', 'http' => [ 'method' => 'PUT', 'requestUri' => '/user/{userId}', ], 'input' => [ 'shape' => 'UpdateUserRequest', ], 'output' => [ 'shape' => 'UpdateUserResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccessKeyId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'AliasString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^alias\\/\\S+', ], 'ApiAccess' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ApplicationPermission' => [ 'type' => 'string', 'enum' => [ 'CreateDataset', 'ManageClusters', 'ManageUsersAndGroups', 'ManageAttributeSets', 'ViewAuditData', 'AccessNotebooks', 'GetTemporaryCredentials', ], ], 'ApplicationPermissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationPermission', ], ], 'AssociateUserToPermissionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'permissionGroupId', 'userId', ], 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', 'location' => 'uri', 'locationName' => 'permissionGroupId', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'userId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'AssociateUserToPermissionGroupResponse' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'AwsCredentials' => [ 'type' => 'structure', 'members' => [ 'accessKeyId' => [ 'shape' => 'AccessKeyId', ], 'secretAccessKey' => [ 'shape' => 'SecretAccessKey', ], 'sessionToken' => [ 'shape' => 'SessionToken', ], 'expiration' => [ 'shape' => 'TimestampEpoch', ], ], 'sensitive' => true, ], 'Boolean' => [ 'type' => 'boolean', ], 'ChangeType' => [ 'type' => 'string', 'enum' => [ 'REPLACE', 'APPEND', 'MODIFY', ], ], 'ChangesetArn' => [ 'type' => 'string', ], 'ChangesetErrorInfo' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'errorCategory' => [ 'shape' => 'ErrorCategory', ], ], ], 'ChangesetId' => [ 'type' => 'string', 'max' => 26, 'min' => 1, ], 'ChangesetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangesetSummary', ], ], 'ChangesetSummary' => [ 'type' => 'structure', 'members' => [ 'changesetId' => [ 'shape' => 'ChangesetId', ], 'changesetArn' => [ 'shape' => 'ChangesetArn', ], 'datasetId' => [ 'shape' => 'DatasetId', ], 'changeType' => [ 'shape' => 'ChangeType', ], 'sourceParams' => [ 'shape' => 'SourceParams', ], 'formatParams' => [ 'shape' => 'FormatParams', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'status' => [ 'shape' => 'IngestionStatus', ], 'errorInfo' => [ 'shape' => 'ChangesetErrorInfo', ], 'activeUntilTimestamp' => [ 'shape' => 'TimestampEpoch', 'box' => true, ], 'activeFromTimestamp' => [ 'shape' => 'TimestampEpoch', 'box' => true, ], 'updatesChangesetId' => [ 'shape' => 'ChangesetId', ], 'updatedByChangesetId' => [ 'shape' => 'ChangesetId', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ColumnDataType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'CHAR', 'INTEGER', 'TINYINT', 'SMALLINT', 'BIGINT', 'FLOAT', 'DOUBLE', 'DATE', 'DATETIME', 'BOOLEAN', 'BINARY', ], ], 'ColumnDefinition' => [ 'type' => 'structure', 'members' => [ 'dataType' => [ 'shape' => 'ColumnDataType', ], 'columnName' => [ 'shape' => 'ColumnName', ], 'columnDescription' => [ 'shape' => 'ColumnDescription', ], ], ], 'ColumnDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]*', ], 'ColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnDefinition', ], ], 'ColumnName' => [ 'type' => 'string', 'max' => 126, 'pattern' => '.*\\S.*', ], 'ColumnNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnName', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], 'reason' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CreateChangesetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'changeType', 'sourceParams', 'formatParams', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'changeType' => [ 'shape' => 'ChangeType', ], 'sourceParams' => [ 'shape' => 'SourceParams', ], 'formatParams' => [ 'shape' => 'FormatParams', ], ], ], 'CreateChangesetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], 'changesetId' => [ 'shape' => 'ChangesetId', ], ], ], 'CreateDataViewRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'destinationTypeParams', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'autoUpdate' => [ 'shape' => 'Boolean', ], 'sortColumns' => [ 'shape' => 'SortColumnList', ], 'partitionColumns' => [ 'shape' => 'PartitionColumnList', ], 'asOfTimestamp' => [ 'shape' => 'TimestampEpoch', 'box' => true, ], 'destinationTypeParams' => [ 'shape' => 'DataViewDestinationTypeParams', ], ], ], 'CreateDataViewResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], 'dataViewId' => [ 'shape' => 'DataViewId', ], ], ], 'CreateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetTitle', 'kind', 'permissionGroupParams', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'datasetTitle' => [ 'shape' => 'DatasetTitle', ], 'kind' => [ 'shape' => 'DatasetKind', ], 'datasetDescription' => [ 'shape' => 'DatasetDescription', ], 'ownerInfo' => [ 'shape' => 'DatasetOwnerInfo', ], 'permissionGroupParams' => [ 'shape' => 'PermissionGroupParams', ], 'alias' => [ 'shape' => 'AliasString', ], 'schemaDefinition' => [ 'shape' => 'SchemaUnion', ], ], ], 'CreateDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], ], ], 'CreatePermissionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'applicationPermissions', ], 'members' => [ 'name' => [ 'shape' => 'PermissionGroupName', ], 'description' => [ 'shape' => 'PermissionGroupDescription', ], 'applicationPermissions' => [ 'shape' => 'ApplicationPermissionList', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreatePermissionGroupResponse' => [ 'type' => 'structure', 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'emailAddress', 'type', ], 'members' => [ 'emailAddress' => [ 'shape' => 'Email', ], 'type' => [ 'shape' => 'UserType', ], 'firstName' => [ 'shape' => 'FirstName', ], 'lastName' => [ 'shape' => 'LastName', ], 'apiAccess' => [ 'shape' => 'ApiAccess', ], 'apiAccessPrincipalArn' => [ 'shape' => 'RoleArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateUserResponse' => [ 'type' => 'structure', 'members' => [ 'userId' => [ 'shape' => 'UserId', ], ], ], 'Credentials' => [ 'type' => 'structure', 'members' => [ 'accessKeyId' => [ 'shape' => 'stringValueLength1to255', ], 'secretAccessKey' => [ 'shape' => 'stringValueMaxLength1000', ], 'sessionToken' => [ 'shape' => 'stringValueMaxLength1000', ], ], 'sensitive' => true, ], 'DataViewArn' => [ 'type' => 'string', ], 'DataViewDestinationType' => [ 'type' => 'string', ], 'DataViewDestinationTypeParams' => [ 'type' => 'structure', 'required' => [ 'destinationType', ], 'members' => [ 'destinationType' => [ 'shape' => 'DataViewDestinationType', ], 's3DestinationExportFileFormat' => [ 'shape' => 'ExportFileFormat', ], 's3DestinationExportFileFormatOptions' => [ 'shape' => 'S3DestinationFormatOptions', ], ], ], 'DataViewErrorInfo' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'errorCategory' => [ 'shape' => 'ErrorCategory', ], ], ], 'DataViewId' => [ 'type' => 'string', 'max' => 26, 'min' => 1, ], 'DataViewList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataViewSummary', ], ], 'DataViewStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'STARTING', 'FAILED', 'CANCELLED', 'TIMEOUT', 'SUCCESS', 'PENDING', 'FAILED_CLEANUP_FAILED', ], ], 'DataViewSummary' => [ 'type' => 'structure', 'members' => [ 'dataViewId' => [ 'shape' => 'DataViewId', ], 'dataViewArn' => [ 'shape' => 'DataViewArn', ], 'datasetId' => [ 'shape' => 'DatasetId', ], 'asOfTimestamp' => [ 'shape' => 'TimestampEpoch', 'box' => true, ], 'partitionColumns' => [ 'shape' => 'PartitionColumnList', ], 'sortColumns' => [ 'shape' => 'SortColumnList', ], 'status' => [ 'shape' => 'DataViewStatus', ], 'errorInfo' => [ 'shape' => 'DataViewErrorInfo', ], 'destinationTypeProperties' => [ 'shape' => 'DataViewDestinationTypeParams', ], 'autoUpdate' => [ 'shape' => 'Boolean', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'lastModifiedTime' => [ 'shape' => 'TimestampEpoch', ], ], ], 'Dataset' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], 'datasetArn' => [ 'shape' => 'DatasetArn', ], 'datasetTitle' => [ 'shape' => 'DatasetTitle', ], 'kind' => [ 'shape' => 'DatasetKind', ], 'datasetDescription' => [ 'shape' => 'DatasetDescription', ], 'ownerInfo' => [ 'shape' => 'DatasetOwnerInfo', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'lastModifiedTime' => [ 'shape' => 'TimestampEpoch', ], 'schemaDefinition' => [ 'shape' => 'SchemaUnion', ], 'alias' => [ 'shape' => 'AliasString', ], ], ], 'DatasetArn' => [ 'type' => 'string', ], 'DatasetDescription' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '[\\s\\S]*', ], 'DatasetId' => [ 'type' => 'string', 'max' => 26, 'min' => 1, ], 'DatasetKind' => [ 'type' => 'string', 'enum' => [ 'TABULAR', 'NON_TABULAR', ], ], 'DatasetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dataset', ], ], 'DatasetOwnerInfo' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'OwnerName', ], 'phoneNumber' => [ 'shape' => 'PhoneNumber', ], 'email' => [ 'shape' => 'Email', ], ], ], 'DatasetStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'FAILED', 'SUCCESS', 'RUNNING', ], ], 'DatasetTitle' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*\\S.*', ], 'DeleteDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], ], ], 'DeleteDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], ], ], 'DeletePermissionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'permissionGroupId', ], 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', 'location' => 'uri', 'locationName' => 'permissionGroupId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeletePermissionGroupResponse' => [ 'type' => 'structure', 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', ], ], ], 'DisableUserRequest' => [ 'type' => 'structure', 'required' => [ 'userId', ], 'members' => [ 'userId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'userId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'DisableUserResponse' => [ 'type' => 'structure', 'members' => [ 'userId' => [ 'shape' => 'UserId', ], ], ], 'DisassociateUserFromPermissionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'permissionGroupId', 'userId', ], 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', 'location' => 'uri', 'locationName' => 'permissionGroupId', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'userId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DisassociateUserFromPermissionGroupResponse' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'Email' => [ 'type' => 'string', 'max' => 320, 'min' => 4, 'pattern' => '[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}', 'sensitive' => true, ], 'EnableUserRequest' => [ 'type' => 'structure', 'required' => [ 'userId', ], 'members' => [ 'userId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'userId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'EnableUserResponse' => [ 'type' => 'structure', 'members' => [ 'userId' => [ 'shape' => 'UserId', ], ], ], 'ErrorCategory' => [ 'type' => 'string', 'enum' => [ 'VALIDATION', 'SERVICE_QUOTA_EXCEEDED', 'ACCESS_DENIED', 'RESOURCE_NOT_FOUND', 'THROTTLING', 'INTERNAL_SERVICE_EXCEPTION', 'CANCELLED', 'USER_RECOVERABLE', ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1000, ], 'ExportFileFormat' => [ 'type' => 'string', 'enum' => [ 'PARQUET', 'DELIMITED_TEXT', ], ], 'FirstName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '.*\\S.*', 'sensitive' => true, ], 'FormatParams' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringMapKey', ], 'value' => [ 'shape' => 'StringMapValue', ], ], 'GetChangesetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'changesetId', ], 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'changesetId' => [ 'shape' => 'ChangesetId', 'location' => 'uri', 'locationName' => 'changesetId', ], ], ], 'GetChangesetResponse' => [ 'type' => 'structure', 'members' => [ 'changesetId' => [ 'shape' => 'ChangesetId', ], 'changesetArn' => [ 'shape' => 'ChangesetArn', ], 'datasetId' => [ 'shape' => 'DatasetId', ], 'changeType' => [ 'shape' => 'ChangeType', ], 'sourceParams' => [ 'shape' => 'SourceParams', ], 'formatParams' => [ 'shape' => 'FormatParams', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'status' => [ 'shape' => 'IngestionStatus', ], 'errorInfo' => [ 'shape' => 'ChangesetErrorInfo', ], 'activeUntilTimestamp' => [ 'shape' => 'TimestampEpoch', 'box' => true, ], 'activeFromTimestamp' => [ 'shape' => 'TimestampEpoch', 'box' => true, ], 'updatesChangesetId' => [ 'shape' => 'ChangesetId', ], 'updatedByChangesetId' => [ 'shape' => 'ChangesetId', ], ], ], 'GetDataViewRequest' => [ 'type' => 'structure', 'required' => [ 'dataViewId', 'datasetId', ], 'members' => [ 'dataViewId' => [ 'shape' => 'DataViewId', 'location' => 'uri', 'locationName' => 'dataviewId', ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], ], ], 'GetDataViewResponse' => [ 'type' => 'structure', 'members' => [ 'autoUpdate' => [ 'shape' => 'Boolean', ], 'partitionColumns' => [ 'shape' => 'PartitionColumnList', ], 'datasetId' => [ 'shape' => 'DatasetId', ], 'asOfTimestamp' => [ 'shape' => 'TimestampEpoch', 'box' => true, ], 'errorInfo' => [ 'shape' => 'DataViewErrorInfo', ], 'lastModifiedTime' => [ 'shape' => 'TimestampEpoch', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'sortColumns' => [ 'shape' => 'SortColumnList', ], 'dataViewId' => [ 'shape' => 'DataViewId', ], 'dataViewArn' => [ 'shape' => 'DataViewArn', ], 'destinationTypeParams' => [ 'shape' => 'DataViewDestinationTypeParams', ], 'status' => [ 'shape' => 'DataViewStatus', ], ], ], 'GetDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', ], 'members' => [ 'datasetId' => [ 'shape' => 'StringValueLength1to255', 'location' => 'uri', 'locationName' => 'datasetId', ], ], ], 'GetDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], 'datasetArn' => [ 'shape' => 'DatasetArn', ], 'datasetTitle' => [ 'shape' => 'DatasetTitle', ], 'kind' => [ 'shape' => 'DatasetKind', ], 'datasetDescription' => [ 'shape' => 'DatasetDescription', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'lastModifiedTime' => [ 'shape' => 'TimestampEpoch', ], 'schemaDefinition' => [ 'shape' => 'SchemaUnion', ], 'alias' => [ 'shape' => 'AliasString', ], 'status' => [ 'shape' => 'DatasetStatus', ], ], ], 'GetExternalDataViewAccessDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'dataViewId', 'datasetId', ], 'members' => [ 'dataViewId' => [ 'shape' => 'DataViewId', 'location' => 'uri', 'locationName' => 'dataviewId', ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], ], ], 'GetExternalDataViewAccessDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'credentials' => [ 'shape' => 'AwsCredentials', ], 's3Location' => [ 'shape' => 'S3Location', ], ], ], 'GetPermissionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'permissionGroupId', ], 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', 'location' => 'uri', 'locationName' => 'permissionGroupId', ], ], ], 'GetPermissionGroupResponse' => [ 'type' => 'structure', 'members' => [ 'permissionGroup' => [ 'shape' => 'PermissionGroup', ], ], ], 'GetProgrammaticAccessCredentialsRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'durationInMinutes' => [ 'shape' => 'SessionDuration', 'location' => 'querystring', 'locationName' => 'durationInMinutes', ], 'environmentId' => [ 'shape' => 'IdType', 'location' => 'querystring', 'locationName' => 'environmentId', ], ], ], 'GetProgrammaticAccessCredentialsResponse' => [ 'type' => 'structure', 'members' => [ 'credentials' => [ 'shape' => 'Credentials', ], 'durationInMinutes' => [ 'shape' => 'SessionDuration', ], ], ], 'GetUserRequest' => [ 'type' => 'structure', 'required' => [ 'userId', ], 'members' => [ 'userId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'GetUserResponse' => [ 'type' => 'structure', 'members' => [ 'userId' => [ 'shape' => 'UserId', ], 'status' => [ 'shape' => 'UserStatus', ], 'firstName' => [ 'shape' => 'FirstName', ], 'lastName' => [ 'shape' => 'LastName', ], 'emailAddress' => [ 'shape' => 'Email', ], 'type' => [ 'shape' => 'UserType', ], 'apiAccess' => [ 'shape' => 'ApiAccess', ], 'apiAccessPrincipalArn' => [ 'shape' => 'RoleArn', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'lastEnabledTime' => [ 'shape' => 'TimestampEpoch', ], 'lastDisabledTime' => [ 'shape' => 'TimestampEpoch', ], 'lastModifiedTime' => [ 'shape' => 'TimestampEpoch', ], 'lastLoginTime' => [ 'shape' => 'TimestampEpoch', ], ], ], 'GetWorkingLocationRequest' => [ 'type' => 'structure', 'members' => [ 'locationType' => [ 'shape' => 'locationType', ], ], ], 'GetWorkingLocationResponse' => [ 'type' => 'structure', 'members' => [ 's3Uri' => [ 'shape' => 'stringValueLength1to1024', ], 's3Path' => [ 'shape' => 'stringValueLength1to1024', ], 's3Bucket' => [ 'shape' => 'stringValueLength1to63', ], ], ], 'IdType' => [ 'type' => 'string', 'max' => 26, 'min' => 1, ], 'IngestionStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'FAILED', 'SUCCESS', 'RUNNING', 'STOP_REQUESTED', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'LastName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '.*\\S.*', 'sensitive' => true, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ListChangesetsRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', ], 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListChangesetsResponse' => [ 'type' => 'structure', 'members' => [ 'changesets' => [ 'shape' => 'ChangesetList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataViewsRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', ], 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDataViewsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'dataViews' => [ 'shape' => 'DataViewList', ], ], ], 'ListDatasetsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDatasetsResponse' => [ 'type' => 'structure', 'members' => [ 'datasets' => [ 'shape' => 'DatasetList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPermissionGroupsByUserRequest' => [ 'type' => 'structure', 'required' => [ 'userId', 'maxResults', ], 'members' => [ 'userId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'userId', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPermissionGroupsByUserResponse' => [ 'type' => 'structure', 'members' => [ 'permissionGroups' => [ 'shape' => 'PermissionGroupByUserList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPermissionGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'maxResults', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPermissionGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'permissionGroups' => [ 'shape' => 'PermissionGroupList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListUsersByPermissionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'permissionGroupId', 'maxResults', ], 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', 'location' => 'uri', 'locationName' => 'permissionGroupId', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListUsersByPermissionGroupResponse' => [ 'type' => 'structure', 'members' => [ 'users' => [ 'shape' => 'UserByPermissionGroupList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListUsersRequest' => [ 'type' => 'structure', 'required' => [ 'maxResults', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListUsersResponse' => [ 'type' => 'structure', 'members' => [ 'users' => [ 'shape' => 'UserList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'OwnerName' => [ 'type' => 'string', 'max' => 250, 'min' => 1, 'pattern' => '.*\\S.*', ], 'PaginationToken' => [ 'type' => 'string', ], 'PartitionColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringValueLength1to255', ], ], 'Password' => [ 'type' => 'string', 'max' => 20, 'min' => 8, 'pattern' => '.*\\S.*', 'sensitive' => true, ], 'PermissionGroup' => [ 'type' => 'structure', 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', ], 'name' => [ 'shape' => 'PermissionGroupName', ], 'description' => [ 'shape' => 'PermissionGroupDescription', ], 'applicationPermissions' => [ 'shape' => 'ApplicationPermissionList', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'lastModifiedTime' => [ 'shape' => 'TimestampEpoch', ], 'membershipStatus' => [ 'shape' => 'PermissionGroupMembershipStatus', ], ], ], 'PermissionGroupByUser' => [ 'type' => 'structure', 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', ], 'name' => [ 'shape' => 'PermissionGroupName', ], 'membershipStatus' => [ 'shape' => 'PermissionGroupMembershipStatus', ], ], ], 'PermissionGroupByUserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionGroupByUser', ], ], 'PermissionGroupDescription' => [ 'type' => 'string', 'max' => 4000, 'min' => 1, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'PermissionGroupId' => [ 'type' => 'string', 'max' => 26, 'min' => 1, 'pattern' => '.*\\S.*', ], 'PermissionGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionGroup', ], ], 'PermissionGroupMembershipStatus' => [ 'type' => 'string', 'enum' => [ 'ADDITION_IN_PROGRESS', 'ADDITION_SUCCESS', 'REMOVAL_IN_PROGRESS', ], ], 'PermissionGroupName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*\\S.*', 'sensitive' => true, ], 'PermissionGroupParams' => [ 'type' => 'structure', 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', ], 'datasetPermissions' => [ 'shape' => 'ResourcePermissionsList', ], ], ], 'PhoneNumber' => [ 'type' => 'string', 'max' => 20, 'min' => 10, 'pattern' => '^[\\+0-9\\#\\,\\(][\\+0-9\\-\\.\\/\\(\\)\\,\\#\\s]+$', ], 'ResetUserPasswordRequest' => [ 'type' => 'structure', 'required' => [ 'userId', ], 'members' => [ 'userId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'userId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'ResetUserPasswordResponse' => [ 'type' => 'structure', 'members' => [ 'userId' => [ 'shape' => 'UserId', ], 'temporaryPassword' => [ 'shape' => 'Password', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], 'reason' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourcePermission' => [ 'type' => 'structure', 'members' => [ 'permission' => [ 'shape' => 'StringValueLength1to250', ], ], ], 'ResourcePermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcePermission', ], ], 'ResultLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[a-z\\-]*:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^.*\\S.*$', ], 'S3DestinationFormatOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringMapKey', ], 'value' => [ 'shape' => 'StringMapValue', ], ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^.*\\S.*$', ], 'S3Location' => [ 'type' => 'structure', 'required' => [ 'bucket', 'key', ], 'members' => [ 'bucket' => [ 'shape' => 'S3BucketName', ], 'key' => [ 'shape' => 'S3Key', ], ], ], 'SchemaDefinition' => [ 'type' => 'structure', 'members' => [ 'columns' => [ 'shape' => 'ColumnList', ], 'primaryKeyColumns' => [ 'shape' => 'ColumnNameList', ], ], ], 'SchemaUnion' => [ 'type' => 'structure', 'members' => [ 'tabularSchemaConfig' => [ 'shape' => 'SchemaDefinition', ], ], ], 'SecretAccessKey' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', 'sensitive' => true, ], 'SessionDuration' => [ 'type' => 'long', 'max' => 60, 'min' => 1, ], 'SessionToken' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', 'sensitive' => true, ], 'SortColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringValueLength1to255', ], ], 'SourceParams' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringMapKey', ], 'value' => [ 'shape' => 'StringMapValue', ], ], 'StatusCode' => [ 'type' => 'integer', ], 'StringMapKey' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'StringMapValue' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'StringValueLength1to250' => [ 'type' => 'string', 'max' => 250, 'min' => 1, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'StringValueLength1to255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TimestampEpoch' => [ 'type' => 'long', ], 'UpdateChangesetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'changesetId', 'sourceParams', 'formatParams', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'changesetId' => [ 'shape' => 'ChangesetId', 'location' => 'uri', 'locationName' => 'changesetId', ], 'sourceParams' => [ 'shape' => 'SourceParams', ], 'formatParams' => [ 'shape' => 'FormatParams', ], ], ], 'UpdateChangesetResponse' => [ 'type' => 'structure', 'members' => [ 'changesetId' => [ 'shape' => 'ChangesetId', ], 'datasetId' => [ 'shape' => 'DatasetId', ], ], ], 'UpdateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'datasetTitle', 'kind', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'datasetTitle' => [ 'shape' => 'DatasetTitle', ], 'kind' => [ 'shape' => 'DatasetKind', ], 'datasetDescription' => [ 'shape' => 'DatasetDescription', ], 'alias' => [ 'shape' => 'AliasString', ], 'schemaDefinition' => [ 'shape' => 'SchemaUnion', ], ], ], 'UpdateDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], ], ], 'UpdatePermissionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'permissionGroupId', ], 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', 'location' => 'uri', 'locationName' => 'permissionGroupId', ], 'name' => [ 'shape' => 'PermissionGroupName', ], 'description' => [ 'shape' => 'PermissionGroupDescription', ], 'applicationPermissions' => [ 'shape' => 'ApplicationPermissionList', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdatePermissionGroupResponse' => [ 'type' => 'structure', 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', ], ], ], 'UpdateUserRequest' => [ 'type' => 'structure', 'required' => [ 'userId', ], 'members' => [ 'userId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'userId', ], 'type' => [ 'shape' => 'UserType', ], 'firstName' => [ 'shape' => 'FirstName', ], 'lastName' => [ 'shape' => 'LastName', ], 'apiAccess' => [ 'shape' => 'ApiAccess', ], 'apiAccessPrincipalArn' => [ 'shape' => 'RoleArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateUserResponse' => [ 'type' => 'structure', 'members' => [ 'userId' => [ 'shape' => 'UserId', ], ], ], 'User' => [ 'type' => 'structure', 'members' => [ 'userId' => [ 'shape' => 'UserId', ], 'status' => [ 'shape' => 'UserStatus', ], 'firstName' => [ 'shape' => 'FirstName', ], 'lastName' => [ 'shape' => 'LastName', ], 'emailAddress' => [ 'shape' => 'Email', ], 'type' => [ 'shape' => 'UserType', ], 'apiAccess' => [ 'shape' => 'ApiAccess', ], 'apiAccessPrincipalArn' => [ 'shape' => 'RoleArn', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'lastEnabledTime' => [ 'shape' => 'TimestampEpoch', ], 'lastDisabledTime' => [ 'shape' => 'TimestampEpoch', ], 'lastModifiedTime' => [ 'shape' => 'TimestampEpoch', ], 'lastLoginTime' => [ 'shape' => 'TimestampEpoch', ], ], ], 'UserByPermissionGroup' => [ 'type' => 'structure', 'members' => [ 'userId' => [ 'shape' => 'UserId', ], 'status' => [ 'shape' => 'UserStatus', ], 'firstName' => [ 'shape' => 'FirstName', ], 'lastName' => [ 'shape' => 'LastName', ], 'emailAddress' => [ 'shape' => 'Email', ], 'type' => [ 'shape' => 'UserType', ], 'apiAccess' => [ 'shape' => 'ApiAccess', ], 'apiAccessPrincipalArn' => [ 'shape' => 'RoleArn', ], 'membershipStatus' => [ 'shape' => 'PermissionGroupMembershipStatus', ], ], ], 'UserByPermissionGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserByPermissionGroup', ], ], 'UserId' => [ 'type' => 'string', 'max' => 26, 'min' => 1, 'pattern' => '.*\\S.*', ], 'UserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'User', ], ], 'UserStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ENABLED', 'DISABLED', ], ], 'UserType' => [ 'type' => 'string', 'enum' => [ 'SUPER_USER', 'APP_USER', ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], 'reason' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'errorMessage' => [ 'type' => 'string', ], 'locationType' => [ 'type' => 'string', 'enum' => [ 'INGESTION', 'SAGEMAKER', ], ], 'stringValueLength1to1024' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*\\S.*', ], 'stringValueLength1to255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'stringValueLength1to63' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '.*\\S.*', ], 'stringValueMaxLength1000' => [ 'type' => 'string', 'max' => 1000, ], ],];
