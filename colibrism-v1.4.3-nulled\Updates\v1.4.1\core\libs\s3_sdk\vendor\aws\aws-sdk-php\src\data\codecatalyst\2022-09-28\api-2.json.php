<?php
// This file was auto-generated from sdk-root/src/data/codecatalyst/2022-09-28/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-09-28', 'endpointPrefix' => 'codecatalyst', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon CodeCatalyst', 'serviceId' => 'CodeCatalyst', 'signatureVersion' => 'bearer', 'signingName' => 'codecatalyst', 'uid' => 'codecatalyst-2022-09-28', ], 'operations' => [ 'CreateAccessToken' => [ 'name' => 'CreateAccessToken', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/accessTokens', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAccessTokenRequest', ], 'output' => [ 'shape' => 'CreateAccessTokenResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateDevEnvironment' => [ 'name' => 'CreateDevEnvironment', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/devEnvironments', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDevEnvironmentRequest', ], 'output' => [ 'shape' => 'CreateDevEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateProject' => [ 'name' => 'CreateProject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/spaces/{spaceName}/projects', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateProjectRequest', ], 'output' => [ 'shape' => 'CreateProjectResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateSourceRepository' => [ 'name' => 'CreateSourceRepository', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/sourceRepositories/{name}', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSourceRepositoryRequest', ], 'output' => [ 'shape' => 'CreateSourceRepositoryResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateSourceRepositoryBranch' => [ 'name' => 'CreateSourceRepositoryBranch', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/sourceRepositories/{sourceRepositoryName}/branches/{name}', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSourceRepositoryBranchRequest', ], 'output' => [ 'shape' => 'CreateSourceRepositoryBranchResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteAccessToken' => [ 'name' => 'DeleteAccessToken', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/accessTokens/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAccessTokenRequest', ], 'output' => [ 'shape' => 'DeleteAccessTokenResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteDevEnvironment' => [ 'name' => 'DeleteDevEnvironment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/devEnvironments/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDevEnvironmentRequest', ], 'output' => [ 'shape' => 'DeleteDevEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteProject' => [ 'name' => 'DeleteProject', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/spaces/{spaceName}/projects/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteProjectRequest', ], 'output' => [ 'shape' => 'DeleteProjectResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteSourceRepository' => [ 'name' => 'DeleteSourceRepository', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/sourceRepositories/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSourceRepositoryRequest', ], 'output' => [ 'shape' => 'DeleteSourceRepositoryResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteSpace' => [ 'name' => 'DeleteSpace', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/spaces/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSpaceRequest', ], 'output' => [ 'shape' => 'DeleteSpaceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'GetDevEnvironment' => [ 'name' => 'GetDevEnvironment', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/devEnvironments/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDevEnvironmentRequest', ], 'output' => [ 'shape' => 'GetDevEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetProject' => [ 'name' => 'GetProject', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/spaces/{spaceName}/projects/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProjectRequest', ], 'output' => [ 'shape' => 'GetProjectResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSourceRepository' => [ 'name' => 'GetSourceRepository', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/sourceRepositories/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSourceRepositoryRequest', ], 'output' => [ 'shape' => 'GetSourceRepositoryResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSourceRepositoryCloneUrls' => [ 'name' => 'GetSourceRepositoryCloneUrls', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/sourceRepositories/{sourceRepositoryName}/cloneUrls', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSourceRepositoryCloneUrlsRequest', ], 'output' => [ 'shape' => 'GetSourceRepositoryCloneUrlsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSpace' => [ 'name' => 'GetSpace', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/spaces/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSpaceRequest', ], 'output' => [ 'shape' => 'GetSpaceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSubscription' => [ 'name' => 'GetSubscription', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/spaces/{spaceName}/subscription', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSubscriptionRequest', ], 'output' => [ 'shape' => 'GetSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetUserDetails' => [ 'name' => 'GetUserDetails', 'http' => [ 'method' => 'GET', 'requestUri' => '/userDetails', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUserDetailsRequest', ], 'output' => [ 'shape' => 'GetUserDetailsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetWorkflow' => [ 'name' => 'GetWorkflow', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/workflows/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkflowRequest', ], 'output' => [ 'shape' => 'GetWorkflowResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetWorkflowRun' => [ 'name' => 'GetWorkflowRun', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/workflowRuns/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkflowRunRequest', ], 'output' => [ 'shape' => 'GetWorkflowRunResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAccessTokens' => [ 'name' => 'ListAccessTokens', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/accessTokens', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAccessTokensRequest', ], 'output' => [ 'shape' => 'ListAccessTokensResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDevEnvironmentSessions' => [ 'name' => 'ListDevEnvironmentSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/devEnvironments/{devEnvironmentId}/sessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDevEnvironmentSessionsRequest', ], 'output' => [ 'shape' => 'ListDevEnvironmentSessionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDevEnvironments' => [ 'name' => 'ListDevEnvironments', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/spaces/{spaceName}/devEnvironments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDevEnvironmentsRequest', ], 'output' => [ 'shape' => 'ListDevEnvironmentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListEventLogs' => [ 'name' => 'ListEventLogs', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/spaces/{spaceName}/eventLogs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEventLogsRequest', ], 'output' => [ 'shape' => 'ListEventLogsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListProjects' => [ 'name' => 'ListProjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/spaces/{spaceName}/projects', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProjectsRequest', ], 'output' => [ 'shape' => 'ListProjectsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSourceRepositories' => [ 'name' => 'ListSourceRepositories', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/sourceRepositories', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSourceRepositoriesRequest', ], 'output' => [ 'shape' => 'ListSourceRepositoriesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSourceRepositoryBranches' => [ 'name' => 'ListSourceRepositoryBranches', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/sourceRepositories/{sourceRepositoryName}/branches', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSourceRepositoryBranchesRequest', ], 'output' => [ 'shape' => 'ListSourceRepositoryBranchesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSpaces' => [ 'name' => 'ListSpaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/spaces', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSpacesRequest', ], 'output' => [ 'shape' => 'ListSpacesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListWorkflowRuns' => [ 'name' => 'ListWorkflowRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/workflowRuns', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWorkflowRunsRequest', ], 'output' => [ 'shape' => 'ListWorkflowRunsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListWorkflows' => [ 'name' => 'ListWorkflows', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/workflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWorkflowsRequest', ], 'output' => [ 'shape' => 'ListWorkflowsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartDevEnvironment' => [ 'name' => 'StartDevEnvironment', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/devEnvironments/{id}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartDevEnvironmentRequest', ], 'output' => [ 'shape' => 'StartDevEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'StartDevEnvironmentSession' => [ 'name' => 'StartDevEnvironmentSession', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/devEnvironments/{id}/session', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartDevEnvironmentSessionRequest', ], 'output' => [ 'shape' => 'StartDevEnvironmentSessionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartWorkflowRun' => [ 'name' => 'StartWorkflowRun', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/workflowRuns', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartWorkflowRunRequest', ], 'output' => [ 'shape' => 'StartWorkflowRunResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'StopDevEnvironment' => [ 'name' => 'StopDevEnvironment', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/devEnvironments/{id}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopDevEnvironmentRequest', ], 'output' => [ 'shape' => 'StopDevEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'StopDevEnvironmentSession' => [ 'name' => 'StopDevEnvironmentSession', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/devEnvironments/{id}/session/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopDevEnvironmentSessionRequest', ], 'output' => [ 'shape' => 'StopDevEnvironmentSessionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateDevEnvironment' => [ 'name' => 'UpdateDevEnvironment', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v1/spaces/{spaceName}/projects/{projectName}/devEnvironments/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDevEnvironmentRequest', ], 'output' => [ 'shape' => 'UpdateDevEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateProject' => [ 'name' => 'UpdateProject', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v1/spaces/{spaceName}/projects/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateProjectRequest', ], 'output' => [ 'shape' => 'UpdateProjectResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateSpace' => [ 'name' => 'UpdateSpace', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v1/spaces/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSpaceRequest', ], 'output' => [ 'shape' => 'UpdateSpaceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'VerifySession' => [ 'name' => 'VerifySession', 'http' => [ 'method' => 'GET', 'requestUri' => '/session', 'responseCode' => 200, ], 'output' => [ 'shape' => 'VerifySessionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessTokenId' => [ 'type' => 'string', 'max' => 36, 'min' => 1, ], 'AccessTokenName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AccessTokenSecret' => [ 'type' => 'string', 'max' => 4000, 'min' => 1, 'sensitive' => true, ], 'AccessTokenSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessTokenSummary', ], ], 'AccessTokenSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'name', ], 'members' => [ 'id' => [ 'shape' => 'AccessTokenId', ], 'name' => [ 'shape' => 'AccessTokenName', ], 'expiresTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'EQ', 'GT', 'GE', 'LT', 'LE', 'BEGINS_WITH', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateAccessTokenRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'AccessTokenName', ], 'expiresTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'CreateAccessTokenResponse' => [ 'type' => 'structure', 'required' => [ 'secret', 'name', 'expiresTime', 'accessTokenId', ], 'members' => [ 'secret' => [ 'shape' => 'AccessTokenSecret', ], 'name' => [ 'shape' => 'AccessTokenName', ], 'expiresTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'accessTokenId' => [ 'shape' => 'AccessTokenId', ], ], ], 'CreateDevEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'instanceType', 'persistentStorage', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'repositories' => [ 'shape' => 'RepositoriesInput', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'alias' => [ 'shape' => 'CreateDevEnvironmentRequestAliasString', ], 'ides' => [ 'shape' => 'IdeConfigurationList', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'inactivityTimeoutMinutes' => [ 'shape' => 'InactivityTimeoutMinutes', ], 'persistentStorage' => [ 'shape' => 'PersistentStorageConfiguration', ], 'vpcConnectionName' => [ 'shape' => 'NameString', ], ], ], 'CreateDevEnvironmentRequestAliasString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9]+(?:[-_\\.][a-zA-Z0-9]+)*', ], 'CreateDevEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'id' => [ 'shape' => 'Uuid', ], 'vpcConnectionName' => [ 'shape' => 'NameString', ], ], ], 'CreateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'displayName', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'displayName' => [ 'shape' => 'ProjectDisplayName', ], 'description' => [ 'shape' => 'ProjectDescription', ], ], ], 'CreateProjectResponse' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'name' => [ 'shape' => 'NameString', ], 'displayName' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], ], ], 'CreateSourceRepositoryBranchRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'sourceRepositoryName', 'name', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'sourceRepositoryName' => [ 'shape' => 'SourceRepositoryNameString', 'location' => 'uri', 'locationName' => 'sourceRepositoryName', ], 'name' => [ 'shape' => 'SourceRepositoryBranchString', 'location' => 'uri', 'locationName' => 'name', ], 'headCommitId' => [ 'shape' => 'String', ], ], ], 'CreateSourceRepositoryBranchResponse' => [ 'type' => 'structure', 'members' => [ 'ref' => [ 'shape' => 'SourceRepositoryBranchRefString', ], 'name' => [ 'shape' => 'SourceRepositoryBranchString', ], 'lastUpdatedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'headCommitId' => [ 'shape' => 'String', ], ], ], 'CreateSourceRepositoryRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'name', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'name' => [ 'shape' => 'SourceRepositoryNameString', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'SourceRepositoryDescriptionString', ], ], ], 'CreateSourceRepositoryResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'name', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'name' => [ 'shape' => 'SourceRepositoryNameString', ], 'description' => [ 'shape' => 'SourceRepositoryDescriptionString', ], ], ], 'DeleteAccessTokenRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'AccessTokenId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteAccessTokenResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDevEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteDevEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'id' => [ 'shape' => 'Uuid', ], ], ], 'DeleteProjectRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'name', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'name' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteProjectResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'name', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'name' => [ 'shape' => 'NameString', ], 'displayName' => [ 'shape' => 'String', ], ], ], 'DeleteSourceRepositoryRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'name', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'name' => [ 'shape' => 'SourceRepositoryNameString', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteSourceRepositoryResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'name', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'name' => [ 'shape' => 'SourceRepositoryNameString', ], ], ], 'DeleteSpaceRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteSpaceResponse' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'displayName' => [ 'shape' => 'String', ], ], ], 'DevEnvironmentAccessDetails' => [ 'type' => 'structure', 'required' => [ 'streamUrl', 'tokenValue', ], 'members' => [ 'streamUrl' => [ 'shape' => 'SensitiveString', ], 'tokenValue' => [ 'shape' => 'SensitiveString', ], ], 'sensitive' => true, ], 'DevEnvironmentRepositorySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevEnvironmentRepositorySummary', ], ], 'DevEnvironmentRepositorySummary' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'SourceRepositoryNameString', ], 'branchName' => [ 'shape' => 'SourceRepositoryBranchString', ], ], ], 'DevEnvironmentSessionConfiguration' => [ 'type' => 'structure', 'required' => [ 'sessionType', ], 'members' => [ 'sessionType' => [ 'shape' => 'DevEnvironmentSessionType', ], 'executeCommandSessionConfiguration' => [ 'shape' => 'ExecuteCommandSessionConfiguration', ], ], ], 'DevEnvironmentSessionSummary' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'devEnvironmentId', 'startedTime', 'id', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'devEnvironmentId' => [ 'shape' => 'Uuid', ], 'startedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'id' => [ 'shape' => 'DevEnvironmentSessionSummaryIdString', ], ], ], 'DevEnvironmentSessionSummaryIdString' => [ 'type' => 'string', 'max' => 96, 'min' => 1, ], 'DevEnvironmentSessionType' => [ 'type' => 'string', 'enum' => [ 'SSM', 'SSH', ], ], 'DevEnvironmentSessionsSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevEnvironmentSessionSummary', ], ], 'DevEnvironmentStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'RUNNING', 'STARTING', 'STOPPING', 'STOPPED', 'FAILED', 'DELETING', 'DELETED', ], ], 'DevEnvironmentSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'lastUpdatedTime', 'creatorId', 'status', 'repositories', 'instanceType', 'inactivityTimeoutMinutes', 'persistentStorage', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'id' => [ 'shape' => 'Uuid', ], 'lastUpdatedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'creatorId' => [ 'shape' => 'DevEnvironmentSummaryCreatorIdString', ], 'status' => [ 'shape' => 'DevEnvironmentStatus', ], 'statusReason' => [ 'shape' => 'StatusReason', ], 'repositories' => [ 'shape' => 'DevEnvironmentRepositorySummaries', ], 'alias' => [ 'shape' => 'DevEnvironmentSummaryAliasString', ], 'ides' => [ 'shape' => 'Ides', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'inactivityTimeoutMinutes' => [ 'shape' => 'InactivityTimeoutMinutes', ], 'persistentStorage' => [ 'shape' => 'PersistentStorage', ], 'vpcConnectionName' => [ 'shape' => 'NameString', ], ], ], 'DevEnvironmentSummaryAliasString' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'DevEnvironmentSummaryCreatorIdString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'DevEnvironmentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevEnvironmentSummary', ], ], 'EmailAddress' => [ 'type' => 'structure', 'members' => [ 'email' => [ 'shape' => 'String', ], 'verified' => [ 'shape' => 'Boolean', ], ], ], 'EventLogEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventLogEntry', ], ], 'EventLogEntry' => [ 'type' => 'structure', 'required' => [ 'id', 'eventName', 'eventType', 'eventCategory', 'eventSource', 'eventTime', 'operationType', 'userIdentity', ], 'members' => [ 'id' => [ 'shape' => 'String', ], 'eventName' => [ 'shape' => 'String', ], 'eventType' => [ 'shape' => 'String', ], 'eventCategory' => [ 'shape' => 'String', ], 'eventSource' => [ 'shape' => 'String', ], 'eventTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'operationType' => [ 'shape' => 'OperationType', ], 'userIdentity' => [ 'shape' => 'UserIdentity', ], 'projectInformation' => [ 'shape' => 'ProjectInformation', ], 'requestId' => [ 'shape' => 'String', ], 'requestPayload' => [ 'shape' => 'EventPayload', ], 'responsePayload' => [ 'shape' => 'EventPayload', ], 'errorCode' => [ 'shape' => 'String', ], 'sourceIpAddress' => [ 'shape' => 'String', ], 'userAgent' => [ 'shape' => 'String', ], ], ], 'EventPayload' => [ 'type' => 'structure', 'members' => [ 'contentType' => [ 'shape' => 'String', ], 'data' => [ 'shape' => 'String', ], ], ], 'ExecuteCommandSessionConfiguration' => [ 'type' => 'structure', 'required' => [ 'command', ], 'members' => [ 'command' => [ 'shape' => 'ExecuteCommandSessionConfigurationCommandString', ], 'arguments' => [ 'shape' => 'ExecuteCommandSessionConfigurationArguments', ], ], ], 'ExecuteCommandSessionConfigurationArguments' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExecuteCommandSessionConfigurationArgumentsMemberString', ], ], 'ExecuteCommandSessionConfigurationArgumentsMemberString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ExecuteCommandSessionConfigurationCommandString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'key', 'values', ], 'members' => [ 'key' => [ 'shape' => 'String', ], 'values' => [ 'shape' => 'StringList', ], 'comparisonOperator' => [ 'shape' => 'String', ], ], ], 'FilterKey' => [ 'type' => 'string', 'enum' => [ 'hasAccessTo', 'name', ], ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'GetDevEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetDevEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', 'lastUpdatedTime', 'creatorId', 'status', 'repositories', 'instanceType', 'inactivityTimeoutMinutes', 'persistentStorage', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'id' => [ 'shape' => 'Uuid', ], 'lastUpdatedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'creatorId' => [ 'shape' => 'GetDevEnvironmentResponseCreatorIdString', ], 'status' => [ 'shape' => 'DevEnvironmentStatus', ], 'statusReason' => [ 'shape' => 'StatusReason', ], 'repositories' => [ 'shape' => 'DevEnvironmentRepositorySummaries', ], 'alias' => [ 'shape' => 'GetDevEnvironmentResponseAliasString', ], 'ides' => [ 'shape' => 'Ides', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'inactivityTimeoutMinutes' => [ 'shape' => 'InactivityTimeoutMinutes', ], 'persistentStorage' => [ 'shape' => 'PersistentStorage', ], 'vpcConnectionName' => [ 'shape' => 'NameString', ], ], ], 'GetDevEnvironmentResponseAliasString' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'GetDevEnvironmentResponseCreatorIdString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'GetProjectRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'name', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'name' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetProjectResponse' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'name' => [ 'shape' => 'String', ], 'displayName' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], ], ], 'GetSourceRepositoryCloneUrlsRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'sourceRepositoryName', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'sourceRepositoryName' => [ 'shape' => 'SourceRepositoryNameString', 'location' => 'uri', 'locationName' => 'sourceRepositoryName', ], ], ], 'GetSourceRepositoryCloneUrlsResponse' => [ 'type' => 'structure', 'required' => [ 'https', ], 'members' => [ 'https' => [ 'shape' => 'String', ], ], ], 'GetSourceRepositoryRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'name', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'name' => [ 'shape' => 'SourceRepositoryNameString', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetSourceRepositoryResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'name', 'lastUpdatedTime', 'createdTime', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'name' => [ 'shape' => 'SourceRepositoryNameString', ], 'description' => [ 'shape' => 'SourceRepositoryDescriptionString', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'createdTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetSpaceRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetSpaceResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'regionName', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'regionName' => [ 'shape' => 'RegionString', ], 'displayName' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], ], ], 'GetSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], ], ], 'GetSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'subscriptionType' => [ 'shape' => 'String', ], 'awsAccountName' => [ 'shape' => 'NameString', ], ], ], 'GetUserDetailsRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'GetUserDetailsRequestIdString', 'location' => 'querystring', 'locationName' => 'id', ], 'userName' => [ 'shape' => 'GetUserDetailsRequestUserNameString', 'location' => 'querystring', 'locationName' => 'userName', ], ], ], 'GetUserDetailsRequestIdString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'GetUserDetailsRequestUserNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 3, 'pattern' => '[a-zA-Z0-9]{3,100}', ], 'GetUserDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'userId' => [ 'shape' => 'String', ], 'userName' => [ 'shape' => 'String', ], 'displayName' => [ 'shape' => 'String', ], 'primaryEmail' => [ 'shape' => 'EmailAddress', ], 'version' => [ 'shape' => 'String', ], ], ], 'GetWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'id', 'projectName', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], 'projectName' => [ 'shape' => 'GetWorkflowRequestProjectNameString', 'location' => 'uri', 'locationName' => 'projectName', ], ], ], 'GetWorkflowRequestProjectNameString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z0-9]+(?:[-_\\.][a-zA-Z0-9]+)*', ], 'GetWorkflowResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', 'name', 'definition', 'createdTime', 'lastUpdatedTime', 'runMode', 'status', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'id' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'String', ], 'sourceRepositoryName' => [ 'shape' => 'SourceRepositoryNameString', ], 'sourceBranchName' => [ 'shape' => 'SourceRepositoryBranchString', ], 'definition' => [ 'shape' => 'WorkflowDefinition', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastUpdatedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'runMode' => [ 'shape' => 'WorkflowRunMode', ], 'status' => [ 'shape' => 'WorkflowStatus', ], ], ], 'GetWorkflowRunRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'id', 'projectName', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], 'projectName' => [ 'shape' => 'GetWorkflowRunRequestProjectNameString', 'location' => 'uri', 'locationName' => 'projectName', ], ], ], 'GetWorkflowRunRequestProjectNameString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z0-9]+(?:[-_\\.][a-zA-Z0-9]+)*', ], 'GetWorkflowRunResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', 'workflowId', 'status', 'startTime', 'lastUpdatedTime', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'id' => [ 'shape' => 'Uuid', ], 'workflowId' => [ 'shape' => 'Uuid', ], 'status' => [ 'shape' => 'WorkflowRunStatus', ], 'statusReasons' => [ 'shape' => 'WorkflowRunStatusReasons', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastUpdatedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'Ide' => [ 'type' => 'structure', 'members' => [ 'runtime' => [ 'shape' => 'IdeRuntimeString', ], 'name' => [ 'shape' => 'IdeNameString', ], ], ], 'IdeConfiguration' => [ 'type' => 'structure', 'members' => [ 'runtime' => [ 'shape' => 'IdeConfigurationRuntimeString', ], 'name' => [ 'shape' => 'IdeConfigurationNameString', ], ], ], 'IdeConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdeConfiguration', ], 'max' => 1, 'min' => 0, ], 'IdeConfigurationNameString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'IdeConfigurationRuntimeString' => [ 'type' => 'string', 'max' => 400, 'min' => 1, ], 'IdeNameString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'IdeRuntimeString' => [ 'type' => 'string', 'max' => 400, 'min' => 1, ], 'Ides' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ide', ], 'max' => 1, 'min' => 0, ], 'InactivityTimeoutMinutes' => [ 'type' => 'integer', 'max' => 1200, 'min' => 0, ], 'InstanceType' => [ 'type' => 'string', 'enum' => [ 'dev.standard1.small', 'dev.standard1.medium', 'dev.standard1.large', 'dev.standard1.xlarge', ], ], 'ListAccessTokensRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListAccessTokensRequestMaxResultsInteger', ], 'nextToken' => [ 'shape' => 'ListAccessTokensRequestNextTokenString', ], ], ], 'ListAccessTokensRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10, ], 'ListAccessTokensRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListAccessTokensResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'AccessTokenSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListDevEnvironmentSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'devEnvironmentId', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'devEnvironmentId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'devEnvironmentId', ], 'nextToken' => [ 'shape' => 'ListDevEnvironmentSessionsRequestNextTokenString', ], 'maxResults' => [ 'shape' => 'ListDevEnvironmentSessionsRequestMaxResultsInteger', ], ], ], 'ListDevEnvironmentSessionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 200, 'min' => 1, ], 'ListDevEnvironmentSessionsRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListDevEnvironmentSessionsResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'DevEnvironmentSessionsSummaryList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListDevEnvironmentsRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', ], 'filters' => [ 'shape' => 'Filters', ], 'nextToken' => [ 'shape' => 'ListDevEnvironmentsRequestNextTokenString', ], 'maxResults' => [ 'shape' => 'ListDevEnvironmentsRequestMaxResultsInteger', ], ], ], 'ListDevEnvironmentsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'ListDevEnvironmentsRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListDevEnvironmentsResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'DevEnvironmentSummaryList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListEventLogsRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'startTime', 'endTime', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'eventName' => [ 'shape' => 'String', ], 'nextToken' => [ 'shape' => 'ListEventLogsRequestNextTokenString', ], 'maxResults' => [ 'shape' => 'ListEventLogsRequestMaxResultsInteger', ], ], ], 'ListEventLogsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 250, 'min' => 1, ], 'ListEventLogsRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListEventLogsResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'items' => [ 'shape' => 'EventLogEntries', ], ], ], 'ListProjectsRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'nextToken' => [ 'shape' => 'ListProjectsRequestNextTokenString', ], 'maxResults' => [ 'shape' => 'ListProjectsRequestMaxResultsInteger', ], 'filters' => [ 'shape' => 'ProjectListFilters', ], ], ], 'ListProjectsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListProjectsRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListProjectsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'items' => [ 'shape' => 'ProjectSummaries', ], ], ], 'ListSourceRepositoriesItem' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'lastUpdatedTime', 'createdTime', ], 'members' => [ 'id' => [ 'shape' => 'SourceRepositoryIdString', ], 'name' => [ 'shape' => 'SourceRepositoryNameString', ], 'description' => [ 'shape' => 'SourceRepositoryDescriptionString', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'createdTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListSourceRepositoriesItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListSourceRepositoriesItem', ], ], 'ListSourceRepositoriesRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'nextToken' => [ 'shape' => 'ListSourceRepositoriesRequestNextTokenString', ], 'maxResults' => [ 'shape' => 'ListSourceRepositoriesRequestMaxResultsInteger', ], ], ], 'ListSourceRepositoriesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 200, 'min' => 1, ], 'ListSourceRepositoriesRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListSourceRepositoriesResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'ListSourceRepositoriesItems', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListSourceRepositoryBranchesItem' => [ 'type' => 'structure', 'members' => [ 'ref' => [ 'shape' => 'SourceRepositoryBranchRefString', ], 'name' => [ 'shape' => 'SourceRepositoryBranchString', ], 'lastUpdatedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'headCommitId' => [ 'shape' => 'String', ], ], ], 'ListSourceRepositoryBranchesItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListSourceRepositoryBranchesItem', ], ], 'ListSourceRepositoryBranchesRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'sourceRepositoryName', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'sourceRepositoryName' => [ 'shape' => 'SourceRepositoryNameString', 'location' => 'uri', 'locationName' => 'sourceRepositoryName', ], 'nextToken' => [ 'shape' => 'ListSourceRepositoryBranchesRequestNextTokenString', ], 'maxResults' => [ 'shape' => 'ListSourceRepositoryBranchesRequestMaxResultsInteger', ], ], ], 'ListSourceRepositoryBranchesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'ListSourceRepositoryBranchesRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListSourceRepositoryBranchesResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'items' => [ 'shape' => 'ListSourceRepositoryBranchesItems', ], ], ], 'ListSpacesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'ListSpacesRequestNextTokenString', ], ], ], 'ListSpacesRequestNextTokenString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ListSpacesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'items' => [ 'shape' => 'SpaceSummaries', ], ], ], 'ListWorkflowRunsRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'workflowId' => [ 'shape' => 'Uuid', 'location' => 'querystring', 'locationName' => 'workflowId', ], 'projectName' => [ 'shape' => 'ListWorkflowRunsRequestProjectNameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'nextToken' => [ 'shape' => 'ListWorkflowRunsRequestNextTokenString', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListWorkflowRunsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'sortBy' => [ 'shape' => 'WorkflowRunSortCriteriaList', ], ], ], 'ListWorkflowRunsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'ListWorkflowRunsRequestNextTokenString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ListWorkflowRunsRequestProjectNameString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z0-9]+(?:[-_\\.][a-zA-Z0-9]+)*', ], 'ListWorkflowRunsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'items' => [ 'shape' => 'WorkflowRunSummaries', ], ], ], 'ListWorkflowsRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', ], 'members' => [ 'spaceName' => [ 'shape' => 'ListWorkflowsRequestSpaceNameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'nextToken' => [ 'shape' => 'ListWorkflowsRequestNextTokenString', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListWorkflowsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'sortBy' => [ 'shape' => 'WorkflowSortCriteriaList', ], ], ], 'ListWorkflowsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListWorkflowsRequestNextTokenString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ListWorkflowsRequestSpaceNameString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z0-9]+(?:[-_\\.][a-zA-Z0-9]+)*', ], 'ListWorkflowsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'items' => [ 'shape' => 'WorkflowSummaries', ], ], ], 'NameString' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '[a-zA-Z0-9]+(?:[-_\\.][a-zA-Z0-9]+)*', ], 'OperationType' => [ 'type' => 'string', 'enum' => [ 'READONLY', 'MUTATION', ], ], 'PersistentStorage' => [ 'type' => 'structure', 'required' => [ 'sizeInGiB', ], 'members' => [ 'sizeInGiB' => [ 'shape' => 'PersistentStorageSizeInGiBInteger', ], ], ], 'PersistentStorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'sizeInGiB', ], 'members' => [ 'sizeInGiB' => [ 'shape' => 'PersistentStorageConfigurationSizeInGiBInteger', ], ], ], 'PersistentStorageConfigurationSizeInGiBInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 64, 'min' => 0, ], 'PersistentStorageSizeInGiBInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 64, 'min' => 0, ], 'ProjectDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => '[a-zA-Z0-9]+(?:[-_a-zA-Z0-9.,;:/\\+=?&$% ])*', ], 'ProjectDisplayName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '[a-zA-Z0-9]+(?:[-_\\. ][a-zA-Z0-9]+)*', ], 'ProjectInformation' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'projectId' => [ 'shape' => 'String', ], ], ], 'ProjectListFilter' => [ 'type' => 'structure', 'required' => [ 'key', 'values', ], 'members' => [ 'key' => [ 'shape' => 'FilterKey', ], 'values' => [ 'shape' => 'StringList', ], 'comparisonOperator' => [ 'shape' => 'ComparisonOperator', ], ], ], 'ProjectListFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectListFilter', ], ], 'ProjectSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectSummary', ], ], 'ProjectSummary' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'displayName' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], ], ], 'RegionString' => [ 'type' => 'string', 'max' => 16, 'min' => 3, 'pattern' => '(us(?:-gov)?|af|ap|ca|cn|eu|sa)-(central|(?:north|south)?(?:east|west)?)-(\\d+)', ], 'RepositoriesInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryInput', ], ], 'RepositoryInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'SourceRepositoryNameString', ], 'branchName' => [ 'shape' => 'SourceRepositoryBranchString', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SourceRepositoryBranchRefString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'SourceRepositoryBranchString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'SourceRepositoryDescriptionString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'SourceRepositoryIdString' => [ 'type' => 'string', 'pattern' => '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}', ], 'SourceRepositoryNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '(?!.*[.]git$)[\\w\\-.]*', ], 'SpaceDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => '[a-zA-Z0-9]+(?:[-_a-zA-Z0-9.,;:/\\+=?&$% ])*', ], 'SpaceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SpaceSummary', ], ], 'SpaceSummary' => [ 'type' => 'structure', 'required' => [ 'name', 'regionName', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'regionName' => [ 'shape' => 'RegionString', ], 'displayName' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], ], ], 'StartDevEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], 'ides' => [ 'shape' => 'IdeConfigurationList', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'inactivityTimeoutMinutes' => [ 'shape' => 'InactivityTimeoutMinutes', ], ], ], 'StartDevEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', 'status', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'id' => [ 'shape' => 'Uuid', ], 'status' => [ 'shape' => 'DevEnvironmentStatus', ], ], ], 'StartDevEnvironmentSessionRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', 'sessionConfiguration', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], 'sessionConfiguration' => [ 'shape' => 'DevEnvironmentSessionConfiguration', ], ], ], 'StartDevEnvironmentSessionResponse' => [ 'type' => 'structure', 'required' => [ 'accessDetails', 'spaceName', 'projectName', 'id', ], 'members' => [ 'accessDetails' => [ 'shape' => 'DevEnvironmentAccessDetails', ], 'sessionId' => [ 'shape' => 'StartDevEnvironmentSessionResponseSessionIdString', ], 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'id' => [ 'shape' => 'Uuid', ], ], ], 'StartDevEnvironmentSessionResponseSessionIdString' => [ 'type' => 'string', 'max' => 96, 'min' => 1, ], 'StartWorkflowRunRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'workflowId', ], 'members' => [ 'spaceName' => [ 'shape' => 'StartWorkflowRunRequestSpaceNameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'StartWorkflowRunRequestProjectNameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'workflowId' => [ 'shape' => 'Uuid', 'location' => 'querystring', 'locationName' => 'workflowId', ], 'clientToken' => [ 'shape' => 'StartWorkflowRunRequestClientTokenString', 'idempotencyToken' => true, ], ], ], 'StartWorkflowRunRequestClientTokenString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9]+(?:[-_\\.][a-zA-Z0-9]+)*', ], 'StartWorkflowRunRequestProjectNameString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z0-9]+(?:[-_\\.][a-zA-Z0-9]+)*', ], 'StartWorkflowRunRequestSpaceNameString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z0-9]+(?:[-_\\.][a-zA-Z0-9]+)*', ], 'StartWorkflowRunResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', 'workflowId', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'id' => [ 'shape' => 'Uuid', ], 'workflowId' => [ 'shape' => 'Uuid', ], ], ], 'StatusReason' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'StopDevEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'StopDevEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', 'status', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'id' => [ 'shape' => 'Uuid', ], 'status' => [ 'shape' => 'DevEnvironmentStatus', ], ], ], 'StopDevEnvironmentSessionRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', 'sessionId', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], 'sessionId' => [ 'shape' => 'StopDevEnvironmentSessionRequestSessionIdString', 'location' => 'uri', 'locationName' => 'sessionId', ], ], ], 'StopDevEnvironmentSessionRequestSessionIdString' => [ 'type' => 'string', 'max' => 96, 'min' => 1, ], 'StopDevEnvironmentSessionResponse' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', 'sessionId', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'id' => [ 'shape' => 'Uuid', ], 'sessionId' => [ 'shape' => 'StopDevEnvironmentSessionResponseSessionIdString', ], ], ], 'StopDevEnvironmentSessionResponseSessionIdString' => [ 'type' => 'string', 'max' => 96, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'UpdateDevEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'projectName', 'id', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'projectName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'projectName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], 'alias' => [ 'shape' => 'UpdateDevEnvironmentRequestAliasString', ], 'ides' => [ 'shape' => 'IdeConfigurationList', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'inactivityTimeoutMinutes' => [ 'shape' => 'InactivityTimeoutMinutes', ], 'clientToken' => [ 'shape' => 'ClientToken', ], ], ], 'UpdateDevEnvironmentRequestAliasString' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '$|^[a-zA-Z0-9]+(?:[-_\\.][a-zA-Z0-9]+)*', ], 'UpdateDevEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'spaceName', 'projectName', ], 'members' => [ 'id' => [ 'shape' => 'Uuid', ], 'spaceName' => [ 'shape' => 'NameString', ], 'projectName' => [ 'shape' => 'NameString', ], 'alias' => [ 'shape' => 'UpdateDevEnvironmentResponseAliasString', ], 'ides' => [ 'shape' => 'IdeConfigurationList', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'inactivityTimeoutMinutes' => [ 'shape' => 'InactivityTimeoutMinutes', ], 'clientToken' => [ 'shape' => 'ClientToken', ], ], ], 'UpdateDevEnvironmentResponseAliasString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9]+(?:[-_\\.][a-zA-Z0-9]+)*', ], 'UpdateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'spaceName', 'name', ], 'members' => [ 'spaceName' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'spaceName', ], 'name' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'ProjectDescription', ], ], ], 'UpdateProjectResponse' => [ 'type' => 'structure', 'members' => [ 'spaceName' => [ 'shape' => 'NameString', ], 'name' => [ 'shape' => 'NameString', ], 'displayName' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], ], ], 'UpdateSpaceRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'NameString', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'SpaceDescription', ], ], ], 'UpdateSpaceResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'displayName' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], ], ], 'UserIdentity' => [ 'type' => 'structure', 'required' => [ 'userType', 'principalId', ], 'members' => [ 'userType' => [ 'shape' => 'UserType', ], 'principalId' => [ 'shape' => 'String', ], 'userName' => [ 'shape' => 'String', ], 'awsAccountId' => [ 'shape' => 'String', ], ], ], 'UserType' => [ 'type' => 'string', 'enum' => [ 'USER', 'AWS_ACCOUNT', 'UNKNOWN', ], ], 'Uuid' => [ 'type' => 'string', 'pattern' => '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}', ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VerifySessionResponse' => [ 'type' => 'structure', 'members' => [ 'identity' => [ 'shape' => 'VerifySessionResponseIdentityString', ], ], ], 'VerifySessionResponseIdentityString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'WorkflowDefinition' => [ 'type' => 'structure', 'required' => [ 'path', ], 'members' => [ 'path' => [ 'shape' => 'String', ], ], ], 'WorkflowDefinitionSummary' => [ 'type' => 'structure', 'required' => [ 'path', ], 'members' => [ 'path' => [ 'shape' => 'String', ], ], ], 'WorkflowRunMode' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'PARALLEL', 'SUPERSEDED', ], ], 'WorkflowRunSortCriteria' => [ 'type' => 'structure', 'members' => [], ], 'WorkflowRunSortCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowRunSortCriteria', ], 'max' => 1, 'min' => 0, ], 'WorkflowRunStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'FAILED', 'STOPPED', 'SUPERSEDED', 'CANCELLED', 'NOT_RUN', 'VALIDATING', 'PROVISIONING', 'IN_PROGRESS', 'STOPPING', 'ABANDONED', ], ], 'WorkflowRunStatusReason' => [ 'type' => 'structure', 'members' => [], ], 'WorkflowRunStatusReasons' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowRunStatusReason', ], ], 'WorkflowRunSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowRunSummary', ], ], 'WorkflowRunSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'workflowId', 'workflowName', 'status', 'startTime', 'lastUpdatedTime', ], 'members' => [ 'id' => [ 'shape' => 'Uuid', ], 'workflowId' => [ 'shape' => 'Uuid', ], 'workflowName' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'WorkflowRunStatus', ], 'statusReasons' => [ 'shape' => 'WorkflowRunStatusReasons', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastUpdatedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'WorkflowSortCriteria' => [ 'type' => 'structure', 'members' => [], ], 'WorkflowSortCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowSortCriteria', ], 'max' => 1, 'min' => 0, ], 'WorkflowStatus' => [ 'type' => 'string', 'enum' => [ 'INVALID', 'ACTIVE', ], ], 'WorkflowSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowSummary', ], ], 'WorkflowSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'sourceRepositoryName', 'sourceBranchName', 'definition', 'createdTime', 'lastUpdatedTime', 'runMode', 'status', ], 'members' => [ 'id' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'String', ], 'sourceRepositoryName' => [ 'shape' => 'SourceRepositoryNameString', ], 'sourceBranchName' => [ 'shape' => 'SourceRepositoryBranchString', ], 'definition' => [ 'shape' => 'WorkflowDefinitionSummary', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastUpdatedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'runMode' => [ 'shape' => 'WorkflowRunMode', ], 'status' => [ 'shape' => 'WorkflowStatus', ], ], ], ],];
