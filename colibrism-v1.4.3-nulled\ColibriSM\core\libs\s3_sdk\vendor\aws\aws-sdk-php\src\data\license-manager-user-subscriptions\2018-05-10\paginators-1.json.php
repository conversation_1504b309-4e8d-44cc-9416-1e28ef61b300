<?php
// This file was auto-generated from sdk-root/src/data/license-manager-user-subscriptions/2018-05-10/paginators-1.json
return [ 'pagination' => [ 'ListIdentityProviders' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'IdentityProviderSummaries', ], 'ListInstances' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'InstanceSummaries', ], 'ListProductSubscriptions' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'ProductUserSummaries', ], 'ListUserAssociations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'InstanceUserSummaries', ], ],];
