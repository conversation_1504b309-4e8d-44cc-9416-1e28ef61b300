search:21  GET https://www.amasvos.com/themes/default/statics/css/libs/main.css net::ERR_ABORTED 404 (Not Found)
search:32  GET https://www.amasvos.com/themes/default/statics/js/wave-audio-path-player.umd.js?version=20 net::ERR_ABORTED 404 (Not Found)
custom.js?version=20:5 🎬 VliX Carousel Preview System Loading...
search:149  GET https://www.amasvos.com/themes/default/statics/js/libs/afterglow/afterglow.min.js?v=1.4.3 net::ERR_ABORTED 404 (Not Found)
search:195 Uncaught TypeError: Cannot read properties of undefined (reading 'config')
    at search:195:30
    at search:198:3
(anonymous) @ search:195
(anonymous) @ search:198
search:6523  GET https://www.amasvos.com/vue_fix_injection.js?v=1751490584 net::ERR_ABORTED 404 (Not Found)
vlix-enhanced-video-manager.js?v=1756864400:9 🎬 VLIX ENHANCED: Loading enhanced video manager...
vlix-enhanced-video-manager.js?v=1756864400:1094 ✅ VLIX ENHANCED: Enhanced video manager loaded with bulletproof monitoring
[Intervention] Slow network is detected. See <URL> for more details. Fallback font will be used while loading: <URL>
[Intervention] Slow network is detected. See <URL> for more details. Fallback font will be used while loading: <URL>
[Intervention] Slow network is detected. See <URL> for more details. Fallback font will be used while loading: <URL>
[Intervention] Slow network is detected. See <URL> for more details. Fallback font will be used while loading: <URL>
[Intervention] Slow network is detected. See <URL> for more details. Fallback font will be used while loading: <URL>
search:15971 Uncaught ReferenceError: loadCommentsForPost is not defined
    at search:15971:34
(anonymous) @ search:15971
global-hls-player.js:246 Optimized MP4 Player with Advanced Caching loaded successfully
smart-video-preloader.js:323 📱 ULTRA DATA SAVING: Smart Video Preloader disabled to prevent background data consumption
search:16019 Uncaught TypeError: Cannot read properties of undefined (reading 'set')
    at search:16019:95
    at search:16279:4
(anonymous) @ search:16019
(anonymous) @ search:16279
search:16285 🚀 ORIGINAL SHORTS SCRIPT LOADING...
search:16289 🚀 SHORTS: Elements found - overlay: true closeBtn: true
search:19306 🎬 SETUP: Universal video click handler installed
search:19392 🎬 SETUP: Universal video click handler installed with capture=true
search:19393 🔧 DEBUG: Code version check - Thread ID fix v2.0 loaded
search:27098 Initializing minimal like state tracking
smart-video-preloader.js:328 Initializing Smart Video Preloader with delay
smart-video-preloader.js:29 Initializing Smart Video Preloader {preloadDistance: 0, isMobile: true}
smart-video-preloader.js:129 Registered video: video-player-1168 Original preload: none
smart-video-preloader.js:129 Registered video: video-player-1109 Original preload: none
smart-video-preloader.js:107 Found 2 videos to manage
smart-video-preloader.js:149 Video exiting unload zone, considering unload: video-player-1109
smart-video-preloader.js:136 Video entering preload zone: video-player-1168
smart-video-preloader.js:161 Smart preloading video for smooth playback: video-player-1168
smart-video-preloader.js:231 📱 PRELOADER: Skipping video.load() to prevent blank flash for: video-player-1168
smart-video-preloader.js:214 Direct video preloaded: video-player-1168
smart-video-preloader.js:142 Video left preload zone: video-player-1109
search:17095 📋 Found descriptions: 0
video-timing-controls.js?v=05012024-1:17 🕒 Initializing Video Timing Controls
custom.js?version=20:402 🚀 Initializing VliX Carousel Preview System
custom.js?version=20:158 🔍 No VliX carousel videos found yet
custom.js?version=20:397 🔍 Watching for dynamic carousel content
custom.js?version=20:414 ✅ VliX Carousel Preview System initialized
vlix-enhanced-video-manager.js?v=1756864400:37 🚀 VLIX ENHANCED: Initializing enhanced video manager
vlix-enhanced-video-manager.js?v=1756864400:105 🛡️ VLIX CONFLICT: Global conflict prevention active
vlix-enhanced-video-manager.js?v=1756864400:46 ✅ VLIX ENHANCED: Enhanced video manager initialized
vlix-enhanced-video-manager.js?v=1756864400:932 🔍 VLIX ENHANCED: Starting comprehensive video monitor
global-hls-player.js:230 DOM loaded, initializing optimized video system
global-hls-player.js:8 Starting optimized MP4 initialization for selector: video.lazy-hls Force load: false
global-hls-player.js:12 Found 0 videos to initialize with optimized caching
seamless-video-sync.js?v=05012024-1:30 🎯 SEAMLESS SYNC: Initializing comprehensive video synchronization system
seamless-video-sync.js?v=05012024-1:77 🎯 SEAMLESS: 5/5 optimization systems ready
seamless-video-sync.js?v=05012024-1:501 🎯 SEAMLESS: Initializing buffer optimization
seamless-video-sync.js?v=05012024-1:635 🎯 SEAMLESS: Performance monitoring initialized
seamless-video-sync.js?v=05012024-1:93 🎯 SEAMLESS: Setting up memory integration
seamless-video-sync.js?v=05012024-1:129 🎯 SEAMLESS: Setting up SPA navigation optimization
seamless-video-sync.js?v=05012024-1:245 🎯 SEAMLESS: Setting up precache integration
seamless-video-sync.js?v=05012024-1:54 ✅ SEAMLESS SYNC: Comprehensive system initialized successfully
search:11610 Loaded follow status cache from localStorage: {}
search:10246 🎬 VLIX PRELOADER: Initializing Instagram/TikTok-style video preloader
search:10422 ✅ VLIX PRELOADER: Video preloader system initialized
search:13969 🎬 Initializing timeline video autoplay...
search:13972 🎬 Found 2 timeline videos
search:12295 Registered video for unified playback: video-player-1168 context: timeline
search:12361 🎬 WATCH MORE: Not in PWA mode, skipping overlay for video: video-player-1168
search:12295 Registered video for unified playback: video-player-1109 context: timeline
search:12361 🎬 WATCH MORE: Not in PWA mode, skipping overlay for video: video-player-1109
search:14443 Premium dropdown enhancements loading...
search:14820 Timeline dropdown using Bootstrap default behavior
smart-video-preloader.js:149 Video exiting unload zone, considering unload: video-player-1168
smart-video-preloader.js:238 Unloading video: video-player-1168
smart-video-preloader.js:264 📱 MOBILE: Skipping aggressive unload to prevent blank videos for: video-player-1168
smart-video-preloader.js:142 Video left preload zone: video-player-1168
custom.js?version=20:158 🔍 No VliX carousel videos found yet
search:12863 Smart video selection: {winner: 'video-player-1168', score: '0.737', visibility: '78.9%', candidates: 1}
search:12624 Playing video: video-player-1168 context: timeline visibility: 0.7892640808540544
search:13875 📹 Timeline video play: video-player-1168
search:13950 📹 PROTECTED: Skipping video.load() to prevent blank container
search:14009 🎬 DELAYED: Started autoplay for visible video: video-player-1168
search:12315 📱 DATA SAVING: Timeline video video-player-1168 visible but not preloaded to save data
search:12315 📱 DATA SAVING: Timeline video video-player-1109 visible but not preloaded to save data
custom.js?version=20:158 🔍 No VliX carousel videos found yet
search:13931 📹 Playing: video-player-1168
search:19206 Saved video state: video-player-1168 time: 0.58
search:176 🔧 Advanced Video Caching Service Worker registered with scope: https://www.amasvos.com/themes/default/statics/js/
search:180 🎯 Service Worker active, video caching enabled
search:12863 Smart video selection: {winner: 'video-player-1168', score: '1.043', visibility: '99.7%', candidates: 1}
search:12315 📱 DATA SAVING: Timeline video video-player-1168 visible but not preloaded to save data
search:12315 📱 DATA SAVING: Timeline video video-player-1168 visible but not preloaded to save data
search:12315 📱 DATA SAVING: Timeline video video-player-1168 visible but not preloaded to save data
search:12759 ⏸️ Paused video: video-player-1168
search:12936 Uncaught TypeError: Cannot read properties of null (reading 'id')
    at window.addEventListener.passive (search:12936:127)
window.addEventListener.passive @ search:12936
search:12315 📱 DATA SAVING: Timeline video video-player-1168 visible but not preloaded to save data
search:12315 📱 DATA SAVING: Timeline video video-player-1168 visible but not preloaded to save data
pwa-icon-512x5120.png:1  GET https://www.amasvos.com/themes/default/statics/img/pwa-icon-512x5120.png 404 (Not Found)
search:88 Failed to update web device ID. [OneSignal push notifications alert]
search:1 Error while trying to use the following icon from the Manifest: https://www.amasvos.com/themes/default/statics/img/pwa-icon-512x5120.png (Download error or resource isn't a valid image)
search:12315 📱 DATA SAVING: Timeline video video-player-1109 visible but not preloaded to save data
search:12990 Unauthorized video play detected, pausing: video-player-1109
search:12863 Smart video selection: {winner: 'video-player-1109', score: '0.666', visibility: '70.1%', candidates: 1}
search:12624 Playing video: video-player-1109 context: timeline visibility: 0.7013234478935698
search:13875 📹 Timeline video play: video-player-1109
search:13931 📹 Playing: video-player-1109
search:12315 📱 DATA SAVING: Timeline video video-player-1109 visible but not preloaded to save data
search:9010 Explore scroll check - loading_more: false load_more: true query_result: 1
search:9012 Explore: Triggering infinite scroll load, offset: 10
search:8951 Explore load_entries: Making AJAX request with offset: 10 type: posts query: 
search:8963 Explore AJAX beforeSend - setting loading_more to true
search:12759 ⏸️ Paused video: video-player-1109
search:12936 Uncaught TypeError: Cannot read properties of null (reading 'id')
    at window.addEventListener.passive (search:12936:127)
window.addEventListener.passive @ search:12936
search:12315 📱 DATA SAVING: Timeline video video-player-1109 visible but not preloaded to save data
search:9010 Explore scroll check - loading_more: true load_more: true query_result: 1
search:9015 Explore: Infinite scroll blocked - already loading or no more content
search:12315 📱 DATA SAVING: Timeline video video-player-1109 visible but not preloaded to save data
search:8987 Explore AJAX failed: parsererror SyntaxError: Unexpected token '<', "			<div class"... is not valid JSON
    at parse (<anonymous>)
    at jquery-3.5.1.min.js?v=05012024-1:2:79369
    at l (jquery-3.5.1.min.js?v=05012024-1:2:79486)
    at XMLHttpRequest.<anonymous> (jquery-3.5.1.min.js?v=05012024-1:2:82254) {readyState: 4, getResponseHeader: ƒ, getAllResponseHeaders: ƒ, setRequestHeader: ƒ, overrideMimeType: ƒ, …}
search:8990 Explore AJAX complete - setting loading_more to false
search:9010 Explore scroll check - loading_more: false load_more: false query_result: 1
search:9015 Explore: Infinite scroll blocked - already loading or no more content
search:9010 Explore scroll check - loading_more: false load_more: false query_result: 1
search:9015 Explore: Infinite scroll blocked - already loading or no more content
custom.js?version=20:19 🎯 User interaction detected, enabling autoplay
search:9010 Explore scroll check - loading_more: false load_more: false query_result: 1
search:9015 Explore: Infinite scroll blocked - already loading or no more content
search:9010 Explore scroll check - loading_more: false load_more: false query_result: 1
search:9015 Explore: Infinite scroll blocked - already loading or no more content
