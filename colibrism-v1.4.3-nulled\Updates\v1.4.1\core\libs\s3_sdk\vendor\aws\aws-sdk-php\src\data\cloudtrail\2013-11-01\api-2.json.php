<?php
// This file was auto-generated from sdk-root/src/data/cloudtrail/2013-11-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2013-11-01', 'endpointPrefix' => 'cloudtrail', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'CloudTrail', 'serviceFullName' => 'AWS CloudTrail', 'serviceId' => 'CloudTrail', 'signatureVersion' => 'v4', 'targetPrefix' => 'com.amazonaws.cloudtrail.v20131101.CloudTrail_20131101', 'uid' => 'cloudtrail-2013-11-01', ], 'operations' => [ 'AddTags' => [ 'name' => 'AddTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsRequest', ], 'output' => [ 'shape' => 'AddTagsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'ChannelARNInvalidException', ], [ 'shape' => 'ResourceTypeNotSupportedException', ], [ 'shape' => 'TagsLimitExceededException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'InvalidTagParameterException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'ChannelNotFoundException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CancelQuery' => [ 'name' => 'CancelQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelQueryRequest', ], 'output' => [ 'shape' => 'CancelQueryResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'InactiveQueryException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'QueryIdNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateChannel' => [ 'name' => 'CreateChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateChannelRequest', ], 'output' => [ 'shape' => 'CreateChannelResponse', ], 'errors' => [ [ 'shape' => 'ChannelMaxLimitExceededException', ], [ 'shape' => 'InvalidSourceException', ], [ 'shape' => 'ChannelAlreadyExistsException', ], [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InvalidEventDataStoreCategoryException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidTagParameterException', ], [ 'shape' => 'TagsLimitExceededException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'CreateEventDataStore' => [ 'name' => 'CreateEventDataStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEventDataStoreRequest', ], 'output' => [ 'shape' => 'CreateEventDataStoreResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreAlreadyExistsException', ], [ 'shape' => 'EventDataStoreMaxLimitExceededException', ], [ 'shape' => 'InvalidEventSelectorsException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidTagParameterException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InsufficientEncryptionPolicyException', ], [ 'shape' => 'InvalidKmsKeyIdException', ], [ 'shape' => 'KmsKeyNotFoundException', ], [ 'shape' => 'KmsException', ], [ 'shape' => 'CloudTrailAccessNotEnabledException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'OrganizationsNotInUseException', ], [ 'shape' => 'OrganizationNotInAllFeaturesModeException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], ], ], 'CreateTrail' => [ 'name' => 'CreateTrail', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTrailRequest', ], 'output' => [ 'shape' => 'CreateTrailResponse', ], 'errors' => [ [ 'shape' => 'MaximumNumberOfTrailsExceededException', ], [ 'shape' => 'TrailAlreadyExistsException', ], [ 'shape' => 'S3BucketDoesNotExistException', ], [ 'shape' => 'InsufficientS3BucketPolicyException', ], [ 'shape' => 'InsufficientSnsTopicPolicyException', ], [ 'shape' => 'InsufficientEncryptionPolicyException', ], [ 'shape' => 'InvalidS3BucketNameException', ], [ 'shape' => 'InvalidS3PrefixException', ], [ 'shape' => 'InvalidSnsTopicNameException', ], [ 'shape' => 'InvalidKmsKeyIdException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'TrailNotProvidedException', ], [ 'shape' => 'TagsLimitExceededException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'KmsKeyNotFoundException', ], [ 'shape' => 'KmsKeyDisabledException', ], [ 'shape' => 'KmsException', ], [ 'shape' => 'InvalidCloudWatchLogsLogGroupArnException', ], [ 'shape' => 'InvalidCloudWatchLogsRoleArnException', ], [ 'shape' => 'CloudWatchLogsDeliveryUnavailableException', ], [ 'shape' => 'InvalidTagParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'CloudTrailAccessNotEnabledException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'OrganizationsNotInUseException', ], [ 'shape' => 'OrganizationNotInAllFeaturesModeException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'CloudTrailInvalidClientTokenIdException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteChannel' => [ 'name' => 'DeleteChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteChannelRequest', ], 'output' => [ 'shape' => 'DeleteChannelResponse', ], 'errors' => [ [ 'shape' => 'ChannelARNInvalidException', ], [ 'shape' => 'ChannelNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'DeleteEventDataStore' => [ 'name' => 'DeleteEventDataStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEventDataStoreRequest', ], 'output' => [ 'shape' => 'DeleteEventDataStoreResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'EventDataStoreTerminationProtectedException', ], [ 'shape' => 'EventDataStoreHasOngoingImportException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'ChannelExistsForEDSException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'EventDataStoreFederationEnabledException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceARNNotValidException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourcePolicyNotFoundException', ], [ 'shape' => 'ResourceTypeNotSupportedException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'DeleteTrail' => [ 'name' => 'DeleteTrail', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTrailRequest', ], 'output' => [ 'shape' => 'DeleteTrailResponse', ], 'errors' => [ [ 'shape' => 'TrailNotFoundException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidHomeRegionException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], ], 'idempotent' => true, ], 'DeregisterOrganizationDelegatedAdmin' => [ 'name' => 'DeregisterOrganizationDelegatedAdmin', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterOrganizationDelegatedAdminRequest', ], 'output' => [ 'shape' => 'DeregisterOrganizationDelegatedAdminResponse', ], 'errors' => [ [ 'shape' => 'AccountNotFoundException', ], [ 'shape' => 'AccountNotRegisteredException', ], [ 'shape' => 'CloudTrailAccessNotEnabledException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotOrganizationManagementAccountException', ], [ 'shape' => 'OrganizationNotInAllFeaturesModeException', ], [ 'shape' => 'OrganizationsNotInUseException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], 'idempotent' => true, ], 'DescribeQuery' => [ 'name' => 'DescribeQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeQueryRequest', ], 'output' => [ 'shape' => 'DescribeQueryResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'QueryIdNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], ], 'idempotent' => true, ], 'DescribeTrails' => [ 'name' => 'DescribeTrails', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTrailsRequest', ], 'output' => [ 'shape' => 'DescribeTrailsResponse', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], ], 'idempotent' => true, ], 'DisableFederation' => [ 'name' => 'DisableFederation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableFederationRequest', ], 'output' => [ 'shape' => 'DisableFederationResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'CloudTrailAccessNotEnabledException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'OrganizationsNotInUseException', ], [ 'shape' => 'OrganizationNotInAllFeaturesModeException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'EnableFederation' => [ 'name' => 'EnableFederation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableFederationRequest', ], 'output' => [ 'shape' => 'EnableFederationResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'CloudTrailAccessNotEnabledException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'OrganizationsNotInUseException', ], [ 'shape' => 'OrganizationNotInAllFeaturesModeException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EventDataStoreFederationEnabledException', ], ], ], 'GetChannel' => [ 'name' => 'GetChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetChannelRequest', ], 'output' => [ 'shape' => 'GetChannelResponse', ], 'errors' => [ [ 'shape' => 'ChannelARNInvalidException', ], [ 'shape' => 'ChannelNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'GetEventDataStore' => [ 'name' => 'GetEventDataStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEventDataStoreRequest', ], 'output' => [ 'shape' => 'GetEventDataStoreResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], ], 'idempotent' => true, ], 'GetEventSelectors' => [ 'name' => 'GetEventSelectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEventSelectorsRequest', ], 'output' => [ 'shape' => 'GetEventSelectorsResponse', ], 'errors' => [ [ 'shape' => 'TrailNotFoundException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], ], 'idempotent' => true, ], 'GetImport' => [ 'name' => 'GetImport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetImportRequest', ], 'output' => [ 'shape' => 'GetImportResponse', ], 'errors' => [ [ 'shape' => 'ImportNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'GetInsightSelectors' => [ 'name' => 'GetInsightSelectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInsightSelectorsRequest', ], 'output' => [ 'shape' => 'GetInsightSelectorsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'TrailNotFoundException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'InsightNotEnabledException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'GetQueryResults' => [ 'name' => 'GetQueryResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetQueryResultsRequest', ], 'output' => [ 'shape' => 'GetQueryResultsResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'QueryIdNotFoundException', ], [ 'shape' => 'InsufficientEncryptionPolicyException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], ], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourcePolicyRequest', ], 'output' => [ 'shape' => 'GetResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceARNNotValidException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourcePolicyNotFoundException', ], [ 'shape' => 'ResourceTypeNotSupportedException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'GetTrail' => [ 'name' => 'GetTrail', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTrailRequest', ], 'output' => [ 'shape' => 'GetTrailResponse', ], 'errors' => [ [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'TrailNotFoundException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], 'idempotent' => true, ], 'GetTrailStatus' => [ 'name' => 'GetTrailStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTrailStatusRequest', ], 'output' => [ 'shape' => 'GetTrailStatusResponse', ], 'errors' => [ [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'TrailNotFoundException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], 'idempotent' => true, ], 'ListChannels' => [ 'name' => 'ListChannels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListChannelsRequest', ], 'output' => [ 'shape' => 'ListChannelsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'ListEventDataStores' => [ 'name' => 'ListEventDataStores', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEventDataStoresRequest', ], 'output' => [ 'shape' => 'ListEventDataStoresResponse', ], 'errors' => [ [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], ], 'idempotent' => true, ], 'ListImportFailures' => [ 'name' => 'ListImportFailures', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListImportFailuresRequest', ], 'output' => [ 'shape' => 'ListImportFailuresResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'ListImports' => [ 'name' => 'ListImports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListImportsRequest', ], 'output' => [ 'shape' => 'ListImportsResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'ListPublicKeys' => [ 'name' => 'ListPublicKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPublicKeysRequest', ], 'output' => [ 'shape' => 'ListPublicKeysResponse', ], 'errors' => [ [ 'shape' => 'InvalidTimeRangeException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'InvalidTokenException', ], ], 'idempotent' => true, ], 'ListQueries' => [ 'name' => 'ListQueries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListQueriesRequest', ], 'output' => [ 'shape' => 'ListQueriesResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'InvalidDateRangeException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidQueryStatusException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], ], 'idempotent' => true, ], 'ListTags' => [ 'name' => 'ListTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsRequest', ], 'output' => [ 'shape' => 'ListTagsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'ChannelARNInvalidException', ], [ 'shape' => 'ResourceTypeNotSupportedException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'InvalidTokenException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], ], 'idempotent' => true, ], 'ListTrails' => [ 'name' => 'ListTrails', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTrailsRequest', ], 'output' => [ 'shape' => 'ListTrailsResponse', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], 'idempotent' => true, ], 'LookupEvents' => [ 'name' => 'LookupEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'LookupEventsRequest', ], 'output' => [ 'shape' => 'LookupEventsResponse', ], 'errors' => [ [ 'shape' => 'InvalidLookupAttributesException', ], [ 'shape' => 'InvalidTimeRangeException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidEventCategoryException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], 'idempotent' => true, ], 'PutEventSelectors' => [ 'name' => 'PutEventSelectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutEventSelectorsRequest', ], 'output' => [ 'shape' => 'PutEventSelectorsResponse', ], 'errors' => [ [ 'shape' => 'TrailNotFoundException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'InvalidHomeRegionException', ], [ 'shape' => 'InvalidEventSelectorsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], ], 'idempotent' => true, ], 'PutInsightSelectors' => [ 'name' => 'PutInsightSelectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutInsightSelectorsRequest', ], 'output' => [ 'shape' => 'PutInsightSelectorsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'TrailNotFoundException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'InvalidHomeRegionException', ], [ 'shape' => 'InvalidInsightSelectorsException', ], [ 'shape' => 'InsufficientS3BucketPolicyException', ], [ 'shape' => 'InsufficientEncryptionPolicyException', ], [ 'shape' => 'S3BucketDoesNotExistException', ], [ 'shape' => 'KmsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceARNNotValidException', ], [ 'shape' => 'ResourcePolicyNotValidException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceTypeNotSupportedException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'RegisterOrganizationDelegatedAdmin' => [ 'name' => 'RegisterOrganizationDelegatedAdmin', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterOrganizationDelegatedAdminRequest', ], 'output' => [ 'shape' => 'RegisterOrganizationDelegatedAdminResponse', ], 'errors' => [ [ 'shape' => 'AccountRegisteredException', ], [ 'shape' => 'AccountNotFoundException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'CannotDelegateManagementAccountException', ], [ 'shape' => 'CloudTrailAccessNotEnabledException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'DelegatedAdminAccountLimitExceededException', ], [ 'shape' => 'NotOrganizationManagementAccountException', ], [ 'shape' => 'OrganizationNotInAllFeaturesModeException', ], [ 'shape' => 'OrganizationsNotInUseException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], 'idempotent' => true, ], 'RemoveTags' => [ 'name' => 'RemoveTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsRequest', ], 'output' => [ 'shape' => 'RemoveTagsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'ChannelARNInvalidException', ], [ 'shape' => 'ResourceTypeNotSupportedException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'InvalidTagParameterException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'ChannelNotFoundException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], ], 'idempotent' => true, ], 'RestoreEventDataStore' => [ 'name' => 'RestoreEventDataStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreEventDataStoreRequest', ], 'output' => [ 'shape' => 'RestoreEventDataStoreResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'EventDataStoreMaxLimitExceededException', ], [ 'shape' => 'InvalidEventDataStoreStatusException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'CloudTrailAccessNotEnabledException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], [ 'shape' => 'OrganizationsNotInUseException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'OrganizationNotInAllFeaturesModeException', ], ], ], 'StartEventDataStoreIngestion' => [ 'name' => 'StartEventDataStoreIngestion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartEventDataStoreIngestionRequest', ], 'output' => [ 'shape' => 'StartEventDataStoreIngestionResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InvalidEventDataStoreStatusException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidEventDataStoreCategoryException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], ], ], 'StartImport' => [ 'name' => 'StartImport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartImportRequest', ], 'output' => [ 'shape' => 'StartImportResponse', ], 'errors' => [ [ 'shape' => 'AccountHasOngoingImportException', ], [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InvalidEventDataStoreStatusException', ], [ 'shape' => 'InvalidEventDataStoreCategoryException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'InvalidImportSourceException', ], [ 'shape' => 'ImportNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InsufficientEncryptionPolicyException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'StartLogging' => [ 'name' => 'StartLogging', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartLoggingRequest', ], 'output' => [ 'shape' => 'StartLoggingResponse', ], 'errors' => [ [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'TrailNotFoundException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'InvalidHomeRegionException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], ], 'idempotent' => true, ], 'StartQuery' => [ 'name' => 'StartQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartQueryRequest', ], 'output' => [ 'shape' => 'StartQueryResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidQueryStatementException', ], [ 'shape' => 'MaxConcurrentQueriesException', ], [ 'shape' => 'InsufficientEncryptionPolicyException', ], [ 'shape' => 'InvalidS3PrefixException', ], [ 'shape' => 'InvalidS3BucketNameException', ], [ 'shape' => 'InsufficientS3BucketPolicyException', ], [ 'shape' => 'S3BucketDoesNotExistException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], ], 'idempotent' => true, ], 'StopEventDataStoreIngestion' => [ 'name' => 'StopEventDataStoreIngestion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopEventDataStoreIngestionRequest', ], 'output' => [ 'shape' => 'StopEventDataStoreIngestionResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InvalidEventDataStoreStatusException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidEventDataStoreCategoryException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], ], ], 'StopImport' => [ 'name' => 'StopImport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopImportRequest', ], 'output' => [ 'shape' => 'StopImportResponse', ], 'errors' => [ [ 'shape' => 'ImportNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'StopLogging' => [ 'name' => 'StopLogging', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopLoggingRequest', ], 'output' => [ 'shape' => 'StopLoggingResponse', ], 'errors' => [ [ 'shape' => 'TrailNotFoundException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidHomeRegionException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], ], 'idempotent' => true, ], 'UpdateChannel' => [ 'name' => 'UpdateChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateChannelRequest', ], 'output' => [ 'shape' => 'UpdateChannelResponse', ], 'errors' => [ [ 'shape' => 'ChannelARNInvalidException', ], [ 'shape' => 'ChannelNotFoundException', ], [ 'shape' => 'ChannelAlreadyExistsException', ], [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InvalidEventDataStoreCategoryException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'UpdateEventDataStore' => [ 'name' => 'UpdateEventDataStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEventDataStoreRequest', ], 'output' => [ 'shape' => 'UpdateEventDataStoreResponse', ], 'errors' => [ [ 'shape' => 'EventDataStoreAlreadyExistsException', ], [ 'shape' => 'EventDataStoreARNInvalidException', ], [ 'shape' => 'EventDataStoreNotFoundException', ], [ 'shape' => 'InvalidEventSelectorsException', ], [ 'shape' => 'InvalidInsightSelectorsException', ], [ 'shape' => 'EventDataStoreHasOngoingImportException', ], [ 'shape' => 'InactiveEventDataStoreException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InsufficientEncryptionPolicyException', ], [ 'shape' => 'InvalidKmsKeyIdException', ], [ 'shape' => 'KmsKeyNotFoundException', ], [ 'shape' => 'KmsException', ], [ 'shape' => 'CloudTrailAccessNotEnabledException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], [ 'shape' => 'OrganizationsNotInUseException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'OrganizationNotInAllFeaturesModeException', ], ], 'idempotent' => true, ], 'UpdateTrail' => [ 'name' => 'UpdateTrail', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTrailRequest', ], 'output' => [ 'shape' => 'UpdateTrailResponse', ], 'errors' => [ [ 'shape' => 'S3BucketDoesNotExistException', ], [ 'shape' => 'InsufficientS3BucketPolicyException', ], [ 'shape' => 'InsufficientSnsTopicPolicyException', ], [ 'shape' => 'InsufficientEncryptionPolicyException', ], [ 'shape' => 'TrailNotFoundException', ], [ 'shape' => 'InvalidS3BucketNameException', ], [ 'shape' => 'InvalidS3PrefixException', ], [ 'shape' => 'InvalidSnsTopicNameException', ], [ 'shape' => 'InvalidKmsKeyIdException', ], [ 'shape' => 'InvalidTrailNameException', ], [ 'shape' => 'TrailNotProvidedException', ], [ 'shape' => 'InvalidEventSelectorsException', ], [ 'shape' => 'CloudTrailARNInvalidException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidHomeRegionException', ], [ 'shape' => 'KmsKeyNotFoundException', ], [ 'shape' => 'KmsKeyDisabledException', ], [ 'shape' => 'KmsException', ], [ 'shape' => 'InvalidCloudWatchLogsLogGroupArnException', ], [ 'shape' => 'InvalidCloudWatchLogsRoleArnException', ], [ 'shape' => 'CloudWatchLogsDeliveryUnavailableException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'CloudTrailAccessNotEnabledException', ], [ 'shape' => 'InsufficientDependencyServiceAccessPermissionException', ], [ 'shape' => 'OrganizationsNotInUseException', ], [ 'shape' => 'NotOrganizationMasterAccountException', ], [ 'shape' => 'OrganizationNotInAllFeaturesModeException', ], [ 'shape' => 'NoManagementAccountSLRExistsException', ], [ 'shape' => 'CloudTrailInvalidClientTokenIdException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AccountHasOngoingImportException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 16, 'min' => 12, 'pattern' => '\\d+', ], 'AccountNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AccountNotRegisteredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AccountRegisteredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AddTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'TagsList', ], 'members' => [ 'ResourceId' => [ 'shape' => 'String', ], 'TagsList' => [ 'shape' => 'TagsList', ], ], ], 'AddTagsResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdvancedEventSelector' => [ 'type' => 'structure', 'required' => [ 'FieldSelectors', ], 'members' => [ 'Name' => [ 'shape' => 'SelectorName', ], 'FieldSelectors' => [ 'shape' => 'AdvancedFieldSelectors', ], ], ], 'AdvancedEventSelectors' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdvancedEventSelector', ], ], 'AdvancedFieldSelector' => [ 'type' => 'structure', 'required' => [ 'Field', ], 'members' => [ 'Field' => [ 'shape' => 'SelectorField', ], 'Equals' => [ 'shape' => 'Operator', ], 'StartsWith' => [ 'shape' => 'Operator', ], 'EndsWith' => [ 'shape' => 'Operator', ], 'NotEquals' => [ 'shape' => 'Operator', ], 'NotStartsWith' => [ 'shape' => 'Operator', ], 'NotEndsWith' => [ 'shape' => 'Operator', ], ], ], 'AdvancedFieldSelectors' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdvancedFieldSelector', ], 'min' => 1, ], 'BillingMode' => [ 'type' => 'string', 'enum' => [ 'EXTENDABLE_RETENTION_PRICING', 'FIXED_RETENTION_PRICING', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'ByteBuffer' => [ 'type' => 'blob', ], 'CancelQueryRequest' => [ 'type' => 'structure', 'required' => [ 'QueryId', ], 'members' => [ 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', 'deprecated' => true, 'deprecatedMessage' => 'EventDataStore is no longer required by CancelQueryRequest', ], 'QueryId' => [ 'shape' => 'UUID', ], ], ], 'CancelQueryResponse' => [ 'type' => 'structure', 'required' => [ 'QueryId', 'QueryStatus', ], 'members' => [ 'QueryId' => [ 'shape' => 'UUID', ], 'QueryStatus' => [ 'shape' => 'QueryStatus', ], ], ], 'CannotDelegateManagementAccountException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Channel' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChannelArn', ], 'Name' => [ 'shape' => 'ChannelName', ], ], ], 'ChannelARNInvalidException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ChannelAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ChannelArn' => [ 'type' => 'string', 'max' => 256, 'min' => 3, 'pattern' => '^[a-zA-Z0-9._/\\-:]+$', ], 'ChannelExistsForEDSException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ChannelMaxLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ChannelName' => [ 'type' => 'string', 'max' => 128, 'min' => 3, 'pattern' => '^[a-zA-Z0-9._\\-]+$', ], 'ChannelNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Channels' => [ 'type' => 'list', 'member' => [ 'shape' => 'Channel', ], ], 'CloudTrailARNInvalidException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CloudTrailAccessNotEnabledException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CloudTrailInvalidClientTokenIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CloudWatchLogsDeliveryUnavailableException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CreateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Source', 'Destinations', ], 'members' => [ 'Name' => [ 'shape' => 'ChannelName', ], 'Source' => [ 'shape' => 'Source', ], 'Destinations' => [ 'shape' => 'Destinations', ], 'Tags' => [ 'shape' => 'TagsList', ], ], ], 'CreateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChannelArn', ], 'Name' => [ 'shape' => 'ChannelName', ], 'Source' => [ 'shape' => 'Source', ], 'Destinations' => [ 'shape' => 'Destinations', ], 'Tags' => [ 'shape' => 'TagsList', ], ], ], 'CreateEventDataStoreRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'EventDataStoreName', ], 'AdvancedEventSelectors' => [ 'shape' => 'AdvancedEventSelectors', ], 'MultiRegionEnabled' => [ 'shape' => 'Boolean', ], 'OrganizationEnabled' => [ 'shape' => 'Boolean', ], 'RetentionPeriod' => [ 'shape' => 'RetentionPeriod', ], 'TerminationProtectionEnabled' => [ 'shape' => 'TerminationProtectionEnabled', ], 'TagsList' => [ 'shape' => 'TagsList', ], 'KmsKeyId' => [ 'shape' => 'EventDataStoreKmsKeyId', ], 'StartIngestion' => [ 'shape' => 'Boolean', ], 'BillingMode' => [ 'shape' => 'BillingMode', ], ], ], 'CreateEventDataStoreResponse' => [ 'type' => 'structure', 'members' => [ 'EventDataStoreArn' => [ 'shape' => 'EventDataStoreArn', ], 'Name' => [ 'shape' => 'EventDataStoreName', ], 'Status' => [ 'shape' => 'EventDataStoreStatus', ], 'AdvancedEventSelectors' => [ 'shape' => 'AdvancedEventSelectors', ], 'MultiRegionEnabled' => [ 'shape' => 'Boolean', ], 'OrganizationEnabled' => [ 'shape' => 'Boolean', ], 'RetentionPeriod' => [ 'shape' => 'RetentionPeriod', ], 'TerminationProtectionEnabled' => [ 'shape' => 'TerminationProtectionEnabled', ], 'TagsList' => [ 'shape' => 'TagsList', ], 'CreatedTimestamp' => [ 'shape' => 'Date', ], 'UpdatedTimestamp' => [ 'shape' => 'Date', ], 'KmsKeyId' => [ 'shape' => 'EventDataStoreKmsKeyId', ], 'BillingMode' => [ 'shape' => 'BillingMode', ], ], ], 'CreateTrailRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'S3BucketName', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'S3BucketName' => [ 'shape' => 'String', ], 'S3KeyPrefix' => [ 'shape' => 'String', ], 'SnsTopicName' => [ 'shape' => 'String', ], 'IncludeGlobalServiceEvents' => [ 'shape' => 'Boolean', ], 'IsMultiRegionTrail' => [ 'shape' => 'Boolean', ], 'EnableLogFileValidation' => [ 'shape' => 'Boolean', ], 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'String', ], 'CloudWatchLogsRoleArn' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'IsOrganizationTrail' => [ 'shape' => 'Boolean', ], 'TagsList' => [ 'shape' => 'TagsList', ], ], ], 'CreateTrailResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'S3BucketName' => [ 'shape' => 'String', ], 'S3KeyPrefix' => [ 'shape' => 'String', ], 'SnsTopicName' => [ 'shape' => 'String', 'deprecated' => true, ], 'SnsTopicARN' => [ 'shape' => 'String', ], 'IncludeGlobalServiceEvents' => [ 'shape' => 'Boolean', ], 'IsMultiRegionTrail' => [ 'shape' => 'Boolean', ], 'TrailARN' => [ 'shape' => 'String', ], 'LogFileValidationEnabled' => [ 'shape' => 'Boolean', ], 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'String', ], 'CloudWatchLogsRoleArn' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'IsOrganizationTrail' => [ 'shape' => 'Boolean', ], ], ], 'DataResource' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Values' => [ 'shape' => 'DataResourceValues', ], ], ], 'DataResourceValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'DataResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataResource', ], ], 'Date' => [ 'type' => 'timestamp', ], 'DelegatedAdminAccountLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeleteChannelRequest' => [ 'type' => 'structure', 'required' => [ 'Channel', ], 'members' => [ 'Channel' => [ 'shape' => 'ChannelArn', ], ], ], 'DeleteChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEventDataStoreRequest' => [ 'type' => 'structure', 'required' => [ 'EventDataStore', ], 'members' => [ 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', ], ], ], 'DeleteEventDataStoreResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTrailRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'DeleteTrailResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeliveryS3Uri' => [ 'type' => 'string', 'max' => 1024, 'pattern' => 's3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/.*)?', ], 'DeliveryStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'FAILED', 'FAILED_SIGNING_FILE', 'PENDING', 'RESOURCE_NOT_FOUND', 'ACCESS_DENIED', 'ACCESS_DENIED_SIGNING_FILE', 'CANCELLED', 'UNKNOWN', ], ], 'DeregisterOrganizationDelegatedAdminRequest' => [ 'type' => 'structure', 'required' => [ 'DelegatedAdminAccountId', ], 'members' => [ 'DelegatedAdminAccountId' => [ 'shape' => 'AccountId', ], ], ], 'DeregisterOrganizationDelegatedAdminResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeQueryRequest' => [ 'type' => 'structure', 'members' => [ 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', 'deprecated' => true, 'deprecatedMessage' => 'EventDataStore is no longer required by DescribeQueryRequest', ], 'QueryId' => [ 'shape' => 'UUID', ], 'QueryAlias' => [ 'shape' => 'QueryAlias', ], ], ], 'DescribeQueryResponse' => [ 'type' => 'structure', 'members' => [ 'QueryId' => [ 'shape' => 'UUID', ], 'QueryString' => [ 'shape' => 'QueryStatement', ], 'QueryStatus' => [ 'shape' => 'QueryStatus', ], 'QueryStatistics' => [ 'shape' => 'QueryStatisticsForDescribeQuery', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'DeliveryS3Uri' => [ 'shape' => 'DeliveryS3Uri', ], 'DeliveryStatus' => [ 'shape' => 'DeliveryStatus', ], ], ], 'DescribeTrailsRequest' => [ 'type' => 'structure', 'members' => [ 'trailNameList' => [ 'shape' => 'TrailNameList', ], 'includeShadowTrails' => [ 'shape' => 'Boolean', ], ], ], 'DescribeTrailsResponse' => [ 'type' => 'structure', 'members' => [ 'trailList' => [ 'shape' => 'TrailList', ], ], ], 'Destination' => [ 'type' => 'structure', 'required' => [ 'Type', 'Location', ], 'members' => [ 'Type' => [ 'shape' => 'DestinationType', ], 'Location' => [ 'shape' => 'Location', ], ], ], 'DestinationType' => [ 'type' => 'string', 'enum' => [ 'EVENT_DATA_STORE', 'AWS_SERVICE', ], ], 'Destinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Destination', ], 'max' => 200, 'min' => 1, ], 'DisableFederationRequest' => [ 'type' => 'structure', 'required' => [ 'EventDataStore', ], 'members' => [ 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', ], ], ], 'DisableFederationResponse' => [ 'type' => 'structure', 'members' => [ 'EventDataStoreArn' => [ 'shape' => 'EventDataStoreArn', ], 'FederationStatus' => [ 'shape' => 'FederationStatus', ], ], ], 'EnableFederationRequest' => [ 'type' => 'structure', 'required' => [ 'EventDataStore', 'FederationRoleArn', ], 'members' => [ 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', ], 'FederationRoleArn' => [ 'shape' => 'FederationRoleArn', ], ], ], 'EnableFederationResponse' => [ 'type' => 'structure', 'members' => [ 'EventDataStoreArn' => [ 'shape' => 'EventDataStoreArn', ], 'FederationStatus' => [ 'shape' => 'FederationStatus', ], 'FederationRoleArn' => [ 'shape' => 'FederationRoleArn', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1000, 'min' => 4, 'pattern' => '.*', ], 'Event' => [ 'type' => 'structure', 'members' => [ 'EventId' => [ 'shape' => 'String', ], 'EventName' => [ 'shape' => 'String', ], 'ReadOnly' => [ 'shape' => 'String', ], 'AccessKeyId' => [ 'shape' => 'String', ], 'EventTime' => [ 'shape' => 'Date', ], 'EventSource' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'Resources' => [ 'shape' => 'ResourceList', ], 'CloudTrailEvent' => [ 'shape' => 'String', ], ], ], 'EventCategory' => [ 'type' => 'string', 'enum' => [ 'insight', ], ], 'EventDataStore' => [ 'type' => 'structure', 'members' => [ 'EventDataStoreArn' => [ 'shape' => 'EventDataStoreArn', ], 'Name' => [ 'shape' => 'EventDataStoreName', ], 'TerminationProtectionEnabled' => [ 'shape' => 'TerminationProtectionEnabled', 'deprecated' => true, 'deprecatedMessage' => 'TerminationProtectionEnabled is no longer returned by ListEventDataStores', ], 'Status' => [ 'shape' => 'EventDataStoreStatus', 'deprecated' => true, 'deprecatedMessage' => 'Status is no longer returned by ListEventDataStores', ], 'AdvancedEventSelectors' => [ 'shape' => 'AdvancedEventSelectors', 'deprecated' => true, 'deprecatedMessage' => 'AdvancedEventSelectors is no longer returned by ListEventDataStores', ], 'MultiRegionEnabled' => [ 'shape' => 'Boolean', 'deprecated' => true, 'deprecatedMessage' => 'MultiRegionEnabled is no longer returned by ListEventDataStores', ], 'OrganizationEnabled' => [ 'shape' => 'Boolean', 'deprecated' => true, 'deprecatedMessage' => 'OrganizationEnabled is no longer returned by ListEventDataStores', ], 'RetentionPeriod' => [ 'shape' => 'RetentionPeriod', 'deprecated' => true, 'deprecatedMessage' => 'RetentionPeriod is no longer returned by ListEventDataStores', ], 'CreatedTimestamp' => [ 'shape' => 'Date', 'deprecated' => true, 'deprecatedMessage' => 'CreatedTimestamp is no longer returned by ListEventDataStores', ], 'UpdatedTimestamp' => [ 'shape' => 'Date', 'deprecated' => true, 'deprecatedMessage' => 'UpdatedTimestamp is no longer returned by ListEventDataStores', ], ], ], 'EventDataStoreARNInvalidException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EventDataStoreAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EventDataStoreArn' => [ 'type' => 'string', 'max' => 256, 'min' => 3, 'pattern' => '^[a-zA-Z0-9._/\\-:]+$', ], 'EventDataStoreFederationEnabledException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EventDataStoreHasOngoingImportException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EventDataStoreKmsKeyId' => [ 'type' => 'string', 'max' => 350, 'min' => 1, 'pattern' => '^[a-zA-Z0-9._/\\-:]+$', ], 'EventDataStoreMaxLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EventDataStoreName' => [ 'type' => 'string', 'max' => 128, 'min' => 3, 'pattern' => '^[a-zA-Z0-9._\\-]+$', ], 'EventDataStoreNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EventDataStoreStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'ENABLED', 'PENDING_DELETION', 'STARTING_INGESTION', 'STOPPING_INGESTION', 'STOPPED_INGESTION', ], ], 'EventDataStoreTerminationProtectedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EventDataStores' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventDataStore', ], ], 'EventSelector' => [ 'type' => 'structure', 'members' => [ 'ReadWriteType' => [ 'shape' => 'ReadWriteType', ], 'IncludeManagementEvents' => [ 'shape' => 'Boolean', ], 'DataResources' => [ 'shape' => 'DataResources', ], 'ExcludeManagementEventSources' => [ 'shape' => 'ExcludeManagementEventSources', ], ], ], 'EventSelectors' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventSelector', ], ], 'EventsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', ], ], 'ExcludeManagementEventSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'FederationRoleArn' => [ 'type' => 'string', 'max' => 125, 'min' => 3, 'pattern' => '^[a-zA-Z0-9._/\\-:@=\\+,\\.]+$', ], 'FederationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLING', 'ENABLED', 'DISABLING', 'DISABLED', ], ], 'GetChannelRequest' => [ 'type' => 'structure', 'required' => [ 'Channel', ], 'members' => [ 'Channel' => [ 'shape' => 'ChannelArn', ], ], ], 'GetChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChannelArn', ], 'Name' => [ 'shape' => 'ChannelName', ], 'Source' => [ 'shape' => 'Source', ], 'SourceConfig' => [ 'shape' => 'SourceConfig', ], 'Destinations' => [ 'shape' => 'Destinations', ], 'IngestionStatus' => [ 'shape' => 'IngestionStatus', ], ], ], 'GetEventDataStoreRequest' => [ 'type' => 'structure', 'required' => [ 'EventDataStore', ], 'members' => [ 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', ], ], ], 'GetEventDataStoreResponse' => [ 'type' => 'structure', 'members' => [ 'EventDataStoreArn' => [ 'shape' => 'EventDataStoreArn', ], 'Name' => [ 'shape' => 'EventDataStoreName', ], 'Status' => [ 'shape' => 'EventDataStoreStatus', ], 'AdvancedEventSelectors' => [ 'shape' => 'AdvancedEventSelectors', ], 'MultiRegionEnabled' => [ 'shape' => 'Boolean', ], 'OrganizationEnabled' => [ 'shape' => 'Boolean', ], 'RetentionPeriod' => [ 'shape' => 'RetentionPeriod', ], 'TerminationProtectionEnabled' => [ 'shape' => 'TerminationProtectionEnabled', ], 'CreatedTimestamp' => [ 'shape' => 'Date', ], 'UpdatedTimestamp' => [ 'shape' => 'Date', ], 'KmsKeyId' => [ 'shape' => 'EventDataStoreKmsKeyId', ], 'BillingMode' => [ 'shape' => 'BillingMode', ], 'FederationStatus' => [ 'shape' => 'FederationStatus', ], 'FederationRoleArn' => [ 'shape' => 'FederationRoleArn', ], ], ], 'GetEventSelectorsRequest' => [ 'type' => 'structure', 'required' => [ 'TrailName', ], 'members' => [ 'TrailName' => [ 'shape' => 'String', ], ], ], 'GetEventSelectorsResponse' => [ 'type' => 'structure', 'members' => [ 'TrailARN' => [ 'shape' => 'String', ], 'EventSelectors' => [ 'shape' => 'EventSelectors', ], 'AdvancedEventSelectors' => [ 'shape' => 'AdvancedEventSelectors', ], ], ], 'GetImportRequest' => [ 'type' => 'structure', 'required' => [ 'ImportId', ], 'members' => [ 'ImportId' => [ 'shape' => 'UUID', ], ], ], 'GetImportResponse' => [ 'type' => 'structure', 'members' => [ 'ImportId' => [ 'shape' => 'UUID', ], 'Destinations' => [ 'shape' => 'ImportDestinations', ], 'ImportSource' => [ 'shape' => 'ImportSource', ], 'StartEventTime' => [ 'shape' => 'Date', ], 'EndEventTime' => [ 'shape' => 'Date', ], 'ImportStatus' => [ 'shape' => 'ImportStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Date', ], 'UpdatedTimestamp' => [ 'shape' => 'Date', ], 'ImportStatistics' => [ 'shape' => 'ImportStatistics', ], ], ], 'GetInsightSelectorsRequest' => [ 'type' => 'structure', 'members' => [ 'TrailName' => [ 'shape' => 'String', ], 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', ], ], ], 'GetInsightSelectorsResponse' => [ 'type' => 'structure', 'members' => [ 'TrailARN' => [ 'shape' => 'String', ], 'InsightSelectors' => [ 'shape' => 'InsightSelectors', ], 'EventDataStoreArn' => [ 'shape' => 'EventDataStoreArn', ], 'InsightsDestination' => [ 'shape' => 'EventDataStoreArn', ], ], ], 'GetQueryResultsRequest' => [ 'type' => 'structure', 'required' => [ 'QueryId', ], 'members' => [ 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', 'deprecated' => true, 'deprecatedMessage' => 'EventDataStore is no longer required by GetQueryResultsRequest', ], 'QueryId' => [ 'shape' => 'UUID', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxQueryResults' => [ 'shape' => 'MaxQueryResults', ], ], ], 'GetQueryResultsResponse' => [ 'type' => 'structure', 'members' => [ 'QueryStatus' => [ 'shape' => 'QueryStatus', ], 'QueryStatistics' => [ 'shape' => 'QueryStatistics', ], 'QueryResultRows' => [ 'shape' => 'QueryResultRows', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'GetResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'GetResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'ResourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'GetTrailRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'GetTrailResponse' => [ 'type' => 'structure', 'members' => [ 'Trail' => [ 'shape' => 'Trail', ], ], ], 'GetTrailStatusRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'GetTrailStatusResponse' => [ 'type' => 'structure', 'members' => [ 'IsLogging' => [ 'shape' => 'Boolean', ], 'LatestDeliveryError' => [ 'shape' => 'String', ], 'LatestNotificationError' => [ 'shape' => 'String', ], 'LatestDeliveryTime' => [ 'shape' => 'Date', ], 'LatestNotificationTime' => [ 'shape' => 'Date', ], 'StartLoggingTime' => [ 'shape' => 'Date', ], 'StopLoggingTime' => [ 'shape' => 'Date', ], 'LatestCloudWatchLogsDeliveryError' => [ 'shape' => 'String', ], 'LatestCloudWatchLogsDeliveryTime' => [ 'shape' => 'Date', ], 'LatestDigestDeliveryTime' => [ 'shape' => 'Date', ], 'LatestDigestDeliveryError' => [ 'shape' => 'String', ], 'LatestDeliveryAttemptTime' => [ 'shape' => 'String', ], 'LatestNotificationAttemptTime' => [ 'shape' => 'String', ], 'LatestNotificationAttemptSucceeded' => [ 'shape' => 'String', ], 'LatestDeliveryAttemptSucceeded' => [ 'shape' => 'String', ], 'TimeLoggingStarted' => [ 'shape' => 'String', ], 'TimeLoggingStopped' => [ 'shape' => 'String', ], ], ], 'ImportDestinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventDataStoreArn', ], 'max' => 1, 'min' => 1, ], 'ImportFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportFailureListItem', ], ], 'ImportFailureListItem' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'ImportFailureStatus', ], 'ErrorType' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'LastUpdatedTime' => [ 'shape' => 'Date', ], ], ], 'ImportFailureStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'RETRY', 'SUCCEEDED', ], ], 'ImportNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ImportSource' => [ 'type' => 'structure', 'required' => [ 'S3', ], 'members' => [ 'S3' => [ 'shape' => 'S3ImportSource', ], ], ], 'ImportStatistics' => [ 'type' => 'structure', 'members' => [ 'PrefixesFound' => [ 'shape' => 'Long', ], 'PrefixesCompleted' => [ 'shape' => 'Long', ], 'FilesCompleted' => [ 'shape' => 'Long', ], 'EventsCompleted' => [ 'shape' => 'Long', ], 'FailedEntries' => [ 'shape' => 'Long', ], ], ], 'ImportStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZING', 'IN_PROGRESS', 'FAILED', 'STOPPED', 'COMPLETED', ], ], 'ImportsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportsListItem', ], ], 'ImportsListItem' => [ 'type' => 'structure', 'members' => [ 'ImportId' => [ 'shape' => 'UUID', ], 'ImportStatus' => [ 'shape' => 'ImportStatus', ], 'Destinations' => [ 'shape' => 'ImportDestinations', ], 'CreatedTimestamp' => [ 'shape' => 'Date', ], 'UpdatedTimestamp' => [ 'shape' => 'Date', ], ], ], 'InactiveEventDataStoreException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InactiveQueryException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'IngestionStatus' => [ 'type' => 'structure', 'members' => [ 'LatestIngestionSuccessTime' => [ 'shape' => 'Date', ], 'LatestIngestionSuccessEventID' => [ 'shape' => 'UUID', ], 'LatestIngestionErrorCode' => [ 'shape' => 'ErrorMessage', ], 'LatestIngestionAttemptTime' => [ 'shape' => 'Date', ], 'LatestIngestionAttemptEventID' => [ 'shape' => 'UUID', ], ], ], 'InsightNotEnabledException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InsightSelector' => [ 'type' => 'structure', 'members' => [ 'InsightType' => [ 'shape' => 'InsightType', ], ], ], 'InsightSelectors' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightSelector', ], ], 'InsightType' => [ 'type' => 'string', 'enum' => [ 'ApiCallRateInsight', 'ApiErrorRateInsight', ], ], 'InsufficientDependencyServiceAccessPermissionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InsufficientEncryptionPolicyException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InsufficientS3BucketPolicyException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InsufficientSnsTopicPolicyException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Integer' => [ 'type' => 'integer', ], 'InvalidCloudWatchLogsLogGroupArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidCloudWatchLogsRoleArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDateRangeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidEventCategoryException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidEventDataStoreCategoryException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidEventDataStoreStatusException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidEventSelectorsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidHomeRegionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidImportSourceException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidInsightSelectorsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidKmsKeyIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidLookupAttributesException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidMaxResultsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidParameterCombinationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidQueryStatementException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidQueryStatusException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidS3BucketNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidS3PrefixException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidSnsTopicNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidSourceException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTagParameterException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTimeRangeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTokenException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTrailNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'KmsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'KmsKeyDisabledException' => [ 'type' => 'structure', 'members' => [], 'deprecated' => true, 'exception' => true, ], 'KmsKeyNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ListChannelsMaxResultsCount' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListChannelsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListChannelsMaxResultsCount', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => 'Channels', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListEventDataStoresMaxResultsCount' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListEventDataStoresRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'ListEventDataStoresMaxResultsCount', ], ], ], 'ListEventDataStoresResponse' => [ 'type' => 'structure', 'members' => [ 'EventDataStores' => [ 'shape' => 'EventDataStores', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImportFailuresMaxResultsCount' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListImportFailuresRequest' => [ 'type' => 'structure', 'required' => [ 'ImportId', ], 'members' => [ 'ImportId' => [ 'shape' => 'UUID', ], 'MaxResults' => [ 'shape' => 'ListImportFailuresMaxResultsCount', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImportFailuresResponse' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'ImportFailureList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImportsMaxResultsCount' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListImportsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListImportsMaxResultsCount', ], 'Destination' => [ 'shape' => 'EventDataStoreArn', ], 'ImportStatus' => [ 'shape' => 'ImportStatus', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImportsResponse' => [ 'type' => 'structure', 'members' => [ 'Imports' => [ 'shape' => 'ImportsList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPublicKeysRequest' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'Date', ], 'EndTime' => [ 'shape' => 'Date', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListPublicKeysResponse' => [ 'type' => 'structure', 'members' => [ 'PublicKeyList' => [ 'shape' => 'PublicKeyList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListQueriesMaxResultsCount' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'ListQueriesRequest' => [ 'type' => 'structure', 'required' => [ 'EventDataStore', ], 'members' => [ 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'ListQueriesMaxResultsCount', ], 'StartTime' => [ 'shape' => 'Date', ], 'EndTime' => [ 'shape' => 'Date', ], 'QueryStatus' => [ 'shape' => 'QueryStatus', ], ], ], 'ListQueriesResponse' => [ 'type' => 'structure', 'members' => [ 'Queries' => [ 'shape' => 'Queries', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceIdList', ], 'members' => [ 'ResourceIdList' => [ 'shape' => 'ResourceIdList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceTagList' => [ 'shape' => 'ResourceTagList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTrailsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTrailsResponse' => [ 'type' => 'structure', 'members' => [ 'Trails' => [ 'shape' => 'Trails', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'Location' => [ 'type' => 'string', 'max' => 1024, 'min' => 3, 'pattern' => '^[a-zA-Z0-9._/\\-:]+$', ], 'Long' => [ 'type' => 'long', ], 'LookupAttribute' => [ 'type' => 'structure', 'required' => [ 'AttributeKey', 'AttributeValue', ], 'members' => [ 'AttributeKey' => [ 'shape' => 'LookupAttributeKey', ], 'AttributeValue' => [ 'shape' => 'LookupAttributeValue', ], ], ], 'LookupAttributeKey' => [ 'type' => 'string', 'enum' => [ 'EventId', 'EventName', 'ReadOnly', 'Username', 'ResourceType', 'ResourceName', 'EventSource', 'AccessKeyId', ], ], 'LookupAttributeValue' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'LookupAttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LookupAttribute', ], ], 'LookupEventsRequest' => [ 'type' => 'structure', 'members' => [ 'LookupAttributes' => [ 'shape' => 'LookupAttributesList', ], 'StartTime' => [ 'shape' => 'Date', ], 'EndTime' => [ 'shape' => 'Date', ], 'EventCategory' => [ 'shape' => 'EventCategory', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'LookupEventsResponse' => [ 'type' => 'structure', 'members' => [ 'Events' => [ 'shape' => 'EventsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'MaxConcurrentQueriesException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaxQueryResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'MaximumNumberOfTrailsExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NextToken' => [ 'type' => 'string', ], 'NoManagementAccountSLRExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NotOrganizationManagementAccountException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NotOrganizationMasterAccountException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'OperationNotPermittedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Operator' => [ 'type' => 'list', 'member' => [ 'shape' => 'OperatorValue', ], 'min' => 1, ], 'OperatorValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '.+', ], 'OrganizationNotInAllFeaturesModeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'OrganizationsNotInUseException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 1000, 'min' => 4, 'pattern' => '.*', ], 'PublicKey' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'ByteBuffer', ], 'ValidityStartTime' => [ 'shape' => 'Date', ], 'ValidityEndTime' => [ 'shape' => 'Date', ], 'Fingerprint' => [ 'shape' => 'String', ], ], ], 'PublicKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PublicKey', ], ], 'PutEventSelectorsRequest' => [ 'type' => 'structure', 'required' => [ 'TrailName', ], 'members' => [ 'TrailName' => [ 'shape' => 'String', ], 'EventSelectors' => [ 'shape' => 'EventSelectors', ], 'AdvancedEventSelectors' => [ 'shape' => 'AdvancedEventSelectors', ], ], ], 'PutEventSelectorsResponse' => [ 'type' => 'structure', 'members' => [ 'TrailARN' => [ 'shape' => 'String', ], 'EventSelectors' => [ 'shape' => 'EventSelectors', ], 'AdvancedEventSelectors' => [ 'shape' => 'AdvancedEventSelectors', ], ], ], 'PutInsightSelectorsRequest' => [ 'type' => 'structure', 'required' => [ 'InsightSelectors', ], 'members' => [ 'TrailName' => [ 'shape' => 'String', ], 'InsightSelectors' => [ 'shape' => 'InsightSelectors', ], 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', ], 'InsightsDestination' => [ 'shape' => 'EventDataStoreArn', ], ], ], 'PutInsightSelectorsResponse' => [ 'type' => 'structure', 'members' => [ 'TrailARN' => [ 'shape' => 'String', ], 'InsightSelectors' => [ 'shape' => 'InsightSelectors', ], 'EventDataStoreArn' => [ 'shape' => 'EventDataStoreArn', ], 'InsightsDestination' => [ 'shape' => 'EventDataStoreArn', ], ], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'ResourcePolicy', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'ResourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'ResourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'Queries' => [ 'type' => 'list', 'member' => [ 'shape' => 'Query', ], ], 'Query' => [ 'type' => 'structure', 'members' => [ 'QueryId' => [ 'shape' => 'UUID', ], 'QueryStatus' => [ 'shape' => 'QueryStatus', ], 'CreationTime' => [ 'shape' => 'Date', ], ], ], 'QueryAlias' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z][a-zA-Z0-9._\\-]*$', ], 'QueryIdNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'QueryParameter' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*', ], 'QueryParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryParameter', ], 'max' => 10, 'min' => 1, ], 'QueryResultColumn' => [ 'type' => 'map', 'key' => [ 'shape' => 'QueryResultKey', ], 'value' => [ 'shape' => 'QueryResultValue', ], ], 'QueryResultKey' => [ 'type' => 'string', ], 'QueryResultRow' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryResultColumn', ], ], 'QueryResultRows' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryResultRow', ], ], 'QueryResultValue' => [ 'type' => 'string', ], 'QueryStatement' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, 'pattern' => '(?s).*', ], 'QueryStatistics' => [ 'type' => 'structure', 'members' => [ 'ResultsCount' => [ 'shape' => 'Integer', ], 'TotalResultsCount' => [ 'shape' => 'Integer', ], 'BytesScanned' => [ 'shape' => 'Long', ], ], ], 'QueryStatisticsForDescribeQuery' => [ 'type' => 'structure', 'members' => [ 'EventsMatched' => [ 'shape' => 'Long', ], 'EventsScanned' => [ 'shape' => 'Long', ], 'BytesScanned' => [ 'shape' => 'Long', ], 'ExecutionTimeInMillis' => [ 'shape' => 'Integer', ], 'CreationTime' => [ 'shape' => 'Date', ], ], ], 'QueryStatus' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'RUNNING', 'FINISHED', 'FAILED', 'CANCELLED', 'TIMED_OUT', ], ], 'ReadWriteType' => [ 'type' => 'string', 'enum' => [ 'ReadOnly', 'WriteOnly', 'All', ], ], 'RegisterOrganizationDelegatedAdminRequest' => [ 'type' => 'structure', 'required' => [ 'MemberAccountId', ], 'members' => [ 'MemberAccountId' => [ 'shape' => 'AccountId', ], ], ], 'RegisterOrganizationDelegatedAdminResponse' => [ 'type' => 'structure', 'members' => [], ], 'RemoveTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'TagsList', ], 'members' => [ 'ResourceId' => [ 'shape' => 'String', ], 'TagsList' => [ 'shape' => 'TagsList', ], ], ], 'RemoveTagsResponse' => [ 'type' => 'structure', 'members' => [], ], 'Resource' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'String', ], 'ResourceName' => [ 'shape' => 'String', ], ], ], 'ResourceARNNotValidException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceArn' => [ 'type' => 'string', 'max' => 256, 'min' => 3, 'pattern' => '^[a-zA-Z0-9._/\\-:]+$', ], 'ResourceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourcePolicy' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, ], 'ResourcePolicyNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourcePolicyNotValidException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceTag' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'String', ], 'TagsList' => [ 'shape' => 'TagsList', ], ], ], 'ResourceTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTag', ], ], 'ResourceTypeNotSupportedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RestoreEventDataStoreRequest' => [ 'type' => 'structure', 'required' => [ 'EventDataStore', ], 'members' => [ 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', ], ], ], 'RestoreEventDataStoreResponse' => [ 'type' => 'structure', 'members' => [ 'EventDataStoreArn' => [ 'shape' => 'EventDataStoreArn', ], 'Name' => [ 'shape' => 'EventDataStoreName', ], 'Status' => [ 'shape' => 'EventDataStoreStatus', ], 'AdvancedEventSelectors' => [ 'shape' => 'AdvancedEventSelectors', ], 'MultiRegionEnabled' => [ 'shape' => 'Boolean', ], 'OrganizationEnabled' => [ 'shape' => 'Boolean', ], 'RetentionPeriod' => [ 'shape' => 'RetentionPeriod', ], 'TerminationProtectionEnabled' => [ 'shape' => 'TerminationProtectionEnabled', ], 'CreatedTimestamp' => [ 'shape' => 'Date', ], 'UpdatedTimestamp' => [ 'shape' => 'Date', ], 'KmsKeyId' => [ 'shape' => 'EventDataStoreKmsKeyId', ], 'BillingMode' => [ 'shape' => 'BillingMode', ], ], ], 'RetentionPeriod' => [ 'type' => 'integer', 'max' => 3653, 'min' => 7, ], 'S3BucketDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'S3ImportSource' => [ 'type' => 'structure', 'required' => [ 'S3LocationUri', 'S3BucketRegion', 'S3BucketAccessRoleArn', ], 'members' => [ 'S3LocationUri' => [ 'shape' => 'String', ], 'S3BucketRegion' => [ 'shape' => 'String', ], 'S3BucketAccessRoleArn' => [ 'shape' => 'String', ], ], ], 'SelectorField' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[\\w|\\d|\\.|_]+', ], 'SelectorName' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '.*', ], 'Source' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'SourceConfig' => [ 'type' => 'structure', 'members' => [ 'ApplyToAllRegions' => [ 'shape' => 'Boolean', ], 'AdvancedEventSelectors' => [ 'shape' => 'AdvancedEventSelectors', ], ], ], 'StartEventDataStoreIngestionRequest' => [ 'type' => 'structure', 'required' => [ 'EventDataStore', ], 'members' => [ 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', ], ], ], 'StartEventDataStoreIngestionResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartImportRequest' => [ 'type' => 'structure', 'members' => [ 'Destinations' => [ 'shape' => 'ImportDestinations', ], 'ImportSource' => [ 'shape' => 'ImportSource', ], 'StartEventTime' => [ 'shape' => 'Date', ], 'EndEventTime' => [ 'shape' => 'Date', ], 'ImportId' => [ 'shape' => 'UUID', ], ], ], 'StartImportResponse' => [ 'type' => 'structure', 'members' => [ 'ImportId' => [ 'shape' => 'UUID', ], 'Destinations' => [ 'shape' => 'ImportDestinations', ], 'ImportSource' => [ 'shape' => 'ImportSource', ], 'StartEventTime' => [ 'shape' => 'Date', ], 'EndEventTime' => [ 'shape' => 'Date', ], 'ImportStatus' => [ 'shape' => 'ImportStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Date', ], 'UpdatedTimestamp' => [ 'shape' => 'Date', ], ], ], 'StartLoggingRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'StartLoggingResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartQueryRequest' => [ 'type' => 'structure', 'members' => [ 'QueryStatement' => [ 'shape' => 'QueryStatement', ], 'DeliveryS3Uri' => [ 'shape' => 'DeliveryS3Uri', ], 'QueryAlias' => [ 'shape' => 'QueryAlias', ], 'QueryParameters' => [ 'shape' => 'QueryParameters', ], ], ], 'StartQueryResponse' => [ 'type' => 'structure', 'members' => [ 'QueryId' => [ 'shape' => 'UUID', ], ], ], 'StopEventDataStoreIngestionRequest' => [ 'type' => 'structure', 'required' => [ 'EventDataStore', ], 'members' => [ 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', ], ], ], 'StopEventDataStoreIngestionResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopImportRequest' => [ 'type' => 'structure', 'required' => [ 'ImportId', ], 'members' => [ 'ImportId' => [ 'shape' => 'UUID', ], ], ], 'StopImportResponse' => [ 'type' => 'structure', 'members' => [ 'ImportId' => [ 'shape' => 'UUID', ], 'ImportSource' => [ 'shape' => 'ImportSource', ], 'Destinations' => [ 'shape' => 'ImportDestinations', ], 'ImportStatus' => [ 'shape' => 'ImportStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Date', ], 'UpdatedTimestamp' => [ 'shape' => 'Date', ], 'StartEventTime' => [ 'shape' => 'Date', ], 'EndEventTime' => [ 'shape' => 'Date', ], 'ImportStatistics' => [ 'shape' => 'ImportStatistics', ], ], ], 'StopLoggingRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'StopLoggingResponse' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'TagsLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, ], 'TerminationProtectionEnabled' => [ 'type' => 'boolean', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Trail' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'S3BucketName' => [ 'shape' => 'String', ], 'S3KeyPrefix' => [ 'shape' => 'String', ], 'SnsTopicName' => [ 'shape' => 'String', 'deprecated' => true, ], 'SnsTopicARN' => [ 'shape' => 'String', ], 'IncludeGlobalServiceEvents' => [ 'shape' => 'Boolean', ], 'IsMultiRegionTrail' => [ 'shape' => 'Boolean', ], 'HomeRegion' => [ 'shape' => 'String', ], 'TrailARN' => [ 'shape' => 'String', ], 'LogFileValidationEnabled' => [ 'shape' => 'Boolean', ], 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'String', ], 'CloudWatchLogsRoleArn' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'HasCustomEventSelectors' => [ 'shape' => 'Boolean', ], 'HasInsightSelectors' => [ 'shape' => 'Boolean', ], 'IsOrganizationTrail' => [ 'shape' => 'Boolean', ], ], ], 'TrailAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TrailInfo' => [ 'type' => 'structure', 'members' => [ 'TrailARN' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'HomeRegion' => [ 'shape' => 'String', ], ], ], 'TrailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Trail', ], ], 'TrailNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TrailNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TrailNotProvidedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Trails' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrailInfo', ], ], 'UUID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-f0-9\\-]+$', ], 'UnsupportedOperationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'UpdateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'Channel', ], 'members' => [ 'Channel' => [ 'shape' => 'ChannelArn', ], 'Destinations' => [ 'shape' => 'Destinations', ], 'Name' => [ 'shape' => 'ChannelName', ], ], ], 'UpdateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChannelArn', ], 'Name' => [ 'shape' => 'ChannelName', ], 'Source' => [ 'shape' => 'Source', ], 'Destinations' => [ 'shape' => 'Destinations', ], ], ], 'UpdateEventDataStoreRequest' => [ 'type' => 'structure', 'required' => [ 'EventDataStore', ], 'members' => [ 'EventDataStore' => [ 'shape' => 'EventDataStoreArn', ], 'Name' => [ 'shape' => 'EventDataStoreName', ], 'AdvancedEventSelectors' => [ 'shape' => 'AdvancedEventSelectors', ], 'MultiRegionEnabled' => [ 'shape' => 'Boolean', ], 'OrganizationEnabled' => [ 'shape' => 'Boolean', ], 'RetentionPeriod' => [ 'shape' => 'RetentionPeriod', ], 'TerminationProtectionEnabled' => [ 'shape' => 'TerminationProtectionEnabled', ], 'KmsKeyId' => [ 'shape' => 'EventDataStoreKmsKeyId', ], 'BillingMode' => [ 'shape' => 'BillingMode', ], ], ], 'UpdateEventDataStoreResponse' => [ 'type' => 'structure', 'members' => [ 'EventDataStoreArn' => [ 'shape' => 'EventDataStoreArn', ], 'Name' => [ 'shape' => 'EventDataStoreName', ], 'Status' => [ 'shape' => 'EventDataStoreStatus', ], 'AdvancedEventSelectors' => [ 'shape' => 'AdvancedEventSelectors', ], 'MultiRegionEnabled' => [ 'shape' => 'Boolean', ], 'OrganizationEnabled' => [ 'shape' => 'Boolean', ], 'RetentionPeriod' => [ 'shape' => 'RetentionPeriod', ], 'TerminationProtectionEnabled' => [ 'shape' => 'TerminationProtectionEnabled', ], 'CreatedTimestamp' => [ 'shape' => 'Date', ], 'UpdatedTimestamp' => [ 'shape' => 'Date', ], 'KmsKeyId' => [ 'shape' => 'EventDataStoreKmsKeyId', ], 'BillingMode' => [ 'shape' => 'BillingMode', ], 'FederationStatus' => [ 'shape' => 'FederationStatus', ], 'FederationRoleArn' => [ 'shape' => 'FederationRoleArn', ], ], ], 'UpdateTrailRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'S3BucketName' => [ 'shape' => 'String', ], 'S3KeyPrefix' => [ 'shape' => 'String', ], 'SnsTopicName' => [ 'shape' => 'String', ], 'IncludeGlobalServiceEvents' => [ 'shape' => 'Boolean', ], 'IsMultiRegionTrail' => [ 'shape' => 'Boolean', ], 'EnableLogFileValidation' => [ 'shape' => 'Boolean', ], 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'String', ], 'CloudWatchLogsRoleArn' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'IsOrganizationTrail' => [ 'shape' => 'Boolean', ], ], ], 'UpdateTrailResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'S3BucketName' => [ 'shape' => 'String', ], 'S3KeyPrefix' => [ 'shape' => 'String', ], 'SnsTopicName' => [ 'shape' => 'String', 'deprecated' => true, ], 'SnsTopicARN' => [ 'shape' => 'String', ], 'IncludeGlobalServiceEvents' => [ 'shape' => 'Boolean', ], 'IsMultiRegionTrail' => [ 'shape' => 'Boolean', ], 'TrailARN' => [ 'shape' => 'String', ], 'LogFileValidationEnabled' => [ 'shape' => 'Boolean', ], 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'String', ], 'CloudWatchLogsRoleArn' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'IsOrganizationTrail' => [ 'shape' => 'Boolean', ], ], ], ],];
