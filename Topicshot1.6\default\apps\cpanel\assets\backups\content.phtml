<div class="cp-app-container" data-app="backups">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Backups
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Manage site (files & DB) backups
                    </h2>
                </div>
                <div class="body">
                    <div class="inline-alertbox-wrapper">
                        <div class="inline-alertbox warning">
                            <div class="icon">
                                <?php echo cl_ficon("warning"); ?>
                            </div>
                            <div class="alert-message">
                                <p>
                                    Please note: Before you start the process of creating a backup copy of your site, the module of PHP (ZipArchive) must be installed on the server, and also make sure that the root directory of your site is writable by the user of the HTTP web server (www-data), otherwise, the process will fail!
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="inline-alertbox-wrapper">
                        <div class="inline-alertbox info">
                            <div class="icon">
                                <?php echo cl_ficon("info"); ?>
                            </div>
                            <div class="alert-message">
                                <p>
                                    Please note: Creating a backup may take some time depending on the amount of content on your site, therefore, after clicking the (Create backup) button, wait for the process to complete without leaving the page!
                                </p>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <form class="form" data-an="form">
                        <div class="form-group">
                            <?php if (not_empty($cl['config']['last_backup'])): ?>
                                <h5>
                                    <span>
                                        Last backup
                                    </span>
                                    <time data-an="last-backup-date">
                                        (<?php echo date('d F, Y - h:m', $cl['config']['last_backup']); ?>)
                                    </time>    
                                </h5>
                            <?php else: ?>
                                <h5>
                                    <span>Last backup</span>
                                    <time data-an="last-backup-date">(00/00/0000 - 00:00)</time>
                                </h5>
                            <?php endif; ?>
                        </div>
                        <div class="form-group no-mb">
                            <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                Create backup
                            </button>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/backups/scripts/app_master_script'); ?>