<div class="cp-app-container" data-app="general-settings">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                General app settings
            </h1>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
            <div class="card">
                <div class="header">
                    <h2>
                        General settings
                    </h2>
                </div>
                <div class="body">
                    <form class="form" data-an="form">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group" data-an="name-input">
                                    <label>
                                        Site name
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config name%}" name="name" type="text" class="form-control" placeholder="Enter the name of your site">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group" data-an="title-input">
                                    <label>
                                        Site title
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config title%}" name="title" type="text" class="form-control" placeholder="Enter the title of your site">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group" data-an="email-input">
                                    <label>
                                        E-mail address
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config email%}" name="email" class="form-control" placeholder="Enter the email address of your site">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group" data-an="keywords-input">
                                    <label>
                                        SEO keywords
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config keywords%}" name="keywords" class="form-control" placeholder="Enter SEO keywords of your site">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group" data-an="description-input">
                                    <label>
                                        Site description
                                    </label>
                                    <div class="form-line">
                                        <textarea name="description" class="form-control" rows="2" placeholder="Enter a description of your site">{%config description%}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 no-mb">
                                <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                    Save changes
                                </button>
                            </div>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
            <div class="card">
                <div class="header">
                    <h2>
                        Site logo settings
                    </h2>
                </div>
                <div class="body">
                    <form class="form" data-an="logo-form">
                        <div class="form-group">
                            <label>
                                Select site LOGO file
                            </label>
                            <div class="form-line">
                                <div class="form-file-input">
                                    <input type="file" name="site_logo" required type="text" class="form-control-file" accept="image/png">

                                    <span class="form-file-input__icon">
                                        <?php echo cl_ficon("arrow_upload"); ?>
                                    </span>
                                </div>
                                <small class="info-feedback">
                                    Here you can upload a logo for your site. The recommended logo size is <b>512x512 pixels</b>. Click on the "Select file" button and select the desired file from your computer. Make sure the file is in supported format (eg. PNG)
                                </small>
                            </div>
                        </div>
                        <div class="form-group no-mb">
                            <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                Save changes
                            </button>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
            <div class="card">
                <div class="header">
                    <h2>
                        Site favicon settings
                    </h2>
                </div>
                <div class="body">
                    <form class="form" data-an="favicon-form">
                        <div class="form-group">
                            <label>
                                Select site FAV-ICON file
                            </label>
                            <div class="form-line">
                                <div class="form-file-input">
                                    <input type="file" name="site_ficon" required type="text" class="form-control-file" accept="image/png">

                                    <span class="form-file-input__icon">
                                        <?php echo cl_ficon("arrow_upload"); ?>
                                    </span>
                                </div>
                                <small class="info-feedback">
                                    Here you can upload a favicon for your site. The recommended favicon size is <b>32x32 pixels</b>, file must be in <b>PNG or ICO</b> format
                                </small>
                            </div>
                        </div>
                        <div class="form-group no-mb">
                            <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                Save changes
                            </button>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
            <div class="card">
                <div class="header">
                    <h2>
                        Manage general site settings
                    </h2>
                </div>
                <div class="body">
                    <form class="form" data-an="form">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group" data-an="acc_validation-input">
                                    <label>
                                        Sign-up confirmation status
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="acc_validation" class="form-control">
                                            <option <?php if($cl['config']['acc_validation'] == 'on') {echo('selected');} ?> value="on">On</option>
                                            <option <?php if($cl['config']['acc_validation'] == 'off') {echo('selected');} ?> value="off">Off</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group" data-an="signup_conf_system-input">
                                    <label>
                                        Sign-up confirmation system
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="signup_conf_system" class="form-control">
                                            <option <?php if($cl['config']['signup_conf_system'] == 'email') {echo('selected');} ?> value="email">With email code</option>
                                            <option <?php if($cl['config']['signup_conf_system'] == 'phone') {echo('selected');} ?> value="phone">With SMS code</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-12">
                                <div class="form-group" data-an="default_sms_provider-input">
                                    <label>
                                        Default SMS provider
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="default_sms_provider" class="form-control">
                                            <option <?php if($cl['config']['default_sms_provider'] == 'twilio') {echo('selected');} ?> value="twilio">Twilio</option>
                                            <option <?php if($cl['config']['default_sms_provider'] == 'infobip') {echo('selected');} ?> value="infobip">InfoBip</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="inline-alertbox-wrapper">
                                    <div class="inline-alertbox info">
                                        <div class="icon">
                                            <?php echo cl_ficon("info"); ?>
                                        </div>
                                        <div class="alert-message">
                                            <p>
                                                Please be aware that enabling the user sign-up confirmation system requires the configuration of an SMTP server to facilitate the sending of verification code emails from your website. 
                                                The configuration for SMTP can be found in the following section: <a href="<?php echo cl_link("admin_panel/gen_settings"); ?>">SMTP settings</a>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="inline-alertbox-wrapper">
                                    <div class="inline-alertbox info">
                                        <div class="icon">
                                            <?php echo cl_ficon("info"); ?>
                                        </div>
                                        <div class="alert-message">
                                            <p>
                                                To enable user registration confirmation via phone numbers, it's imperative to configure the SMS sending system by inputting the API keys. Specifically, you should insert the API keys for your default SMS sending system within one of the following sections: <a href="<?php echo cl_link("admin_panel/twilio_settings"); ?>">Twilio API Settings</a>, <a href="<?php echo cl_link("admin_panel/infobip_settings"); ?>">Infobip API Settings</a>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <br>
                        <hr>
                        <br>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group" data-an="giphy_api_key-input">
                                    <label>
                                        Giphy api
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config giphy_api_key%}" name="giphy_api_key" class="form-control" placeholder="Enter Giphy GIFS API Key">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group" data-an="ffmpeg_binary-input">
                                    <label>
                                        FFMPEG binary
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config ffmpeg_binary%}" name="ffmpeg_binary" class="form-control" placeholder="Enter the path to the FFMPEG binary file">
                                    </div>
                                    <small class="info-feedback">
                                        If the ffmpeg system is installed on your server, then in this field you need to put the path to it, otherwise leave the default value
                                    </small>
                                    <hr>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group" data-an="site_currency-input">
                                    <label>
                                        Default site currency
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="site_currency" data-size="5" class="form-control">
                                            <?php foreach ($cl['site_currencies'] as $k => $v): ?>
                                                <option value="<?php echo($k); ?>" <?php if($k == $cl['config']['site_currency']) { echo('selected'); } ?>>
                                                    <?php echo($v['name']); ?> (<?php echo($v['symbol']); ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="currency_symbol_pos-input">
                                    <label>
                                        Currency symbol position
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="currency_symbol_pos" data-size="2" class="form-control">
                                            <option value="before" <?php if($cl['config']['currency_symbol_pos'] == "before") { echo('selected'); } ?>>
                                                Before
                                            </option>
                                            <option value="after" <?php if($cl['config']['currency_symbol_pos'] == "after") { echo('selected'); } ?>>
                                                After
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="wallet_min_amount-input">
                                    <label>
                                        Wallet topup min amount
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config wallet_min_amount%}" name="wallet_min_amount" class="form-control" placeholder="Enter the min user wallet topup amount">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-group" data-an="cont_sales_comrate-input">
                                    <label>
                                        Content sales commission (%)
                                    </label>
                                    <div class="form-line">
                                        <input type="number" step="any" min="0" max="99" value="{%config cont_sales_comrate%}" name="cont_sales_comrate" class="form-control" placeholder="Enter content sales commission rate in (%)">
                                    </div>
                                    <small class="info-feedback">
                                        <b class="col-red">IMPORTANT!</b>
                                        <span class="d-block">
                                            Please note that the value of this field must be numeric and contain from 1% to 100% the commission percentage from subscriptions to paid profiles, more precisely the percentage commission from the sale of content
                                        </span>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group" data-an="withdrawal_payment_methods-input">
                                    <label>
                                        Wallet withdrawal payment methods
                                    </label>
                                    <div class="form-line">
                                        <textarea name="withdrawal_payment_methods" rows="4" class="form-control" placeholder="E.g: PayPal, Google Pay, Skrill, Bank Transfer, etc..">{%config withdrawal_payment_methods%}</textarea>
                                    </div>
                                    <small class="info-feedback">
                                        <b class="col-red">IMPORTANT!</b>
                                        <span class="d-block">
                                            Please make sure to enter the names separated by commas, only those payment systems through which you can withdraw funds from the wallets of your users 
                                        </span>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group" data-an="page_update_interval-input">
                                    <label>
                                        Page update time interval
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config page_update_interval%}" name="page_update_interval" class="form-control" placeholder="Enter the time interval for page update. Default (30) seconds">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="chat_update_interval-input">
                                    <label>
                                        Chat update time interval
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config chat_update_interval%}" name="chat_update_interval" class="form-control" placeholder="Enter the time interval for chat update. Default (5) seconds">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="country_id-input">
                                    <label>
                                        User default country
                                    </label>
                                    <div class="form-line form-select">
                                        <select data-size="5" name="country_id" class="form-control">
                                            <?php foreach ($cl['countries'] as $k => $v): ?>
                                                <option value="<?php echo($k); ?>" <?php if($k == $cl['config']['country_id']) { echo('selected'); } ?>>
                                                    <?php echo($v); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group" data-an="max_post_len-input">
                                    <label>
                                        Maximum post character length
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config max_post_len%}" name="max_post_len" class="form-control" placeholder="Enter the maximum length of the post text (for example, 600).">
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-12">
                                <div class="form-group" data-an="post_arec_length-input">
                                    <label>
                                        Max post audio record length
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config post_arec_length%}" name="post_arec_length" class="form-control" placeholder="Enter the post audio record max length. Default (30) seconds">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group" data-an="max_upload_size-input">
                                    <label>
                                        Max upload size
                                    </label>
                                    <div class="form-line form-select">
                                        <select data-size="5" name="max_upload_size" class="form-control">
                                            <option value="2097152" <?php echo (($cl['config']['max_upload_size'] == '2097152') ? 'selected': ''); ?>>2 MB</option>
                                            <option value="6291456" <?php echo (($cl['config']['max_upload_size'] == '6291456') ? 'selected': ''); ?>>6 MB</option>
                                            <option value="12582912" <?php echo (($cl['config']['max_upload_size'] == '12582912') ? 'selected': ''); ?>>12 MB</option>
                                            <option value="25165824" <?php echo (($cl['config']['max_upload_size'] == '25165824') ? 'selected': ''); ?>>24 MB</option>
                                            <option value="50331648" <?php echo (($cl['config']['max_upload_size'] == '50331648') ? 'selected': ''); ?>>48 MB</option>
                                            <option value="100663296" <?php echo (($cl['config']['max_upload_size'] == '100663296') ? 'selected': ''); ?>>96 MB</option>
                                            <option value="268435456" <?php echo (($cl['config']['max_upload_size'] == '268435456')? 'selected': ''); ?>>256 MB</option>
                                            <option value="536870912" <?php echo (($cl['config']['max_upload_size'] == '536870912') ? 'selected': ''); ?>>512 MB</option>
                                            <option value="1073741824" <?php echo (($cl['config']['max_upload_size'] == '1073741824') ? 'selected': ''); ?>>1 GB</option>
                                            <option value="5368709120" <?php echo (($cl['config']['max_upload_size'] == '5368709120') ? 'selected': ''); ?>>5 GB</option>
                                            <option value="10737418240" <?php echo (($cl['config']['max_upload_size'] == '10737418240') ? 'selected': ''); ?>>10 GB</option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        Select the maximum limit for uploaded media files such as videos, images,  etc. If the user-selected media file exceeds the limit you set, they will receive an error message and will not be able to upload the file.
                                    </small>
                                    <hr>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group" data-an="google_analytics-input">
                                    <label>
                                        Google analytics
                                    </label>
                                    <div class="form-line">
                                        <textarea name="google_analytics" class="form-control" rows="5" placeholder="Put your Google Analytics JS code here">{%config google_analytics%}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 no-mb">
                                <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                    Save changes
                                </button>
                            </div>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php echo cl_template('cpanel/assets/gen_settings/scripts/app_master_script'); ?>
