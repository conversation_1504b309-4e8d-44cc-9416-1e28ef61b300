<?php
// This file was auto-generated from sdk-root/src/data/macie2/2020-01-01/api-2.json
return [ 'metadata' => [ 'apiVersion' => '2020-01-01', 'endpointPrefix' => 'macie2', 'signingName' => 'macie2', 'serviceFullName' => 'Amazon Macie 2', 'serviceId' => 'Macie2', 'protocol' => 'rest-json', 'jsonVersion' => '1.1', 'uid' => 'macie2-2020-01-01', 'signatureVersion' => 'v4', ], 'operations' => [ 'AcceptInvitation' => [ 'name' => 'AcceptInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/invitations/accept', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AcceptInvitationRequest', ], 'output' => [ 'shape' => 'AcceptInvitationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'BatchGetCustomDataIdentifiers' => [ 'name' => 'BatchGetCustomDataIdentifiers', 'http' => [ 'method' => 'POST', 'requestUri' => '/custom-data-identifiers/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetCustomDataIdentifiersRequest', ], 'output' => [ 'shape' => 'BatchGetCustomDataIdentifiersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateAllowList' => [ 'name' => 'CreateAllowList', 'http' => [ 'method' => 'POST', 'requestUri' => '/allow-lists', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAllowListRequest', ], 'output' => [ 'shape' => 'CreateAllowListResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateClassificationJob' => [ 'name' => 'CreateClassificationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateClassificationJobRequest', ], 'output' => [ 'shape' => 'CreateClassificationJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateCustomDataIdentifier' => [ 'name' => 'CreateCustomDataIdentifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/custom-data-identifiers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateCustomDataIdentifierRequest', ], 'output' => [ 'shape' => 'CreateCustomDataIdentifierResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateFindingsFilter' => [ 'name' => 'CreateFindingsFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/findingsfilters', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFindingsFilterRequest', ], 'output' => [ 'shape' => 'CreateFindingsFilterResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateInvitations' => [ 'name' => 'CreateInvitations', 'http' => [ 'method' => 'POST', 'requestUri' => '/invitations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateInvitationsRequest', ], 'output' => [ 'shape' => 'CreateInvitationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateMember' => [ 'name' => 'CreateMember', 'http' => [ 'method' => 'POST', 'requestUri' => '/members', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMemberRequest', ], 'output' => [ 'shape' => 'CreateMemberResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateSampleFindings' => [ 'name' => 'CreateSampleFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings/sample', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSampleFindingsRequest', ], 'output' => [ 'shape' => 'CreateSampleFindingsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeclineInvitations' => [ 'name' => 'DeclineInvitations', 'http' => [ 'method' => 'POST', 'requestUri' => '/invitations/decline', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeclineInvitationsRequest', ], 'output' => [ 'shape' => 'DeclineInvitationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteAllowList' => [ 'name' => 'DeleteAllowList', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/allow-lists/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAllowListRequest', ], 'output' => [ 'shape' => 'DeleteAllowListResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteCustomDataIdentifier' => [ 'name' => 'DeleteCustomDataIdentifier', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/custom-data-identifiers/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCustomDataIdentifierRequest', ], 'output' => [ 'shape' => 'DeleteCustomDataIdentifierResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteFindingsFilter' => [ 'name' => 'DeleteFindingsFilter', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/findingsfilters/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFindingsFilterRequest', ], 'output' => [ 'shape' => 'DeleteFindingsFilterResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteInvitations' => [ 'name' => 'DeleteInvitations', 'http' => [ 'method' => 'POST', 'requestUri' => '/invitations/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteInvitationsRequest', ], 'output' => [ 'shape' => 'DeleteInvitationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteMember' => [ 'name' => 'DeleteMember', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/members/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMemberRequest', ], 'output' => [ 'shape' => 'DeleteMemberResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DescribeBuckets' => [ 'name' => 'DescribeBuckets', 'http' => [ 'method' => 'POST', 'requestUri' => '/datasources/s3', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBucketsRequest', ], 'output' => [ 'shape' => 'DescribeBucketsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DescribeClassificationJob' => [ 'name' => 'DescribeClassificationJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeClassificationJobRequest', ], 'output' => [ 'shape' => 'DescribeClassificationJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DescribeOrganizationConfiguration' => [ 'name' => 'DescribeOrganizationConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/admin/configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DisableMacie' => [ 'name' => 'DisableMacie', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/macie', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisableMacieRequest', ], 'output' => [ 'shape' => 'DisableMacieResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DisableOrganizationAdminAccount' => [ 'name' => 'DisableOrganizationAdminAccount', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/admin', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisableOrganizationAdminAccountRequest', ], 'output' => [ 'shape' => 'DisableOrganizationAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DisassociateFromAdministratorAccount' => [ 'name' => 'DisassociateFromAdministratorAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/administrator/disassociate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateFromAdministratorAccountRequest', ], 'output' => [ 'shape' => 'DisassociateFromAdministratorAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DisassociateFromMasterAccount' => [ 'name' => 'DisassociateFromMasterAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/master/disassociate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateFromMasterAccountRequest', ], 'output' => [ 'shape' => 'DisassociateFromMasterAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DisassociateMember' => [ 'name' => 'DisassociateMember', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/disassociate/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateMemberRequest', ], 'output' => [ 'shape' => 'DisassociateMemberResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'EnableMacie' => [ 'name' => 'EnableMacie', 'http' => [ 'method' => 'POST', 'requestUri' => '/macie', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EnableMacieRequest', ], 'output' => [ 'shape' => 'EnableMacieResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'EnableOrganizationAdminAccount' => [ 'name' => 'EnableOrganizationAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/admin', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EnableOrganizationAdminAccountRequest', ], 'output' => [ 'shape' => 'EnableOrganizationAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetAdministratorAccount' => [ 'name' => 'GetAdministratorAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/administrator', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAdministratorAccountRequest', ], 'output' => [ 'shape' => 'GetAdministratorAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetAllowList' => [ 'name' => 'GetAllowList', 'http' => [ 'method' => 'GET', 'requestUri' => '/allow-lists/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAllowListRequest', ], 'output' => [ 'shape' => 'GetAllowListResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetAutomatedDiscoveryConfiguration' => [ 'name' => 'GetAutomatedDiscoveryConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/automated-discovery/configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAutomatedDiscoveryConfigurationRequest', ], 'output' => [ 'shape' => 'GetAutomatedDiscoveryConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetBucketStatistics' => [ 'name' => 'GetBucketStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/datasources/s3/statistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBucketStatisticsRequest', ], 'output' => [ 'shape' => 'GetBucketStatisticsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetClassificationExportConfiguration' => [ 'name' => 'GetClassificationExportConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/classification-export-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetClassificationExportConfigurationRequest', ], 'output' => [ 'shape' => 'GetClassificationExportConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetClassificationScope' => [ 'name' => 'GetClassificationScope', 'http' => [ 'method' => 'GET', 'requestUri' => '/classification-scopes/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetClassificationScopeRequest', ], 'output' => [ 'shape' => 'GetClassificationScopeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetCustomDataIdentifier' => [ 'name' => 'GetCustomDataIdentifier', 'http' => [ 'method' => 'GET', 'requestUri' => '/custom-data-identifiers/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCustomDataIdentifierRequest', ], 'output' => [ 'shape' => 'GetCustomDataIdentifierResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetFindingStatistics' => [ 'name' => 'GetFindingStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings/statistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingStatisticsRequest', ], 'output' => [ 'shape' => 'GetFindingStatisticsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetFindings' => [ 'name' => 'GetFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings/describe', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingsRequest', ], 'output' => [ 'shape' => 'GetFindingsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetFindingsFilter' => [ 'name' => 'GetFindingsFilter', 'http' => [ 'method' => 'GET', 'requestUri' => '/findingsfilters/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingsFilterRequest', ], 'output' => [ 'shape' => 'GetFindingsFilterResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetFindingsPublicationConfiguration' => [ 'name' => 'GetFindingsPublicationConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/findings-publication-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingsPublicationConfigurationRequest', ], 'output' => [ 'shape' => 'GetFindingsPublicationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetInvitationsCount' => [ 'name' => 'GetInvitationsCount', 'http' => [ 'method' => 'GET', 'requestUri' => '/invitations/count', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetInvitationsCountRequest', ], 'output' => [ 'shape' => 'GetInvitationsCountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetMacieSession' => [ 'name' => 'GetMacieSession', 'http' => [ 'method' => 'GET', 'requestUri' => '/macie', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMacieSessionRequest', ], 'output' => [ 'shape' => 'GetMacieSessionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetMasterAccount' => [ 'name' => 'GetMasterAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/master', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMasterAccountRequest', ], 'output' => [ 'shape' => 'GetMasterAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetMember' => [ 'name' => 'GetMember', 'http' => [ 'method' => 'GET', 'requestUri' => '/members/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMemberRequest', ], 'output' => [ 'shape' => 'GetMemberResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetResourceProfile' => [ 'name' => 'GetResourceProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/resource-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResourceProfileRequest', ], 'output' => [ 'shape' => 'GetResourceProfileResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetRevealConfiguration' => [ 'name' => 'GetRevealConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/reveal-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRevealConfigurationRequest', ], 'output' => [ 'shape' => 'GetRevealConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSensitiveDataOccurrences' => [ 'name' => 'GetSensitiveDataOccurrences', 'http' => [ 'method' => 'GET', 'requestUri' => '/findings/{findingId}/reveal', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSensitiveDataOccurrencesRequest', ], 'output' => [ 'shape' => 'GetSensitiveDataOccurrencesResponse', ], 'errors' => [ [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetSensitiveDataOccurrencesAvailability' => [ 'name' => 'GetSensitiveDataOccurrencesAvailability', 'http' => [ 'method' => 'GET', 'requestUri' => '/findings/{findingId}/reveal/availability', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSensitiveDataOccurrencesAvailabilityRequest', ], 'output' => [ 'shape' => 'GetSensitiveDataOccurrencesAvailabilityResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSensitivityInspectionTemplate' => [ 'name' => 'GetSensitivityInspectionTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/templates/sensitivity-inspections/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSensitivityInspectionTemplateRequest', ], 'output' => [ 'shape' => 'GetSensitivityInspectionTemplateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetUsageStatistics' => [ 'name' => 'GetUsageStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/usage/statistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUsageStatisticsRequest', ], 'output' => [ 'shape' => 'GetUsageStatisticsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetUsageTotals' => [ 'name' => 'GetUsageTotals', 'http' => [ 'method' => 'GET', 'requestUri' => '/usage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUsageTotalsRequest', ], 'output' => [ 'shape' => 'GetUsageTotalsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListAllowLists' => [ 'name' => 'ListAllowLists', 'http' => [ 'method' => 'GET', 'requestUri' => '/allow-lists', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAllowListsRequest', ], 'output' => [ 'shape' => 'ListAllowListsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListClassificationJobs' => [ 'name' => 'ListClassificationJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/jobs/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListClassificationJobsRequest', ], 'output' => [ 'shape' => 'ListClassificationJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListClassificationScopes' => [ 'name' => 'ListClassificationScopes', 'http' => [ 'method' => 'GET', 'requestUri' => '/classification-scopes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListClassificationScopesRequest', ], 'output' => [ 'shape' => 'ListClassificationScopesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCustomDataIdentifiers' => [ 'name' => 'ListCustomDataIdentifiers', 'http' => [ 'method' => 'POST', 'requestUri' => '/custom-data-identifiers/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCustomDataIdentifiersRequest', ], 'output' => [ 'shape' => 'ListCustomDataIdentifiersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListFindings' => [ 'name' => 'ListFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFindingsRequest', ], 'output' => [ 'shape' => 'ListFindingsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListFindingsFilters' => [ 'name' => 'ListFindingsFilters', 'http' => [ 'method' => 'GET', 'requestUri' => '/findingsfilters', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFindingsFiltersRequest', ], 'output' => [ 'shape' => 'ListFindingsFiltersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListInvitations' => [ 'name' => 'ListInvitations', 'http' => [ 'method' => 'GET', 'requestUri' => '/invitations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInvitationsRequest', ], 'output' => [ 'shape' => 'ListInvitationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListManagedDataIdentifiers' => [ 'name' => 'ListManagedDataIdentifiers', 'http' => [ 'method' => 'POST', 'requestUri' => '/managed-data-identifiers/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListManagedDataIdentifiersRequest', ], 'output' => [ 'shape' => 'ListManagedDataIdentifiersResponse', ], 'errors' => [], ], 'ListMembers' => [ 'name' => 'ListMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/members', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMembersRequest', ], 'output' => [ 'shape' => 'ListMembersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListOrganizationAdminAccounts' => [ 'name' => 'ListOrganizationAdminAccounts', 'http' => [ 'method' => 'GET', 'requestUri' => '/admin', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOrganizationAdminAccountsRequest', ], 'output' => [ 'shape' => 'ListOrganizationAdminAccountsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListResourceProfileArtifacts' => [ 'name' => 'ListResourceProfileArtifacts', 'http' => [ 'method' => 'GET', 'requestUri' => '/resource-profiles/artifacts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResourceProfileArtifactsRequest', ], 'output' => [ 'shape' => 'ListResourceProfileArtifactsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListResourceProfileDetections' => [ 'name' => 'ListResourceProfileDetections', 'http' => [ 'method' => 'GET', 'requestUri' => '/resource-profiles/detections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResourceProfileDetectionsRequest', ], 'output' => [ 'shape' => 'ListResourceProfileDetectionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListSensitivityInspectionTemplates' => [ 'name' => 'ListSensitivityInspectionTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/templates/sensitivity-inspections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSensitivityInspectionTemplatesRequest', ], 'output' => [ 'shape' => 'ListSensitivityInspectionTemplatesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [], ], 'PutClassificationExportConfiguration' => [ 'name' => 'PutClassificationExportConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/classification-export-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutClassificationExportConfigurationRequest', ], 'output' => [ 'shape' => 'PutClassificationExportConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutFindingsPublicationConfiguration' => [ 'name' => 'PutFindingsPublicationConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/findings-publication-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutFindingsPublicationConfigurationRequest', ], 'output' => [ 'shape' => 'PutFindingsPublicationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'SearchResources' => [ 'name' => 'SearchResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/datasources/search-resources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchResourcesRequest', ], 'output' => [ 'shape' => 'SearchResourcesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [], ], 'TestCustomDataIdentifier' => [ 'name' => 'TestCustomDataIdentifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/custom-data-identifiers/test', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TestCustomDataIdentifierRequest', ], 'output' => [ 'shape' => 'TestCustomDataIdentifierResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [], ], 'UpdateAllowList' => [ 'name' => 'UpdateAllowList', 'http' => [ 'method' => 'PUT', 'requestUri' => '/allow-lists/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAllowListRequest', ], 'output' => [ 'shape' => 'UpdateAllowListResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateAutomatedDiscoveryConfiguration' => [ 'name' => 'UpdateAutomatedDiscoveryConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/automated-discovery/configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAutomatedDiscoveryConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateAutomatedDiscoveryConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateClassificationJob' => [ 'name' => 'UpdateClassificationJob', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateClassificationJobRequest', ], 'output' => [ 'shape' => 'UpdateClassificationJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateClassificationScope' => [ 'name' => 'UpdateClassificationScope', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/classification-scopes/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateClassificationScopeRequest', ], 'output' => [ 'shape' => 'UpdateClassificationScopeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateFindingsFilter' => [ 'name' => 'UpdateFindingsFilter', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/findingsfilters/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFindingsFilterRequest', ], 'output' => [ 'shape' => 'UpdateFindingsFilterResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateMacieSession' => [ 'name' => 'UpdateMacieSession', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/macie', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMacieSessionRequest', ], 'output' => [ 'shape' => 'UpdateMacieSessionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateMemberSession' => [ 'name' => 'UpdateMemberSession', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/macie/members/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMemberSessionRequest', ], 'output' => [ 'shape' => 'UpdateMemberSessionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateOrganizationConfiguration' => [ 'name' => 'UpdateOrganizationConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/admin/configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateResourceProfile' => [ 'name' => 'UpdateResourceProfile', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/resource-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateResourceProfileRequest', ], 'output' => [ 'shape' => 'UpdateResourceProfileResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateResourceProfileDetections' => [ 'name' => 'UpdateResourceProfileDetections', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/resource-profiles/detections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateResourceProfileDetectionsRequest', ], 'output' => [ 'shape' => 'UpdateResourceProfileDetectionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateRevealConfiguration' => [ 'name' => 'UpdateRevealConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/reveal-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRevealConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateRevealConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateSensitivityInspectionTemplate' => [ 'name' => 'UpdateSensitivityInspectionTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/templates/sensitivity-inspections/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSensitivityInspectionTemplateRequest', ], 'output' => [ 'shape' => 'UpdateSensitivityInspectionTemplateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AcceptInvitationRequest' => [ 'type' => 'structure', 'members' => [ 'administratorAccountId' => [ 'shape' => '__string', 'locationName' => 'administratorAccountId', ], 'invitationId' => [ 'shape' => '__string', 'locationName' => 'invitationId', ], 'masterAccount' => [ 'shape' => '__string', 'locationName' => 'masterAccount', ], ], 'required' => [ 'invitationId', ], ], 'AcceptInvitationResponse' => [ 'type' => 'structure', 'members' => [], ], 'AccessControlList' => [ 'type' => 'structure', 'members' => [ 'allowsPublicReadAccess' => [ 'shape' => '__boolean', 'locationName' => 'allowsPublicReadAccess', ], 'allowsPublicWriteAccess' => [ 'shape' => '__boolean', 'locationName' => 'allowsPublicWriteAccess', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 403, ], ], 'AccountDetail' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'email' => [ 'shape' => '__string', 'locationName' => 'email', ], ], 'required' => [ 'email', 'accountId', ], ], 'AccountLevelPermissions' => [ 'type' => 'structure', 'members' => [ 'blockPublicAccess' => [ 'shape' => 'BlockPublicAccess', 'locationName' => 'blockPublicAccess', ], ], ], 'AdminAccount' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'status' => [ 'shape' => 'AdminStatus', 'locationName' => 'status', ], ], ], 'AdminStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLING_IN_PROGRESS', ], ], 'AllowListCriteria' => [ 'type' => 'structure', 'members' => [ 'regex' => [ 'shape' => '__stringMin1Max512PatternSS', 'locationName' => 'regex', ], 's3WordsList' => [ 'shape' => 'S3WordsList', 'locationName' => 's3WordsList', ], ], ], 'AllowListStatus' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'AllowListStatusCode', 'locationName' => 'code', ], 'description' => [ 'shape' => '__stringMin1Max1024PatternSS', 'locationName' => 'description', ], ], 'required' => [ 'code', ], ], 'AllowListStatusCode' => [ 'type' => 'string', 'enum' => [ 'OK', 'S3_OBJECT_NOT_FOUND', 'S3_USER_ACCESS_DENIED', 'S3_OBJECT_ACCESS_DENIED', 'S3_THROTTLED', 'S3_OBJECT_OVERSIZE', 'S3_OBJECT_EMPTY', 'UNKNOWN_ERROR', ], ], 'AllowListSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__stringMin71Max89PatternArnAwsAwsCnAwsUsGovMacie2AZ19920D12AllowListAZ0922', 'locationName' => 'arn', ], 'createdAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'description' => [ 'shape' => '__stringMin1Max512PatternSS', 'locationName' => 'description', ], 'id' => [ 'shape' => '__stringMin22Max22PatternAZ0922', 'locationName' => 'id', ], 'name' => [ 'shape' => '__stringMin1Max128Pattern', 'locationName' => 'name', ], 'updatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'updatedAt', ], ], ], 'AllowsUnencryptedObjectUploads' => [ 'type' => 'string', 'enum' => [ 'TRUE', 'FALSE', 'UNKNOWN', ], ], 'ApiCallDetails' => [ 'type' => 'structure', 'members' => [ 'api' => [ 'shape' => '__string', 'locationName' => 'api', ], 'apiServiceName' => [ 'shape' => '__string', 'locationName' => 'apiServiceName', ], 'firstSeen' => [ 'shape' => '__timestampIso8601', 'locationName' => 'firstSeen', ], 'lastSeen' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastSeen', ], ], ], 'AssumedRole' => [ 'type' => 'structure', 'members' => [ 'accessKeyId' => [ 'shape' => '__string', 'locationName' => 'accessKeyId', ], 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'principalId' => [ 'shape' => '__string', 'locationName' => 'principalId', ], 'sessionContext' => [ 'shape' => 'SessionContext', 'locationName' => 'sessionContext', ], ], ], 'AutomatedDiscoveryStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AvailabilityCode' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'UNAVAILABLE', ], ], 'AwsAccount' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'principalId' => [ 'shape' => '__string', 'locationName' => 'principalId', ], ], ], 'AwsService' => [ 'type' => 'structure', 'members' => [ 'invokedBy' => [ 'shape' => '__string', 'locationName' => 'invokedBy', ], ], ], 'BatchGetCustomDataIdentifierSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'createdAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'deleted' => [ 'shape' => '__boolean', 'locationName' => 'deleted', ], 'description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], ], ], 'BatchGetCustomDataIdentifiersRequest' => [ 'type' => 'structure', 'members' => [ 'ids' => [ 'shape' => '__listOf__string', 'locationName' => 'ids', ], ], ], 'BatchGetCustomDataIdentifiersResponse' => [ 'type' => 'structure', 'members' => [ 'customDataIdentifiers' => [ 'shape' => '__listOfBatchGetCustomDataIdentifierSummary', 'locationName' => 'customDataIdentifiers', ], 'notFoundIdentifierIds' => [ 'shape' => '__listOf__string', 'locationName' => 'notFoundIdentifierIds', ], ], ], 'BlockPublicAccess' => [ 'type' => 'structure', 'members' => [ 'blockPublicAcls' => [ 'shape' => '__boolean', 'locationName' => 'blockPublicAcls', ], 'blockPublicPolicy' => [ 'shape' => '__boolean', 'locationName' => 'blockPublicPolicy', ], 'ignorePublicAcls' => [ 'shape' => '__boolean', 'locationName' => 'ignorePublicAcls', ], 'restrictPublicBuckets' => [ 'shape' => '__boolean', 'locationName' => 'restrictPublicBuckets', ], ], ], 'BucketCountByEffectivePermission' => [ 'type' => 'structure', 'members' => [ 'publiclyAccessible' => [ 'shape' => '__long', 'locationName' => 'publiclyAccessible', ], 'publiclyReadable' => [ 'shape' => '__long', 'locationName' => 'publiclyReadable', ], 'publiclyWritable' => [ 'shape' => '__long', 'locationName' => 'publiclyWritable', ], 'unknown' => [ 'shape' => '__long', 'locationName' => 'unknown', ], ], ], 'BucketCountByEncryptionType' => [ 'type' => 'structure', 'members' => [ 'kmsManaged' => [ 'shape' => '__long', 'locationName' => 'kmsManaged', ], 's3Managed' => [ 'shape' => '__long', 'locationName' => 's3Managed', ], 'unencrypted' => [ 'shape' => '__long', 'locationName' => 'unencrypted', ], 'unknown' => [ 'shape' => '__long', 'locationName' => 'unknown', ], ], ], 'BucketCountBySharedAccessType' => [ 'type' => 'structure', 'members' => [ 'external' => [ 'shape' => '__long', 'locationName' => 'external', ], 'internal' => [ 'shape' => '__long', 'locationName' => 'internal', ], 'notShared' => [ 'shape' => '__long', 'locationName' => 'notShared', ], 'unknown' => [ 'shape' => '__long', 'locationName' => 'unknown', ], ], ], 'BucketCountPolicyAllowsUnencryptedObjectUploads' => [ 'type' => 'structure', 'members' => [ 'allowsUnencryptedObjectUploads' => [ 'shape' => '__long', 'locationName' => 'allowsUnencryptedObjectUploads', ], 'deniesUnencryptedObjectUploads' => [ 'shape' => '__long', 'locationName' => 'deniesUnencryptedObjectUploads', ], 'unknown' => [ 'shape' => '__long', 'locationName' => 'unknown', ], ], ], 'BucketCriteria' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'BucketCriteriaAdditionalProperties', ], ], 'BucketCriteriaAdditionalProperties' => [ 'type' => 'structure', 'members' => [ 'eq' => [ 'shape' => '__listOf__string', 'locationName' => 'eq', ], 'gt' => [ 'shape' => '__long', 'locationName' => 'gt', ], 'gte' => [ 'shape' => '__long', 'locationName' => 'gte', ], 'lt' => [ 'shape' => '__long', 'locationName' => 'lt', ], 'lte' => [ 'shape' => '__long', 'locationName' => 'lte', ], 'neq' => [ 'shape' => '__listOf__string', 'locationName' => 'neq', ], 'prefix' => [ 'shape' => '__string', 'locationName' => 'prefix', ], ], ], 'BucketLevelPermissions' => [ 'type' => 'structure', 'members' => [ 'accessControlList' => [ 'shape' => 'AccessControlList', 'locationName' => 'accessControlList', ], 'blockPublicAccess' => [ 'shape' => 'BlockPublicAccess', 'locationName' => 'blockPublicAccess', ], 'bucketPolicy' => [ 'shape' => 'BucketPolicy', 'locationName' => 'bucketPolicy', ], ], ], 'BucketMetadata' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'allowsUnencryptedObjectUploads' => [ 'shape' => 'AllowsUnencryptedObjectUploads', 'locationName' => 'allowsUnencryptedObjectUploads', ], 'bucketArn' => [ 'shape' => '__string', 'locationName' => 'bucketArn', ], 'bucketCreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'bucketCreatedAt', ], 'bucketName' => [ 'shape' => '__string', 'locationName' => 'bucketName', ], 'classifiableObjectCount' => [ 'shape' => '__long', 'locationName' => 'classifiableObjectCount', ], 'classifiableSizeInBytes' => [ 'shape' => '__long', 'locationName' => 'classifiableSizeInBytes', ], 'errorCode' => [ 'shape' => 'BucketMetadataErrorCode', 'locationName' => 'errorCode', ], 'errorMessage' => [ 'shape' => '__string', 'locationName' => 'errorMessage', ], 'jobDetails' => [ 'shape' => 'JobDetails', 'locationName' => 'jobDetails', ], 'lastAutomatedDiscoveryTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastAutomatedDiscoveryTime', ], 'lastUpdated' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastUpdated', ], 'objectCount' => [ 'shape' => '__long', 'locationName' => 'objectCount', ], 'objectCountByEncryptionType' => [ 'shape' => 'ObjectCountByEncryptionType', 'locationName' => 'objectCountByEncryptionType', ], 'publicAccess' => [ 'shape' => 'BucketPublicAccess', 'locationName' => 'publicAccess', ], 'region' => [ 'shape' => '__string', 'locationName' => 'region', ], 'replicationDetails' => [ 'shape' => 'ReplicationDetails', 'locationName' => 'replicationDetails', ], 'sensitivityScore' => [ 'shape' => '__integer', 'locationName' => 'sensitivityScore', ], 'serverSideEncryption' => [ 'shape' => 'BucketServerSideEncryption', 'locationName' => 'serverSideEncryption', ], 'sharedAccess' => [ 'shape' => 'SharedAccess', 'locationName' => 'sharedAccess', ], 'sizeInBytes' => [ 'shape' => '__long', 'locationName' => 'sizeInBytes', ], 'sizeInBytesCompressed' => [ 'shape' => '__long', 'locationName' => 'sizeInBytesCompressed', ], 'tags' => [ 'shape' => '__listOfKeyValuePair', 'locationName' => 'tags', ], 'unclassifiableObjectCount' => [ 'shape' => 'ObjectLevelStatistics', 'locationName' => 'unclassifiableObjectCount', ], 'unclassifiableObjectSizeInBytes' => [ 'shape' => 'ObjectLevelStatistics', 'locationName' => 'unclassifiableObjectSizeInBytes', ], 'versioning' => [ 'shape' => '__boolean', 'locationName' => 'versioning', ], ], ], 'BucketMetadataErrorCode' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED', ], ], 'BucketPermissionConfiguration' => [ 'type' => 'structure', 'members' => [ 'accountLevelPermissions' => [ 'shape' => 'AccountLevelPermissions', 'locationName' => 'accountLevelPermissions', ], 'bucketLevelPermissions' => [ 'shape' => 'BucketLevelPermissions', 'locationName' => 'bucketLevelPermissions', ], ], ], 'BucketPolicy' => [ 'type' => 'structure', 'members' => [ 'allowsPublicReadAccess' => [ 'shape' => '__boolean', 'locationName' => 'allowsPublicReadAccess', ], 'allowsPublicWriteAccess' => [ 'shape' => '__boolean', 'locationName' => 'allowsPublicWriteAccess', ], ], ], 'BucketPublicAccess' => [ 'type' => 'structure', 'members' => [ 'effectivePermission' => [ 'shape' => 'EffectivePermission', 'locationName' => 'effectivePermission', ], 'permissionConfiguration' => [ 'shape' => 'BucketPermissionConfiguration', 'locationName' => 'permissionConfiguration', ], ], ], 'BucketServerSideEncryption' => [ 'type' => 'structure', 'members' => [ 'kmsMasterKeyId' => [ 'shape' => '__string', 'locationName' => 'kmsMasterKeyId', ], 'type' => [ 'shape' => 'Type', 'locationName' => 'type', ], ], ], 'BucketSortCriteria' => [ 'type' => 'structure', 'members' => [ 'attributeName' => [ 'shape' => '__string', 'locationName' => 'attributeName', ], 'orderBy' => [ 'shape' => 'OrderBy', 'locationName' => 'orderBy', ], ], ], 'BucketStatisticsBySensitivity' => [ 'type' => 'structure', 'members' => [ 'classificationError' => [ 'shape' => 'SensitivityAggregations', 'locationName' => 'classificationError', ], 'notClassified' => [ 'shape' => 'SensitivityAggregations', 'locationName' => 'notClassified', ], 'notSensitive' => [ 'shape' => 'SensitivityAggregations', 'locationName' => 'notSensitive', ], 'sensitive' => [ 'shape' => 'SensitivityAggregations', 'locationName' => 'sensitive', ], ], ], 'Cell' => [ 'type' => 'structure', 'members' => [ 'cellReference' => [ 'shape' => '__string', 'locationName' => 'cellReference', ], 'column' => [ 'shape' => '__long', 'locationName' => 'column', ], 'columnName' => [ 'shape' => '__string', 'locationName' => 'columnName', ], 'row' => [ 'shape' => '__long', 'locationName' => 'row', ], ], ], 'Cells' => [ 'type' => 'list', 'member' => [ 'shape' => 'Cell', ], ], 'ClassificationDetails' => [ 'type' => 'structure', 'members' => [ 'detailedResultsLocation' => [ 'shape' => '__string', 'locationName' => 'detailedResultsLocation', ], 'jobArn' => [ 'shape' => '__string', 'locationName' => 'jobArn', ], 'jobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'originType' => [ 'shape' => 'OriginType', 'locationName' => 'originType', ], 'result' => [ 'shape' => 'ClassificationResult', 'locationName' => 'result', ], ], ], 'ClassificationExportConfiguration' => [ 'type' => 'structure', 'members' => [ 's3Destination' => [ 'shape' => 'S3Destination', 'locationName' => 's3Destination', ], ], ], 'ClassificationResult' => [ 'type' => 'structure', 'members' => [ 'additionalOccurrences' => [ 'shape' => '__boolean', 'locationName' => 'additionalOccurrences', ], 'customDataIdentifiers' => [ 'shape' => 'CustomDataIdentifiers', 'locationName' => 'customDataIdentifiers', ], 'mimeType' => [ 'shape' => '__string', 'locationName' => 'mimeType', ], 'sensitiveData' => [ 'shape' => 'SensitiveData', 'locationName' => 'sensitiveData', ], 'sizeClassified' => [ 'shape' => '__long', 'locationName' => 'sizeClassified', ], 'status' => [ 'shape' => 'ClassificationResultStatus', 'locationName' => 'status', ], ], ], 'ClassificationResultStatus' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => '__string', 'locationName' => 'code', ], 'reason' => [ 'shape' => '__string', 'locationName' => 'reason', ], ], ], 'ClassificationScopeId' => [ 'type' => 'string', 'pattern' => '^[0-9a-z]*$', ], 'ClassificationScopeName' => [ 'type' => 'string', 'pattern' => '^[0-9a-zA-Z_\\\\-]*$', ], 'ClassificationScopeSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ClassificationScopeId', 'locationName' => 'id', ], 'name' => [ 'shape' => 'ClassificationScopeName', 'locationName' => 'name', ], ], ], 'ClassificationScopeUpdateOperation' => [ 'type' => 'string', 'enum' => [ 'ADD', 'REPLACE', 'REMOVE', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 409, ], ], 'CreateAllowListRequest' => [ 'type' => 'structure', 'members' => [ 'clientToken' => [ 'shape' => '__string', 'locationName' => 'clientToken', 'idempotencyToken' => true, ], 'criteria' => [ 'shape' => 'AllowListCriteria', 'locationName' => 'criteria', ], 'description' => [ 'shape' => '__stringMin1Max512PatternSS', 'locationName' => 'description', ], 'name' => [ 'shape' => '__stringMin1Max128Pattern', 'locationName' => 'name', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'criteria', 'clientToken', 'name', ], ], 'CreateAllowListResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__stringMin71Max89PatternArnAwsAwsCnAwsUsGovMacie2AZ19920D12AllowListAZ0922', 'locationName' => 'arn', ], 'id' => [ 'shape' => '__stringMin22Max22PatternAZ0922', 'locationName' => 'id', ], ], ], 'CreateClassificationJobRequest' => [ 'type' => 'structure', 'members' => [ 'allowListIds' => [ 'shape' => '__listOf__string', 'locationName' => 'allowListIds', ], 'clientToken' => [ 'shape' => '__string', 'locationName' => 'clientToken', 'idempotencyToken' => true, ], 'customDataIdentifierIds' => [ 'shape' => '__listOf__string', 'locationName' => 'customDataIdentifierIds', ], 'description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'initialRun' => [ 'shape' => '__boolean', 'locationName' => 'initialRun', ], 'jobType' => [ 'shape' => 'JobType', 'locationName' => 'jobType', ], 'managedDataIdentifierIds' => [ 'shape' => '__listOf__string', 'locationName' => 'managedDataIdentifierIds', ], 'managedDataIdentifierSelector' => [ 'shape' => 'ManagedDataIdentifierSelector', 'locationName' => 'managedDataIdentifierSelector', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 's3JobDefinition' => [ 'shape' => 'S3JobDefinition', 'locationName' => 's3JobDefinition', ], 'samplingPercentage' => [ 'shape' => '__integer', 'locationName' => 'samplingPercentage', ], 'scheduleFrequency' => [ 'shape' => 'JobScheduleFrequency', 'locationName' => 'scheduleFrequency', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 's3JobDefinition', 'jobType', 'clientToken', 'name', ], ], 'CreateClassificationJobResponse' => [ 'type' => 'structure', 'members' => [ 'jobArn' => [ 'shape' => '__string', 'locationName' => 'jobArn', ], 'jobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], ], ], 'CreateCustomDataIdentifierRequest' => [ 'type' => 'structure', 'members' => [ 'clientToken' => [ 'shape' => '__string', 'locationName' => 'clientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'ignoreWords' => [ 'shape' => '__listOf__string', 'locationName' => 'ignoreWords', ], 'keywords' => [ 'shape' => '__listOf__string', 'locationName' => 'keywords', ], 'maximumMatchDistance' => [ 'shape' => '__integer', 'locationName' => 'maximumMatchDistance', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'regex' => [ 'shape' => '__string', 'locationName' => 'regex', ], 'severityLevels' => [ 'shape' => 'SeverityLevelList', 'locationName' => 'severityLevels', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'regex', 'name', ], ], 'CreateCustomDataIdentifierResponse' => [ 'type' => 'structure', 'members' => [ 'customDataIdentifierId' => [ 'shape' => '__string', 'locationName' => 'customDataIdentifierId', ], ], ], 'CreateFindingsFilterRequest' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'FindingsFilterAction', 'locationName' => 'action', ], 'clientToken' => [ 'shape' => '__string', 'locationName' => 'clientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'findingCriteria' => [ 'shape' => 'FindingCriteria', 'locationName' => 'findingCriteria', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'position' => [ 'shape' => '__integer', 'locationName' => 'position', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'action', 'findingCriteria', 'name', ], ], 'CreateFindingsFilterResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], ], ], 'CreateInvitationsRequest' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => '__listOf__string', 'locationName' => 'accountIds', ], 'disableEmailNotification' => [ 'shape' => '__boolean', 'locationName' => 'disableEmailNotification', ], 'message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'required' => [ 'accountIds', ], ], 'CreateInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'unprocessedAccounts' => [ 'shape' => '__listOfUnprocessedAccount', 'locationName' => 'unprocessedAccounts', ], ], ], 'CreateMemberRequest' => [ 'type' => 'structure', 'members' => [ 'account' => [ 'shape' => 'AccountDetail', 'locationName' => 'account', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'account', ], ], 'CreateMemberResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], ], ], 'CreateSampleFindingsRequest' => [ 'type' => 'structure', 'members' => [ 'findingTypes' => [ 'shape' => '__listOfFindingType', 'locationName' => 'findingTypes', ], ], ], 'CreateSampleFindingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'CriteriaBlockForJob' => [ 'type' => 'structure', 'members' => [ 'and' => [ 'shape' => '__listOfCriteriaForJob', 'locationName' => 'and', ], ], ], 'CriteriaForJob' => [ 'type' => 'structure', 'members' => [ 'simpleCriterion' => [ 'shape' => 'SimpleCriterionForJob', 'locationName' => 'simpleCriterion', ], 'tagCriterion' => [ 'shape' => 'TagCriterionForJob', 'locationName' => 'tagCriterion', ], ], ], 'Criterion' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'CriterionAdditionalProperties', ], ], 'CriterionAdditionalProperties' => [ 'type' => 'structure', 'members' => [ 'eq' => [ 'shape' => '__listOf__string', 'locationName' => 'eq', ], 'eqExactMatch' => [ 'shape' => '__listOf__string', 'locationName' => 'eqExactMatch', ], 'gt' => [ 'shape' => '__long', 'locationName' => 'gt', ], 'gte' => [ 'shape' => '__long', 'locationName' => 'gte', ], 'lt' => [ 'shape' => '__long', 'locationName' => 'lt', ], 'lte' => [ 'shape' => '__long', 'locationName' => 'lte', ], 'neq' => [ 'shape' => '__listOf__string', 'locationName' => 'neq', ], ], ], 'Currency' => [ 'type' => 'string', 'enum' => [ 'USD', ], ], 'CustomDataIdentifierSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'createdAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], ], ], 'CustomDataIdentifiers' => [ 'type' => 'structure', 'members' => [ 'detections' => [ 'shape' => 'CustomDetections', 'locationName' => 'detections', ], 'totalCount' => [ 'shape' => '__long', 'locationName' => 'totalCount', ], ], ], 'CustomDetection' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'count' => [ 'shape' => '__long', 'locationName' => 'count', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'occurrences' => [ 'shape' => 'Occurrences', 'locationName' => 'occurrences', ], ], ], 'CustomDetections' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomDetection', ], ], 'DailySchedule' => [ 'type' => 'structure', 'members' => [], ], 'DataIdentifierSeverity' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', ], ], 'DataIdentifierType' => [ 'type' => 'string', 'enum' => [ 'CUSTOM', 'MANAGED', ], ], 'DayOfWeek' => [ 'type' => 'string', 'enum' => [ 'SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', ], ], 'DeclineInvitationsRequest' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => '__listOf__string', 'locationName' => 'accountIds', ], ], 'required' => [ 'accountIds', ], ], 'DeclineInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'unprocessedAccounts' => [ 'shape' => '__listOfUnprocessedAccount', 'locationName' => 'unprocessedAccounts', ], ], ], 'DefaultDetection' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => '__long', 'locationName' => 'count', ], 'occurrences' => [ 'shape' => 'Occurrences', 'locationName' => 'occurrences', ], 'type' => [ 'shape' => '__string', 'locationName' => 'type', ], ], ], 'DefaultDetections' => [ 'type' => 'list', 'member' => [ 'shape' => 'DefaultDetection', ], ], 'DeleteAllowListRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], 'ignoreJobChecks' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'ignoreJobChecks', ], ], 'required' => [ 'id', ], ], 'DeleteAllowListResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCustomDataIdentifierRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], ], 'required' => [ 'id', ], ], 'DeleteCustomDataIdentifierResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFindingsFilterRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], ], 'required' => [ 'id', ], ], 'DeleteFindingsFilterResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInvitationsRequest' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => '__listOf__string', 'locationName' => 'accountIds', ], ], 'required' => [ 'accountIds', ], ], 'DeleteInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'unprocessedAccounts' => [ 'shape' => '__listOfUnprocessedAccount', 'locationName' => 'unprocessedAccounts', ], ], ], 'DeleteMemberRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], ], 'required' => [ 'id', ], ], 'DeleteMemberResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeBucketsRequest' => [ 'type' => 'structure', 'members' => [ 'criteria' => [ 'shape' => 'BucketCriteria', 'locationName' => 'criteria', ], 'maxResults' => [ 'shape' => '__integer', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'sortCriteria' => [ 'shape' => 'BucketSortCriteria', 'locationName' => 'sortCriteria', ], ], ], 'DescribeBucketsResponse' => [ 'type' => 'structure', 'members' => [ 'buckets' => [ 'shape' => '__listOfBucketMetadata', 'locationName' => 'buckets', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'DescribeClassificationJobRequest' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'jobId', ], ], 'required' => [ 'jobId', ], ], 'DescribeClassificationJobResponse' => [ 'type' => 'structure', 'members' => [ 'allowListIds' => [ 'shape' => '__listOf__string', 'locationName' => 'allowListIds', ], 'clientToken' => [ 'shape' => '__string', 'locationName' => 'clientToken', 'idempotencyToken' => true, ], 'createdAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'customDataIdentifierIds' => [ 'shape' => '__listOf__string', 'locationName' => 'customDataIdentifierIds', ], 'description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'initialRun' => [ 'shape' => '__boolean', 'locationName' => 'initialRun', ], 'jobArn' => [ 'shape' => '__string', 'locationName' => 'jobArn', ], 'jobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'jobStatus' => [ 'shape' => 'JobStatus', 'locationName' => 'jobStatus', ], 'jobType' => [ 'shape' => 'JobType', 'locationName' => 'jobType', ], 'lastRunErrorStatus' => [ 'shape' => 'LastRunErrorStatus', 'locationName' => 'lastRunErrorStatus', ], 'lastRunTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastRunTime', ], 'managedDataIdentifierIds' => [ 'shape' => '__listOf__string', 'locationName' => 'managedDataIdentifierIds', ], 'managedDataIdentifierSelector' => [ 'shape' => 'ManagedDataIdentifierSelector', 'locationName' => 'managedDataIdentifierSelector', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 's3JobDefinition' => [ 'shape' => 'S3JobDefinition', 'locationName' => 's3JobDefinition', ], 'samplingPercentage' => [ 'shape' => '__integer', 'locationName' => 'samplingPercentage', ], 'scheduleFrequency' => [ 'shape' => 'JobScheduleFrequency', 'locationName' => 'scheduleFrequency', ], 'statistics' => [ 'shape' => 'Statistics', 'locationName' => 'statistics', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'userPausedDetails' => [ 'shape' => 'UserPausedDetails', 'locationName' => 'userPausedDetails', ], ], ], 'DescribeOrganizationConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'autoEnable' => [ 'shape' => '__boolean', 'locationName' => 'autoEnable', ], 'maxAccountLimitReached' => [ 'shape' => '__boolean', 'locationName' => 'maxAccountLimitReached', ], ], ], 'DetectedDataDetails' => [ 'type' => 'structure', 'members' => [ 'value' => [ 'shape' => '__stringMin1Max128', 'locationName' => 'value', ], ], 'required' => [ 'value', ], ], 'Detection' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'count' => [ 'shape' => '__long', 'locationName' => 'count', ], 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'suppressed' => [ 'shape' => '__boolean', 'locationName' => 'suppressed', ], 'type' => [ 'shape' => 'DataIdentifierType', 'locationName' => 'type', ], ], ], 'DisableMacieRequest' => [ 'type' => 'structure', 'members' => [], ], 'DisableMacieResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisableOrganizationAdminAccountRequest' => [ 'type' => 'structure', 'members' => [ 'adminAccountId' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'adminAccountId', ], ], 'required' => [ 'adminAccountId', ], ], 'DisableOrganizationAdminAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromAdministratorAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromAdministratorAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromMasterAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromMasterAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateMemberRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], ], 'required' => [ 'id', ], ], 'DisassociateMemberResponse' => [ 'type' => 'structure', 'members' => [], ], 'DomainDetails' => [ 'type' => 'structure', 'members' => [ 'domainName' => [ 'shape' => '__string', 'locationName' => 'domainName', ], ], ], 'EffectivePermission' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'NOT_PUBLIC', 'UNKNOWN', ], ], 'Empty' => [ 'type' => 'structure', 'members' => [], ], 'EnableMacieRequest' => [ 'type' => 'structure', 'members' => [ 'clientToken' => [ 'shape' => '__string', 'locationName' => 'clientToken', 'idempotencyToken' => true, ], 'findingPublishingFrequency' => [ 'shape' => 'FindingPublishingFrequency', 'locationName' => 'findingPublishingFrequency', ], 'status' => [ 'shape' => 'MacieStatus', 'locationName' => 'status', ], ], ], 'EnableMacieResponse' => [ 'type' => 'structure', 'members' => [], ], 'EnableOrganizationAdminAccountRequest' => [ 'type' => 'structure', 'members' => [ 'adminAccountId' => [ 'shape' => '__string', 'locationName' => 'adminAccountId', ], 'clientToken' => [ 'shape' => '__string', 'locationName' => 'clientToken', 'idempotencyToken' => true, ], ], 'required' => [ 'adminAccountId', ], ], 'EnableOrganizationAdminAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'AES256', 'aws:kms', 'UNKNOWN', ], ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'ClientError', 'InternalError', ], ], 'FederatedUser' => [ 'type' => 'structure', 'members' => [ 'accessKeyId' => [ 'shape' => '__string', 'locationName' => 'accessKeyId', ], 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'principalId' => [ 'shape' => '__string', 'locationName' => 'principalId', ], 'sessionContext' => [ 'shape' => 'SessionContext', 'locationName' => 'sessionContext', ], ], ], 'Finding' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'archived' => [ 'shape' => '__boolean', 'locationName' => 'archived', ], 'category' => [ 'shape' => 'FindingCategory', 'locationName' => 'category', ], 'classificationDetails' => [ 'shape' => 'ClassificationDetails', 'locationName' => 'classificationDetails', ], 'count' => [ 'shape' => '__long', 'locationName' => 'count', ], 'createdAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'partition' => [ 'shape' => '__string', 'locationName' => 'partition', ], 'policyDetails' => [ 'shape' => 'PolicyDetails', 'locationName' => 'policyDetails', ], 'region' => [ 'shape' => '__string', 'locationName' => 'region', ], 'resourcesAffected' => [ 'shape' => 'ResourcesAffected', 'locationName' => 'resourcesAffected', ], 'sample' => [ 'shape' => '__boolean', 'locationName' => 'sample', ], 'schemaVersion' => [ 'shape' => '__string', 'locationName' => 'schemaVersion', ], 'severity' => [ 'shape' => 'Severity', 'locationName' => 'severity', ], 'title' => [ 'shape' => '__string', 'locationName' => 'title', ], 'type' => [ 'shape' => 'FindingType', 'locationName' => 'type', ], 'updatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'updatedAt', ], ], ], 'FindingAction' => [ 'type' => 'structure', 'members' => [ 'actionType' => [ 'shape' => 'FindingActionType', 'locationName' => 'actionType', ], 'apiCallDetails' => [ 'shape' => 'ApiCallDetails', 'locationName' => 'apiCallDetails', ], ], ], 'FindingActionType' => [ 'type' => 'string', 'enum' => [ 'AWS_API_CALL', ], ], 'FindingActor' => [ 'type' => 'structure', 'members' => [ 'domainDetails' => [ 'shape' => 'DomainDetails', 'locationName' => 'domainDetails', ], 'ipAddressDetails' => [ 'shape' => 'IpAddressDetails', 'locationName' => 'ipAddressDetails', ], 'userIdentity' => [ 'shape' => 'UserIdentity', 'locationName' => 'userIdentity', ], ], ], 'FindingCategory' => [ 'type' => 'string', 'enum' => [ 'CLASSIFICATION', 'POLICY', ], ], 'FindingCriteria' => [ 'type' => 'structure', 'members' => [ 'criterion' => [ 'shape' => 'Criterion', 'locationName' => 'criterion', ], ], ], 'FindingPublishingFrequency' => [ 'type' => 'string', 'enum' => [ 'FIFTEEN_MINUTES', 'ONE_HOUR', 'SIX_HOURS', ], ], 'FindingStatisticsSortAttributeName' => [ 'type' => 'string', 'enum' => [ 'groupKey', 'count', ], ], 'FindingStatisticsSortCriteria' => [ 'type' => 'structure', 'members' => [ 'attributeName' => [ 'shape' => 'FindingStatisticsSortAttributeName', 'locationName' => 'attributeName', ], 'orderBy' => [ 'shape' => 'OrderBy', 'locationName' => 'orderBy', ], ], ], 'FindingType' => [ 'type' => 'string', 'enum' => [ 'SensitiveData:S3Object/Multiple', 'SensitiveData:S3Object/Financial', 'SensitiveData:S3Object/Personal', 'SensitiveData:S3Object/Credentials', 'SensitiveData:S3Object/CustomIdentifier', 'Policy:IAMUser/S3BucketPublic', 'Policy:IAMUser/S3BucketSharedExternally', 'Policy:IAMUser/S3BucketReplicatedExternally', 'Policy:IAMUser/S3BucketEncryptionDisabled', 'Policy:IAMUser/S3BlockPublicAccessDisabled', 'Policy:IAMUser/S3BucketSharedWithCloudFront', ], ], 'FindingsFilterAction' => [ 'type' => 'string', 'enum' => [ 'ARCHIVE', 'NOOP', ], ], 'FindingsFilterListItem' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'FindingsFilterAction', 'locationName' => 'action', ], 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'GetAdministratorAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAdministratorAccountResponse' => [ 'type' => 'structure', 'members' => [ 'administrator' => [ 'shape' => 'Invitation', 'locationName' => 'administrator', ], ], ], 'GetAllowListRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], ], 'required' => [ 'id', ], ], 'GetAllowListResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__stringMin71Max89PatternArnAwsAwsCnAwsUsGovMacie2AZ19920D12AllowListAZ0922', 'locationName' => 'arn', ], 'createdAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'criteria' => [ 'shape' => 'AllowListCriteria', 'locationName' => 'criteria', ], 'description' => [ 'shape' => '__stringMin1Max512PatternSS', 'locationName' => 'description', ], 'id' => [ 'shape' => '__stringMin22Max22PatternAZ0922', 'locationName' => 'id', ], 'name' => [ 'shape' => '__stringMin1Max128Pattern', 'locationName' => 'name', ], 'status' => [ 'shape' => 'AllowListStatus', 'locationName' => 'status', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'updatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'updatedAt', ], ], ], 'GetAutomatedDiscoveryConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAutomatedDiscoveryConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'classificationScopeId' => [ 'shape' => 'ClassificationScopeId', 'locationName' => 'classificationScopeId', ], 'disabledAt' => [ 'shape' => 'Timestamp', 'locationName' => 'disabledAt', ], 'firstEnabledAt' => [ 'shape' => 'Timestamp', 'locationName' => 'firstEnabledAt', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'lastUpdatedAt', ], 'sensitivityInspectionTemplateId' => [ 'shape' => 'SensitivityInspectionTemplateId', 'locationName' => 'sensitivityInspectionTemplateId', ], 'status' => [ 'shape' => 'AutomatedDiscoveryStatus', 'locationName' => 'status', ], ], ], 'GetBucketStatisticsRequest' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], ], ], 'GetBucketStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'bucketCount' => [ 'shape' => '__long', 'locationName' => 'bucketCount', ], 'bucketCountByEffectivePermission' => [ 'shape' => 'BucketCountByEffectivePermission', 'locationName' => 'bucketCountByEffectivePermission', ], 'bucketCountByEncryptionType' => [ 'shape' => 'BucketCountByEncryptionType', 'locationName' => 'bucketCountByEncryptionType', ], 'bucketCountByObjectEncryptionRequirement' => [ 'shape' => 'BucketCountPolicyAllowsUnencryptedObjectUploads', 'locationName' => 'bucketCountByObjectEncryptionRequirement', ], 'bucketCountBySharedAccessType' => [ 'shape' => 'BucketCountBySharedAccessType', 'locationName' => 'bucketCountBySharedAccessType', ], 'bucketStatisticsBySensitivity' => [ 'shape' => 'BucketStatisticsBySensitivity', 'locationName' => 'bucketStatisticsBySensitivity', ], 'classifiableObjectCount' => [ 'shape' => '__long', 'locationName' => 'classifiableObjectCount', ], 'classifiableSizeInBytes' => [ 'shape' => '__long', 'locationName' => 'classifiableSizeInBytes', ], 'lastUpdated' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastUpdated', ], 'objectCount' => [ 'shape' => '__long', 'locationName' => 'objectCount', ], 'sizeInBytes' => [ 'shape' => '__long', 'locationName' => 'sizeInBytes', ], 'sizeInBytesCompressed' => [ 'shape' => '__long', 'locationName' => 'sizeInBytesCompressed', ], 'unclassifiableObjectCount' => [ 'shape' => 'ObjectLevelStatistics', 'locationName' => 'unclassifiableObjectCount', ], 'unclassifiableObjectSizeInBytes' => [ 'shape' => 'ObjectLevelStatistics', 'locationName' => 'unclassifiableObjectSizeInBytes', ], ], ], 'GetClassificationExportConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetClassificationExportConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'ClassificationExportConfiguration', 'locationName' => 'configuration', ], ], ], 'GetClassificationScopeRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], ], 'required' => [ 'id', ], ], 'GetClassificationScopeResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ClassificationScopeId', 'locationName' => 'id', ], 'name' => [ 'shape' => 'ClassificationScopeName', 'locationName' => 'name', ], 's3' => [ 'shape' => 'S3ClassificationScope', 'locationName' => 's3', ], ], ], 'GetCustomDataIdentifierRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], ], 'required' => [ 'id', ], ], 'GetCustomDataIdentifierResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'createdAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'deleted' => [ 'shape' => '__boolean', 'locationName' => 'deleted', ], 'description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'ignoreWords' => [ 'shape' => '__listOf__string', 'locationName' => 'ignoreWords', ], 'keywords' => [ 'shape' => '__listOf__string', 'locationName' => 'keywords', ], 'maximumMatchDistance' => [ 'shape' => '__integer', 'locationName' => 'maximumMatchDistance', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'regex' => [ 'shape' => '__string', 'locationName' => 'regex', ], 'severityLevels' => [ 'shape' => 'SeverityLevelList', 'locationName' => 'severityLevels', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'GetFindingStatisticsRequest' => [ 'type' => 'structure', 'members' => [ 'findingCriteria' => [ 'shape' => 'FindingCriteria', 'locationName' => 'findingCriteria', ], 'groupBy' => [ 'shape' => 'GroupBy', 'locationName' => 'groupBy', ], 'size' => [ 'shape' => '__integer', 'locationName' => 'size', ], 'sortCriteria' => [ 'shape' => 'FindingStatisticsSortCriteria', 'locationName' => 'sortCriteria', ], ], 'required' => [ 'groupBy', ], ], 'GetFindingStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'countsByGroup' => [ 'shape' => '__listOfGroupCount', 'locationName' => 'countsByGroup', ], ], ], 'GetFindingsFilterRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], ], 'required' => [ 'id', ], ], 'GetFindingsFilterResponse' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'FindingsFilterAction', 'locationName' => 'action', ], 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'findingCriteria' => [ 'shape' => 'FindingCriteria', 'locationName' => 'findingCriteria', ], 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'position' => [ 'shape' => '__integer', 'locationName' => 'position', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'GetFindingsPublicationConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetFindingsPublicationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'securityHubConfiguration' => [ 'shape' => 'SecurityHubConfiguration', 'locationName' => 'securityHubConfiguration', ], ], ], 'GetFindingsRequest' => [ 'type' => 'structure', 'members' => [ 'findingIds' => [ 'shape' => '__listOf__string', 'locationName' => 'findingIds', ], 'sortCriteria' => [ 'shape' => 'SortCriteria', 'locationName' => 'sortCriteria', ], ], 'required' => [ 'findingIds', ], ], 'GetFindingsResponse' => [ 'type' => 'structure', 'members' => [ 'findings' => [ 'shape' => '__listOfFinding', 'locationName' => 'findings', ], ], ], 'GetInvitationsCountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetInvitationsCountResponse' => [ 'type' => 'structure', 'members' => [ 'invitationsCount' => [ 'shape' => '__long', 'locationName' => 'invitationsCount', ], ], ], 'GetMacieSessionRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetMacieSessionResponse' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'findingPublishingFrequency' => [ 'shape' => 'FindingPublishingFrequency', 'locationName' => 'findingPublishingFrequency', ], 'serviceRole' => [ 'shape' => '__string', 'locationName' => 'serviceRole', ], 'status' => [ 'shape' => 'MacieStatus', 'locationName' => 'status', ], 'updatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'updatedAt', ], ], ], 'GetMasterAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetMasterAccountResponse' => [ 'type' => 'structure', 'members' => [ 'master' => [ 'shape' => 'Invitation', 'locationName' => 'master', ], ], ], 'GetMemberRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], ], 'required' => [ 'id', ], ], 'GetMemberResponse' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'administratorAccountId' => [ 'shape' => '__string', 'locationName' => 'administratorAccountId', ], 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'email' => [ 'shape' => '__string', 'locationName' => 'email', ], 'invitedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'invitedAt', ], 'masterAccountId' => [ 'shape' => '__string', 'locationName' => 'masterAccountId', ], 'relationshipStatus' => [ 'shape' => 'RelationshipStatus', 'locationName' => 'relationshipStatus', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'updatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'updatedAt', ], ], ], 'GetResourceProfileRequest' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], 'required' => [ 'resourceArn', ], ], 'GetResourceProfileResponse' => [ 'type' => 'structure', 'members' => [ 'profileUpdatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'profileUpdatedAt', ], 'sensitivityScore' => [ 'shape' => '__integer', 'locationName' => 'sensitivityScore', ], 'sensitivityScoreOverridden' => [ 'shape' => '__boolean', 'locationName' => 'sensitivityScoreOverridden', ], 'statistics' => [ 'shape' => 'ResourceStatistics', 'locationName' => 'statistics', ], ], ], 'GetRevealConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetRevealConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'RevealConfiguration', 'locationName' => 'configuration', ], 'retrievalConfiguration' => [ 'shape' => 'RetrievalConfiguration', 'locationName' => 'retrievalConfiguration', ], ], ], 'GetSensitiveDataOccurrencesAvailabilityRequest' => [ 'type' => 'structure', 'members' => [ 'findingId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'findingId', ], ], 'required' => [ 'findingId', ], ], 'GetSensitiveDataOccurrencesAvailabilityResponse' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'AvailabilityCode', 'locationName' => 'code', ], 'reasons' => [ 'shape' => '__listOfUnavailabilityReasonCode', 'locationName' => 'reasons', ], ], ], 'GetSensitiveDataOccurrencesRequest' => [ 'type' => 'structure', 'members' => [ 'findingId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'findingId', ], ], 'required' => [ 'findingId', ], ], 'GetSensitiveDataOccurrencesResponse' => [ 'type' => 'structure', 'members' => [ 'error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'sensitiveDataOccurrences' => [ 'shape' => 'SensitiveDataOccurrences', 'locationName' => 'sensitiveDataOccurrences', ], 'status' => [ 'shape' => 'RevealRequestStatus', 'locationName' => 'status', ], ], ], 'GetSensitivityInspectionTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], ], 'required' => [ 'id', ], ], 'GetSensitivityInspectionTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'excludes' => [ 'shape' => 'SensitivityInspectionTemplateExcludes', 'locationName' => 'excludes', ], 'includes' => [ 'shape' => 'SensitivityInspectionTemplateIncludes', 'locationName' => 'includes', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'sensitivityInspectionTemplateId' => [ 'shape' => 'SensitivityInspectionTemplateId', 'locationName' => 'sensitivityInspectionTemplateId', ], ], ], 'GetUsageStatisticsRequest' => [ 'type' => 'structure', 'members' => [ 'filterBy' => [ 'shape' => '__listOfUsageStatisticsFilter', 'locationName' => 'filterBy', ], 'maxResults' => [ 'shape' => '__integer', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'UsageStatisticsSortBy', 'locationName' => 'sortBy', ], 'timeRange' => [ 'shape' => 'TimeRange', 'locationName' => 'timeRange', ], ], ], 'GetUsageStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'records' => [ 'shape' => '__listOfUsageRecord', 'locationName' => 'records', ], 'timeRange' => [ 'shape' => 'TimeRange', 'locationName' => 'timeRange', ], ], ], 'GetUsageTotalsRequest' => [ 'type' => 'structure', 'members' => [ 'timeRange' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'timeRange', ], ], ], 'GetUsageTotalsResponse' => [ 'type' => 'structure', 'members' => [ 'timeRange' => [ 'shape' => 'TimeRange', 'locationName' => 'timeRange', ], 'usageTotals' => [ 'shape' => '__listOfUsageTotal', 'locationName' => 'usageTotals', ], ], ], 'GroupBy' => [ 'type' => 'string', 'enum' => [ 'resourcesAffected.s3Bucket.name', 'type', 'classificationDetails.jobId', 'severity.description', ], ], 'GroupCount' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => '__long', 'locationName' => 'count', ], 'groupKey' => [ 'shape' => '__string', 'locationName' => 'groupKey', ], ], ], 'IamUser' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'principalId' => [ 'shape' => '__string', 'locationName' => 'principalId', ], 'userName' => [ 'shape' => '__string', 'locationName' => 'userName', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 500, ], ], 'Invitation' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'invitationId' => [ 'shape' => '__string', 'locationName' => 'invitationId', ], 'invitedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'invitedAt', ], 'relationshipStatus' => [ 'shape' => 'RelationshipStatus', 'locationName' => 'relationshipStatus', ], ], ], 'IpAddressDetails' => [ 'type' => 'structure', 'members' => [ 'ipAddressV4' => [ 'shape' => '__string', 'locationName' => 'ipAddressV4', ], 'ipCity' => [ 'shape' => 'IpCity', 'locationName' => 'ipCity', ], 'ipCountry' => [ 'shape' => 'IpCountry', 'locationName' => 'ipCountry', ], 'ipGeoLocation' => [ 'shape' => 'IpGeoLocation', 'locationName' => 'ipGeoLocation', ], 'ipOwner' => [ 'shape' => 'IpOwner', 'locationName' => 'ipOwner', ], ], ], 'IpCity' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], ], ], 'IpCountry' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => '__string', 'locationName' => 'code', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], ], ], 'IpGeoLocation' => [ 'type' => 'structure', 'members' => [ 'lat' => [ 'shape' => '__double', 'locationName' => 'lat', ], 'lon' => [ 'shape' => '__double', 'locationName' => 'lon', ], ], ], 'IpOwner' => [ 'type' => 'structure', 'members' => [ 'asn' => [ 'shape' => '__string', 'locationName' => 'asn', ], 'asnOrg' => [ 'shape' => '__string', 'locationName' => 'asnOrg', ], 'isp' => [ 'shape' => '__string', 'locationName' => 'isp', ], 'org' => [ 'shape' => '__string', 'locationName' => 'org', ], ], ], 'IsDefinedInJob' => [ 'type' => 'string', 'enum' => [ 'TRUE', 'FALSE', 'UNKNOWN', ], ], 'IsMonitoredByJob' => [ 'type' => 'string', 'enum' => [ 'TRUE', 'FALSE', 'UNKNOWN', ], ], 'JobComparator' => [ 'type' => 'string', 'enum' => [ 'EQ', 'GT', 'GTE', 'LT', 'LTE', 'NE', 'CONTAINS', 'STARTS_WITH', ], ], 'JobDetails' => [ 'type' => 'structure', 'members' => [ 'isDefinedInJob' => [ 'shape' => 'IsDefinedInJob', 'locationName' => 'isDefinedInJob', ], 'isMonitoredByJob' => [ 'shape' => 'IsMonitoredByJob', 'locationName' => 'isMonitoredByJob', ], 'lastJobId' => [ 'shape' => '__string', 'locationName' => 'lastJobId', ], 'lastJobRunTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastJobRunTime', ], ], ], 'JobScheduleFrequency' => [ 'type' => 'structure', 'members' => [ 'dailySchedule' => [ 'shape' => 'DailySchedule', 'locationName' => 'dailySchedule', ], 'monthlySchedule' => [ 'shape' => 'MonthlySchedule', 'locationName' => 'monthlySchedule', ], 'weeklySchedule' => [ 'shape' => 'WeeklySchedule', 'locationName' => 'weeklySchedule', ], ], ], 'JobScopeTerm' => [ 'type' => 'structure', 'members' => [ 'simpleScopeTerm' => [ 'shape' => 'SimpleScopeTerm', 'locationName' => 'simpleScopeTerm', ], 'tagScopeTerm' => [ 'shape' => 'TagScopeTerm', 'locationName' => 'tagScopeTerm', ], ], ], 'JobScopingBlock' => [ 'type' => 'structure', 'members' => [ 'and' => [ 'shape' => '__listOfJobScopeTerm', 'locationName' => 'and', ], ], ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'PAUSED', 'CANCELLED', 'COMPLETE', 'IDLE', 'USER_PAUSED', ], ], 'JobSummary' => [ 'type' => 'structure', 'members' => [ 'bucketCriteria' => [ 'shape' => 'S3BucketCriteriaForJob', 'locationName' => 'bucketCriteria', ], 'bucketDefinitions' => [ 'shape' => '__listOfS3BucketDefinitionForJob', 'locationName' => 'bucketDefinitions', ], 'createdAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'jobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'jobStatus' => [ 'shape' => 'JobStatus', 'locationName' => 'jobStatus', ], 'jobType' => [ 'shape' => 'JobType', 'locationName' => 'jobType', ], 'lastRunErrorStatus' => [ 'shape' => 'LastRunErrorStatus', 'locationName' => 'lastRunErrorStatus', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'userPausedDetails' => [ 'shape' => 'UserPausedDetails', 'locationName' => 'userPausedDetails', ], ], ], 'JobType' => [ 'type' => 'string', 'enum' => [ 'ONE_TIME', 'SCHEDULED', ], ], 'KeyValuePair' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => '__string', 'locationName' => 'key', ], 'value' => [ 'shape' => '__string', 'locationName' => 'value', ], ], ], 'KeyValuePairList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyValuePair', ], ], 'LastRunErrorStatus' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LastRunErrorStatusCode', 'locationName' => 'code', ], ], ], 'LastRunErrorStatusCode' => [ 'type' => 'string', 'enum' => [ 'NONE', 'ERROR', ], ], 'ListAllowListsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAllowListsResponse' => [ 'type' => 'structure', 'members' => [ 'allowLists' => [ 'shape' => '__listOfAllowListSummary', 'locationName' => 'allowLists', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListClassificationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'filterCriteria' => [ 'shape' => 'ListJobsFilterCriteria', 'locationName' => 'filterCriteria', ], 'maxResults' => [ 'shape' => '__integer', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'sortCriteria' => [ 'shape' => 'ListJobsSortCriteria', 'locationName' => 'sortCriteria', ], ], ], 'ListClassificationJobsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => '__listOfJobSummary', 'locationName' => 'items', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListClassificationScopesRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListClassificationScopesResponse' => [ 'type' => 'structure', 'members' => [ 'classificationScopes' => [ 'shape' => '__listOfClassificationScopeSummary', 'locationName' => 'classificationScopes', ], 'nextToken' => [ 'shape' => 'NextToken', 'locationName' => 'nextToken', ], ], ], 'ListCustomDataIdentifiersRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => '__integer', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListCustomDataIdentifiersResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => '__listOfCustomDataIdentifierSummary', 'locationName' => 'items', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListFindingsFiltersRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFindingsFiltersResponse' => [ 'type' => 'structure', 'members' => [ 'findingsFilterListItems' => [ 'shape' => '__listOfFindingsFilterListItem', 'locationName' => 'findingsFilterListItems', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListFindingsRequest' => [ 'type' => 'structure', 'members' => [ 'findingCriteria' => [ 'shape' => 'FindingCriteria', 'locationName' => 'findingCriteria', ], 'maxResults' => [ 'shape' => '__integer', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'sortCriteria' => [ 'shape' => 'SortCriteria', 'locationName' => 'sortCriteria', ], ], ], 'ListFindingsResponse' => [ 'type' => 'structure', 'members' => [ 'findingIds' => [ 'shape' => '__listOf__string', 'locationName' => 'findingIds', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListInvitationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'invitations' => [ 'shape' => '__listOfInvitation', 'locationName' => 'invitations', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListJobsFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'excludes' => [ 'shape' => '__listOfListJobsFilterTerm', 'locationName' => 'excludes', ], 'includes' => [ 'shape' => '__listOfListJobsFilterTerm', 'locationName' => 'includes', ], ], ], 'ListJobsFilterKey' => [ 'type' => 'string', 'enum' => [ 'jobType', 'jobStatus', 'createdAt', 'name', ], ], 'ListJobsFilterTerm' => [ 'type' => 'structure', 'members' => [ 'comparator' => [ 'shape' => 'JobComparator', 'locationName' => 'comparator', ], 'key' => [ 'shape' => 'ListJobsFilterKey', 'locationName' => 'key', ], 'values' => [ 'shape' => '__listOf__string', 'locationName' => 'values', ], ], ], 'ListJobsSortAttributeName' => [ 'type' => 'string', 'enum' => [ 'createdAt', 'jobStatus', 'name', 'jobType', ], ], 'ListJobsSortCriteria' => [ 'type' => 'structure', 'members' => [ 'attributeName' => [ 'shape' => 'ListJobsSortAttributeName', 'locationName' => 'attributeName', ], 'orderBy' => [ 'shape' => 'OrderBy', 'locationName' => 'orderBy', ], ], ], 'ListManagedDataIdentifiersRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListManagedDataIdentifiersResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => '__listOfManagedDataIdentifierSummary', 'locationName' => 'items', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListMembersRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'onlyAssociated' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'onlyAssociated', ], ], ], 'ListMembersResponse' => [ 'type' => 'structure', 'members' => [ 'members' => [ 'shape' => '__listOfMember', 'locationName' => 'members', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListOrganizationAdminAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListOrganizationAdminAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'adminAccounts' => [ 'shape' => '__listOfAdminAccount', 'locationName' => 'adminAccounts', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListResourceProfileArtifactsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'resourceArn' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], 'required' => [ 'resourceArn', ], ], 'ListResourceProfileArtifactsResponse' => [ 'type' => 'structure', 'members' => [ 'artifacts' => [ 'shape' => '__listOfResourceProfileArtifact', 'locationName' => 'artifacts', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListResourceProfileDetectionsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'resourceArn' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], 'required' => [ 'resourceArn', ], ], 'ListResourceProfileDetectionsResponse' => [ 'type' => 'structure', 'members' => [ 'detections' => [ 'shape' => '__listOfDetection', 'locationName' => 'detections', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListSensitivityInspectionTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSensitivityInspectionTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'sensitivityInspectionTemplates' => [ 'shape' => '__listOfSensitivityInspectionTemplatesEntry', 'locationName' => 'sensitivityInspectionTemplates', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], 'required' => [ 'resourceArn', ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'MacieStatus' => [ 'type' => 'string', 'enum' => [ 'PAUSED', 'ENABLED', ], ], 'ManagedDataIdentifierSelector' => [ 'type' => 'string', 'enum' => [ 'ALL', 'EXCLUDE', 'INCLUDE', 'NONE', 'RECOMMENDED', ], ], 'ManagedDataIdentifierSummary' => [ 'type' => 'structure', 'members' => [ 'category' => [ 'shape' => 'SensitiveDataItemCategory', 'locationName' => 'category', ], 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], ], ], 'MatchingBucket' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'bucketName' => [ 'shape' => '__string', 'locationName' => 'bucketName', ], 'classifiableObjectCount' => [ 'shape' => '__long', 'locationName' => 'classifiableObjectCount', ], 'classifiableSizeInBytes' => [ 'shape' => '__long', 'locationName' => 'classifiableSizeInBytes', ], 'errorCode' => [ 'shape' => 'BucketMetadataErrorCode', 'locationName' => 'errorCode', ], 'errorMessage' => [ 'shape' => '__string', 'locationName' => 'errorMessage', ], 'jobDetails' => [ 'shape' => 'JobDetails', 'locationName' => 'jobDetails', ], 'lastAutomatedDiscoveryTime' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastAutomatedDiscoveryTime', ], 'objectCount' => [ 'shape' => '__long', 'locationName' => 'objectCount', ], 'objectCountByEncryptionType' => [ 'shape' => 'ObjectCountByEncryptionType', 'locationName' => 'objectCountByEncryptionType', ], 'sensitivityScore' => [ 'shape' => '__integer', 'locationName' => 'sensitivityScore', ], 'sizeInBytes' => [ 'shape' => '__long', 'locationName' => 'sizeInBytes', ], 'sizeInBytesCompressed' => [ 'shape' => '__long', 'locationName' => 'sizeInBytesCompressed', ], 'unclassifiableObjectCount' => [ 'shape' => 'ObjectLevelStatistics', 'locationName' => 'unclassifiableObjectCount', ], 'unclassifiableObjectSizeInBytes' => [ 'shape' => 'ObjectLevelStatistics', 'locationName' => 'unclassifiableObjectSizeInBytes', ], ], ], 'MatchingResource' => [ 'type' => 'structure', 'members' => [ 'matchingBucket' => [ 'shape' => 'MatchingBucket', 'locationName' => 'matchingBucket', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'min' => 1, 'max' => 25, ], 'Member' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'administratorAccountId' => [ 'shape' => '__string', 'locationName' => 'administratorAccountId', ], 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'email' => [ 'shape' => '__string', 'locationName' => 'email', ], 'invitedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'invitedAt', ], 'masterAccountId' => [ 'shape' => '__string', 'locationName' => 'masterAccountId', ], 'relationshipStatus' => [ 'shape' => 'RelationshipStatus', 'locationName' => 'relationshipStatus', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'updatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'updatedAt', ], ], ], 'MonthlySchedule' => [ 'type' => 'structure', 'members' => [ 'dayOfMonth' => [ 'shape' => '__integer', 'locationName' => 'dayOfMonth', ], ], ], 'NextToken' => [ 'type' => 'string', 'pattern' => '^.*$', ], 'ObjectCountByEncryptionType' => [ 'type' => 'structure', 'members' => [ 'customerManaged' => [ 'shape' => '__long', 'locationName' => 'customerManaged', ], 'kmsManaged' => [ 'shape' => '__long', 'locationName' => 'kmsManaged', ], 's3Managed' => [ 'shape' => '__long', 'locationName' => 's3Managed', ], 'unencrypted' => [ 'shape' => '__long', 'locationName' => 'unencrypted', ], 'unknown' => [ 'shape' => '__long', 'locationName' => 'unknown', ], ], ], 'ObjectLevelStatistics' => [ 'type' => 'structure', 'members' => [ 'fileType' => [ 'shape' => '__long', 'locationName' => 'fileType', ], 'storageClass' => [ 'shape' => '__long', 'locationName' => 'storageClass', ], 'total' => [ 'shape' => '__long', 'locationName' => 'total', ], ], ], 'Occurrences' => [ 'type' => 'structure', 'members' => [ 'cells' => [ 'shape' => 'Cells', 'locationName' => 'cells', ], 'lineRanges' => [ 'shape' => 'Ranges', 'locationName' => 'lineRanges', ], 'offsetRanges' => [ 'shape' => 'Ranges', 'locationName' => 'offsetRanges', ], 'pages' => [ 'shape' => 'Pages', 'locationName' => 'pages', ], 'records' => [ 'shape' => 'Records', 'locationName' => 'records', ], ], ], 'OrderBy' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'OriginType' => [ 'type' => 'string', 'enum' => [ 'SENSITIVE_DATA_DISCOVERY_JOB', 'AUTOMATED_SENSITIVE_DATA_DISCOVERY', ], ], 'Page' => [ 'type' => 'structure', 'members' => [ 'lineRange' => [ 'shape' => 'Range', 'locationName' => 'lineRange', ], 'offsetRange' => [ 'shape' => 'Range', 'locationName' => 'offsetRange', ], 'pageNumber' => [ 'shape' => '__long', 'locationName' => 'pageNumber', ], ], ], 'Pages' => [ 'type' => 'list', 'member' => [ 'shape' => 'Page', ], ], 'PolicyDetails' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'FindingAction', 'locationName' => 'action', ], 'actor' => [ 'shape' => 'FindingActor', 'locationName' => 'actor', ], ], ], 'PutClassificationExportConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'ClassificationExportConfiguration', 'locationName' => 'configuration', ], ], 'required' => [ 'configuration', ], ], 'PutClassificationExportConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'ClassificationExportConfiguration', 'locationName' => 'configuration', ], ], ], 'PutFindingsPublicationConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'clientToken' => [ 'shape' => '__string', 'locationName' => 'clientToken', 'idempotencyToken' => true, ], 'securityHubConfiguration' => [ 'shape' => 'SecurityHubConfiguration', 'locationName' => 'securityHubConfiguration', ], ], ], 'PutFindingsPublicationConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'Range' => [ 'type' => 'structure', 'members' => [ 'end' => [ 'shape' => '__long', 'locationName' => 'end', ], 'start' => [ 'shape' => '__long', 'locationName' => 'start', ], 'startColumn' => [ 'shape' => '__long', 'locationName' => 'startColumn', ], ], ], 'Ranges' => [ 'type' => 'list', 'member' => [ 'shape' => 'Range', ], ], 'Record' => [ 'type' => 'structure', 'members' => [ 'jsonPath' => [ 'shape' => '__string', 'locationName' => 'jsonPath', ], 'recordIndex' => [ 'shape' => '__long', 'locationName' => 'recordIndex', ], ], ], 'Records' => [ 'type' => 'list', 'member' => [ 'shape' => 'Record', ], ], 'RelationshipStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Paused', 'Invited', 'Created', 'Removed', 'Resigned', 'EmailVerificationInProgress', 'EmailVerificationFailed', 'RegionDisabled', 'AccountSuspended', ], ], 'ReplicationDetails' => [ 'type' => 'structure', 'members' => [ 'replicated' => [ 'shape' => '__boolean', 'locationName' => 'replicated', ], 'replicatedExternally' => [ 'shape' => '__boolean', 'locationName' => 'replicatedExternally', ], 'replicationAccounts' => [ 'shape' => '__listOf__string', 'locationName' => 'replicationAccounts', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 404, ], ], 'ResourceProfileArtifact' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'classificationResultStatus' => [ 'shape' => '__string', 'locationName' => 'classificationResultStatus', ], 'sensitive' => [ 'shape' => '__boolean', 'locationName' => 'sensitive', ], ], 'required' => [ 'classificationResultStatus', 'arn', ], ], 'ResourceStatistics' => [ 'type' => 'structure', 'members' => [ 'totalBytesClassified' => [ 'shape' => '__long', 'locationName' => 'totalBytesClassified', ], 'totalDetections' => [ 'shape' => '__long', 'locationName' => 'totalDetections', ], 'totalDetectionsSuppressed' => [ 'shape' => '__long', 'locationName' => 'totalDetectionsSuppressed', ], 'totalItemsClassified' => [ 'shape' => '__long', 'locationName' => 'totalItemsClassified', ], 'totalItemsSensitive' => [ 'shape' => '__long', 'locationName' => 'totalItemsSensitive', ], 'totalItemsSkipped' => [ 'shape' => '__long', 'locationName' => 'totalItemsSkipped', ], 'totalItemsSkippedInvalidEncryption' => [ 'shape' => '__long', 'locationName' => 'totalItemsSkippedInvalidEncryption', ], 'totalItemsSkippedInvalidKms' => [ 'shape' => '__long', 'locationName' => 'totalItemsSkippedInvalidKms', ], 'totalItemsSkippedPermissionDenied' => [ 'shape' => '__long', 'locationName' => 'totalItemsSkippedPermissionDenied', ], ], ], 'ResourcesAffected' => [ 'type' => 'structure', 'members' => [ 's3Bucket' => [ 'shape' => 'S3Bucket', 'locationName' => 's3Bucket', ], 's3Object' => [ 'shape' => 'S3Object', 'locationName' => 's3Object', ], ], ], 'RetrievalConfiguration' => [ 'type' => 'structure', 'members' => [ 'externalId' => [ 'shape' => '__string', 'locationName' => 'externalId', ], 'retrievalMode' => [ 'shape' => 'RetrievalMode', 'locationName' => 'retrievalMode', ], 'roleName' => [ 'shape' => '__stringMin1Max64PatternW', 'locationName' => 'roleName', ], ], 'required' => [ 'retrievalMode', ], ], 'RetrievalMode' => [ 'type' => 'string', 'enum' => [ 'CALLER_CREDENTIALS', 'ASSUME_ROLE', ], ], 'RevealConfiguration' => [ 'type' => 'structure', 'members' => [ 'kmsKeyId' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'kmsKeyId', ], 'status' => [ 'shape' => 'RevealStatus', 'locationName' => 'status', ], ], 'required' => [ 'status', ], ], 'RevealRequestStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'PROCESSING', 'ERROR', ], ], 'RevealStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'S3Bucket' => [ 'type' => 'structure', 'members' => [ 'allowsUnencryptedObjectUploads' => [ 'shape' => 'AllowsUnencryptedObjectUploads', 'locationName' => 'allowsUnencryptedObjectUploads', ], 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'createdAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'defaultServerSideEncryption' => [ 'shape' => 'ServerSideEncryption', 'locationName' => 'defaultServerSideEncryption', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'owner' => [ 'shape' => 'S3BucketOwner', 'locationName' => 'owner', ], 'publicAccess' => [ 'shape' => 'BucketPublicAccess', 'locationName' => 'publicAccess', ], 'tags' => [ 'shape' => 'KeyValuePairList', 'locationName' => 'tags', ], ], ], 'S3BucketCriteriaForJob' => [ 'type' => 'structure', 'members' => [ 'excludes' => [ 'shape' => 'CriteriaBlockForJob', 'locationName' => 'excludes', ], 'includes' => [ 'shape' => 'CriteriaBlockForJob', 'locationName' => 'includes', ], ], ], 'S3BucketDefinitionForJob' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'buckets' => [ 'shape' => '__listOf__string', 'locationName' => 'buckets', ], ], 'required' => [ 'accountId', 'buckets', ], ], 'S3BucketName' => [ 'type' => 'string', 'pattern' => '^[A-Za-z0-9.\\-_]{3,255}$', ], 'S3BucketOwner' => [ 'type' => 'structure', 'members' => [ 'displayName' => [ 'shape' => '__string', 'locationName' => 'displayName', ], 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], ], ], 'S3ClassificationScope' => [ 'type' => 'structure', 'members' => [ 'excludes' => [ 'shape' => 'S3ClassificationScopeExclusion', 'locationName' => 'excludes', ], ], 'required' => [ 'excludes', ], ], 'S3ClassificationScopeExclusion' => [ 'type' => 'structure', 'members' => [ 'bucketNames' => [ 'shape' => '__listOfS3BucketName', 'locationName' => 'bucketNames', ], ], 'required' => [ 'bucketNames', ], ], 'S3ClassificationScopeExclusionUpdate' => [ 'type' => 'structure', 'members' => [ 'bucketNames' => [ 'shape' => '__listOfS3BucketName', 'locationName' => 'bucketNames', ], 'operation' => [ 'shape' => 'ClassificationScopeUpdateOperation', 'locationName' => 'operation', ], ], 'required' => [ 'bucketNames', 'operation', ], ], 'S3ClassificationScopeUpdate' => [ 'type' => 'structure', 'members' => [ 'excludes' => [ 'shape' => 'S3ClassificationScopeExclusionUpdate', 'locationName' => 'excludes', ], ], 'required' => [ 'excludes', ], ], 'S3Destination' => [ 'type' => 'structure', 'members' => [ 'bucketName' => [ 'shape' => '__string', 'locationName' => 'bucketName', ], 'keyPrefix' => [ 'shape' => '__string', 'locationName' => 'keyPrefix', ], 'kmsKeyArn' => [ 'shape' => '__string', 'locationName' => 'kmsKeyArn', ], ], 'required' => [ 'bucketName', 'kmsKeyArn', ], ], 'S3JobDefinition' => [ 'type' => 'structure', 'members' => [ 'bucketCriteria' => [ 'shape' => 'S3BucketCriteriaForJob', 'locationName' => 'bucketCriteria', ], 'bucketDefinitions' => [ 'shape' => '__listOfS3BucketDefinitionForJob', 'locationName' => 'bucketDefinitions', ], 'scoping' => [ 'shape' => 'Scoping', 'locationName' => 'scoping', ], ], ], 'S3Object' => [ 'type' => 'structure', 'members' => [ 'bucketArn' => [ 'shape' => '__string', 'locationName' => 'bucketArn', ], 'eTag' => [ 'shape' => '__string', 'locationName' => 'eTag', ], 'extension' => [ 'shape' => '__string', 'locationName' => 'extension', ], 'key' => [ 'shape' => '__string', 'locationName' => 'key', ], 'lastModified' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastModified', ], 'path' => [ 'shape' => '__string', 'locationName' => 'path', ], 'publicAccess' => [ 'shape' => '__boolean', 'locationName' => 'publicAccess', ], 'serverSideEncryption' => [ 'shape' => 'ServerSideEncryption', 'locationName' => 'serverSideEncryption', ], 'size' => [ 'shape' => '__long', 'locationName' => 'size', ], 'storageClass' => [ 'shape' => 'StorageClass', 'locationName' => 'storageClass', ], 'tags' => [ 'shape' => 'KeyValuePairList', 'locationName' => 'tags', ], 'versionId' => [ 'shape' => '__string', 'locationName' => 'versionId', ], ], ], 'S3WordsList' => [ 'type' => 'structure', 'members' => [ 'bucketName' => [ 'shape' => '__stringMin3Max255PatternAZaZ093255', 'locationName' => 'bucketName', ], 'objectKey' => [ 'shape' => '__stringMin1Max1024PatternSS', 'locationName' => 'objectKey', ], ], 'required' => [ 'bucketName', 'objectKey', ], ], 'ScopeFilterKey' => [ 'type' => 'string', 'enum' => [ 'OBJECT_EXTENSION', 'OBJECT_LAST_MODIFIED_DATE', 'OBJECT_SIZE', 'OBJECT_KEY', ], ], 'Scoping' => [ 'type' => 'structure', 'members' => [ 'excludes' => [ 'shape' => 'JobScopingBlock', 'locationName' => 'excludes', ], 'includes' => [ 'shape' => 'JobScopingBlock', 'locationName' => 'includes', ], ], ], 'SearchResourcesBucketCriteria' => [ 'type' => 'structure', 'members' => [ 'excludes' => [ 'shape' => 'SearchResourcesCriteriaBlock', 'locationName' => 'excludes', ], 'includes' => [ 'shape' => 'SearchResourcesCriteriaBlock', 'locationName' => 'includes', ], ], ], 'SearchResourcesComparator' => [ 'type' => 'string', 'enum' => [ 'EQ', 'NE', ], ], 'SearchResourcesCriteria' => [ 'type' => 'structure', 'members' => [ 'simpleCriterion' => [ 'shape' => 'SearchResourcesSimpleCriterion', 'locationName' => 'simpleCriterion', ], 'tagCriterion' => [ 'shape' => 'SearchResourcesTagCriterion', 'locationName' => 'tagCriterion', ], ], ], 'SearchResourcesCriteriaBlock' => [ 'type' => 'structure', 'members' => [ 'and' => [ 'shape' => '__listOfSearchResourcesCriteria', 'locationName' => 'and', ], ], ], 'SearchResourcesRequest' => [ 'type' => 'structure', 'members' => [ 'bucketCriteria' => [ 'shape' => 'SearchResourcesBucketCriteria', 'locationName' => 'bucketCriteria', ], 'maxResults' => [ 'shape' => '__integer', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'sortCriteria' => [ 'shape' => 'SearchResourcesSortCriteria', 'locationName' => 'sortCriteria', ], ], ], 'SearchResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'matchingResources' => [ 'shape' => '__listOfMatchingResource', 'locationName' => 'matchingResources', ], 'nextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'SearchResourcesSimpleCriterion' => [ 'type' => 'structure', 'members' => [ 'comparator' => [ 'shape' => 'SearchResourcesComparator', 'locationName' => 'comparator', ], 'key' => [ 'shape' => 'SearchResourcesSimpleCriterionKey', 'locationName' => 'key', ], 'values' => [ 'shape' => '__listOf__string', 'locationName' => 'values', ], ], ], 'SearchResourcesSimpleCriterionKey' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_ID', 'S3_BUCKET_NAME', 'S3_BUCKET_EFFECTIVE_PERMISSION', 'S3_BUCKET_SHARED_ACCESS', ], ], 'SearchResourcesSortAttributeName' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_ID', 'RESOURCE_NAME', 'S3_CLASSIFIABLE_OBJECT_COUNT', 'S3_CLASSIFIABLE_SIZE_IN_BYTES', ], ], 'SearchResourcesSortCriteria' => [ 'type' => 'structure', 'members' => [ 'attributeName' => [ 'shape' => 'SearchResourcesSortAttributeName', 'locationName' => 'attributeName', ], 'orderBy' => [ 'shape' => 'OrderBy', 'locationName' => 'orderBy', ], ], ], 'SearchResourcesTagCriterion' => [ 'type' => 'structure', 'members' => [ 'comparator' => [ 'shape' => 'SearchResourcesComparator', 'locationName' => 'comparator', ], 'tagValues' => [ 'shape' => '__listOfSearchResourcesTagCriterionPair', 'locationName' => 'tagValues', ], ], ], 'SearchResourcesTagCriterionPair' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => '__string', 'locationName' => 'key', ], 'value' => [ 'shape' => '__string', 'locationName' => 'value', ], ], ], 'SecurityHubConfiguration' => [ 'type' => 'structure', 'members' => [ 'publishClassificationFindings' => [ 'shape' => '__boolean', 'locationName' => 'publishClassificationFindings', ], 'publishPolicyFindings' => [ 'shape' => '__boolean', 'locationName' => 'publishPolicyFindings', ], ], 'required' => [ 'publishPolicyFindings', 'publishClassificationFindings', ], ], 'SensitiveData' => [ 'type' => 'list', 'member' => [ 'shape' => 'SensitiveDataItem', ], ], 'SensitiveDataItem' => [ 'type' => 'structure', 'members' => [ 'category' => [ 'shape' => 'SensitiveDataItemCategory', 'locationName' => 'category', ], 'detections' => [ 'shape' => 'DefaultDetections', 'locationName' => 'detections', ], 'totalCount' => [ 'shape' => '__long', 'locationName' => 'totalCount', ], ], ], 'SensitiveDataItemCategory' => [ 'type' => 'string', 'enum' => [ 'FINANCIAL_INFORMATION', 'PERSONAL_INFORMATION', 'CREDENTIALS', 'CUSTOM_IDENTIFIER', ], ], 'SensitiveDataOccurrences' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__listOfDetectedDataDetails', ], ], 'SensitivityAggregations' => [ 'type' => 'structure', 'members' => [ 'classifiableSizeInBytes' => [ 'shape' => '__long', 'locationName' => 'classifiableSizeInBytes', ], 'publiclyAccessibleCount' => [ 'shape' => '__long', 'locationName' => 'publiclyAccessibleCount', ], 'totalCount' => [ 'shape' => '__long', 'locationName' => 'totalCount', ], 'totalSizeInBytes' => [ 'shape' => '__long', 'locationName' => 'totalSizeInBytes', ], ], ], 'SensitivityInspectionTemplateExcludes' => [ 'type' => 'structure', 'members' => [ 'managedDataIdentifierIds' => [ 'shape' => '__listOf__string', 'locationName' => 'managedDataIdentifierIds', ], ], ], 'SensitivityInspectionTemplateId' => [ 'type' => 'string', ], 'SensitivityInspectionTemplateIncludes' => [ 'type' => 'structure', 'members' => [ 'allowListIds' => [ 'shape' => '__listOf__string', 'locationName' => 'allowListIds', ], 'customDataIdentifierIds' => [ 'shape' => '__listOf__string', 'locationName' => 'customDataIdentifierIds', ], 'managedDataIdentifierIds' => [ 'shape' => '__listOf__string', 'locationName' => 'managedDataIdentifierIds', ], ], ], 'SensitivityInspectionTemplatesEntry' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], ], ], 'ServerSideEncryption' => [ 'type' => 'structure', 'members' => [ 'encryptionType' => [ 'shape' => 'EncryptionType', 'locationName' => 'encryptionType', ], 'kmsMasterKeyId' => [ 'shape' => '__string', 'locationName' => 'kmsMasterKeyId', ], ], ], 'ServiceLimit' => [ 'type' => 'structure', 'members' => [ 'isServiceLimited' => [ 'shape' => '__boolean', 'locationName' => 'isServiceLimited', ], 'unit' => [ 'shape' => 'Unit', 'locationName' => 'unit', ], 'value' => [ 'shape' => '__long', 'locationName' => 'value', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 402, ], ], 'SessionContext' => [ 'type' => 'structure', 'members' => [ 'attributes' => [ 'shape' => 'SessionContextAttributes', 'locationName' => 'attributes', ], 'sessionIssuer' => [ 'shape' => 'SessionIssuer', 'locationName' => 'sessionIssuer', ], ], ], 'SessionContextAttributes' => [ 'type' => 'structure', 'members' => [ 'creationDate' => [ 'shape' => '__timestampIso8601', 'locationName' => 'creationDate', ], 'mfaAuthenticated' => [ 'shape' => '__boolean', 'locationName' => 'mfaAuthenticated', ], ], ], 'SessionIssuer' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'principalId' => [ 'shape' => '__string', 'locationName' => 'principalId', ], 'type' => [ 'shape' => '__string', 'locationName' => 'type', ], 'userName' => [ 'shape' => '__string', 'locationName' => 'userName', ], ], ], 'Severity' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'SeverityDescription', 'locationName' => 'description', ], 'score' => [ 'shape' => '__long', 'locationName' => 'score', ], ], ], 'SeverityDescription' => [ 'type' => 'string', 'enum' => [ 'Low', 'Medium', 'High', ], ], 'SeverityLevel' => [ 'type' => 'structure', 'members' => [ 'occurrencesThreshold' => [ 'shape' => '__long', 'locationName' => 'occurrencesThreshold', ], 'severity' => [ 'shape' => 'DataIdentifierSeverity', 'locationName' => 'severity', ], ], 'required' => [ 'occurrencesThreshold', 'severity', ], ], 'SeverityLevelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SeverityLevel', ], ], 'SharedAccess' => [ 'type' => 'string', 'enum' => [ 'EXTERNAL', 'INTERNAL', 'NOT_SHARED', 'UNKNOWN', ], ], 'SimpleCriterionForJob' => [ 'type' => 'structure', 'members' => [ 'comparator' => [ 'shape' => 'JobComparator', 'locationName' => 'comparator', ], 'key' => [ 'shape' => 'SimpleCriterionKeyForJob', 'locationName' => 'key', ], 'values' => [ 'shape' => '__listOf__string', 'locationName' => 'values', ], ], ], 'SimpleCriterionKeyForJob' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_ID', 'S3_BUCKET_NAME', 'S3_BUCKET_EFFECTIVE_PERMISSION', 'S3_BUCKET_SHARED_ACCESS', ], ], 'SimpleScopeTerm' => [ 'type' => 'structure', 'members' => [ 'comparator' => [ 'shape' => 'JobComparator', 'locationName' => 'comparator', ], 'key' => [ 'shape' => 'ScopeFilterKey', 'locationName' => 'key', ], 'values' => [ 'shape' => '__listOf__string', 'locationName' => 'values', ], ], ], 'SortCriteria' => [ 'type' => 'structure', 'members' => [ 'attributeName' => [ 'shape' => '__string', 'locationName' => 'attributeName', ], 'orderBy' => [ 'shape' => 'OrderBy', 'locationName' => 'orderBy', ], ], ], 'Statistics' => [ 'type' => 'structure', 'members' => [ 'approximateNumberOfObjectsToProcess' => [ 'shape' => '__double', 'locationName' => 'approximateNumberOfObjectsToProcess', ], 'numberOfRuns' => [ 'shape' => '__double', 'locationName' => 'numberOfRuns', ], ], ], 'StorageClass' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'REDUCED_REDUNDANCY', 'STANDARD_IA', 'INTELLIGENT_TIERING', 'DEEP_ARCHIVE', 'ONEZONE_IA', 'GLACIER', 'GLACIER_IR', 'OUTPOSTS', ], ], 'SuppressDataIdentifier' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'type' => [ 'shape' => 'DataIdentifierType', 'locationName' => 'type', ], ], ], 'TagCriterionForJob' => [ 'type' => 'structure', 'members' => [ 'comparator' => [ 'shape' => 'JobComparator', 'locationName' => 'comparator', ], 'tagValues' => [ 'shape' => '__listOfTagCriterionPairForJob', 'locationName' => 'tagValues', ], ], ], 'TagCriterionPairForJob' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => '__string', 'locationName' => 'key', ], 'value' => [ 'shape' => '__string', 'locationName' => 'value', ], ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__string', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'resourceArn', 'tags', ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagScopeTerm' => [ 'type' => 'structure', 'members' => [ 'comparator' => [ 'shape' => 'JobComparator', 'locationName' => 'comparator', ], 'key' => [ 'shape' => '__string', 'locationName' => 'key', ], 'tagValues' => [ 'shape' => '__listOfTagValuePair', 'locationName' => 'tagValues', ], 'target' => [ 'shape' => 'TagTarget', 'locationName' => 'target', ], ], ], 'TagTarget' => [ 'type' => 'string', 'enum' => [ 'S3_OBJECT', ], ], 'TagValuePair' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => '__string', 'locationName' => 'key', ], 'value' => [ 'shape' => '__string', 'locationName' => 'value', ], ], ], 'TestCustomDataIdentifierRequest' => [ 'type' => 'structure', 'members' => [ 'ignoreWords' => [ 'shape' => '__listOf__string', 'locationName' => 'ignoreWords', ], 'keywords' => [ 'shape' => '__listOf__string', 'locationName' => 'keywords', ], 'maximumMatchDistance' => [ 'shape' => '__integer', 'locationName' => 'maximumMatchDistance', ], 'regex' => [ 'shape' => '__string', 'locationName' => 'regex', ], 'sampleText' => [ 'shape' => '__string', 'locationName' => 'sampleText', ], ], 'required' => [ 'regex', 'sampleText', ], ], 'TestCustomDataIdentifierResponse' => [ 'type' => 'structure', 'members' => [ 'matchCount' => [ 'shape' => '__integer', 'locationName' => 'matchCount', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 429, ], ], 'TimeRange' => [ 'type' => 'string', 'enum' => [ 'MONTH_TO_DATE', 'PAST_30_DAYS', ], ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Type' => [ 'type' => 'string', 'enum' => [ 'NONE', 'AES256', 'aws:kms', ], ], 'UnavailabilityReasonCode' => [ 'type' => 'string', 'enum' => [ 'OBJECT_EXCEEDS_SIZE_QUOTA', 'UNSUPPORTED_OBJECT_TYPE', 'UNSUPPORTED_FINDING_TYPE', 'INVALID_CLASSIFICATION_RESULT', 'OBJECT_UNAVAILABLE', 'ACCOUNT_NOT_IN_ORGANIZATION', 'MISSING_GET_MEMBER_PERMISSION', 'ROLE_TOO_PERMISSIVE', 'MEMBER_ROLE_TOO_PERMISSIVE', 'INVALID_RESULT_SIGNATURE', 'RESULT_NOT_SIGNED', ], ], 'Unit' => [ 'type' => 'string', 'enum' => [ 'TERABYTES', ], ], 'UnprocessableEntityException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'required' => [ 'message', ], 'exception' => true, 'error' => [ 'httpStatusCode' => 422, ], ], 'UnprocessedAccount' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'errorCode' => [ 'shape' => 'ErrorCode', 'locationName' => 'errorCode', ], 'errorMessage' => [ 'shape' => '__string', 'locationName' => 'errorMessage', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => '__listOf__string', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], 'required' => [ 'tagKeys', 'resourceArn', ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAllowListRequest' => [ 'type' => 'structure', 'members' => [ 'criteria' => [ 'shape' => 'AllowListCriteria', 'locationName' => 'criteria', ], 'description' => [ 'shape' => '__stringMin1Max512PatternSS', 'locationName' => 'description', ], 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], 'name' => [ 'shape' => '__stringMin1Max128Pattern', 'locationName' => 'name', ], ], 'required' => [ 'id', 'criteria', 'name', ], ], 'UpdateAllowListResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__stringMin71Max89PatternArnAwsAwsCnAwsUsGovMacie2AZ19920D12AllowListAZ0922', 'locationName' => 'arn', ], 'id' => [ 'shape' => '__stringMin22Max22PatternAZ0922', 'locationName' => 'id', ], ], ], 'UpdateAutomatedDiscoveryConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'AutomatedDiscoveryStatus', 'locationName' => 'status', ], ], 'required' => [ 'status', ], ], 'UpdateAutomatedDiscoveryConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateClassificationJobRequest' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'jobId', ], 'jobStatus' => [ 'shape' => 'JobStatus', 'locationName' => 'jobStatus', ], ], 'required' => [ 'jobId', 'jobStatus', ], ], 'UpdateClassificationJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateClassificationScopeRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], 's3' => [ 'shape' => 'S3ClassificationScopeUpdate', 'locationName' => 's3', ], ], 'required' => [ 'id', ], ], 'UpdateClassificationScopeResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFindingsFilterRequest' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'FindingsFilterAction', 'locationName' => 'action', ], 'clientToken' => [ 'shape' => '__string', 'locationName' => 'clientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'findingCriteria' => [ 'shape' => 'FindingCriteria', 'locationName' => 'findingCriteria', ], 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], 'name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'position' => [ 'shape' => '__integer', 'locationName' => 'position', ], ], 'required' => [ 'id', ], ], 'UpdateFindingsFilterResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'id' => [ 'shape' => '__string', 'locationName' => 'id', ], ], ], 'UpdateMacieSessionRequest' => [ 'type' => 'structure', 'members' => [ 'findingPublishingFrequency' => [ 'shape' => 'FindingPublishingFrequency', 'locationName' => 'findingPublishingFrequency', ], 'status' => [ 'shape' => 'MacieStatus', 'locationName' => 'status', ], ], ], 'UpdateMacieSessionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMemberSessionRequest' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], 'status' => [ 'shape' => 'MacieStatus', 'locationName' => 'status', ], ], 'required' => [ 'id', 'status', ], ], 'UpdateMemberSessionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateOrganizationConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'autoEnable' => [ 'shape' => '__boolean', 'locationName' => 'autoEnable', ], ], 'required' => [ 'autoEnable', ], ], 'UpdateOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateResourceProfileDetectionsRequest' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'suppressDataIdentifiers' => [ 'shape' => '__listOfSuppressDataIdentifier', 'locationName' => 'suppressDataIdentifiers', ], ], 'required' => [ 'resourceArn', ], ], 'UpdateResourceProfileDetectionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateResourceProfileRequest' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'sensitivityScoreOverride' => [ 'shape' => '__integer', 'locationName' => 'sensitivityScoreOverride', ], ], 'required' => [ 'resourceArn', ], ], 'UpdateResourceProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRetrievalConfiguration' => [ 'type' => 'structure', 'members' => [ 'retrievalMode' => [ 'shape' => 'RetrievalMode', 'locationName' => 'retrievalMode', ], 'roleName' => [ 'shape' => '__stringMin1Max64PatternW', 'locationName' => 'roleName', ], ], 'required' => [ 'retrievalMode', ], ], 'UpdateRevealConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'RevealConfiguration', 'locationName' => 'configuration', ], 'retrievalConfiguration' => [ 'shape' => 'UpdateRetrievalConfiguration', 'locationName' => 'retrievalConfiguration', ], ], 'required' => [ 'configuration', ], ], 'UpdateRevealConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'RevealConfiguration', 'locationName' => 'configuration', ], 'retrievalConfiguration' => [ 'shape' => 'RetrievalConfiguration', 'locationName' => 'retrievalConfiguration', ], ], ], 'UpdateSensitivityInspectionTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'excludes' => [ 'shape' => 'SensitivityInspectionTemplateExcludes', 'locationName' => 'excludes', ], 'id' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'id', ], 'includes' => [ 'shape' => 'SensitivityInspectionTemplateIncludes', 'locationName' => 'includes', ], ], 'required' => [ 'id', ], ], 'UpdateSensitivityInspectionTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'UsageByAccount' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', 'locationName' => 'currency', ], 'estimatedCost' => [ 'shape' => '__string', 'locationName' => 'estimatedCost', ], 'serviceLimit' => [ 'shape' => 'ServiceLimit', 'locationName' => 'serviceLimit', ], 'type' => [ 'shape' => 'UsageType', 'locationName' => 'type', ], ], ], 'UsageRecord' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'automatedDiscoveryFreeTrialStartDate' => [ 'shape' => '__timestampIso8601', 'locationName' => 'automatedDiscoveryFreeTrialStartDate', ], 'freeTrialStartDate' => [ 'shape' => '__timestampIso8601', 'locationName' => 'freeTrialStartDate', ], 'usage' => [ 'shape' => '__listOfUsageByAccount', 'locationName' => 'usage', ], ], ], 'UsageStatisticsFilter' => [ 'type' => 'structure', 'members' => [ 'comparator' => [ 'shape' => 'UsageStatisticsFilterComparator', 'locationName' => 'comparator', ], 'key' => [ 'shape' => 'UsageStatisticsFilterKey', 'locationName' => 'key', ], 'values' => [ 'shape' => '__listOf__string', 'locationName' => 'values', ], ], ], 'UsageStatisticsFilterComparator' => [ 'type' => 'string', 'enum' => [ 'GT', 'GTE', 'LT', 'LTE', 'EQ', 'NE', 'CONTAINS', ], ], 'UsageStatisticsFilterKey' => [ 'type' => 'string', 'enum' => [ 'accountId', 'serviceLimit', 'freeTrialStartDate', 'total', ], ], 'UsageStatisticsSortBy' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'UsageStatisticsSortKey', 'locationName' => 'key', ], 'orderBy' => [ 'shape' => 'OrderBy', 'locationName' => 'orderBy', ], ], ], 'UsageStatisticsSortKey' => [ 'type' => 'string', 'enum' => [ 'accountId', 'total', 'serviceLimitValue', 'freeTrialStartDate', ], ], 'UsageTotal' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', 'locationName' => 'currency', ], 'estimatedCost' => [ 'shape' => '__string', 'locationName' => 'estimatedCost', ], 'type' => [ 'shape' => 'UsageType', 'locationName' => 'type', ], ], ], 'UsageType' => [ 'type' => 'string', 'enum' => [ 'DATA_INVENTORY_EVALUATION', 'SENSITIVE_DATA_DISCOVERY', 'AUTOMATED_SENSITIVE_DATA_DISCOVERY', 'AUTOMATED_OBJECT_MONITORING', ], ], 'UserIdentity' => [ 'type' => 'structure', 'members' => [ 'assumedRole' => [ 'shape' => 'AssumedRole', 'locationName' => 'assumedRole', ], 'awsAccount' => [ 'shape' => 'AwsAccount', 'locationName' => 'awsAccount', ], 'awsService' => [ 'shape' => 'AwsService', 'locationName' => 'awsService', ], 'federatedUser' => [ 'shape' => 'FederatedUser', 'locationName' => 'federatedUser', ], 'iamUser' => [ 'shape' => 'IamUser', 'locationName' => 'iamUser', ], 'root' => [ 'shape' => 'UserIdentityRoot', 'locationName' => 'root', ], 'type' => [ 'shape' => 'UserIdentityType', 'locationName' => 'type', ], ], ], 'UserIdentityRoot' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => '__string', 'locationName' => 'accountId', ], 'arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'principalId' => [ 'shape' => '__string', 'locationName' => 'principalId', ], ], ], 'UserIdentityType' => [ 'type' => 'string', 'enum' => [ 'AssumedRole', 'IAMUser', 'FederatedUser', 'Root', 'AWSAccount', 'AWSService', ], ], 'UserPausedDetails' => [ 'type' => 'structure', 'members' => [ 'jobExpiresAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'jobExpiresAt', ], 'jobImminentExpirationHealthEventArn' => [ 'shape' => '__string', 'locationName' => 'jobImminentExpirationHealthEventArn', ], 'jobPausedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'jobPausedAt', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 400, ], ], 'WeeklySchedule' => [ 'type' => 'structure', 'members' => [ 'dayOfWeek' => [ 'shape' => 'DayOfWeek', 'locationName' => 'dayOfWeek', ], ], ], '__boolean' => [ 'type' => 'boolean', ], '__double' => [ 'type' => 'double', ], '__integer' => [ 'type' => 'integer', ], '__listOfAdminAccount' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdminAccount', ], ], '__listOfAllowListSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'AllowListSummary', ], ], '__listOfBatchGetCustomDataIdentifierSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetCustomDataIdentifierSummary', ], ], '__listOfBucketMetadata' => [ 'type' => 'list', 'member' => [ 'shape' => 'BucketMetadata', ], ], '__listOfClassificationScopeSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClassificationScopeSummary', ], ], '__listOfCriteriaForJob' => [ 'type' => 'list', 'member' => [ 'shape' => 'CriteriaForJob', ], ], '__listOfCustomDataIdentifierSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomDataIdentifierSummary', ], ], '__listOfDetectedDataDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectedDataDetails', ], ], '__listOfDetection' => [ 'type' => 'list', 'member' => [ 'shape' => 'Detection', ], ], '__listOfFinding' => [ 'type' => 'list', 'member' => [ 'shape' => 'Finding', ], ], '__listOfFindingType' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingType', ], ], '__listOfFindingsFilterListItem' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingsFilterListItem', ], ], '__listOfGroupCount' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupCount', ], ], '__listOfInvitation' => [ 'type' => 'list', 'member' => [ 'shape' => 'Invitation', ], ], '__listOfJobScopeTerm' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobScopeTerm', ], ], '__listOfJobSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobSummary', ], ], '__listOfKeyValuePair' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyValuePair', ], ], '__listOfListJobsFilterTerm' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListJobsFilterTerm', ], ], '__listOfManagedDataIdentifierSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedDataIdentifierSummary', ], ], '__listOfMatchingResource' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchingResource', ], ], '__listOfMember' => [ 'type' => 'list', 'member' => [ 'shape' => 'Member', ], ], '__listOfResourceProfileArtifact' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceProfileArtifact', ], ], '__listOfS3BucketDefinitionForJob' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3BucketDefinitionForJob', ], ], '__listOfS3BucketName' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3BucketName', ], ], '__listOfSearchResourcesCriteria' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchResourcesCriteria', ], ], '__listOfSearchResourcesTagCriterionPair' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchResourcesTagCriterionPair', ], ], '__listOfSensitivityInspectionTemplatesEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'SensitivityInspectionTemplatesEntry', ], ], '__listOfSuppressDataIdentifier' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuppressDataIdentifier', ], ], '__listOfTagCriterionPairForJob' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagCriterionPairForJob', ], ], '__listOfTagValuePair' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagValuePair', ], ], '__listOfUnavailabilityReasonCode' => [ 'type' => 'list', 'min' => 0, 'member' => [ 'shape' => 'UnavailabilityReasonCode', ], ], '__listOfUnprocessedAccount' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedAccount', ], ], '__listOfUsageByAccount' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageByAccount', ], ], '__listOfUsageRecord' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageRecord', ], ], '__listOfUsageStatisticsFilter' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageStatisticsFilter', ], ], '__listOfUsageTotal' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageTotal', ], ], '__listOf__string' => [ 'type' => 'list', 'member' => [ 'shape' => '__string', ], ], '__long' => [ 'type' => 'long', ], '__string' => [ 'type' => 'string', ], '__stringMin1Max1024PatternSS' => [ 'type' => 'string', 'min' => 1, 'max' => 1024, 'pattern' => '^[\\s\\S]+$', ], '__stringMin1Max128' => [ 'type' => 'string', 'min' => 1, 'max' => 128, ], '__stringMin1Max128Pattern' => [ 'type' => 'string', 'min' => 1, 'max' => 128, 'pattern' => '^.+$', ], '__stringMin1Max2048' => [ 'type' => 'string', 'min' => 1, 'max' => 2048, ], '__stringMin1Max512PatternSS' => [ 'type' => 'string', 'min' => 1, 'max' => 512, 'pattern' => '^[\\s\\S]+$', ], '__stringMin1Max64PatternW' => [ 'type' => 'string', 'min' => 1, 'max' => 64, 'pattern' => '^[\\w+=,.@-]*$', ], '__stringMin22Max22PatternAZ0922' => [ 'type' => 'string', 'min' => 22, 'max' => 22, 'pattern' => '^[a-z0-9]{22}$', ], '__stringMin3Max255PatternAZaZ093255' => [ 'type' => 'string', 'min' => 3, 'max' => 255, 'pattern' => '^[A-Za-z0-9.\\-_]{3,255}$', ], '__stringMin71Max89PatternArnAwsAwsCnAwsUsGovMacie2AZ19920D12AllowListAZ0922' => [ 'type' => 'string', 'min' => 71, 'max' => 89, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov):macie2:[a-z1-9-]{9,20}:\\d{12}:allow-list\\/[a-z0-9]{22}$', ], '__timestampIso8601' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], '__timestampUnix' => [ 'type' => 'timestamp', 'timestampFormat' => 'unixTimestamp', ], ],];
