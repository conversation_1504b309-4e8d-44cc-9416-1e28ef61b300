<?php
// This file was auto-generated from sdk-root/src/data/es/2015-01-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-01-01', 'endpointPrefix' => 'es', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Elasticsearch Service', 'serviceId' => 'Elasticsearch Service', 'signatureVersion' => 'v4', 'uid' => 'es-2015-01-01', ], 'operations' => [ 'AcceptInboundCrossClusterSearchConnection' => [ 'name' => 'AcceptInboundCrossClusterSearchConnection', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2015-01-01/es/ccs/inboundConnection/{ConnectionId}/accept', ], 'input' => [ 'shape' => 'AcceptInboundCrossClusterSearchConnectionRequest', ], 'output' => [ 'shape' => 'AcceptInboundCrossClusterSearchConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'AddTags' => [ 'name' => 'AddTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/tags', ], 'input' => [ 'shape' => 'AddTagsRequest', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'AssociatePackage' => [ 'name' => 'AssociatePackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/packages/associate/{PackageID}/{DomainName}', ], 'input' => [ 'shape' => 'AssociatePackageRequest', ], 'output' => [ 'shape' => 'AssociatePackageResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'AuthorizeVpcEndpointAccess' => [ 'name' => 'AuthorizeVpcEndpointAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/domain/{DomainName}/authorizeVpcEndpointAccess', ], 'input' => [ 'shape' => 'AuthorizeVpcEndpointAccessRequest', ], 'output' => [ 'shape' => 'AuthorizeVpcEndpointAccessResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'BaseException', ], ], ], 'CancelElasticsearchServiceSoftwareUpdate' => [ 'name' => 'CancelElasticsearchServiceSoftwareUpdate', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/serviceSoftwareUpdate/cancel', ], 'input' => [ 'shape' => 'CancelElasticsearchServiceSoftwareUpdateRequest', ], 'output' => [ 'shape' => 'CancelElasticsearchServiceSoftwareUpdateResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateElasticsearchDomain' => [ 'name' => 'CreateElasticsearchDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/domain', ], 'input' => [ 'shape' => 'CreateElasticsearchDomainRequest', ], 'output' => [ 'shape' => 'CreateElasticsearchDomainResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidTypeException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateOutboundCrossClusterSearchConnection' => [ 'name' => 'CreateOutboundCrossClusterSearchConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/ccs/outboundConnection', ], 'input' => [ 'shape' => 'CreateOutboundCrossClusterSearchConnectionRequest', ], 'output' => [ 'shape' => 'CreateOutboundCrossClusterSearchConnectionResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'CreatePackage' => [ 'name' => 'CreatePackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/packages', ], 'input' => [ 'shape' => 'CreatePackageRequest', ], 'output' => [ 'shape' => 'CreatePackageResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidTypeException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateVpcEndpoint' => [ 'name' => 'CreateVpcEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/vpcEndpoints', ], 'input' => [ 'shape' => 'CreateVpcEndpointRequest', ], 'output' => [ 'shape' => 'CreateVpcEndpointResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'BaseException', ], ], ], 'DeleteElasticsearchDomain' => [ 'name' => 'DeleteElasticsearchDomain', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2015-01-01/es/domain/{DomainName}', ], 'input' => [ 'shape' => 'DeleteElasticsearchDomainRequest', ], 'output' => [ 'shape' => 'DeleteElasticsearchDomainResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteElasticsearchServiceRole' => [ 'name' => 'DeleteElasticsearchServiceRole', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2015-01-01/es/role', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteInboundCrossClusterSearchConnection' => [ 'name' => 'DeleteInboundCrossClusterSearchConnection', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2015-01-01/es/ccs/inboundConnection/{ConnectionId}', ], 'input' => [ 'shape' => 'DeleteInboundCrossClusterSearchConnectionRequest', ], 'output' => [ 'shape' => 'DeleteInboundCrossClusterSearchConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'DeleteOutboundCrossClusterSearchConnection' => [ 'name' => 'DeleteOutboundCrossClusterSearchConnection', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2015-01-01/es/ccs/outboundConnection/{ConnectionId}', ], 'input' => [ 'shape' => 'DeleteOutboundCrossClusterSearchConnectionRequest', ], 'output' => [ 'shape' => 'DeleteOutboundCrossClusterSearchConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'DeletePackage' => [ 'name' => 'DeletePackage', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2015-01-01/packages/{PackageID}', ], 'input' => [ 'shape' => 'DeletePackageRequest', ], 'output' => [ 'shape' => 'DeletePackageResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteVpcEndpoint' => [ 'name' => 'DeleteVpcEndpoint', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2015-01-01/es/vpcEndpoints/{VpcEndpointId}', ], 'input' => [ 'shape' => 'DeleteVpcEndpointRequest', ], 'output' => [ 'shape' => 'DeleteVpcEndpointResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'BaseException', ], ], ], 'DescribeDomainAutoTunes' => [ 'name' => 'DescribeDomainAutoTunes', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/domain/{DomainName}/autoTunes', ], 'input' => [ 'shape' => 'DescribeDomainAutoTunesRequest', ], 'output' => [ 'shape' => 'DescribeDomainAutoTunesResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeDomainChangeProgress' => [ 'name' => 'DescribeDomainChangeProgress', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/domain/{DomainName}/progress', ], 'input' => [ 'shape' => 'DescribeDomainChangeProgressRequest', ], 'output' => [ 'shape' => 'DescribeDomainChangeProgressResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeElasticsearchDomain' => [ 'name' => 'DescribeElasticsearchDomain', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/domain/{DomainName}', ], 'input' => [ 'shape' => 'DescribeElasticsearchDomainRequest', ], 'output' => [ 'shape' => 'DescribeElasticsearchDomainResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeElasticsearchDomainConfig' => [ 'name' => 'DescribeElasticsearchDomainConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/domain/{DomainName}/config', ], 'input' => [ 'shape' => 'DescribeElasticsearchDomainConfigRequest', ], 'output' => [ 'shape' => 'DescribeElasticsearchDomainConfigResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeElasticsearchDomains' => [ 'name' => 'DescribeElasticsearchDomains', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/domain-info', ], 'input' => [ 'shape' => 'DescribeElasticsearchDomainsRequest', ], 'output' => [ 'shape' => 'DescribeElasticsearchDomainsResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeElasticsearchInstanceTypeLimits' => [ 'name' => 'DescribeElasticsearchInstanceTypeLimits', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/instanceTypeLimits/{ElasticsearchVersion}/{InstanceType}', ], 'input' => [ 'shape' => 'DescribeElasticsearchInstanceTypeLimitsRequest', ], 'output' => [ 'shape' => 'DescribeElasticsearchInstanceTypeLimitsResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidTypeException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeInboundCrossClusterSearchConnections' => [ 'name' => 'DescribeInboundCrossClusterSearchConnections', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/ccs/inboundConnection/search', ], 'input' => [ 'shape' => 'DescribeInboundCrossClusterSearchConnectionsRequest', ], 'output' => [ 'shape' => 'DescribeInboundCrossClusterSearchConnectionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'DescribeOutboundCrossClusterSearchConnections' => [ 'name' => 'DescribeOutboundCrossClusterSearchConnections', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/ccs/outboundConnection/search', ], 'input' => [ 'shape' => 'DescribeOutboundCrossClusterSearchConnectionsRequest', ], 'output' => [ 'shape' => 'DescribeOutboundCrossClusterSearchConnectionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'DescribePackages' => [ 'name' => 'DescribePackages', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/packages/describe', ], 'input' => [ 'shape' => 'DescribePackagesRequest', ], 'output' => [ 'shape' => 'DescribePackagesResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeReservedElasticsearchInstanceOfferings' => [ 'name' => 'DescribeReservedElasticsearchInstanceOfferings', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/reservedInstanceOfferings', ], 'input' => [ 'shape' => 'DescribeReservedElasticsearchInstanceOfferingsRequest', ], 'output' => [ 'shape' => 'DescribeReservedElasticsearchInstanceOfferingsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeReservedElasticsearchInstances' => [ 'name' => 'DescribeReservedElasticsearchInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/reservedInstances', ], 'input' => [ 'shape' => 'DescribeReservedElasticsearchInstancesRequest', ], 'output' => [ 'shape' => 'DescribeReservedElasticsearchInstancesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'DescribeVpcEndpoints' => [ 'name' => 'DescribeVpcEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/vpcEndpoints/describe', ], 'input' => [ 'shape' => 'DescribeVpcEndpointsRequest', ], 'output' => [ 'shape' => 'DescribeVpcEndpointsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'BaseException', ], ], ], 'DissociatePackage' => [ 'name' => 'DissociatePackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/packages/dissociate/{PackageID}/{DomainName}', ], 'input' => [ 'shape' => 'DissociatePackageRequest', ], 'output' => [ 'shape' => 'DissociatePackageResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetCompatibleElasticsearchVersions' => [ 'name' => 'GetCompatibleElasticsearchVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/compatibleVersions', ], 'input' => [ 'shape' => 'GetCompatibleElasticsearchVersionsRequest', ], 'output' => [ 'shape' => 'GetCompatibleElasticsearchVersionsResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'GetPackageVersionHistory' => [ 'name' => 'GetPackageVersionHistory', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/packages/{PackageID}/history', ], 'input' => [ 'shape' => 'GetPackageVersionHistoryRequest', ], 'output' => [ 'shape' => 'GetPackageVersionHistoryResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetUpgradeHistory' => [ 'name' => 'GetUpgradeHistory', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/upgradeDomain/{DomainName}/history', ], 'input' => [ 'shape' => 'GetUpgradeHistoryRequest', ], 'output' => [ 'shape' => 'GetUpgradeHistoryResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'GetUpgradeStatus' => [ 'name' => 'GetUpgradeStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/upgradeDomain/{DomainName}/status', ], 'input' => [ 'shape' => 'GetUpgradeStatusRequest', ], 'output' => [ 'shape' => 'GetUpgradeStatusResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'ListDomainNames' => [ 'name' => 'ListDomainNames', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/domain', ], 'input' => [ 'shape' => 'ListDomainNamesRequest', ], 'output' => [ 'shape' => 'ListDomainNamesResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListDomainsForPackage' => [ 'name' => 'ListDomainsForPackage', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/packages/{PackageID}/domains', ], 'input' => [ 'shape' => 'ListDomainsForPackageRequest', ], 'output' => [ 'shape' => 'ListDomainsForPackageResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListElasticsearchInstanceTypes' => [ 'name' => 'ListElasticsearchInstanceTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/instanceTypes/{ElasticsearchVersion}', ], 'input' => [ 'shape' => 'ListElasticsearchInstanceTypesRequest', ], 'output' => [ 'shape' => 'ListElasticsearchInstanceTypesResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListElasticsearchVersions' => [ 'name' => 'ListElasticsearchVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/versions', ], 'input' => [ 'shape' => 'ListElasticsearchVersionsRequest', ], 'output' => [ 'shape' => 'ListElasticsearchVersionsResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPackagesForDomain' => [ 'name' => 'ListPackagesForDomain', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/domain/{DomainName}/packages', ], 'input' => [ 'shape' => 'ListPackagesForDomainRequest', ], 'output' => [ 'shape' => 'ListPackagesForDomainResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTags' => [ 'name' => 'ListTags', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/tags/', ], 'input' => [ 'shape' => 'ListTagsRequest', ], 'output' => [ 'shape' => 'ListTagsResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'ListVpcEndpointAccess' => [ 'name' => 'ListVpcEndpointAccess', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/domain/{DomainName}/listVpcEndpointAccess', ], 'input' => [ 'shape' => 'ListVpcEndpointAccessRequest', ], 'output' => [ 'shape' => 'ListVpcEndpointAccessResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'BaseException', ], ], ], 'ListVpcEndpoints' => [ 'name' => 'ListVpcEndpoints', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/vpcEndpoints', ], 'input' => [ 'shape' => 'ListVpcEndpointsRequest', ], 'output' => [ 'shape' => 'ListVpcEndpointsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'BaseException', ], ], ], 'ListVpcEndpointsForDomain' => [ 'name' => 'ListVpcEndpointsForDomain', 'http' => [ 'method' => 'GET', 'requestUri' => '/2015-01-01/es/domain/{DomainName}/vpcEndpoints', ], 'input' => [ 'shape' => 'ListVpcEndpointsForDomainRequest', ], 'output' => [ 'shape' => 'ListVpcEndpointsForDomainResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'BaseException', ], ], ], 'PurchaseReservedElasticsearchInstanceOffering' => [ 'name' => 'PurchaseReservedElasticsearchInstanceOffering', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/purchaseReservedInstanceOffering', ], 'input' => [ 'shape' => 'PurchaseReservedElasticsearchInstanceOfferingRequest', ], 'output' => [ 'shape' => 'PurchaseReservedElasticsearchInstanceOfferingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'RejectInboundCrossClusterSearchConnection' => [ 'name' => 'RejectInboundCrossClusterSearchConnection', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2015-01-01/es/ccs/inboundConnection/{ConnectionId}/reject', ], 'input' => [ 'shape' => 'RejectInboundCrossClusterSearchConnectionRequest', ], 'output' => [ 'shape' => 'RejectInboundCrossClusterSearchConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'RemoveTags' => [ 'name' => 'RemoveTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/tags-removal', ], 'input' => [ 'shape' => 'RemoveTagsRequest', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'RevokeVpcEndpointAccess' => [ 'name' => 'RevokeVpcEndpointAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/domain/{DomainName}/revokeVpcEndpointAccess', ], 'input' => [ 'shape' => 'RevokeVpcEndpointAccessRequest', ], 'output' => [ 'shape' => 'RevokeVpcEndpointAccessResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'BaseException', ], ], ], 'StartElasticsearchServiceSoftwareUpdate' => [ 'name' => 'StartElasticsearchServiceSoftwareUpdate', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/serviceSoftwareUpdate/start', ], 'input' => [ 'shape' => 'StartElasticsearchServiceSoftwareUpdateRequest', ], 'output' => [ 'shape' => 'StartElasticsearchServiceSoftwareUpdateResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateElasticsearchDomainConfig' => [ 'name' => 'UpdateElasticsearchDomainConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/domain/{DomainName}/config', ], 'input' => [ 'shape' => 'UpdateElasticsearchDomainConfigRequest', ], 'output' => [ 'shape' => 'UpdateElasticsearchDomainConfigResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidTypeException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdatePackage' => [ 'name' => 'UpdatePackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/packages/update', ], 'input' => [ 'shape' => 'UpdatePackageRequest', ], 'output' => [ 'shape' => 'UpdatePackageResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateVpcEndpoint' => [ 'name' => 'UpdateVpcEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/vpcEndpoints/update', ], 'input' => [ 'shape' => 'UpdateVpcEndpointRequest', ], 'output' => [ 'shape' => 'UpdateVpcEndpointResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BaseException', ], ], ], 'UpgradeElasticsearchDomain' => [ 'name' => 'UpgradeElasticsearchDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/2015-01-01/es/upgradeDomain', ], 'input' => [ 'shape' => 'UpgradeElasticsearchDomainRequest', ], 'output' => [ 'shape' => 'UpgradeElasticsearchDomainResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', ], 'AWSAccount' => [ 'type' => 'string', 'pattern' => '^[0-9]+$', ], 'AcceptInboundCrossClusterSearchConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'CrossClusterSearchConnectionId', ], 'members' => [ 'CrossClusterSearchConnectionId' => [ 'shape' => 'CrossClusterSearchConnectionId', 'location' => 'uri', 'locationName' => 'ConnectionId', ], ], ], 'AcceptInboundCrossClusterSearchConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'CrossClusterSearchConnection' => [ 'shape' => 'InboundCrossClusterSearchConnection', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccessPoliciesStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'PolicyDocument', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'AddTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ARN', 'TagList', ], 'members' => [ 'ARN' => [ 'shape' => 'ARN', ], 'TagList' => [ 'shape' => 'TagList', ], ], ], 'AdditionalLimit' => [ 'type' => 'structure', 'members' => [ 'LimitName' => [ 'shape' => 'LimitName', ], 'LimitValues' => [ 'shape' => 'LimitValueList', ], ], ], 'AdditionalLimitList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdditionalLimit', ], ], 'AdvancedOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'AdvancedOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'AdvancedOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'AdvancedSecurityOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'InternalUserDatabaseEnabled' => [ 'shape' => 'Boolean', ], 'SAMLOptions' => [ 'shape' => 'SAMLOptionsOutput', ], 'AnonymousAuthDisableDate' => [ 'shape' => 'DisableTimestamp', ], 'AnonymousAuthEnabled' => [ 'shape' => 'Boolean', ], ], ], 'AdvancedSecurityOptionsInput' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'InternalUserDatabaseEnabled' => [ 'shape' => 'Boolean', ], 'MasterUserOptions' => [ 'shape' => 'MasterUserOptions', ], 'SAMLOptions' => [ 'shape' => 'SAMLOptionsInput', ], 'AnonymousAuthEnabled' => [ 'shape' => 'Boolean', ], ], ], 'AdvancedSecurityOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'AdvancedSecurityOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'AssociatePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageID', 'DomainName', ], 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', 'location' => 'uri', 'locationName' => 'PackageID', ], 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'AssociatePackageResponse' => [ 'type' => 'structure', 'members' => [ 'DomainPackageDetails' => [ 'shape' => 'DomainPackageDetails', ], ], ], 'AuthorizeVpcEndpointAccessRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Account', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Account' => [ 'shape' => 'AWSAccount', ], ], ], 'AuthorizeVpcEndpointAccessResponse' => [ 'type' => 'structure', 'required' => [ 'AuthorizedPrincipal', ], 'members' => [ 'AuthorizedPrincipal' => [ 'shape' => 'AuthorizedPrincipal', ], ], ], 'AuthorizedPrincipal' => [ 'type' => 'structure', 'members' => [ 'PrincipalType' => [ 'shape' => 'PrincipalType', ], 'Principal' => [ 'shape' => 'String', ], ], ], 'AuthorizedPrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthorizedPrincipal', ], ], 'AutoTune' => [ 'type' => 'structure', 'members' => [ 'AutoTuneType' => [ 'shape' => 'AutoTuneType', ], 'AutoTuneDetails' => [ 'shape' => 'AutoTuneDetails', ], ], ], 'AutoTuneDate' => [ 'type' => 'timestamp', ], 'AutoTuneDesiredState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AutoTuneDetails' => [ 'type' => 'structure', 'members' => [ 'ScheduledAutoTuneDetails' => [ 'shape' => 'ScheduledAutoTuneDetails', ], ], ], 'AutoTuneList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoTune', ], ], 'AutoTuneMaintenanceSchedule' => [ 'type' => 'structure', 'members' => [ 'StartAt' => [ 'shape' => 'StartAt', ], 'Duration' => [ 'shape' => 'Duration', ], 'CronExpressionForRecurrence' => [ 'shape' => 'String', ], ], ], 'AutoTuneMaintenanceScheduleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoTuneMaintenanceSchedule', ], 'max' => 100, ], 'AutoTuneOptions' => [ 'type' => 'structure', 'members' => [ 'DesiredState' => [ 'shape' => 'AutoTuneDesiredState', ], 'RollbackOnDisable' => [ 'shape' => 'RollbackOnDisable', ], 'MaintenanceSchedules' => [ 'shape' => 'AutoTuneMaintenanceScheduleList', ], ], ], 'AutoTuneOptionsInput' => [ 'type' => 'structure', 'members' => [ 'DesiredState' => [ 'shape' => 'AutoTuneDesiredState', ], 'MaintenanceSchedules' => [ 'shape' => 'AutoTuneMaintenanceScheduleList', ], ], ], 'AutoTuneOptionsOutput' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'AutoTuneState', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'AutoTuneOptionsStatus' => [ 'type' => 'structure', 'members' => [ 'Options' => [ 'shape' => 'AutoTuneOptions', ], 'Status' => [ 'shape' => 'AutoTuneStatus', ], ], ], 'AutoTuneState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', 'ENABLE_IN_PROGRESS', 'DISABLE_IN_PROGRESS', 'DISABLED_AND_ROLLBACK_SCHEDULED', 'DISABLED_AND_ROLLBACK_IN_PROGRESS', 'DISABLED_AND_ROLLBACK_COMPLETE', 'DISABLED_AND_ROLLBACK_ERROR', 'ERROR', ], ], 'AutoTuneStatus' => [ 'type' => 'structure', 'required' => [ 'CreationDate', 'UpdateDate', 'State', ], 'members' => [ 'CreationDate' => [ 'shape' => 'UpdateTimestamp', ], 'UpdateDate' => [ 'shape' => 'UpdateTimestamp', ], 'UpdateVersion' => [ 'shape' => 'UIntValue', ], 'State' => [ 'shape' => 'AutoTuneState', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'PendingDeletion' => [ 'shape' => 'Boolean', ], ], ], 'AutoTuneType' => [ 'type' => 'string', 'enum' => [ 'SCHEDULED_ACTION', ], ], 'BackendRole' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'BaseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Boolean' => [ 'type' => 'boolean', ], 'CancelElasticsearchServiceSoftwareUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], ], ], 'CancelElasticsearchServiceSoftwareUpdateResponse' => [ 'type' => 'structure', 'members' => [ 'ServiceSoftwareOptions' => [ 'shape' => 'ServiceSoftwareOptions', ], ], ], 'ChangeProgressDetails' => [ 'type' => 'structure', 'members' => [ 'ChangeId' => [ 'shape' => 'GUID', ], 'Message' => [ 'shape' => 'Message', ], ], ], 'ChangeProgressStage' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ChangeProgressStageName', ], 'Status' => [ 'shape' => 'ChangeProgressStageStatus', ], 'Description' => [ 'shape' => 'Description', ], 'LastUpdated' => [ 'shape' => 'LastUpdated', ], ], ], 'ChangeProgressStageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeProgressStage', ], ], 'ChangeProgressStageName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ChangeProgressStageStatus' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ChangeProgressStatusDetails' => [ 'type' => 'structure', 'members' => [ 'ChangeId' => [ 'shape' => 'GUID', ], 'StartTime' => [ 'shape' => 'UpdateTimestamp', ], 'Status' => [ 'shape' => 'OverallChangeStatus', ], 'PendingProperties' => [ 'shape' => 'StringList', ], 'CompletedProperties' => [ 'shape' => 'StringList', ], 'TotalNumberOfStages' => [ 'shape' => 'TotalNumberOfStages', ], 'ChangeProgressStages' => [ 'shape' => 'ChangeProgressStageList', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'CloudWatchLogsLogGroupArn' => [ 'type' => 'string', ], 'CognitoOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'UserPoolId' => [ 'shape' => 'UserPoolId', ], 'IdentityPoolId' => [ 'shape' => 'IdentityPoolId', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'CognitoOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'CognitoOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'ColdStorageOptions' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'CommitMessage' => [ 'type' => 'string', 'max' => 160, ], 'CompatibleElasticsearchVersionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompatibleVersionsMap', ], ], 'CompatibleVersionsMap' => [ 'type' => 'structure', 'members' => [ 'SourceVersion' => [ 'shape' => 'ElasticsearchVersionString', ], 'TargetVersions' => [ 'shape' => 'ElasticsearchVersionList', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConnectionAlias' => [ 'type' => 'string', 'max' => 20, ], 'CreateElasticsearchDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'ElasticsearchVersion' => [ 'shape' => 'ElasticsearchVersionString', ], 'ElasticsearchClusterConfig' => [ 'shape' => 'ElasticsearchClusterConfig', ], 'EBSOptions' => [ 'shape' => 'EBSOptions', ], 'AccessPolicies' => [ 'shape' => 'PolicyDocument', ], 'SnapshotOptions' => [ 'shape' => 'SnapshotOptions', ], 'VPCOptions' => [ 'shape' => 'VPCOptions', ], 'CognitoOptions' => [ 'shape' => 'CognitoOptions', ], 'EncryptionAtRestOptions' => [ 'shape' => 'EncryptionAtRestOptions', ], 'NodeToNodeEncryptionOptions' => [ 'shape' => 'NodeToNodeEncryptionOptions', ], 'AdvancedOptions' => [ 'shape' => 'AdvancedOptions', ], 'LogPublishingOptions' => [ 'shape' => 'LogPublishingOptions', ], 'DomainEndpointOptions' => [ 'shape' => 'DomainEndpointOptions', ], 'AdvancedSecurityOptions' => [ 'shape' => 'AdvancedSecurityOptionsInput', ], 'AutoTuneOptions' => [ 'shape' => 'AutoTuneOptionsInput', ], 'TagList' => [ 'shape' => 'TagList', ], ], ], 'CreateElasticsearchDomainResponse' => [ 'type' => 'structure', 'members' => [ 'DomainStatus' => [ 'shape' => 'ElasticsearchDomainStatus', ], ], ], 'CreateOutboundCrossClusterSearchConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'SourceDomainInfo', 'DestinationDomainInfo', 'ConnectionAlias', ], 'members' => [ 'SourceDomainInfo' => [ 'shape' => 'DomainInformation', ], 'DestinationDomainInfo' => [ 'shape' => 'DomainInformation', ], 'ConnectionAlias' => [ 'shape' => 'ConnectionAlias', ], ], ], 'CreateOutboundCrossClusterSearchConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'SourceDomainInfo' => [ 'shape' => 'DomainInformation', ], 'DestinationDomainInfo' => [ 'shape' => 'DomainInformation', ], 'ConnectionAlias' => [ 'shape' => 'ConnectionAlias', ], 'ConnectionStatus' => [ 'shape' => 'OutboundCrossClusterSearchConnectionStatus', ], 'CrossClusterSearchConnectionId' => [ 'shape' => 'CrossClusterSearchConnectionId', ], ], ], 'CreatePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageName', 'PackageType', 'PackageSource', ], 'members' => [ 'PackageName' => [ 'shape' => 'PackageName', ], 'PackageType' => [ 'shape' => 'PackageType', ], 'PackageDescription' => [ 'shape' => 'PackageDescription', ], 'PackageSource' => [ 'shape' => 'PackageSource', ], ], ], 'CreatePackageResponse' => [ 'type' => 'structure', 'members' => [ 'PackageDetails' => [ 'shape' => 'PackageDetails', ], ], ], 'CreateVpcEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'DomainArn', 'VpcOptions', ], 'members' => [ 'DomainArn' => [ 'shape' => 'DomainArn', ], 'VpcOptions' => [ 'shape' => 'VPCOptions', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'CreateVpcEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'VpcEndpoint', ], 'members' => [ 'VpcEndpoint' => [ 'shape' => 'VpcEndpoint', ], ], ], 'CreatedAt' => [ 'type' => 'timestamp', ], 'CrossClusterSearchConnectionId' => [ 'type' => 'string', ], 'CrossClusterSearchConnectionStatusMessage' => [ 'type' => 'string', ], 'DeleteElasticsearchDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DeleteElasticsearchDomainResponse' => [ 'type' => 'structure', 'members' => [ 'DomainStatus' => [ 'shape' => 'ElasticsearchDomainStatus', ], ], ], 'DeleteInboundCrossClusterSearchConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'CrossClusterSearchConnectionId', ], 'members' => [ 'CrossClusterSearchConnectionId' => [ 'shape' => 'CrossClusterSearchConnectionId', 'location' => 'uri', 'locationName' => 'ConnectionId', ], ], ], 'DeleteInboundCrossClusterSearchConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'CrossClusterSearchConnection' => [ 'shape' => 'InboundCrossClusterSearchConnection', ], ], ], 'DeleteOutboundCrossClusterSearchConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'CrossClusterSearchConnectionId', ], 'members' => [ 'CrossClusterSearchConnectionId' => [ 'shape' => 'CrossClusterSearchConnectionId', 'location' => 'uri', 'locationName' => 'ConnectionId', ], ], ], 'DeleteOutboundCrossClusterSearchConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'CrossClusterSearchConnection' => [ 'shape' => 'OutboundCrossClusterSearchConnection', ], ], ], 'DeletePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageID', ], 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', 'location' => 'uri', 'locationName' => 'PackageID', ], ], ], 'DeletePackageResponse' => [ 'type' => 'structure', 'members' => [ 'PackageDetails' => [ 'shape' => 'PackageDetails', ], ], ], 'DeleteVpcEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointId', ], 'members' => [ 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', 'location' => 'uri', 'locationName' => 'VpcEndpointId', ], ], ], 'DeleteVpcEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointSummary', ], 'members' => [ 'VpcEndpointSummary' => [ 'shape' => 'VpcEndpointSummary', ], ], ], 'DeploymentCloseDateTimeStamp' => [ 'type' => 'timestamp', ], 'DeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_UPDATE', 'IN_PROGRESS', 'COMPLETED', 'NOT_ELIGIBLE', 'ELIGIBLE', ], ], 'DeploymentType' => [ 'type' => 'string', 'max' => 128, 'min' => 2, ], 'DescribeDomainAutoTunesRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDomainAutoTunesResponse' => [ 'type' => 'structure', 'members' => [ 'AutoTunes' => [ 'shape' => 'AutoTuneList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDomainChangeProgressRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'ChangeId' => [ 'shape' => 'GUID', 'location' => 'querystring', 'locationName' => 'changeid', ], ], ], 'DescribeDomainChangeProgressResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeProgressStatus' => [ 'shape' => 'ChangeProgressStatusDetails', ], ], ], 'DescribeElasticsearchDomainConfigRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DescribeElasticsearchDomainConfigResponse' => [ 'type' => 'structure', 'required' => [ 'DomainConfig', ], 'members' => [ 'DomainConfig' => [ 'shape' => 'ElasticsearchDomainConfig', ], ], ], 'DescribeElasticsearchDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DescribeElasticsearchDomainResponse' => [ 'type' => 'structure', 'required' => [ 'DomainStatus', ], 'members' => [ 'DomainStatus' => [ 'shape' => 'ElasticsearchDomainStatus', ], ], ], 'DescribeElasticsearchDomainsRequest' => [ 'type' => 'structure', 'required' => [ 'DomainNames', ], 'members' => [ 'DomainNames' => [ 'shape' => 'DomainNameList', ], ], ], 'DescribeElasticsearchDomainsResponse' => [ 'type' => 'structure', 'required' => [ 'DomainStatusList', ], 'members' => [ 'DomainStatusList' => [ 'shape' => 'ElasticsearchDomainStatusList', ], ], ], 'DescribeElasticsearchInstanceTypeLimitsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceType', 'ElasticsearchVersion', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domainName', ], 'InstanceType' => [ 'shape' => 'ESPartitionInstanceType', 'location' => 'uri', 'locationName' => 'InstanceType', ], 'ElasticsearchVersion' => [ 'shape' => 'ElasticsearchVersionString', 'location' => 'uri', 'locationName' => 'ElasticsearchVersion', ], ], ], 'DescribeElasticsearchInstanceTypeLimitsResponse' => [ 'type' => 'structure', 'members' => [ 'LimitsByRole' => [ 'shape' => 'LimitsByRole', ], ], ], 'DescribeInboundCrossClusterSearchConnectionsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInboundCrossClusterSearchConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'CrossClusterSearchConnections' => [ 'shape' => 'InboundCrossClusterSearchConnections', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeOutboundCrossClusterSearchConnectionsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeOutboundCrossClusterSearchConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'CrossClusterSearchConnections' => [ 'shape' => 'OutboundCrossClusterSearchConnections', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePackagesFilter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DescribePackagesFilterName', ], 'Value' => [ 'shape' => 'DescribePackagesFilterValues', ], ], ], 'DescribePackagesFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribePackagesFilter', ], ], 'DescribePackagesFilterName' => [ 'type' => 'string', 'enum' => [ 'PackageID', 'PackageName', 'PackageStatus', ], ], 'DescribePackagesFilterValue' => [ 'type' => 'string', 'pattern' => '^[0-9a-zA-Z\\*\\.\\\\/\\?-]*$', ], 'DescribePackagesFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribePackagesFilterValue', ], 'min' => 1, ], 'DescribePackagesRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'DescribePackagesFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePackagesResponse' => [ 'type' => 'structure', 'members' => [ 'PackageDetailsList' => [ 'shape' => 'PackageDetailsList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeReservedElasticsearchInstanceOfferingsRequest' => [ 'type' => 'structure', 'members' => [ 'ReservedElasticsearchInstanceOfferingId' => [ 'shape' => 'GUID', 'location' => 'querystring', 'locationName' => 'offeringId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'DescribeReservedElasticsearchInstanceOfferingsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ReservedElasticsearchInstanceOfferings' => [ 'shape' => 'ReservedElasticsearchInstanceOfferingList', ], ], ], 'DescribeReservedElasticsearchInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'ReservedElasticsearchInstanceId' => [ 'shape' => 'GUID', 'location' => 'querystring', 'locationName' => 'reservationId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'DescribeReservedElasticsearchInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'ReservedElasticsearchInstances' => [ 'shape' => 'ReservedElasticsearchInstanceList', ], ], ], 'DescribeVpcEndpointsRequest' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointIds', ], 'members' => [ 'VpcEndpointIds' => [ 'shape' => 'VpcEndpointIdList', ], ], ], 'DescribeVpcEndpointsResponse' => [ 'type' => 'structure', 'required' => [ 'VpcEndpoints', 'VpcEndpointErrors', ], 'members' => [ 'VpcEndpoints' => [ 'shape' => 'VpcEndpoints', ], 'VpcEndpointErrors' => [ 'shape' => 'VpcEndpointErrorList', ], ], ], 'Description' => [ 'type' => 'string', ], 'DisableTimestamp' => [ 'type' => 'timestamp', ], 'DisabledOperationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'DissociatePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageID', 'DomainName', ], 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', 'location' => 'uri', 'locationName' => 'PackageID', ], 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DissociatePackageResponse' => [ 'type' => 'structure', 'members' => [ 'DomainPackageDetails' => [ 'shape' => 'DomainPackageDetails', ], ], ], 'DomainArn' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:[a-z]+:[a-z0-9\\-]+:[0-9]+:domain\\/[a-z0-9\\-]+', ], 'DomainEndpointOptions' => [ 'type' => 'structure', 'members' => [ 'EnforceHTTPS' => [ 'shape' => 'Boolean', ], 'TLSSecurityPolicy' => [ 'shape' => 'TLSSecurityPolicy', ], 'CustomEndpointEnabled' => [ 'shape' => 'Boolean', ], 'CustomEndpoint' => [ 'shape' => 'DomainNameFqdn', ], 'CustomEndpointCertificateArn' => [ 'shape' => 'ARN', ], ], ], 'DomainEndpointOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'DomainEndpointOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'DomainId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'DomainInfo' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'EngineType' => [ 'shape' => 'EngineType', ], ], ], 'DomainInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainInfo', ], ], 'DomainInformation' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'OwnerId' => [ 'shape' => 'OwnerId', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'Region' => [ 'shape' => 'Region', ], ], ], 'DomainName' => [ 'type' => 'string', 'max' => 28, 'min' => 3, 'pattern' => '[a-z][a-z0-9\\-]+', ], 'DomainNameFqdn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(((?!-)[A-Za-z0-9-]{0,62}[A-Za-z0-9])\\.)+((?!-)[A-Za-z0-9-]{1,62}[A-Za-z0-9])$', ], 'DomainNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainName', ], ], 'DomainPackageDetails' => [ 'type' => 'structure', 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', ], 'PackageName' => [ 'shape' => 'PackageName', ], 'PackageType' => [ 'shape' => 'PackageType', ], 'LastUpdated' => [ 'shape' => 'LastUpdated', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'DomainPackageStatus' => [ 'shape' => 'DomainPackageStatus', ], 'PackageVersion' => [ 'shape' => 'PackageVersion', ], 'ReferencePath' => [ 'shape' => 'ReferencePath', ], 'ErrorDetails' => [ 'shape' => 'ErrorDetails', ], ], ], 'DomainPackageDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainPackageDetails', ], ], 'DomainPackageStatus' => [ 'type' => 'string', 'enum' => [ 'ASSOCIATING', 'ASSOCIATION_FAILED', 'ACTIVE', 'DISSOCIATING', 'DISSOCIATION_FAILED', ], ], 'Double' => [ 'type' => 'double', ], 'DryRun' => [ 'type' => 'boolean', ], 'DryRunResults' => [ 'type' => 'structure', 'members' => [ 'DeploymentType' => [ 'shape' => 'DeploymentType', ], 'Message' => [ 'shape' => 'Message', ], ], ], 'Duration' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'DurationValue', ], 'Unit' => [ 'shape' => 'TimeUnit', ], ], ], 'DurationValue' => [ 'type' => 'long', 'max' => 24, 'min' => 1, ], 'EBSOptions' => [ 'type' => 'structure', 'members' => [ 'EBSEnabled' => [ 'shape' => 'Boolean', ], 'VolumeType' => [ 'shape' => 'VolumeType', ], 'VolumeSize' => [ 'shape' => 'IntegerClass', ], 'Iops' => [ 'shape' => 'IntegerClass', ], 'Throughput' => [ 'shape' => 'IntegerClass', ], ], ], 'EBSOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'EBSOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'ESPartitionInstanceType' => [ 'type' => 'string', 'enum' => [ 'm3.medium.elasticsearch', 'm3.large.elasticsearch', 'm3.xlarge.elasticsearch', 'm3.2xlarge.elasticsearch', 'm4.large.elasticsearch', 'm4.xlarge.elasticsearch', 'm4.2xlarge.elasticsearch', 'm4.4xlarge.elasticsearch', 'm4.10xlarge.elasticsearch', 'm5.large.elasticsearch', 'm5.xlarge.elasticsearch', 'm5.2xlarge.elasticsearch', 'm5.4xlarge.elasticsearch', 'm5.12xlarge.elasticsearch', 'r5.large.elasticsearch', 'r5.xlarge.elasticsearch', 'r5.2xlarge.elasticsearch', 'r5.4xlarge.elasticsearch', 'r5.12xlarge.elasticsearch', 'c5.large.elasticsearch', 'c5.xlarge.elasticsearch', 'c5.2xlarge.elasticsearch', 'c5.4xlarge.elasticsearch', 'c5.9xlarge.elasticsearch', 'c5.18xlarge.elasticsearch', 'ultrawarm1.medium.elasticsearch', 'ultrawarm1.large.elasticsearch', 't2.micro.elasticsearch', 't2.small.elasticsearch', 't2.medium.elasticsearch', 'r3.large.elasticsearch', 'r3.xlarge.elasticsearch', 'r3.2xlarge.elasticsearch', 'r3.4xlarge.elasticsearch', 'r3.8xlarge.elasticsearch', 'i2.xlarge.elasticsearch', 'i2.2xlarge.elasticsearch', 'd2.xlarge.elasticsearch', 'd2.2xlarge.elasticsearch', 'd2.4xlarge.elasticsearch', 'd2.8xlarge.elasticsearch', 'c4.large.elasticsearch', 'c4.xlarge.elasticsearch', 'c4.2xlarge.elasticsearch', 'c4.4xlarge.elasticsearch', 'c4.8xlarge.elasticsearch', 'r4.large.elasticsearch', 'r4.xlarge.elasticsearch', 'r4.2xlarge.elasticsearch', 'r4.4xlarge.elasticsearch', 'r4.8xlarge.elasticsearch', 'r4.16xlarge.elasticsearch', 'i3.large.elasticsearch', 'i3.xlarge.elasticsearch', 'i3.2xlarge.elasticsearch', 'i3.4xlarge.elasticsearch', 'i3.8xlarge.elasticsearch', 'i3.16xlarge.elasticsearch', ], ], 'ESWarmPartitionInstanceType' => [ 'type' => 'string', 'enum' => [ 'ultrawarm1.medium.elasticsearch', 'ultrawarm1.large.elasticsearch', ], ], 'ElasticsearchClusterConfig' => [ 'type' => 'structure', 'members' => [ 'InstanceType' => [ 'shape' => 'ESPartitionInstanceType', ], 'InstanceCount' => [ 'shape' => 'IntegerClass', ], 'DedicatedMasterEnabled' => [ 'shape' => 'Boolean', ], 'ZoneAwarenessEnabled' => [ 'shape' => 'Boolean', ], 'ZoneAwarenessConfig' => [ 'shape' => 'ZoneAwarenessConfig', ], 'DedicatedMasterType' => [ 'shape' => 'ESPartitionInstanceType', ], 'DedicatedMasterCount' => [ 'shape' => 'IntegerClass', ], 'WarmEnabled' => [ 'shape' => 'Boolean', ], 'WarmType' => [ 'shape' => 'ESWarmPartitionInstanceType', ], 'WarmCount' => [ 'shape' => 'IntegerClass', ], 'ColdStorageOptions' => [ 'shape' => 'ColdStorageOptions', ], ], ], 'ElasticsearchClusterConfigStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'ElasticsearchClusterConfig', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'ElasticsearchDomainConfig' => [ 'type' => 'structure', 'members' => [ 'ElasticsearchVersion' => [ 'shape' => 'ElasticsearchVersionStatus', ], 'ElasticsearchClusterConfig' => [ 'shape' => 'ElasticsearchClusterConfigStatus', ], 'EBSOptions' => [ 'shape' => 'EBSOptionsStatus', ], 'AccessPolicies' => [ 'shape' => 'AccessPoliciesStatus', ], 'SnapshotOptions' => [ 'shape' => 'SnapshotOptionsStatus', ], 'VPCOptions' => [ 'shape' => 'VPCDerivedInfoStatus', ], 'CognitoOptions' => [ 'shape' => 'CognitoOptionsStatus', ], 'EncryptionAtRestOptions' => [ 'shape' => 'EncryptionAtRestOptionsStatus', ], 'NodeToNodeEncryptionOptions' => [ 'shape' => 'NodeToNodeEncryptionOptionsStatus', ], 'AdvancedOptions' => [ 'shape' => 'AdvancedOptionsStatus', ], 'LogPublishingOptions' => [ 'shape' => 'LogPublishingOptionsStatus', ], 'DomainEndpointOptions' => [ 'shape' => 'DomainEndpointOptionsStatus', ], 'AdvancedSecurityOptions' => [ 'shape' => 'AdvancedSecurityOptionsStatus', ], 'AutoTuneOptions' => [ 'shape' => 'AutoTuneOptionsStatus', ], 'ChangeProgressDetails' => [ 'shape' => 'ChangeProgressDetails', ], ], ], 'ElasticsearchDomainStatus' => [ 'type' => 'structure', 'required' => [ 'DomainId', 'DomainName', 'ARN', 'ElasticsearchClusterConfig', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'ARN' => [ 'shape' => 'ARN', ], 'Created' => [ 'shape' => 'Boolean', ], 'Deleted' => [ 'shape' => 'Boolean', ], 'Endpoint' => [ 'shape' => 'ServiceUrl', ], 'Endpoints' => [ 'shape' => 'EndpointsMap', ], 'Processing' => [ 'shape' => 'Boolean', ], 'UpgradeProcessing' => [ 'shape' => 'Boolean', ], 'ElasticsearchVersion' => [ 'shape' => 'ElasticsearchVersionString', ], 'ElasticsearchClusterConfig' => [ 'shape' => 'ElasticsearchClusterConfig', ], 'EBSOptions' => [ 'shape' => 'EBSOptions', ], 'AccessPolicies' => [ 'shape' => 'PolicyDocument', ], 'SnapshotOptions' => [ 'shape' => 'SnapshotOptions', ], 'VPCOptions' => [ 'shape' => 'VPCDerivedInfo', ], 'CognitoOptions' => [ 'shape' => 'CognitoOptions', ], 'EncryptionAtRestOptions' => [ 'shape' => 'EncryptionAtRestOptions', ], 'NodeToNodeEncryptionOptions' => [ 'shape' => 'NodeToNodeEncryptionOptions', ], 'AdvancedOptions' => [ 'shape' => 'AdvancedOptions', ], 'LogPublishingOptions' => [ 'shape' => 'LogPublishingOptions', ], 'ServiceSoftwareOptions' => [ 'shape' => 'ServiceSoftwareOptions', ], 'DomainEndpointOptions' => [ 'shape' => 'DomainEndpointOptions', ], 'AdvancedSecurityOptions' => [ 'shape' => 'AdvancedSecurityOptions', ], 'AutoTuneOptions' => [ 'shape' => 'AutoTuneOptionsOutput', ], 'ChangeProgressDetails' => [ 'shape' => 'ChangeProgressDetails', ], ], ], 'ElasticsearchDomainStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ElasticsearchDomainStatus', ], ], 'ElasticsearchInstanceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ESPartitionInstanceType', ], ], 'ElasticsearchVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ElasticsearchVersionString', ], ], 'ElasticsearchVersionStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'ElasticsearchVersionString', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'ElasticsearchVersionString' => [ 'type' => 'string', 'pattern' => '^[0-9]{1}\\.[0-9]{1,2}$|^OpenSearch_[0-9]{1,2}\\.[0-9]{1,2}$|^OS_[0-9]{1,2}\\.[0-9]{1,2}$', ], 'EncryptionAtRestOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'EncryptionAtRestOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'EncryptionAtRestOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'Endpoint' => [ 'type' => 'string', 'pattern' => '^[A-Za-z0-9\\-\\.]+$', ], 'EndpointsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'ServiceUrl', ], ], 'EngineType' => [ 'type' => 'string', 'enum' => [ 'OpenSearch', 'Elasticsearch', ], ], 'ErrorDetails' => [ 'type' => 'structure', 'members' => [ 'ErrorType' => [ 'shape' => 'ErrorType', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ErrorType' => [ 'type' => 'string', ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Values' => [ 'shape' => 'ValueStringList', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'GUID' => [ 'type' => 'string', 'pattern' => '\\p{XDigit}{8}-\\p{XDigit}{4}-\\p{XDigit}{4}-\\p{XDigit}{4}-\\p{XDigit}{12}', ], 'GetCompatibleElasticsearchVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domainName', ], ], ], 'GetCompatibleElasticsearchVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'CompatibleElasticsearchVersions' => [ 'shape' => 'CompatibleElasticsearchVersionsList', ], ], ], 'GetPackageVersionHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'PackageID', ], 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', 'location' => 'uri', 'locationName' => 'PackageID', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetPackageVersionHistoryResponse' => [ 'type' => 'structure', 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', ], 'PackageVersionHistoryList' => [ 'shape' => 'PackageVersionHistoryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetUpgradeHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetUpgradeHistoryResponse' => [ 'type' => 'structure', 'members' => [ 'UpgradeHistories' => [ 'shape' => 'UpgradeHistoryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetUpgradeStatusRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'GetUpgradeStatusResponse' => [ 'type' => 'structure', 'members' => [ 'UpgradeStep' => [ 'shape' => 'UpgradeStep', ], 'StepStatus' => [ 'shape' => 'UpgradeStatus', ], 'UpgradeName' => [ 'shape' => 'UpgradeName', ], ], ], 'IdentityPoolId' => [ 'type' => 'string', 'max' => 55, 'min' => 1, 'pattern' => '[\\w-]+:[0-9a-f-]+', ], 'InboundCrossClusterSearchConnection' => [ 'type' => 'structure', 'members' => [ 'SourceDomainInfo' => [ 'shape' => 'DomainInformation', ], 'DestinationDomainInfo' => [ 'shape' => 'DomainInformation', ], 'CrossClusterSearchConnectionId' => [ 'shape' => 'CrossClusterSearchConnectionId', ], 'ConnectionStatus' => [ 'shape' => 'InboundCrossClusterSearchConnectionStatus', ], ], ], 'InboundCrossClusterSearchConnectionStatus' => [ 'type' => 'structure', 'members' => [ 'StatusCode' => [ 'shape' => 'InboundCrossClusterSearchConnectionStatusCode', ], 'Message' => [ 'shape' => 'CrossClusterSearchConnectionStatusMessage', ], ], ], 'InboundCrossClusterSearchConnectionStatusCode' => [ 'type' => 'string', 'enum' => [ 'PENDING_ACCEPTANCE', 'APPROVED', 'REJECTING', 'REJECTED', 'DELETING', 'DELETED', ], ], 'InboundCrossClusterSearchConnections' => [ 'type' => 'list', 'member' => [ 'shape' => 'InboundCrossClusterSearchConnection', ], ], 'InstanceCount' => [ 'type' => 'integer', 'min' => 1, ], 'InstanceCountLimits' => [ 'type' => 'structure', 'members' => [ 'MinimumInstanceCount' => [ 'shape' => 'MinimumInstanceCount', ], 'MaximumInstanceCount' => [ 'shape' => 'MaximumInstanceCount', ], ], ], 'InstanceLimits' => [ 'type' => 'structure', 'members' => [ 'InstanceCountLimits' => [ 'shape' => 'InstanceCountLimits', ], ], ], 'InstanceRole' => [ 'type' => 'string', ], 'Integer' => [ 'type' => 'integer', ], 'IntegerClass' => [ 'type' => 'integer', ], 'InternalException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'InvalidPaginationTokenException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidTypeException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Issue' => [ 'type' => 'string', ], 'Issues' => [ 'type' => 'list', 'member' => [ 'shape' => 'Issue', ], ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'LastUpdated' => [ 'type' => 'timestamp', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'LimitName' => [ 'type' => 'string', ], 'LimitValue' => [ 'type' => 'string', ], 'LimitValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LimitValue', ], ], 'Limits' => [ 'type' => 'structure', 'members' => [ 'StorageTypes' => [ 'shape' => 'StorageTypeList', ], 'InstanceLimits' => [ 'shape' => 'InstanceLimits', ], 'AdditionalLimits' => [ 'shape' => 'AdditionalLimitList', ], ], ], 'LimitsByRole' => [ 'type' => 'map', 'key' => [ 'shape' => 'InstanceRole', ], 'value' => [ 'shape' => 'Limits', ], ], 'ListDomainNamesRequest' => [ 'type' => 'structure', 'members' => [ 'EngineType' => [ 'shape' => 'EngineType', 'location' => 'querystring', 'locationName' => 'engineType', ], ], ], 'ListDomainNamesResponse' => [ 'type' => 'structure', 'members' => [ 'DomainNames' => [ 'shape' => 'DomainInfoList', ], ], ], 'ListDomainsForPackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageID', ], 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', 'location' => 'uri', 'locationName' => 'PackageID', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDomainsForPackageResponse' => [ 'type' => 'structure', 'members' => [ 'DomainPackageDetailsList' => [ 'shape' => 'DomainPackageDetailsList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListElasticsearchInstanceTypesRequest' => [ 'type' => 'structure', 'required' => [ 'ElasticsearchVersion', ], 'members' => [ 'ElasticsearchVersion' => [ 'shape' => 'ElasticsearchVersionString', 'location' => 'uri', 'locationName' => 'ElasticsearchVersion', ], 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domainName', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListElasticsearchInstanceTypesResponse' => [ 'type' => 'structure', 'members' => [ 'ElasticsearchInstanceTypes' => [ 'shape' => 'ElasticsearchInstanceTypeList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListElasticsearchVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListElasticsearchVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'ElasticsearchVersions' => [ 'shape' => 'ElasticsearchVersionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPackagesForDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPackagesForDomainResponse' => [ 'type' => 'structure', 'members' => [ 'DomainPackageDetailsList' => [ 'shape' => 'DomainPackageDetailsList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ARN', ], 'members' => [ 'ARN' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'arn', ], ], ], 'ListTagsResponse' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'ListVpcEndpointAccessRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListVpcEndpointAccessResponse' => [ 'type' => 'structure', 'required' => [ 'AuthorizedPrincipalList', 'NextToken', ], 'members' => [ 'AuthorizedPrincipalList' => [ 'shape' => 'AuthorizedPrincipalList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListVpcEndpointsForDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListVpcEndpointsForDomainResponse' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointSummaryList', 'NextToken', ], 'members' => [ 'VpcEndpointSummaryList' => [ 'shape' => 'VpcEndpointSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListVpcEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListVpcEndpointsResponse' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointSummaryList', 'NextToken', ], 'members' => [ 'VpcEndpointSummaryList' => [ 'shape' => 'VpcEndpointSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'LogPublishingOption' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'CloudWatchLogsLogGroupArn', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'LogPublishingOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'LogType', ], 'value' => [ 'shape' => 'LogPublishingOption', ], ], 'LogPublishingOptionsStatus' => [ 'type' => 'structure', 'members' => [ 'Options' => [ 'shape' => 'LogPublishingOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'LogType' => [ 'type' => 'string', 'enum' => [ 'INDEX_SLOW_LOGS', 'SEARCH_SLOW_LOGS', 'ES_APPLICATION_LOGS', 'AUDIT_LOGS', ], ], 'MasterUserOptions' => [ 'type' => 'structure', 'members' => [ 'MasterUserARN' => [ 'shape' => 'ARN', ], 'MasterUserName' => [ 'shape' => 'Username', ], 'MasterUserPassword' => [ 'shape' => 'Password', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, ], 'MaximumInstanceCount' => [ 'type' => 'integer', ], 'Message' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'MinimumInstanceCount' => [ 'type' => 'integer', ], 'NextToken' => [ 'type' => 'string', ], 'NodeToNodeEncryptionOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'NodeToNodeEncryptionOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'NodeToNodeEncryptionOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'NonEmptyString' => [ 'type' => 'string', 'min' => 1, ], 'OptionState' => [ 'type' => 'string', 'enum' => [ 'RequiresIndexDocuments', 'Processing', 'Active', ], ], 'OptionStatus' => [ 'type' => 'structure', 'required' => [ 'CreationDate', 'UpdateDate', 'State', ], 'members' => [ 'CreationDate' => [ 'shape' => 'UpdateTimestamp', ], 'UpdateDate' => [ 'shape' => 'UpdateTimestamp', ], 'UpdateVersion' => [ 'shape' => 'UIntValue', ], 'State' => [ 'shape' => 'OptionState', ], 'PendingDeletion' => [ 'shape' => 'Boolean', ], ], ], 'OutboundCrossClusterSearchConnection' => [ 'type' => 'structure', 'members' => [ 'SourceDomainInfo' => [ 'shape' => 'DomainInformation', ], 'DestinationDomainInfo' => [ 'shape' => 'DomainInformation', ], 'CrossClusterSearchConnectionId' => [ 'shape' => 'CrossClusterSearchConnectionId', ], 'ConnectionAlias' => [ 'shape' => 'ConnectionAlias', ], 'ConnectionStatus' => [ 'shape' => 'OutboundCrossClusterSearchConnectionStatus', ], ], ], 'OutboundCrossClusterSearchConnectionStatus' => [ 'type' => 'structure', 'members' => [ 'StatusCode' => [ 'shape' => 'OutboundCrossClusterSearchConnectionStatusCode', ], 'Message' => [ 'shape' => 'CrossClusterSearchConnectionStatusMessage', ], ], ], 'OutboundCrossClusterSearchConnectionStatusCode' => [ 'type' => 'string', 'enum' => [ 'PENDING_ACCEPTANCE', 'VALIDATING', 'VALIDATION_FAILED', 'PROVISIONING', 'ACTIVE', 'REJECTED', 'DELETING', 'DELETED', ], ], 'OutboundCrossClusterSearchConnections' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutboundCrossClusterSearchConnection', ], ], 'OverallChangeStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', ], ], 'OwnerId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, ], 'PackageDescription' => [ 'type' => 'string', 'max' => 1024, ], 'PackageDetails' => [ 'type' => 'structure', 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', ], 'PackageName' => [ 'shape' => 'PackageName', ], 'PackageType' => [ 'shape' => 'PackageType', ], 'PackageDescription' => [ 'shape' => 'PackageDescription', ], 'PackageStatus' => [ 'shape' => 'PackageStatus', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], 'LastUpdatedAt' => [ 'shape' => 'LastUpdated', ], 'AvailablePackageVersion' => [ 'shape' => 'PackageVersion', ], 'ErrorDetails' => [ 'shape' => 'ErrorDetails', ], ], ], 'PackageDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageDetails', ], ], 'PackageID' => [ 'type' => 'string', ], 'PackageName' => [ 'type' => 'string', 'max' => 28, 'min' => 3, 'pattern' => '[a-z][a-z0-9\\-]+', ], 'PackageSource' => [ 'type' => 'structure', 'members' => [ 'S3BucketName' => [ 'shape' => 'S3BucketName', ], 'S3Key' => [ 'shape' => 'S3Key', ], ], ], 'PackageStatus' => [ 'type' => 'string', 'enum' => [ 'COPYING', 'COPY_FAILED', 'VALIDATING', 'VALIDATION_FAILED', 'AVAILABLE', 'DELETING', 'DELETED', 'DELETE_FAILED', ], ], 'PackageType' => [ 'type' => 'string', 'enum' => [ 'TXT-DICTIONARY', ], ], 'PackageVersion' => [ 'type' => 'string', ], 'PackageVersionHistory' => [ 'type' => 'structure', 'members' => [ 'PackageVersion' => [ 'shape' => 'PackageVersion', ], 'CommitMessage' => [ 'shape' => 'CommitMessage', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], ], ], 'PackageVersionHistoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageVersionHistory', ], ], 'Password' => [ 'type' => 'string', 'min' => 8, 'sensitive' => true, ], 'PolicyDocument' => [ 'type' => 'string', ], 'PrincipalType' => [ 'type' => 'string', 'enum' => [ 'AWS_ACCOUNT', 'AWS_SERVICE', ], ], 'PurchaseReservedElasticsearchInstanceOfferingRequest' => [ 'type' => 'structure', 'required' => [ 'ReservedElasticsearchInstanceOfferingId', 'ReservationName', ], 'members' => [ 'ReservedElasticsearchInstanceOfferingId' => [ 'shape' => 'GUID', ], 'ReservationName' => [ 'shape' => 'ReservationToken', ], 'InstanceCount' => [ 'shape' => 'InstanceCount', ], ], ], 'PurchaseReservedElasticsearchInstanceOfferingResponse' => [ 'type' => 'structure', 'members' => [ 'ReservedElasticsearchInstanceId' => [ 'shape' => 'GUID', ], 'ReservationName' => [ 'shape' => 'ReservationToken', ], ], ], 'RecurringCharge' => [ 'type' => 'structure', 'members' => [ 'RecurringChargeAmount' => [ 'shape' => 'Double', ], 'RecurringChargeFrequency' => [ 'shape' => 'String', ], ], ], 'RecurringChargeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecurringCharge', ], ], 'ReferencePath' => [ 'type' => 'string', ], 'Region' => [ 'type' => 'string', ], 'RejectInboundCrossClusterSearchConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'CrossClusterSearchConnectionId', ], 'members' => [ 'CrossClusterSearchConnectionId' => [ 'shape' => 'CrossClusterSearchConnectionId', 'location' => 'uri', 'locationName' => 'ConnectionId', ], ], ], 'RejectInboundCrossClusterSearchConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'CrossClusterSearchConnection' => [ 'shape' => 'InboundCrossClusterSearchConnection', ], ], ], 'RemoveTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ARN', 'TagKeys', ], 'members' => [ 'ARN' => [ 'shape' => 'ARN', ], 'TagKeys' => [ 'shape' => 'StringList', ], ], ], 'ReservationToken' => [ 'type' => 'string', 'max' => 64, 'min' => 5, ], 'ReservedElasticsearchInstance' => [ 'type' => 'structure', 'members' => [ 'ReservationName' => [ 'shape' => 'ReservationToken', ], 'ReservedElasticsearchInstanceId' => [ 'shape' => 'GUID', ], 'ReservedElasticsearchInstanceOfferingId' => [ 'shape' => 'String', ], 'ElasticsearchInstanceType' => [ 'shape' => 'ESPartitionInstanceType', ], 'StartTime' => [ 'shape' => 'UpdateTimestamp', ], 'Duration' => [ 'shape' => 'Integer', ], 'FixedPrice' => [ 'shape' => 'Double', ], 'UsagePrice' => [ 'shape' => 'Double', ], 'CurrencyCode' => [ 'shape' => 'String', ], 'ElasticsearchInstanceCount' => [ 'shape' => 'Integer', ], 'State' => [ 'shape' => 'String', ], 'PaymentOption' => [ 'shape' => 'ReservedElasticsearchInstancePaymentOption', ], 'RecurringCharges' => [ 'shape' => 'RecurringChargeList', ], ], ], 'ReservedElasticsearchInstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservedElasticsearchInstance', ], ], 'ReservedElasticsearchInstanceOffering' => [ 'type' => 'structure', 'members' => [ 'ReservedElasticsearchInstanceOfferingId' => [ 'shape' => 'GUID', ], 'ElasticsearchInstanceType' => [ 'shape' => 'ESPartitionInstanceType', ], 'Duration' => [ 'shape' => 'Integer', ], 'FixedPrice' => [ 'shape' => 'Double', ], 'UsagePrice' => [ 'shape' => 'Double', ], 'CurrencyCode' => [ 'shape' => 'String', ], 'PaymentOption' => [ 'shape' => 'ReservedElasticsearchInstancePaymentOption', ], 'RecurringCharges' => [ 'shape' => 'RecurringChargeList', ], ], ], 'ReservedElasticsearchInstanceOfferingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservedElasticsearchInstanceOffering', ], ], 'ReservedElasticsearchInstancePaymentOption' => [ 'type' => 'string', 'enum' => [ 'ALL_UPFRONT', 'PARTIAL_UPFRONT', 'NO_UPFRONT', ], ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'RevokeVpcEndpointAccessRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Account', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Account' => [ 'shape' => 'AWSAccount', ], ], ], 'RevokeVpcEndpointAccessResponse' => [ 'type' => 'structure', 'members' => [], ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'RollbackOnDisable' => [ 'type' => 'string', 'enum' => [ 'NO_ROLLBACK', 'DEFAULT_ROLLBACK', ], ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, ], 'S3Key' => [ 'type' => 'string', ], 'SAMLEntityId' => [ 'type' => 'string', 'max' => 512, 'min' => 8, ], 'SAMLIdp' => [ 'type' => 'structure', 'required' => [ 'MetadataContent', 'EntityId', ], 'members' => [ 'MetadataContent' => [ 'shape' => 'SAMLMetadata', ], 'EntityId' => [ 'shape' => 'SAMLEntityId', ], ], ], 'SAMLMetadata' => [ 'type' => 'string', 'max' => 1048576, 'min' => 1, ], 'SAMLOptionsInput' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'Idp' => [ 'shape' => 'SAMLIdp', ], 'MasterUserName' => [ 'shape' => 'Username', ], 'MasterBackendRole' => [ 'shape' => 'BackendRole', ], 'SubjectKey' => [ 'shape' => 'String', ], 'RolesKey' => [ 'shape' => 'String', ], 'SessionTimeoutMinutes' => [ 'shape' => 'IntegerClass', ], ], ], 'SAMLOptionsOutput' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'Idp' => [ 'shape' => 'SAMLIdp', ], 'SubjectKey' => [ 'shape' => 'String', ], 'RolesKey' => [ 'shape' => 'String', ], 'SessionTimeoutMinutes' => [ 'shape' => 'IntegerClass', ], ], ], 'ScheduledAutoTuneActionType' => [ 'type' => 'string', 'enum' => [ 'JVM_HEAP_SIZE_TUNING', 'JVM_YOUNG_GEN_TUNING', ], ], 'ScheduledAutoTuneDescription' => [ 'type' => 'string', ], 'ScheduledAutoTuneDetails' => [ 'type' => 'structure', 'members' => [ 'Date' => [ 'shape' => 'AutoTuneDate', ], 'ActionType' => [ 'shape' => 'ScheduledAutoTuneActionType', ], 'Action' => [ 'shape' => 'ScheduledAutoTuneDescription', ], 'Severity' => [ 'shape' => 'ScheduledAutoTuneSeverityType', ], ], ], 'ScheduledAutoTuneSeverityType' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', ], ], 'ServiceSoftwareOptions' => [ 'type' => 'structure', 'members' => [ 'CurrentVersion' => [ 'shape' => 'String', ], 'NewVersion' => [ 'shape' => 'String', ], 'UpdateAvailable' => [ 'shape' => 'Boolean', ], 'Cancellable' => [ 'shape' => 'Boolean', ], 'UpdateStatus' => [ 'shape' => 'DeploymentStatus', ], 'Description' => [ 'shape' => 'String', ], 'AutomatedUpdateDate' => [ 'shape' => 'DeploymentCloseDateTimeStamp', ], 'OptionalDeployment' => [ 'shape' => 'Boolean', ], ], ], 'ServiceUrl' => [ 'type' => 'string', ], 'SnapshotOptions' => [ 'type' => 'structure', 'members' => [ 'AutomatedSnapshotStartHour' => [ 'shape' => 'IntegerClass', ], ], ], 'SnapshotOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'SnapshotOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'StartAt' => [ 'type' => 'timestamp', ], 'StartElasticsearchServiceSoftwareUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], ], ], 'StartElasticsearchServiceSoftwareUpdateResponse' => [ 'type' => 'structure', 'members' => [ 'ServiceSoftwareOptions' => [ 'shape' => 'ServiceSoftwareOptions', ], ], ], 'StartTimestamp' => [ 'type' => 'timestamp', ], 'StorageSubTypeName' => [ 'type' => 'string', ], 'StorageType' => [ 'type' => 'structure', 'members' => [ 'StorageTypeName' => [ 'shape' => 'StorageTypeName', ], 'StorageSubTypeName' => [ 'shape' => 'StorageSubTypeName', ], 'StorageTypeLimits' => [ 'shape' => 'StorageTypeLimitList', ], ], ], 'StorageTypeLimit' => [ 'type' => 'structure', 'members' => [ 'LimitName' => [ 'shape' => 'LimitName', ], 'LimitValues' => [ 'shape' => 'LimitValueList', ], ], ], 'StorageTypeLimitList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageTypeLimit', ], ], 'StorageTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageType', ], ], 'StorageTypeName' => [ 'type' => 'string', ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TLSSecurityPolicy' => [ 'type' => 'string', 'enum' => [ 'Policy-Min-TLS-1-0-2019-07', 'Policy-Min-TLS-1-2-2019-07', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TimeUnit' => [ 'type' => 'string', 'enum' => [ 'HOURS', ], ], 'TotalNumberOfStages' => [ 'type' => 'integer', ], 'UIntValue' => [ 'type' => 'integer', 'min' => 0, ], 'UpdateElasticsearchDomainConfigRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'ElasticsearchClusterConfig' => [ 'shape' => 'ElasticsearchClusterConfig', ], 'EBSOptions' => [ 'shape' => 'EBSOptions', ], 'SnapshotOptions' => [ 'shape' => 'SnapshotOptions', ], 'VPCOptions' => [ 'shape' => 'VPCOptions', ], 'CognitoOptions' => [ 'shape' => 'CognitoOptions', ], 'AdvancedOptions' => [ 'shape' => 'AdvancedOptions', ], 'AccessPolicies' => [ 'shape' => 'PolicyDocument', ], 'LogPublishingOptions' => [ 'shape' => 'LogPublishingOptions', ], 'DomainEndpointOptions' => [ 'shape' => 'DomainEndpointOptions', ], 'AdvancedSecurityOptions' => [ 'shape' => 'AdvancedSecurityOptionsInput', ], 'NodeToNodeEncryptionOptions' => [ 'shape' => 'NodeToNodeEncryptionOptions', ], 'EncryptionAtRestOptions' => [ 'shape' => 'EncryptionAtRestOptions', ], 'AutoTuneOptions' => [ 'shape' => 'AutoTuneOptions', ], 'DryRun' => [ 'shape' => 'DryRun', ], ], ], 'UpdateElasticsearchDomainConfigResponse' => [ 'type' => 'structure', 'required' => [ 'DomainConfig', ], 'members' => [ 'DomainConfig' => [ 'shape' => 'ElasticsearchDomainConfig', ], 'DryRunResults' => [ 'shape' => 'DryRunResults', ], ], ], 'UpdatePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageID', 'PackageSource', ], 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', ], 'PackageSource' => [ 'shape' => 'PackageSource', ], 'PackageDescription' => [ 'shape' => 'PackageDescription', ], 'CommitMessage' => [ 'shape' => 'CommitMessage', ], ], ], 'UpdatePackageResponse' => [ 'type' => 'structure', 'members' => [ 'PackageDetails' => [ 'shape' => 'PackageDetails', ], ], ], 'UpdateTimestamp' => [ 'type' => 'timestamp', ], 'UpdateVpcEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointId', 'VpcOptions', ], 'members' => [ 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], 'VpcOptions' => [ 'shape' => 'VPCOptions', ], ], ], 'UpdateVpcEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'VpcEndpoint', ], 'members' => [ 'VpcEndpoint' => [ 'shape' => 'VpcEndpoint', ], ], ], 'UpgradeElasticsearchDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'TargetVersion', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'TargetVersion' => [ 'shape' => 'ElasticsearchVersionString', ], 'PerformCheckOnly' => [ 'shape' => 'Boolean', ], ], ], 'UpgradeElasticsearchDomainResponse' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'TargetVersion' => [ 'shape' => 'ElasticsearchVersionString', ], 'PerformCheckOnly' => [ 'shape' => 'Boolean', ], 'ChangeProgressDetails' => [ 'shape' => 'ChangeProgressDetails', ], ], ], 'UpgradeHistory' => [ 'type' => 'structure', 'members' => [ 'UpgradeName' => [ 'shape' => 'UpgradeName', ], 'StartTimestamp' => [ 'shape' => 'StartTimestamp', ], 'UpgradeStatus' => [ 'shape' => 'UpgradeStatus', ], 'StepsList' => [ 'shape' => 'UpgradeStepsList', ], ], ], 'UpgradeHistoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpgradeHistory', ], ], 'UpgradeName' => [ 'type' => 'string', ], 'UpgradeStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCEEDED', 'SUCCEEDED_WITH_ISSUES', 'FAILED', ], ], 'UpgradeStep' => [ 'type' => 'string', 'enum' => [ 'PRE_UPGRADE_CHECK', 'SNAPSHOT', 'UPGRADE', ], ], 'UpgradeStepItem' => [ 'type' => 'structure', 'members' => [ 'UpgradeStep' => [ 'shape' => 'UpgradeStep', ], 'UpgradeStepStatus' => [ 'shape' => 'UpgradeStatus', ], 'Issues' => [ 'shape' => 'Issues', ], 'ProgressPercent' => [ 'shape' => 'Double', ], ], ], 'UpgradeStepsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpgradeStepItem', ], ], 'UserPoolId' => [ 'type' => 'string', 'max' => 55, 'min' => 1, 'pattern' => '[\\w-]+_[0-9a-zA-Z]+', ], 'Username' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'VPCDerivedInfo' => [ 'type' => 'structure', 'members' => [ 'VPCId' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'StringList', ], 'AvailabilityZones' => [ 'shape' => 'StringList', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], ], ], 'VPCDerivedInfoStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'VPCDerivedInfo', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'VPCOptions' => [ 'type' => 'structure', 'members' => [ 'SubnetIds' => [ 'shape' => 'StringList', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ValueStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'min' => 1, ], 'VolumeType' => [ 'type' => 'string', 'enum' => [ 'standard', 'gp2', 'io1', 'gp3', ], ], 'VpcEndpoint' => [ 'type' => 'structure', 'members' => [ 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], 'VpcEndpointOwner' => [ 'shape' => 'AWSAccount', ], 'DomainArn' => [ 'shape' => 'DomainArn', ], 'VpcOptions' => [ 'shape' => 'VPCDerivedInfo', ], 'Status' => [ 'shape' => 'VpcEndpointStatus', ], 'Endpoint' => [ 'shape' => 'Endpoint', ], ], ], 'VpcEndpointError' => [ 'type' => 'structure', 'members' => [ 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], 'ErrorCode' => [ 'shape' => 'VpcEndpointErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'VpcEndpointErrorCode' => [ 'type' => 'string', 'enum' => [ 'ENDPOINT_NOT_FOUND', 'SERVER_ERROR', ], ], 'VpcEndpointErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcEndpointError', ], ], 'VpcEndpointId' => [ 'type' => 'string', 'max' => 256, 'min' => 5, 'pattern' => '^aos-[a-zA-Z0-9]*$', ], 'VpcEndpointIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcEndpointId', ], ], 'VpcEndpointStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATE_FAILED', 'ACTIVE', 'UPDATING', 'UPDATE_FAILED', 'DELETING', 'DELETE_FAILED', ], ], 'VpcEndpointSummary' => [ 'type' => 'structure', 'members' => [ 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], 'VpcEndpointOwner' => [ 'shape' => 'String', ], 'DomainArn' => [ 'shape' => 'DomainArn', ], 'Status' => [ 'shape' => 'VpcEndpointStatus', ], ], ], 'VpcEndpointSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcEndpointSummary', ], ], 'VpcEndpoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcEndpoint', ], ], 'ZoneAwarenessConfig' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZoneCount' => [ 'shape' => 'IntegerClass', ], ], ], ],];
