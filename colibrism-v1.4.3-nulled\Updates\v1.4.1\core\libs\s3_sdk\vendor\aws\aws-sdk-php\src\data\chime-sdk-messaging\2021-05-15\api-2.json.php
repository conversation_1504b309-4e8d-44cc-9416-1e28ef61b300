<?php
// This file was auto-generated from sdk-root/src/data/chime-sdk-messaging/2021-05-15/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-05-15', 'endpointPrefix' => 'messaging-chime', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Chime SDK Messaging', 'serviceId' => 'Chime SDK Messaging', 'signatureVersion' => 'v4', 'signingName' => 'chime', 'uid' => 'chime-sdk-messaging-2021-05-15', ], 'operations' => [ 'AssociateChannelFlow' => [ 'name' => 'AssociateChannelFlow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channels/{channelArn}/channel-flow', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateChannelFlowRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchCreateChannelMembership' => [ 'name' => 'BatchCreateChannelMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}/memberships?operation=batch-create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchCreateChannelMembershipRequest', ], 'output' => [ 'shape' => 'BatchCreateChannelMembershipResponse', ], 'errors' => [ [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], ], ], 'ChannelFlowCallback' => [ 'name' => 'ChannelFlowCallback', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}?operation=channel-flow-callback', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ChannelFlowCallbackRequest', ], 'output' => [ 'shape' => 'ChannelFlowCallbackResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateChannel' => [ 'name' => 'CreateChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChannelRequest', ], 'output' => [ 'shape' => 'CreateChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateChannelBan' => [ 'name' => 'CreateChannelBan', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}/bans', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChannelBanRequest', ], 'output' => [ 'shape' => 'CreateChannelBanResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateChannelFlow' => [ 'name' => 'CreateChannelFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/channel-flows', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChannelFlowRequest', ], 'output' => [ 'shape' => 'CreateChannelFlowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateChannelMembership' => [ 'name' => 'CreateChannelMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}/memberships', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChannelMembershipRequest', ], 'output' => [ 'shape' => 'CreateChannelMembershipResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateChannelModerator' => [ 'name' => 'CreateChannelModerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}/moderators', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChannelModeratorRequest', ], 'output' => [ 'shape' => 'CreateChannelModeratorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteChannel' => [ 'name' => 'DeleteChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channels/{channelArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChannelRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteChannelBan' => [ 'name' => 'DeleteChannelBan', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channels/{channelArn}/bans/{memberArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChannelBanRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteChannelFlow' => [ 'name' => 'DeleteChannelFlow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channel-flows/{channelFlowArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChannelFlowRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteChannelMembership' => [ 'name' => 'DeleteChannelMembership', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channels/{channelArn}/memberships/{memberArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChannelMembershipRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteChannelMessage' => [ 'name' => 'DeleteChannelMessage', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channels/{channelArn}/messages/{messageId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChannelMessageRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteChannelModerator' => [ 'name' => 'DeleteChannelModerator', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channels/{channelArn}/moderators/{channelModeratorArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChannelModeratorRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteMessagingStreamingConfigurations' => [ 'name' => 'DeleteMessagingStreamingConfigurations', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/app-instances/{appInstanceArn}/streaming-configurations', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMessagingStreamingConfigurationsRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DescribeChannel' => [ 'name' => 'DescribeChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelRequest', ], 'output' => [ 'shape' => 'DescribeChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DescribeChannelBan' => [ 'name' => 'DescribeChannelBan', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/bans/{memberArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelBanRequest', ], 'output' => [ 'shape' => 'DescribeChannelBanResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DescribeChannelFlow' => [ 'name' => 'DescribeChannelFlow', 'http' => [ 'method' => 'GET', 'requestUri' => '/channel-flows/{channelFlowArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelFlowRequest', ], 'output' => [ 'shape' => 'DescribeChannelFlowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DescribeChannelMembership' => [ 'name' => 'DescribeChannelMembership', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/memberships/{memberArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelMembershipRequest', ], 'output' => [ 'shape' => 'DescribeChannelMembershipResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DescribeChannelMembershipForAppInstanceUser' => [ 'name' => 'DescribeChannelMembershipForAppInstanceUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}?scope=app-instance-user-membership', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelMembershipForAppInstanceUserRequest', ], 'output' => [ 'shape' => 'DescribeChannelMembershipForAppInstanceUserResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DescribeChannelModeratedByAppInstanceUser' => [ 'name' => 'DescribeChannelModeratedByAppInstanceUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}?scope=app-instance-user-moderated-channel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelModeratedByAppInstanceUserRequest', ], 'output' => [ 'shape' => 'DescribeChannelModeratedByAppInstanceUserResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DescribeChannelModerator' => [ 'name' => 'DescribeChannelModerator', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/moderators/{channelModeratorArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelModeratorRequest', ], 'output' => [ 'shape' => 'DescribeChannelModeratorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DisassociateChannelFlow' => [ 'name' => 'DisassociateChannelFlow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channels/{channelArn}/channel-flow/{channelFlowArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateChannelFlowRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetChannelMembershipPreferences' => [ 'name' => 'GetChannelMembershipPreferences', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/memberships/{memberArn}/preferences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelMembershipPreferencesRequest', ], 'output' => [ 'shape' => 'GetChannelMembershipPreferencesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetChannelMessage' => [ 'name' => 'GetChannelMessage', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/messages/{messageId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelMessageRequest', ], 'output' => [ 'shape' => 'GetChannelMessageResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetChannelMessageStatus' => [ 'name' => 'GetChannelMessageStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/messages/{messageId}?scope=message-status', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelMessageStatusRequest', ], 'output' => [ 'shape' => 'GetChannelMessageStatusResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetMessagingSessionEndpoint' => [ 'name' => 'GetMessagingSessionEndpoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/endpoints/messaging-session', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMessagingSessionEndpointRequest', ], 'output' => [ 'shape' => 'GetMessagingSessionEndpointResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetMessagingStreamingConfigurations' => [ 'name' => 'GetMessagingStreamingConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/app-instances/{appInstanceArn}/streaming-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMessagingStreamingConfigurationsRequest', ], 'output' => [ 'shape' => 'GetMessagingStreamingConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListChannelBans' => [ 'name' => 'ListChannelBans', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/bans', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelBansRequest', ], 'output' => [ 'shape' => 'ListChannelBansResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListChannelFlows' => [ 'name' => 'ListChannelFlows', 'http' => [ 'method' => 'GET', 'requestUri' => '/channel-flows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelFlowsRequest', ], 'output' => [ 'shape' => 'ListChannelFlowsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListChannelMemberships' => [ 'name' => 'ListChannelMemberships', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/memberships', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelMembershipsRequest', ], 'output' => [ 'shape' => 'ListChannelMembershipsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListChannelMembershipsForAppInstanceUser' => [ 'name' => 'ListChannelMembershipsForAppInstanceUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels?scope=app-instance-user-memberships', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelMembershipsForAppInstanceUserRequest', ], 'output' => [ 'shape' => 'ListChannelMembershipsForAppInstanceUserResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListChannelMessages' => [ 'name' => 'ListChannelMessages', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/messages', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelMessagesRequest', ], 'output' => [ 'shape' => 'ListChannelMessagesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListChannelModerators' => [ 'name' => 'ListChannelModerators', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/moderators', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelModeratorsRequest', ], 'output' => [ 'shape' => 'ListChannelModeratorsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListChannels' => [ 'name' => 'ListChannels', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelsRequest', ], 'output' => [ 'shape' => 'ListChannelsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListChannelsAssociatedWithChannelFlow' => [ 'name' => 'ListChannelsAssociatedWithChannelFlow', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels?scope=channel-flow-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelsAssociatedWithChannelFlowRequest', ], 'output' => [ 'shape' => 'ListChannelsAssociatedWithChannelFlowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListChannelsModeratedByAppInstanceUser' => [ 'name' => 'ListChannelsModeratedByAppInstanceUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels?scope=app-instance-user-moderated-channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelsModeratedByAppInstanceUserRequest', ], 'output' => [ 'shape' => 'ListChannelsModeratedByAppInstanceUserResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListSubChannels' => [ 'name' => 'ListSubChannels', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/subchannels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSubChannelsRequest', ], 'output' => [ 'shape' => 'ListSubChannelsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'PutChannelExpirationSettings' => [ 'name' => 'PutChannelExpirationSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channels/{channelArn}/expiration-settings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutChannelExpirationSettingsRequest', ], 'output' => [ 'shape' => 'PutChannelExpirationSettingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'PutChannelMembershipPreferences' => [ 'name' => 'PutChannelMembershipPreferences', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channels/{channelArn}/memberships/{memberArn}/preferences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutChannelMembershipPreferencesRequest', ], 'output' => [ 'shape' => 'PutChannelMembershipPreferencesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'PutMessagingStreamingConfigurations' => [ 'name' => 'PutMessagingStreamingConfigurations', 'http' => [ 'method' => 'PUT', 'requestUri' => '/app-instances/{appInstanceArn}/streaming-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutMessagingStreamingConfigurationsRequest', ], 'output' => [ 'shape' => 'PutMessagingStreamingConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'RedactChannelMessage' => [ 'name' => 'RedactChannelMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}/messages/{messageId}?operation=redact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RedactChannelMessageRequest', ], 'output' => [ 'shape' => 'RedactChannelMessageResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'SearchChannels' => [ 'name' => 'SearchChannels', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels?operation=search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchChannelsRequest', ], 'output' => [ 'shape' => 'SearchChannelsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'SendChannelMessage' => [ 'name' => 'SendChannelMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}/messages', 'responseCode' => 201, ], 'input' => [ 'shape' => 'SendChannelMessageRequest', ], 'output' => [ 'shape' => 'SendChannelMessageResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags?operation=tag-resource', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags?operation=untag-resource', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateChannel' => [ 'name' => 'UpdateChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channels/{channelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelRequest', ], 'output' => [ 'shape' => 'UpdateChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateChannelFlow' => [ 'name' => 'UpdateChannelFlow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channel-flows/{channelFlowArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelFlowRequest', ], 'output' => [ 'shape' => 'UpdateChannelFlowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateChannelMessage' => [ 'name' => 'UpdateChannelMessage', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channels/{channelArn}/messages/{messageId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelMessageRequest', ], 'output' => [ 'shape' => 'UpdateChannelMessageResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateChannelReadMarker' => [ 'name' => 'UpdateChannelReadMarker', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channels/{channelArn}/readMarker', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelReadMarkerRequest', ], 'output' => [ 'shape' => 'UpdateChannelReadMarkerResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], ], 'shapes' => [ 'AllowNotifications' => [ 'type' => 'string', 'enum' => [ 'ALL', 'NONE', 'FILTERED', ], ], 'AppInstanceUserMembershipSummary' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ChannelMembershipType', ], 'ReadMarkerTimestamp' => [ 'shape' => 'Timestamp', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], ], ], 'AssociateChannelFlowRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChannelFlowArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChannelFlowArn' => [ 'shape' => 'ChimeArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'BatchChannelMemberships' => [ 'type' => 'structure', 'members' => [ 'InvitedBy' => [ 'shape' => 'Identity', ], 'Type' => [ 'shape' => 'ChannelMembershipType', ], 'Members' => [ 'shape' => 'Members', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], ], ], 'BatchCreateChannelMembershipError' => [ 'type' => 'structure', 'members' => [ 'MemberArn' => [ 'shape' => 'ChimeArn', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'BatchCreateChannelMembershipErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateChannelMembershipError', ], ], 'BatchCreateChannelMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArns', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'Type' => [ 'shape' => 'ChannelMembershipType', ], 'MemberArns' => [ 'shape' => 'MemberArns', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], ], ], 'BatchCreateChannelMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'BatchChannelMemberships' => [ 'shape' => 'BatchChannelMemberships', ], 'Errors' => [ 'shape' => 'BatchCreateChannelMembershipErrors', ], ], ], 'CallbackIdType' => [ 'type' => 'string', 'max' => 64, 'min' => 32, ], 'Channel' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'Mode' => [ 'shape' => 'ChannelMode', ], 'Privacy' => [ 'shape' => 'ChannelPrivacy', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'CreatedBy' => [ 'shape' => 'Identity', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastMessageTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'ChannelFlowArn' => [ 'shape' => 'ChimeArn', ], 'ElasticChannelConfiguration' => [ 'shape' => 'ElasticChannelConfiguration', ], 'ExpirationSettings' => [ 'shape' => 'ExpirationSettings', ], ], ], 'ChannelAssociatedWithFlowSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'Mode' => [ 'shape' => 'ChannelMode', ], 'Privacy' => [ 'shape' => 'ChannelPrivacy', ], 'Metadata' => [ 'shape' => 'Metadata', ], ], ], 'ChannelAssociatedWithFlowSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelAssociatedWithFlowSummary', ], ], 'ChannelBan' => [ 'type' => 'structure', 'members' => [ 'Member' => [ 'shape' => 'Identity', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'Identity', ], ], ], 'ChannelBanSummary' => [ 'type' => 'structure', 'members' => [ 'Member' => [ 'shape' => 'Identity', ], ], ], 'ChannelBanSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelBanSummary', ], ], 'ChannelFlow' => [ 'type' => 'structure', 'members' => [ 'ChannelFlowArn' => [ 'shape' => 'ChimeArn', ], 'Processors' => [ 'shape' => 'ProcessorList', ], 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ChannelFlowCallbackRequest' => [ 'type' => 'structure', 'required' => [ 'CallbackId', 'ChannelArn', 'ChannelMessage', ], 'members' => [ 'CallbackId' => [ 'shape' => 'CallbackIdType', 'idempotencyToken' => true, ], 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'DeleteResource' => [ 'shape' => 'NonNullableBoolean', ], 'ChannelMessage' => [ 'shape' => 'ChannelMessageCallback', ], ], ], 'ChannelFlowCallbackResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'CallbackId' => [ 'shape' => 'CallbackIdType', ], ], ], 'ChannelFlowExecutionOrder' => [ 'type' => 'integer', 'max' => 3, 'min' => 1, ], 'ChannelFlowSummary' => [ 'type' => 'structure', 'members' => [ 'ChannelFlowArn' => [ 'shape' => 'ChimeArn', ], 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'Processors' => [ 'shape' => 'ProcessorList', ], ], ], 'ChannelFlowSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelFlowSummary', ], ], 'ChannelId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9]([A-Za-z0-9\\:\\-\\_\\.\\@]{0,62}[A-Za-z0-9])?', 'sensitive' => true, ], 'ChannelMemberArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChimeArn', ], 'max' => 10, 'min' => 1, ], 'ChannelMembership' => [ 'type' => 'structure', 'members' => [ 'InvitedBy' => [ 'shape' => 'Identity', ], 'Type' => [ 'shape' => 'ChannelMembershipType', ], 'Member' => [ 'shape' => 'Identity', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], ], ], 'ChannelMembershipForAppInstanceUserSummary' => [ 'type' => 'structure', 'members' => [ 'ChannelSummary' => [ 'shape' => 'ChannelSummary', ], 'AppInstanceUserMembershipSummary' => [ 'shape' => 'AppInstanceUserMembershipSummary', ], ], ], 'ChannelMembershipForAppInstanceUserSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelMembershipForAppInstanceUserSummary', ], ], 'ChannelMembershipPreferences' => [ 'type' => 'structure', 'members' => [ 'PushNotifications' => [ 'shape' => 'PushNotificationPreferences', ], ], ], 'ChannelMembershipSummary' => [ 'type' => 'structure', 'members' => [ 'Member' => [ 'shape' => 'Identity', ], ], ], 'ChannelMembershipSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelMembershipSummary', ], ], 'ChannelMembershipType' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'HIDDEN', ], ], 'ChannelMessage' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'MessageId' => [ 'shape' => 'MessageId', ], 'Content' => [ 'shape' => 'Content', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'Type' => [ 'shape' => 'ChannelMessageType', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastEditedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'Sender' => [ 'shape' => 'Identity', ], 'Redacted' => [ 'shape' => 'NonNullableBoolean', ], 'Persistence' => [ 'shape' => 'ChannelMessagePersistenceType', ], 'Status' => [ 'shape' => 'ChannelMessageStatusStructure', ], 'MessageAttributes' => [ 'shape' => 'MessageAttributeMap', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], 'ContentType' => [ 'shape' => 'ContentType', ], 'Target' => [ 'shape' => 'TargetList', ], ], ], 'ChannelMessageCallback' => [ 'type' => 'structure', 'required' => [ 'MessageId', ], 'members' => [ 'MessageId' => [ 'shape' => 'MessageId', ], 'Content' => [ 'shape' => 'NonEmptyContent', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'PushNotification' => [ 'shape' => 'PushNotificationConfiguration', ], 'MessageAttributes' => [ 'shape' => 'MessageAttributeMap', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], 'ContentType' => [ 'shape' => 'ContentType', ], ], ], 'ChannelMessagePersistenceType' => [ 'type' => 'string', 'enum' => [ 'PERSISTENT', 'NON_PERSISTENT', ], ], 'ChannelMessageStatus' => [ 'type' => 'string', 'enum' => [ 'SENT', 'PENDING', 'FAILED', 'DENIED', ], ], 'ChannelMessageStatusStructure' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'ChannelMessageStatus', ], 'Detail' => [ 'shape' => 'StatusDetail', ], ], ], 'ChannelMessageSummary' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'MessageId', ], 'Content' => [ 'shape' => 'Content', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'Type' => [ 'shape' => 'ChannelMessageType', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastEditedTimestamp' => [ 'shape' => 'Timestamp', ], 'Sender' => [ 'shape' => 'Identity', ], 'Redacted' => [ 'shape' => 'NonNullableBoolean', ], 'Status' => [ 'shape' => 'ChannelMessageStatusStructure', ], 'MessageAttributes' => [ 'shape' => 'MessageAttributeMap', ], 'ContentType' => [ 'shape' => 'ContentType', ], 'Target' => [ 'shape' => 'TargetList', ], ], ], 'ChannelMessageSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelMessageSummary', ], ], 'ChannelMessageType' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'CONTROL', ], ], 'ChannelMode' => [ 'type' => 'string', 'enum' => [ 'UNRESTRICTED', 'RESTRICTED', ], ], 'ChannelModeratedByAppInstanceUserSummary' => [ 'type' => 'structure', 'members' => [ 'ChannelSummary' => [ 'shape' => 'ChannelSummary', ], ], ], 'ChannelModeratedByAppInstanceUserSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelModeratedByAppInstanceUserSummary', ], ], 'ChannelModerator' => [ 'type' => 'structure', 'members' => [ 'Moderator' => [ 'shape' => 'Identity', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'Identity', ], ], ], 'ChannelModeratorArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChimeArn', ], 'max' => 10, 'min' => 1, ], 'ChannelModeratorSummary' => [ 'type' => 'structure', 'members' => [ 'Moderator' => [ 'shape' => 'Identity', ], ], ], 'ChannelModeratorSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelModeratorSummary', ], ], 'ChannelPrivacy' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'PRIVATE', ], ], 'ChannelSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'Mode' => [ 'shape' => 'ChannelMode', ], 'Privacy' => [ 'shape' => 'ChannelPrivacy', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'LastMessageTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ChannelSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelSummary', ], ], 'ChimeArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 5, 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 64, 'min' => 2, 'pattern' => '[-_a-zA-Z0-9]*', 'sensitive' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Content' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'ContentType' => [ 'type' => 'string', 'max' => 45, 'min' => 0, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'CreateChannelBanRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'CreateChannelBanResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'Member' => [ 'shape' => 'Identity', ], ], ], 'CreateChannelFlowRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', 'Processors', 'Name', 'ClientRequestToken', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', ], 'Processors' => [ 'shape' => 'ProcessorList', ], 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateChannelFlowResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelFlowArn' => [ 'shape' => 'ChimeArn', ], ], ], 'CreateChannelMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', 'Type', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', ], 'Type' => [ 'shape' => 'ChannelMembershipType', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], ], ], 'CreateChannelMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'Member' => [ 'shape' => 'Identity', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], ], ], 'CreateChannelModeratorRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChannelModeratorArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChannelModeratorArn' => [ 'shape' => 'ChimeArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'CreateChannelModeratorResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'ChannelModerator' => [ 'shape' => 'Identity', ], ], ], 'CreateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', 'Name', 'ClientRequestToken', 'ChimeBearer', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', ], 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'Mode' => [ 'shape' => 'ChannelMode', ], 'Privacy' => [ 'shape' => 'ChannelPrivacy', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'ChannelId' => [ 'shape' => 'ChannelId', ], 'MemberArns' => [ 'shape' => 'ChannelMemberArns', ], 'ModeratorArns' => [ 'shape' => 'ChannelModeratorArns', ], 'ElasticChannelConfiguration' => [ 'shape' => 'ElasticChannelConfiguration', ], 'ExpirationSettings' => [ 'shape' => 'ExpirationSettings', ], ], ], 'CreateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], ], ], 'DeleteChannelBanRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'memberArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DeleteChannelFlowRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelFlowArn', ], 'members' => [ 'ChannelFlowArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelFlowArn', ], ], ], 'DeleteChannelMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'memberArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', 'location' => 'querystring', 'locationName' => 'sub-channel-id', ], ], ], 'DeleteChannelMessageRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MessageId', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MessageId' => [ 'shape' => 'MessageId', 'location' => 'uri', 'locationName' => 'messageId', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', 'location' => 'querystring', 'locationName' => 'sub-channel-id', ], ], ], 'DeleteChannelModeratorRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChannelModeratorArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChannelModeratorArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelModeratorArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DeleteChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DeleteMessagingStreamingConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], ], ], 'DescribeChannelBanRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'memberArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DescribeChannelBanResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelBan' => [ 'shape' => 'ChannelBan', ], ], ], 'DescribeChannelFlowRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelFlowArn', ], 'members' => [ 'ChannelFlowArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelFlowArn', ], ], ], 'DescribeChannelFlowResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelFlow' => [ 'shape' => 'ChannelFlow', ], ], ], 'DescribeChannelMembershipForAppInstanceUserRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'AppInstanceUserArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'app-instance-user-arn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DescribeChannelMembershipForAppInstanceUserResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelMembership' => [ 'shape' => 'ChannelMembershipForAppInstanceUserSummary', ], ], ], 'DescribeChannelMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'memberArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', 'location' => 'querystring', 'locationName' => 'sub-channel-id', ], ], ], 'DescribeChannelMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelMembership' => [ 'shape' => 'ChannelMembership', ], ], ], 'DescribeChannelModeratedByAppInstanceUserRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'AppInstanceUserArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'app-instance-user-arn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DescribeChannelModeratedByAppInstanceUserResponse' => [ 'type' => 'structure', 'members' => [ 'Channel' => [ 'shape' => 'ChannelModeratedByAppInstanceUserSummary', ], ], ], 'DescribeChannelModeratorRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChannelModeratorArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChannelModeratorArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelModeratorArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DescribeChannelModeratorResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelModerator' => [ 'shape' => 'ChannelModerator', ], ], ], 'DescribeChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DescribeChannelResponse' => [ 'type' => 'structure', 'members' => [ 'Channel' => [ 'shape' => 'Channel', ], ], ], 'DisassociateChannelFlowRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChannelFlowArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChannelFlowArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelFlowArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ElasticChannelConfiguration' => [ 'type' => 'structure', 'required' => [ 'MaximumSubChannels', 'TargetMembershipsPerSubChannel', 'MinimumMembershipPercentage', ], 'members' => [ 'MaximumSubChannels' => [ 'shape' => 'MaximumSubChannels', ], 'TargetMembershipsPerSubChannel' => [ 'shape' => 'TargetMembershipsPerSubChannel', ], 'MinimumMembershipPercentage' => [ 'shape' => 'MinimumMembershipPercentage', ], ], ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'BadRequest', 'Conflict', 'Forbidden', 'NotFound', 'PreconditionFailed', 'ResourceLimitExceeded', 'ServiceFailure', 'AccessDenied', 'ServiceUnavailable', 'Throttled', 'Throttling', 'Unauthorized', 'Unprocessable', 'VoiceConnectorGroupAssociationsExist', 'PhoneNumberAssociationsExist', ], ], 'ExpirationCriterion' => [ 'type' => 'string', 'enum' => [ 'CREATED_TIMESTAMP', 'LAST_MESSAGE_TIMESTAMP', ], ], 'ExpirationDays' => [ 'type' => 'integer', 'max' => 5475, 'min' => 1, ], 'ExpirationSettings' => [ 'type' => 'structure', 'required' => [ 'ExpirationDays', 'ExpirationCriterion', ], 'members' => [ 'ExpirationDays' => [ 'shape' => 'ExpirationDays', ], 'ExpirationCriterion' => [ 'shape' => 'ExpirationCriterion', ], ], ], 'FallbackAction' => [ 'type' => 'string', 'enum' => [ 'CONTINUE', 'ABORT', ], ], 'FilterRule' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'GetChannelMembershipPreferencesRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'memberArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'GetChannelMembershipPreferencesResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'Member' => [ 'shape' => 'Identity', ], 'Preferences' => [ 'shape' => 'ChannelMembershipPreferences', ], ], ], 'GetChannelMessageRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MessageId', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MessageId' => [ 'shape' => 'MessageId', 'location' => 'uri', 'locationName' => 'messageId', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', 'location' => 'querystring', 'locationName' => 'sub-channel-id', ], ], ], 'GetChannelMessageResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelMessage' => [ 'shape' => 'ChannelMessage', ], ], ], 'GetChannelMessageStatusRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MessageId', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MessageId' => [ 'shape' => 'MessageId', 'location' => 'uri', 'locationName' => 'messageId', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', 'location' => 'querystring', 'locationName' => 'sub-channel-id', ], ], ], 'GetChannelMessageStatusResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'ChannelMessageStatusStructure', ], ], ], 'GetMessagingSessionEndpointRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetMessagingSessionEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'MessagingSessionEndpoint', ], ], ], 'GetMessagingStreamingConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], ], ], 'GetMessagingStreamingConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'StreamingConfigurations' => [ 'shape' => 'StreamingConfigurationList', ], ], ], 'Identity' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ChimeArn', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'InvocationType' => [ 'type' => 'string', 'enum' => [ 'ASYNC', ], ], 'LambdaConfiguration' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'InvocationType', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'LambdaFunctionArn', ], 'InvocationType' => [ 'shape' => 'InvocationType', ], ], ], 'LambdaFunctionArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 15, 'pattern' => 'arn:aws:lambda:[a-z]{2}-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9\\-_\\.]+(:(\\$LATEST|[a-zA-Z0-9\\-_]+))?', ], 'ListChannelBansRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ListChannelBansResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ChannelBans' => [ 'shape' => 'ChannelBanSummaryList', ], ], ], 'ListChannelFlowsRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'app-instance-arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListChannelFlowsResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelFlows' => [ 'shape' => 'ChannelFlowSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListChannelMembershipsForAppInstanceUserRequest' => [ 'type' => 'structure', 'required' => [ 'ChimeBearer', ], 'members' => [ 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'app-instance-user-arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ListChannelMembershipsForAppInstanceUserResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelMemberships' => [ 'shape' => 'ChannelMembershipForAppInstanceUserSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListChannelMembershipsRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'Type' => [ 'shape' => 'ChannelMembershipType', 'location' => 'querystring', 'locationName' => 'type', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', 'location' => 'querystring', 'locationName' => 'sub-channel-id', ], ], ], 'ListChannelMembershipsResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'ChannelMemberships' => [ 'shape' => 'ChannelMembershipSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListChannelMessagesRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'SortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sort-order', ], 'NotBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'not-before', ], 'NotAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'not-after', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', 'location' => 'querystring', 'locationName' => 'sub-channel-id', ], ], ], 'ListChannelMessagesResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ChannelMessages' => [ 'shape' => 'ChannelMessageSummaryList', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], ], ], 'ListChannelModeratorsRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ListChannelModeratorsResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ChannelModerators' => [ 'shape' => 'ChannelModeratorSummaryList', ], ], ], 'ListChannelsAssociatedWithChannelFlowRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelFlowArn', ], 'members' => [ 'ChannelFlowArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'channel-flow-arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListChannelsAssociatedWithChannelFlowResponse' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => 'ChannelAssociatedWithFlowSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListChannelsModeratedByAppInstanceUserRequest' => [ 'type' => 'structure', 'required' => [ 'ChimeBearer', ], 'members' => [ 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'app-instance-user-arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ListChannelsModeratedByAppInstanceUserResponse' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => 'ChannelModeratedByAppInstanceUserSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListChannelsRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', 'ChimeBearer', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'app-instance-arn', ], 'Privacy' => [ 'shape' => 'ChannelPrivacy', 'location' => 'querystring', 'locationName' => 'privacy', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ListChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => 'ChannelSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSubChannelsRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListSubChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'SubChannels' => [ 'shape' => 'SubChannelSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'MaximumSubChannels' => [ 'type' => 'integer', 'min' => 2, ], 'MemberArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChimeArn', ], 'max' => 100, 'min' => 1, ], 'Members' => [ 'type' => 'list', 'member' => [ 'shape' => 'Identity', ], ], 'MembershipCount' => [ 'type' => 'integer', ], 'MessageAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'MessageAttributeName', ], 'value' => [ 'shape' => 'MessageAttributeValue', ], ], 'MessageAttributeName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'MessageAttributeStringValue' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'MessageAttributeStringValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageAttributeStringValue', ], ], 'MessageAttributeValue' => [ 'type' => 'structure', 'members' => [ 'StringValues' => [ 'shape' => 'MessageAttributeStringValues', ], ], ], 'MessageId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[-_a-zA-Z0-9]*', ], 'MessagingDataType' => [ 'type' => 'string', 'enum' => [ 'Channel', 'ChannelMessage', ], ], 'MessagingSessionEndpoint' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'UrlType', ], ], ], 'Metadata' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', 'sensitive' => true, ], 'MinimumMembershipPercentage' => [ 'type' => 'integer', 'max' => 40, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '.*', 'sensitive' => true, ], 'NonEmptyContent' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'NonEmptyResourceName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', 'sensitive' => true, ], 'NonNullableBoolean' => [ 'type' => 'boolean', ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'Processor' => [ 'type' => 'structure', 'required' => [ 'Name', 'Configuration', 'ExecutionOrder', 'FallbackAction', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'Configuration' => [ 'shape' => 'ProcessorConfiguration', ], 'ExecutionOrder' => [ 'shape' => 'ChannelFlowExecutionOrder', ], 'FallbackAction' => [ 'shape' => 'FallbackAction', ], ], ], 'ProcessorConfiguration' => [ 'type' => 'structure', 'required' => [ 'Lambda', ], 'members' => [ 'Lambda' => [ 'shape' => 'LambdaConfiguration', ], ], ], 'ProcessorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Processor', ], 'max' => 3, 'min' => 1, ], 'PushNotificationBody' => [ 'type' => 'string', 'max' => 150, 'min' => 1, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'PushNotificationConfiguration' => [ 'type' => 'structure', 'members' => [ 'Title' => [ 'shape' => 'PushNotificationTitle', ], 'Body' => [ 'shape' => 'PushNotificationBody', ], 'Type' => [ 'shape' => 'PushNotificationType', ], ], ], 'PushNotificationPreferences' => [ 'type' => 'structure', 'required' => [ 'AllowNotifications', ], 'members' => [ 'AllowNotifications' => [ 'shape' => 'AllowNotifications', ], 'FilterRule' => [ 'shape' => 'FilterRule', ], ], ], 'PushNotificationTitle' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '.*', 'sensitive' => true, ], 'PushNotificationType' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'VOIP', ], ], 'PutChannelExpirationSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'ExpirationSettings' => [ 'shape' => 'ExpirationSettings', ], ], ], 'PutChannelExpirationSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'ExpirationSettings' => [ 'shape' => 'ExpirationSettings', ], ], ], 'PutChannelMembershipPreferencesRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', 'ChimeBearer', 'Preferences', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'memberArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'Preferences' => [ 'shape' => 'ChannelMembershipPreferences', ], ], ], 'PutChannelMembershipPreferencesResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'Member' => [ 'shape' => 'Identity', ], 'Preferences' => [ 'shape' => 'ChannelMembershipPreferences', ], ], ], 'PutMessagingStreamingConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', 'StreamingConfigurations', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], 'StreamingConfigurations' => [ 'shape' => 'StreamingConfigurationList', ], ], ], 'PutMessagingStreamingConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'StreamingConfigurations' => [ 'shape' => 'StreamingConfigurationList', ], ], ], 'RedactChannelMessageRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MessageId', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MessageId' => [ 'shape' => 'MessageId', 'location' => 'uri', 'locationName' => 'messageId', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], ], ], 'RedactChannelMessageResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'MessageId' => [ 'shape' => 'MessageId', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], ], ], 'ResourceLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceName' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', 'sensitive' => true, ], 'SearchChannelsRequest' => [ 'type' => 'structure', 'required' => [ 'Fields', ], 'members' => [ 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'Fields' => [ 'shape' => 'SearchFields', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'SearchChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => 'ChannelSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'SearchField' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', 'Operator', ], 'members' => [ 'Key' => [ 'shape' => 'SearchFieldKey', ], 'Values' => [ 'shape' => 'SearchFieldValues', ], 'Operator' => [ 'shape' => 'SearchFieldOperator', ], ], ], 'SearchFieldKey' => [ 'type' => 'string', 'enum' => [ 'MEMBERS', ], ], 'SearchFieldOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'INCLUDES', ], ], 'SearchFieldValue' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'SearchFieldValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchFieldValue', ], 'max' => 20, 'min' => 1, ], 'SearchFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchField', ], 'max' => 20, 'min' => 1, ], 'SendChannelMessageRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'Content', 'Type', 'Persistence', 'ClientRequestToken', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'Content' => [ 'shape' => 'NonEmptyContent', ], 'Type' => [ 'shape' => 'ChannelMessageType', ], 'Persistence' => [ 'shape' => 'ChannelMessagePersistenceType', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'PushNotification' => [ 'shape' => 'PushNotificationConfiguration', ], 'MessageAttributes' => [ 'shape' => 'MessageAttributeMap', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], 'ContentType' => [ 'shape' => 'ContentType', ], 'Target' => [ 'shape' => 'TargetList', ], ], ], 'SendChannelMessageResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'MessageId' => [ 'shape' => 'MessageId', ], 'Status' => [ 'shape' => 'ChannelMessageStatusStructure', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], ], ], 'ServiceFailureException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'StatusDetail' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'StreamingConfiguration' => [ 'type' => 'structure', 'required' => [ 'DataType', 'ResourceArn', ], 'members' => [ 'DataType' => [ 'shape' => 'MessagingDataType', ], 'ResourceArn' => [ 'shape' => 'ChimeArn', ], ], ], 'StreamingConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamingConfiguration', ], 'max' => 2, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'SubChannelId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[-_a-zA-Z0-9]*', ], 'SubChannelSummary' => [ 'type' => 'structure', 'members' => [ 'SubChannelId' => [ 'shape' => 'SubChannelId', ], 'MembershipCount' => [ 'shape' => 'MembershipCount', ], ], ], 'SubChannelSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubChannelSummary', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ChimeArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'sensitive' => true, ], 'Target' => [ 'type' => 'structure', 'members' => [ 'MemberArn' => [ 'shape' => 'ChimeArn', ], ], ], 'TargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Target', ], 'max' => 1, 'min' => 1, ], 'TargetMembershipsPerSubChannel' => [ 'type' => 'integer', 'min' => 2, ], 'ThrottledClientException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UnauthorizedClientException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ChimeArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UpdateChannelFlowRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelFlowArn', 'Processors', 'Name', ], 'members' => [ 'ChannelFlowArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelFlowArn', ], 'Processors' => [ 'shape' => 'ProcessorList', ], 'Name' => [ 'shape' => 'NonEmptyResourceName', ], ], ], 'UpdateChannelFlowResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelFlowArn' => [ 'shape' => 'ChimeArn', ], ], ], 'UpdateChannelMessageRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MessageId', 'Content', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MessageId' => [ 'shape' => 'MessageId', 'location' => 'uri', 'locationName' => 'messageId', ], 'Content' => [ 'shape' => 'NonEmptyContent', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], 'ContentType' => [ 'shape' => 'ContentType', ], ], ], 'UpdateChannelMessageResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'MessageId' => [ 'shape' => 'MessageId', ], 'Status' => [ 'shape' => 'ChannelMessageStatusStructure', ], 'SubChannelId' => [ 'shape' => 'SubChannelId', ], ], ], 'UpdateChannelReadMarkerRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'UpdateChannelReadMarkerResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], ], ], 'UpdateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChimeBearer', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'Mode' => [ 'shape' => 'ChannelMode', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'UpdateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], ], ], 'UrlType' => [ 'type' => 'string', 'max' => 4096, ], ],];
